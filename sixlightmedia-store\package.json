{"name": "sixlightmedia-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@imagekit/next": "^2.1.2", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "chart.js": "^4.4.9", "fabric": "^5.5.2", "gsap": "^3.13.0", "imagekit": "^6.0.0", "imagekitio-react": "^4.3.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "three": "^0.176.0", "zod": "^3.25.32"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/fabric": "^5.3.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}