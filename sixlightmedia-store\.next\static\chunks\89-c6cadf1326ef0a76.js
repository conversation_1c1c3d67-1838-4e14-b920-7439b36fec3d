(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[89],{5089:function(t,e,r){(function(t,e){"use strict";var r,n,o=e&&"object"==typeof e&&"default"in e?e:{default:e};function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){a(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var l={MANDATORY_INITIALIZATION_MISSING:{message:"Missing urlEndpoint during SDK initialization",help:""},INVALID_TRANSFORMATION_POSITION:{message:"Invalid transformationPosition parameter",help:""},MISSING_UPLOAD_FILE_PARAMETER:{message:"Missing file parameter for upload",help:""},MISSING_UPLOAD_FILENAME_PARAMETER:{message:"Missing fileName parameter for upload",help:""},MISSING_PUBLIC_KEY:{message:"Missing public key for upload",help:""},UPLOAD_ENDPOINT_NETWORK_ERROR:{message:"Request to ImageKit upload endpoint failed due to network error",help:""},INVALID_UPLOAD_OPTIONS:{message:"Invalid uploadOptions parameter",help:""},MISSING_SIGNATURE:{message:"Missing signature for upload. The SDK expects token, signature and expire for authentication.",help:""},MISSING_TOKEN:{message:"Missing token for upload. The SDK expects token, signature and expire for authentication.",help:""},MISSING_EXPIRE:{message:"Missing expire for upload. The SDK expects token, signature and expire for authentication.",help:""},INVALID_TRANSFORMATION:{message:"Invalid transformation parameter. Please include at least pre, post, or both.",help:""},INVALID_PRE_TRANSFORMATION:{message:"Invalid pre transformation parameter.",help:""},INVALID_POST_TRANSFORMATION:{message:"Invalid post transformation parameter.",help:""}};function f(t,e,r){"function"==typeof r&&(t?r(e,null):r(null,e))}var p=function(t,e){var r,n,o=s({},t);return Object.defineProperty(o,"$ResponseMetadata",{value:{statusCode:e.status,headers:(r={},Object.keys(n=e.getAllResponseHeaders()).length&&n.trim().split(/[\r\n]+/).map(function(t){return t.split(/: /)}).forEach(function(t){r[t[0].trim()]=t[1].trim()}),r)},enumerable:!1,writable:!1}),o},h=function(t,e,r){d(t,e).then(function(t){return f(!1,t,r)},function(t){return f(!0,t,r)})},d=function(t,e){return new Promise(function(r,n){t.open("POST","https://upload.imagekit.io/api/v1/files/upload"),t.onerror=function(t){return n(l.UPLOAD_ENDPOINT_NETWORK_ERROR)},t.onload=function(){if(200===t.status)try{var e=JSON.parse(t.responseText),o=p(e,t);return r(o)}catch(t){return n(t)}try{var e=JSON.parse(t.responseText),i=p(e,t);return n(i)}catch(t){return n(t)}},t.send(e)})},y=function(t,e,r,n){if(!e.file)return void f(!0,l.MISSING_UPLOAD_FILE_PARAMETER,n);if(!e.fileName)return void f(!0,l.MISSING_UPLOAD_FILENAME_PARAMETER,n);if(!r.publicKey)return void f(!0,l.MISSING_PUBLIC_KEY,n);if(!e.token)return void f(!0,l.MISSING_TOKEN,n);if(!e.signature)return void f(!0,l.MISSING_SIGNATURE,n);if(!e.expire)return void f(!0,l.MISSING_EXPIRE,n);if(e.transformation){if(!(Object.keys(e.transformation).includes("pre")||Object.keys(e.transformation).includes("post")))return void f(!0,l.INVALID_TRANSFORMATION,n);if(Object.keys(e.transformation).includes("pre")&&!e.transformation.pre)return void f(!0,l.INVALID_PRE_TRANSFORMATION,n);if(Object.keys(e.transformation).includes("post"))if(!Array.isArray(e.transformation.post))return void f(!0,l.INVALID_POST_TRANSFORMATION,n);else{var o,a,u=function(t,e){if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(o=function(t,e){if(t){if("string"==typeof t)return c(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return c(t,void 0)}}(t))){o&&(t=o);var r=0,n=function(){};return{s:n,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:n}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i,a=!0,u=!1;return{s:function(){o=t[Symbol.iterator]()},n:function(){var t=o.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==o.return||o.return()}finally{if(u)throw i}}}}(e.transformation.post);try{for(u.s();!(a=u.n()).done;){var s=a.value;if("abs"===s.type&&!(s.protocol||s.value)||"transformation"===s.type&&!s.value)return void f(!0,l.INVALID_POST_TRANSFORMATION,n)}}catch(t){u.e(t)}finally{u.f()}}}var p=new FormData;for(o in e)o&&("file"===o&&"string"!=typeof e.file?p.append("file",e.file,String(e.fileName)):"tags"===o&&Array.isArray(e.tags)?p.append("tags",e.tags.join(",")):"signature"===o?p.append("signature",e.signature):"expire"===o?p.append("expire",String(e.expire)):"token"===o?p.append("token",e.token):"responseFields"===o&&Array.isArray(e.responseFields)?p.append("responseFields",e.responseFields.join(",")):"extensions"===o&&Array.isArray(e.extensions)?p.append("extensions",JSON.stringify(e.extensions)):"customMetadata"!==o||"object"!==i(e.customMetadata)||Array.isArray(e.customMetadata)||null===e.customMetadata?"transformation"===o&&"object"===i(e.transformation)&&null!==e.transformation?p.append(o,JSON.stringify(e.transformation)):"checks"===o&&e.checks?p.append("checks",e.checks):void 0!==e[o]&&p.append(o,String(e[o])):p.append("customMetadata",JSON.stringify(e.customMetadata)));p.append("publicKey",r.publicKey),h(t,p,n)},m={width:"w",height:"h",aspectRatio:"ar",quality:"q",crop:"c",cropMode:"cm",focus:"fo",x:"x",y:"y",format:"f",radius:"r",background:"bg",border:"b",rotation:"rt",rotate:"rt",blur:"bl",named:"n",progressive:"pr",lossless:"lo",trim:"t",metadata:"md",colorProfile:"cp",defaultImage:"di",dpr:"dpr",effectSharpen:"e-sharpen",effectUSM:"e-usm",effectContrast:"e-contrast",effectGray:"e-grayscale",original:"orig",effectShadow:"e-shadow",effectGradient:"e-gradient",raw:"raw"},v="path",g="query",b=[v,g],O={getDefault:function(){return v},addAsQueryParameter:function(t){return t.transformationPosition===g},validParameters:function(t){return void 0!==t.transformationPosition&&-1!=b.indexOf(t.transformationPosition)},getTransformKey:function(t){return t&&(m[t]||m[t.toLowerCase()])||""},getChainTransformDelimiter:function(){return":"},getTransformDelimiter:function(){return","},getTransformKeyValueDelimiter:function(){return"-"}};function S(t,e){var r=e||"/",n=RegExp(r+"{1,}","g");return t.join(r).replace(n,r)}var w=function(t){if(!t.path&&!t.src)return"";try{t.path?(o=new URL(t.urlEndpoint).pathname,r=new URL(S([t.urlEndpoint.replace(o,""),t.path]))):(r=new URL(t.src),n=!0)}catch(t){return console.error(t),""}for(var e in t.queryParameters)r.searchParams.append(e,String(t.queryParameters[e]));var r,n,o,i=function(t){if(!Array.isArray(t))return"";for(var e=[],r=0,n=t.length;r<n;r++){var o=[];for(var i in t[r])if(void 0!==t[r][i]&&null!==t[r][i]){var a=O.getTransformKey(i);if(a||(a=i),"-"===t[r][i])o.push(a);else if("raw"===i)o.push(t[r][i]);else{var u,s,c=t[r][i];"di"===a&&("string"==typeof("string"==typeof(u=c||"")&&"/"==u[0]&&(u=u.slice(1)),s=u)&&"/"==s[s.length-1]&&(s=s.substring(0,s.length-1)),c=(c=s).replace(/\//g,"@@")),o.push([a,c].join(O.getTransformKeyValueDelimiter()))}}e.push(o.join(O.getTransformDelimiter()))}return e.join(O.getChainTransformDelimiter())}(t.transformation);return i&&i.length&&(O.addAsQueryParameter(t)||n?r.searchParams.append("tr",i):r.pathname=S(["tr"+O.getChainTransformDelimiter()+i,r.pathname])),o?r.pathname=S([o,r.pathname]):r.pathname=S([r.pathname]),r.href},I=function(){var t;function e(t){if(!(this instanceof e))throw TypeError("Cannot call a class as a function");if(a(this,"options",{sdkVersion:"javascript-".concat("3.0.2"),publicKey:"",urlEndpoint:"",transformationPosition:O.getDefault()}),this.options=s(s({},this.options),t||{}),!this.options.urlEndpoint)throw l.MANDATORY_INITIALIZATION_MISSING;if(!O.validParameters(this.options))throw l.INVALID_TRANSFORMATION_POSITION}return t=[{key:"url",value:function(t){return w(s(s({},this.options),t))}},{key:"upload",value:function(t,e,r){if("function"==typeof e?o=e:r=e||{},!t||"object"!==i(t))return f(!0,l.INVALID_UPLOAD_OPTIONS,o);var n,o,a=s(s({},this.options),r),u=(t||{}).xhr;delete t.xhr;var c=u||new XMLHttpRequest;return(n=this,function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(e.length!==y.length||void 0===e[e.length-1])return new Promise(function(t,r){e.pop(),e.push(function(e){if(e)return r(e);for(var n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];t(o.length>1?o:o[0])}),y.call.apply(y,[n].concat(e))});if("function"!=typeof e[e.length-1])throw Error("Callback must be a function.");y.call.apply(y,[n].concat(e))})(c,t,a,o)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}(e.prototype,t),e}();function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function E(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){!function(t,e,r){var n;(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:String(n))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(){A=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function l(e,r,n,i){var a,u,s,c,l=Object.create((r&&r.prototype instanceof m?r:m).prototype);return o(l,"_invoke",{value:(a=e,u=n,s=new N(i||[]),c=p,function(e,r){if(c===h)throw Error("Generator is already running");if(c===d){if("throw"===e)throw r;return{value:t,done:!0}}for(s.method=e,s.arg=r;;){var n=s.delegate;if(n){var o=function e(r,n){var o=n.method,i=r.iterator[o];if(i===t)return n.delegate=null,"throw"===o&&r.iterator.return&&(n.method="return",n.arg=t,e(r,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=TypeError("The iterator does not provide a '"+o+"' method")),y;var a=f(i,r.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,y;var u=a.arg;return u?u.done?(n[r.resultName]=u.value,n.next=r.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):u:(n.method="throw",n.arg=TypeError("iterator result is not an object"),n.delegate=null,y)}(n,s);if(o){if(o===y)continue;return o}}if("next"===s.method)s.sent=s._sent=s.arg;else if("throw"===s.method){if(c===p)throw c=d,s.arg;s.dispatchException(s.arg)}else"return"===s.method&&s.abrupt("return",s.arg);c=h;var i=f(a,u,s);if("normal"===i.type){if(c=s.done?d:"suspendedYield",i.arg===y)continue;return{value:i.arg,done:s.done}}"throw"===i.type&&(c=d,s.method="throw",s.arg=i.arg)}})}),l}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="executing",d="completed",y={};function m(){}function v(){}function g(){}var b={};c(b,a,function(){return this});var O=Object.getPrototypeOf,S=O&&O(O(x([])));S&&S!==r&&n.call(S,a)&&(b=S);var w=g.prototype=m.prototype=Object.create(b);function I(t){["next","throw","return"].forEach(function(e){c(t,e,function(t){return this._invoke(e,t)})})}function P(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(r,a){!function r(o,i,a,u){var s=f(t[o],t,i);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==typeof l&&n.call(l,"__await")?e.resolve(l.__await).then(function(t){r("next",t,a,u)},function(t){r("throw",t,a,u)}):e.resolve(l).then(function(t){c.value=t,a(c)},function(t){return r("throw",t,a,u)})}u(s.arg)}(o,i,r,a)})}return r=r?r.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function x(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return v.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:v,configurable:!0}),v.displayName=c(g,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,c(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},e.awrap=function(t){return{__await:t}},I(P.prototype),c(P.prototype,u,function(){return this}),e.AsyncIterator=P,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new P(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},I(w),c(w,s,"Generator"),c(w,a,function(){return this}),c(w,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=x,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:x(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(t,e,r,n,o,i,a){try{var u=t[i](a),s=u.value}catch(t){r(t);return}u.done?e(s):Promise.resolve(s).then(n,o)}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function j(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function _(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],s=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||M(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){if(t){if("string"==typeof t)return L(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return L(t,e)}}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function k(){}function R(){}R.resetWarningCache=k;var D=function(){function t(t,e,r,n,o,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:R,resetWarningCache:k};return r.PropTypes=r,r},C=(r=n={path:void 0,exports:{},require:function(t,e){throw null==e&&n.path,Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}},n.exports,r.exports=D(),n.exports),q={publicKey:C.string,urlEndpoint:C.string,authenticator:C.func},F=E(E({},q),{},{transformationPosition:C.oneOf(["path","query"])}),U=E(E({},F),{},{ikClient:C.instanceOf(I)}),K=e.createContext({}),G={loading:C.oneOf(["lazy"]),lqip:C.shape({active:C.bool,quality:C.number,threshold:C.number,blur:C.number,raw:C.string}),path:C.string,src:C.string,queryParameters:C.objectOf(C.oneOfType([C.string,C.number]).isRequired),transformation:C.arrayOf(C.object.isRequired),transformationPosition:C.oneOf(["path","query"])},V=E(E({},q),G),z=function(){try{return navigator.connection.effectiveType}catch(t){return"4g"}},Y=function(t,e,r){var n,o=t.urlEndpoint,i=t.lqip,a=t.src,u=t.path,s=t.transformation,c=t.transformationPosition,l=t.queryParameters;if(a)n={urlEndpoint:o||r.urlEndpoint,src:a,transformation:s||void 0,transformationPosition:c||r.transformationPosition||void 0,queryParameters:l||{}};else{if(!u)return{originalSrc:""};n={urlEndpoint:o||r.urlEndpoint,path:u,transformation:s||void 0,transformationPosition:c||r.transformationPosition||void 0,queryParameters:l||{}}}var f={originalSrc:e.url(n)};if(i&&i.active){var p,h=Math.round(i.quality||i.threshold||20),d=Math.round(i.blur||6),y=n.transformation?function(t){if(Array.isArray(t))return L(t)}(p=n.transformation)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(p)||M(p)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}():[];i.raw&&"string"==typeof i.raw&&""!==i.raw.trim()?y.push({raw:i.raw.trim()}):y.push({quality:String(h),blur:String(d)}),f.lqipSrc=e.url(E(E({},n),{},{transformation:y}))}return f},J=function(t,e){var r=t.lqip,n=void 0===r?null:r,o=t.loading,i=e.intersected,a=e.originalSrcLoaded,u=e.originalSrc,s=e.lqipSrc,c=function(t){return t&&t.active};if("lazy"!==o&&!c(n))return u;if("lazy"!==o&&c(n))if(a)return u;else return s;if("lazy"!==o||c(n))if(i&&a)return u;else return s;if(i)return u;return""},W=function(t){var r=e.useContext(K);return{getIKClient:function(){if(r&&r.ikClient)return r.ikClient;var e=t.urlEndpoint;if(!(e=e||r&&r.urlEndpoint)||""===e.trim())throw Error("Missing urlEndpoint during initialization");return new I({urlEndpoint:e,sdkVersion:""})}}},H=["urlEndpoint","authenticator","publicKey","loading","lqip","path","src","transformation","transformationPosition","queryParameters"],X=function(t){var r,n,i=e.useRef(null),a=W(E({},t)).getIKClient,u=e.useContext(K),s=_(e.useState(void 0),2),c=s[0],l=s[1],f=_(e.useState(""),2),p=f[0],h=f[1],d=_(e.useState(""),2),y=d[0],m=d[1],v=_(e.useState(!1),2),g=v[0],b=v[1],O=_(e.useState(void 0),2),S=O[0],w=O[1],I=_(e.useState(!1),2),P=I[0],T=I[1],M=_(e.useState(!1),2),L=M[0],k=M[1];e.useEffect(function(){var e=Y(t,a(),u),r=e.originalSrc,n=e.lqipSrc;h(r),m(n||""),T(!0)},[u,t]);var R=(r=A().mark(function e(){var r;return A().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,J(t,{originalSrc:p,lqipSrc:y,intersected:L,contextOptions:u,initialzeState:P,originalSrcLoaded:g,observe:S});case 2:(r=e.sent)&&l(r);case 4:case"end":return e.stop()}},e)}),n=function(){var t=this,e=arguments;return new Promise(function(n,o){var i=r.apply(t,e);function a(t){N(i,n,o,a,u,"next",t)}function u(t){N(i,n,o,a,u,"throw",t)}a(void 0)})},function(){return n.apply(this,arguments)}),D=function(){var t=new Image;t.onload=function(){b(!0)},t.src=p};e.useEffect(function(){g&&R()},[g]),e.useEffect(function(){var e=i.current,r=t.loading;if(P)if(window&&"IntersectionObserver"in window&&"lazy"===r){var n=z(),o="1250px";"4g"!==n&&(o="2500px");var a=new IntersectionObserver(function(t){var e=t[0];e&&e.isIntersecting&&!L&&(k(!0),w(function(t){t&&t.disconnect()}),D(),R())},{rootMargin:"".concat(o," 0px ").concat(o," 0px")});e&&(a.observe(e),w(a))}else k(!0),D(),R();return function(){S&&S.disconnect()}},[t,p,y]),t.urlEndpoint,t.authenticator,t.publicKey,t.loading,t.lqip,t.path,t.src,t.transformation,t.transformationPosition,t.queryParameters;var C=j(t,H);return o.default.createElement("img",x({alt:t.alt||"",src:c||void 0,ref:i},C))};X.propTypes=V;var B={path:C.string,src:C.string,queryParameters:C.objectOf(C.oneOfType([C.string,C.number]).isRequired),transformation:C.arrayOf(C.object.isRequired),transformationPosition:C.oneOf(["path","query"])},$=E(E({},q),B),Q=["urlEndpoint","publicKey","authenticator","path","src","transformation","transformationPosition","queryParameters"],Z=function(t){var r=e.useRef(null),n=_(e.useState({currentUrl:void 0,contextOptions:{}}),2),i=n[0],a=n[1],u=W(E({},t)).getIKClient,s=e.useContext(K);e.useEffect(function(){var e=Y(t,u(),s).originalSrc;a(function(t){return E(E({},t),{},{currentUrl:e,contextOptions:s})})},[s,t]);var c=i.currentUrl;t.urlEndpoint,t.publicKey,t.authenticator,t.path,t.src,t.transformation,t.transformationPosition,t.queryParameters;var l=j(t,Q);return o.default.createElement("video",x({},l,{ref:r,key:c}),o.default.createElement("source",{src:c,type:"video/mp4"}))};Z.propTypes=$;var tt=["publicKey","urlEndpoint","authenticator","fileName","useUniqueFileName","tags","folder","isPrivateFile","customCoordinates","responseFields","onError","onSuccess","onUploadStart","onUploadProgress","validateFile","webhookUrl","overwriteFile","overwriteAITags","overwriteTags","overwriteCustomMetadata","extensions","customMetadata","transformation","checks","overrideParameters"],te=e.forwardRef(function(t,r){var n=_(e.useState({}),2),i=n[0],a=n[1],u=e.useContext(K),s=W(E({},t)).getIKClient;e.useEffect(function(){r&&"object"===T(r)&&r.hasOwnProperty("current")&&(r.current.abort=function(){i.xhr&&i.xhr.abort()})},[i.xhr,r]),t.publicKey,t.urlEndpoint,t.authenticator;var c=t.fileName,l=t.useUniqueFileName,f=t.tags,p=t.folder,h=t.isPrivateFile,d=t.customCoordinates,y=t.responseFields,m=t.onError,v=t.onSuccess;t.onUploadStart,t.onUploadProgress,t.validateFile;var g=t.webhookUrl,b=t.overwriteFile,O=t.overwriteAITags,S=t.overwriteTags,w=t.overwriteCustomMetadata,I=t.extensions,P=t.customMetadata,A=t.transformation,N=t.checks;t.overrideParameters;var M=j(t,tt),L=function(e){var r,n=t.publicKey||u.publicKey,o=t.authenticator||u.authenticator,i=t.urlEndpoint||u.urlEndpoint;if(!n||""===n.trim()){console.error("Missing publicKey"),m&&"function"==typeof m&&m({message:"Missing publicKey"});return}if(!o){console.error("The authenticator function is not provided."),m&&"function"==typeof m&&m({message:"The authenticator function is not provided."});return}if("function"!=typeof o){console.error("The provided authenticator is not a function."),m&&"function"==typeof m&&m({message:"The provided authenticator is not a function."});return}if(!i||""===i.trim()){console.error("Missing urlEndpoint"),m&&"function"==typeof m&&m({message:"Missing urlEndpoint"});return}var E=s(),T=null==(r=e.target.files)?void 0:r[0];if(T&&(!t.validateFile||t.validateFile(T))){t.onUploadStart&&"function"==typeof t.onUploadStart&&t.onUploadStart(e);var x={};t.overrideParameters&&"function"==typeof t.overrideParameters&&(x=t.overrideParameters(T)||{});var j=new XMLHttpRequest,_=function(e){t.onUploadProgress&&"function"==typeof t.onUploadProgress&&t.onUploadProgress(e)};j.upload.addEventListener("progress",_);var M={file:T,fileName:x.fileName||c||T.name,useUniqueFileName:x.useUniqueFileName||l,tags:x.tags||f,folder:x.folder||p,isPrivateFile:x.isPrivateFile||h,customCoordinates:x.customCoordinates||d,responseFields:y,extensions:x.extensions||I,webhookUrl:x.webhookUrl||g,overwriteFile:x.overwriteFile||b,overwriteAITags:x.overwriteAITags||O,overwriteTags:x.overwriteTags||S,overwriteCustomMetadata:x.overwriteCustomMetadata||w,customMetadata:x.customMetadata||P,signature:"",expire:0,token:"",xhr:j,transformation:x.transformation||A,checks:x.checks||N},L=o();if(!(L instanceof Promise)){m&&"function"==typeof m&&m({message:"The authenticator function is expected to return a Promise instance."});return}L.then(function(t){var e=t.signature,r=t.token,o=t.expire;M.signature=e,M.expire=o,M.token=r,E.upload(M,function(t,e){t?m&&"function"==typeof m&&(console.log(t),m(t)):v&&"function"==typeof v&&v(e),j.upload.removeEventListener("progress",_)},{publicKey:n}),a({xhr:j})}).catch(function(t){var e;e=t instanceof Array?t[0]:t,m&&"function"==typeof m&&m({message:String(e)})})}};return o.default.createElement("input",x({},M,{ref:r,type:"file",onChange:function(e){t.onChange&&"function"==typeof t.onChange&&t.onChange(e),L(e)}}))});t.IKContext=function(t){var e=function(t){for(var e={},r=Object.keys(U),n=0;n<r.length;n++){var o=r[n],i=t[o];i&&(e[o]=i)}return e}(E({},t));return e.urlEndpoint&&""!==e.urlEndpoint.trim()&&(e.ikClient=new I({urlEndpoint:e.urlEndpoint,sdkVersion:""})),o.default.createElement(K.Provider,{value:e},t.children)},t.IKCore=I,t.IKImage=X,t.IKUpload=te,t.IKVideo=Z,Object.defineProperty(t,"__esModule",{value:!0})})(e,r(2115))}}]);