{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n      VERIFY_EMAIL: \"/auth/verify-email\",\n      FORGOT_PASSWORD: \"/auth/forgot-password\",\n      RESET_PASSWORD: \"/auth/reset-password\",\n      RESEND_VERIFICATION: \"/auth/resend-verification\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      UPDATE_PROFILE: \"/user/profile\",\n      UPLOAD_PROFILE_IMAGE: \"/user/upload-profile-image\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACjB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;QACvB;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,6EAAgE;IAClE,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/api/auth.ts"], "sourcesContent": ["import { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport async function login(email: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function register(email: string, password: string, name?: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password, name }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function verifyEmail(token: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.VERIFY_EMAIL), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function forgotPassword(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.FORGOT_PASSWORD),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n\r\nexport async function resetPassword(token: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function resendVerification(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESEND_VERIFICATION),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,eAAe,MAAM,KAAa,EAAE,QAAgB;IACzD,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG;QAClE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAa;IAC3E,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;YAAU;QAAK;IAC/C;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,YAAY,KAAa;IAC7C,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG;QACzE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,eAAe,KAAa;IAChD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GACnD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,cAAc,KAAa,EAAE,QAAgB;IACjE,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,GAAG;QAC3E,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,mBAAmB,KAAa;IACpD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,GACvD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/forgot-password/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport { forgotPassword } from \"@/api/auth\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { z } from \"zod\";\nimport { Mail, ArrowLeft, CheckCircle, AlertCircle } from \"lucide-react\";\n\n// Zod validation schema\nconst forgotPasswordSchema = z.object({\n  email: z.string().email(\"Please enter a valid email address\"),\n});\n\ntype ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;\n\nexport default function ForgotPasswordPage() {\n  const [formData, setFormData] = useState<ForgotPasswordFormData>({\n    email: \"\",\n  });\n  const [errors, setErrors] = useState<Partial<ForgotPasswordFormData>>({});\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState(false);\n  const [message, setMessage] = useState(\"\");\n\n  const handleInputChange = (field: keyof ForgotPasswordFormData, value: string) => {\n    setFormData((prev) => ({ ...prev, [field]: value }));\n    // Clear field-specific error when user starts typing\n    if (errors[field]) {\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setErrors({});\n    setMessage(\"\");\n\n    // Validate form data with Zod\n    const validation = forgotPasswordSchema.safeParse(formData);\n    if (!validation.success) {\n      setLoading(false);\n      const fieldErrors: Partial<ForgotPasswordFormData> = {};\n      validation.error.errors.forEach((err) => {\n        if (err.path[0]) {\n          fieldErrors[err.path[0] as keyof ForgotPasswordFormData] = err.message;\n        }\n      });\n      setErrors(fieldErrors);\n      return;\n    }\n\n    try {\n      const result = await forgotPassword(formData.email);\n      setLoading(false);\n      setSuccess(true);\n      setMessage(result.message || \"Password reset link sent to your email!\");\n    } catch (error) {\n      setLoading(false);\n      setMessage(\"Failed to send reset email. Please try again.\");\n    }\n  };\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-4\">\n        <div className=\"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100\">\n          <div className=\"flex flex-col items-center mb-6\">\n            <Image\n              src=\"/6 Light Logo.png\"\n              alt=\"6 Light Logo\"\n              width={64}\n              height={64}\n              className=\"mb-4\"\n            />\n            <CheckCircle className=\"h-16 w-16 text-green-500 mb-4\" />\n            <h1 className=\"text-2xl font-bold text-green-700\">Email Sent!</h1>\n          </div>\n\n          <div className=\"space-y-4\">\n            <p className=\"text-gray-600\">{message}</p>\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <p className=\"text-sm text-blue-800\">\n                <strong>📧 Check your email:</strong> We've sent a password reset link to{\" \"}\n                <span className=\"font-mono\">{formData.email}</span>\n              </p>\n            </div>\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <p className=\"text-sm text-yellow-800\">\n                <strong>⏰ Link expires in 1 hour</strong> for your security.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"space-y-3 mt-6\">\n            <Link\n              href=\"/login\"\n              className=\"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Login\n            </Link>\n            <button\n              onClick={() => {\n                setSuccess(false);\n                setFormData({ email: \"\" });\n                setMessage(\"\");\n              }}\n              className=\"w-full text-gray-600 hover:text-gray-800 transition-colors\"\n            >\n              Send to different email\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4\">\n      <div className=\"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100\">\n        <div className=\"flex flex-col items-center mb-6\">\n          <Image\n            src=\"/6 Light Logo.png\"\n            alt=\"6 Light Logo\"\n            width={64}\n            height={64}\n            className=\"mb-4\"\n          />\n          <h1 className=\"text-2xl font-bold text-gray-900\">Forgot Password</h1>\n          <p className=\"text-gray-600 text-center mt-2\">\n            Enter your email address and we'll send you a link to reset your password.\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-1\">\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n              Email Address\n            </label>\n            <div className=\"relative\">\n              <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n              <input\n                id=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => handleInputChange(\"email\", e.target.value)}\n                placeholder=\"Enter your email address\"\n                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ${\n                  errors.email\n                    ? \"border-red-500 focus:ring-red-300\"\n                    : \"border-gray-300 focus:ring-blue-300\"\n                }`}\n                autoComplete=\"email\"\n                aria-invalid={!!errors.email}\n                aria-describedby={errors.email ? \"email-error\" : undefined}\n              />\n            </div>\n            {errors.email && (\n              <div\n                id=\"email-error\"\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\n              >\n                <AlertCircle size={16} />\n                {errors.email}\n              </div>\n            )}\n          </div>\n\n          <button\n            type=\"submit\"\n            className={`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${\n              loading\n                ? \"bg-gray-400 cursor-not-allowed\"\n                : \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white\"\n            }`}\n            disabled={loading}\n          >\n            {loading ? (\n              <div className=\"flex items-center justify-center gap-2\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                <span>Sending Reset Link...</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center justify-center gap-2\">\n                <Mail className=\"h-4 w-4\" />\n                <span>Send Reset Link</span>\n              </div>\n            )}\n          </button>\n\n          {message && !success && (\n            <div className=\"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200\">\n              <AlertCircle size={16} />\n              {message}\n            </div>\n          )}\n        </form>\n\n        <div className=\"mt-6 pt-6 border-t border-gray-200 text-center\">\n          <Link\n            href=\"/login\"\n            className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\"\n          >\n            <ArrowLeft className=\"h-4 w-4\" />\n            Back to Login\n          </Link>\n        </div>\n\n        <div className=\"mt-4 text-center\">\n          <p className=\"text-sm text-gray-500\">\n            Don't have an account?{\" \"}\n            <Link href=\"/register\" className=\"text-blue-600 hover:text-blue-800 font-medium\">\n              Sign up\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;;AAQA,wBAAwB;AACxB,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;QAC/D,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC,CAAC;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAC,OAAqC;QAC9D,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,qDAAqD;QACrD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,UAAU,CAAC;QACX,WAAW;QAEX,8BAA8B;QAC9B,MAAM,aAAa,qBAAqB,SAAS,CAAC;QAClD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,WAAW;YACX,MAAM,cAA+C,CAAC;YACtD,WAAW,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC/B,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;oBACf,WAAW,CAAC,IAAI,IAAI,CAAC,EAAE,CAAiC,GAAG,IAAI,OAAO;gBACxE;YACF;YACA,UAAU;YACV;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,KAAK;YAClD,WAAW;YACX,WAAW;YACX,WAAW,OAAO,OAAO,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,WAAW;YACX,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;0CAC9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;sDAAO;;;;;;wCAA6B;wCAAqC;sDAC1E,8OAAC;4CAAK,WAAU;sDAAa,SAAS,KAAK;;;;;;;;;;;;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;sDAAO;;;;;;wCAAiC;;;;;;;;;;;;;;;;;;kCAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;oCACP,WAAW;oCACX,YAAY;wCAAE,OAAO;oCAAG;oCACxB,WAAW;gCACb;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;8BAKhD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA0C;;;;;;8CAG3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,aAAY;4CACZ,WAAW,CAAC,oFAAoF,EAC9F,OAAO,KAAK,GACR,sCACA,uCACJ;4CACF,cAAa;4CACb,gBAAc,CAAC,CAAC,OAAO,KAAK;4CAC5B,oBAAkB,OAAO,KAAK,GAAG,gBAAgB;;;;;;;;;;;;gCAGpD,OAAO,KAAK,kBACX,8OAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,KAAK;;;;;;;;;;;;;sCAKnB,8OAAC;4BACC,MAAK;4BACL,WAAW,CAAC,6EAA6E,EACvF,UACI,mCACA,mGACJ;4BACF,UAAU;sCAET,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;qDAGR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;wBAKX,WAAW,CAAC,yBACX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;gCAClB;;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAwB;4BACZ;0CACvB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7F", "debugId": null}}]}