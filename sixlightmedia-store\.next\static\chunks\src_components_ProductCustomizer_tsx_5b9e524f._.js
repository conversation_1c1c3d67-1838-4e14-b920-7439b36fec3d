(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ProductCustomizer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ProductCustomizer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/type.js [app-client] (ecmascript) <export default as Type>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-cw.js [app-client] (ecmascript) <export default as RotateCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Move$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/move.js [app-client] (ecmascript) <export default as Move>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-client] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/undo.js [app-client] (ecmascript) <export default as Undo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/redo.js [app-client] (ecmascript) <export default as Redo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-client] (ecmascript) <export default as Save>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Square$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square.js [app-client] (ecmascript) <export default as Square>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle.js [app-client] (ecmascript) <export default as Circle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Triangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle.js [app-client] (ecmascript) <export default as Triangle>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
// Dynamic import for fabric to avoid SSR issues
let fabric = null;
// Debounce utility function
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function ProductCustomizer({ // New interface props
productImage, onCustomizationChange, initialCustomization, productId, // Legacy props for backward compatibility
initialData, onSave, onCanvasReady, productImageUrl, productName = "Custom Product" }) {
    _s();
    // Normalize props to handle both interfaces
    const normalizedProductImage = productImage || productImageUrl || "";
    const normalizedInitialData = initialCustomization || initialData;
    const normalizedOnSave = onCustomizationChange || onSave;
    // Debounced auto-save function
    const debouncedAutoSave = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ProductCustomizer.useMemo[debouncedAutoSave]": ()=>debounce({
                "ProductCustomizer.useMemo[debouncedAutoSave]": (data)=>{
                    if (normalizedOnSave) {
                        normalizedOnSave(data);
                    }
                }
            }["ProductCustomizer.useMemo[debouncedAutoSave]"], 500)
    }["ProductCustomizer.useMemo[debouncedAutoSave]"], [
        normalizedOnSave
    ]);
    // Canvas and editor state
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [canvas, setCanvas] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("text");
    const [activeObject, setActiveObject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [canvasHistory, setCanvasHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [historyIndex, setHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1);
    const [fabricLoaded, setFabricLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasUnsavedChanges, setHasUnsavedChanges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showUnsavedWarning, setShowUnsavedWarning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isExporting, setIsExporting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showSaveSuccess, setShowSaveSuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Text properties
    const [textColor, setTextColor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("#000000");
    const [fontSize, setFontSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(24);
    const [fontFamily, setFontFamily] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("Arial");
    const [textAlign, setTextAlign] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("left");
    const [fontWeight, setFontWeight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("normal");
    const [fontStyle, setFontStyle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("normal");
    const [textDecoration, setTextDecoration] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("none");
    // Available fonts
    const fonts = [
        "Arial",
        "Helvetica",
        "Times New Roman",
        "Courier New",
        "Verdana",
        "Georgia",
        "Palatino",
        "Garamond",
        "Bookman",
        "Comic Sans MS",
        "Trebuchet MS",
        "Impact"
    ];
    // Available colors
    const colors = [
        "#000000",
        "#FFFFFF",
        "#FF0000",
        "#00FF00",
        "#0000FF",
        "#FFFF00",
        "#FF00FF",
        "#00FFFF",
        "#FFA500",
        "#800080",
        "#008000",
        "#800000",
        "#008080",
        "#000080",
        "#FFC0CB",
        "#A52A2A",
        "#808080",
        "#C0C0C0"
    ];
    // Sample images for quick insertion
    const sampleImages = [
        "/sample-images/logo1.png",
        "/sample-images/logo2.png",
        "/sample-images/shape1.svg",
        "/sample-images/shape2.svg",
        "/sample-images/pattern1.png",
        "/sample-images/pattern2.png"
    ];
    // Initialize Fabric canvas
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProductCustomizer.useEffect": ()=>{
            if (!canvasRef.current || "object" === "undefined") return;
            // Set a timeout to prevent infinite loading
            const loadingTimeout = setTimeout({
                "ProductCustomizer.useEffect.loadingTimeout": ()=>{
                    console.error("Fabric.js loading timeout - forcing fallback");
                    setFabricLoaded(true);
                }
            }["ProductCustomizer.useEffect.loadingTimeout"], 10000); // 10 second timeout
            // Load Fabric.js dynamically
            const loadFabric = {
                "ProductCustomizer.useEffect.loadFabric": async ()=>{
                    try {
                        console.log("Loading Fabric.js...");
                        if (!fabric) {
                            // Simple dynamic import
                            const fabricModule = await __turbopack_context__.r("[project]/node_modules/fabric/dist/fabric.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                            fabric = fabricModule.fabric;
                            console.log("Fabric.js loaded successfully:", !!fabric);
                        }
                        if (!fabric) {
                            console.error("Fabric.js failed to load - showing fallback");
                            // Set fabricLoaded to true to show fallback message
                            setFabricLoaded(true);
                            clearTimeout(loadingTimeout);
                            return;
                        }
                        // Create canvas
                        console.log("Creating Fabric canvas...");
                        const fabricCanvas = new fabric.Canvas(canvasRef.current, {
                            width: 500,
                            height: 500,
                            backgroundColor: "#FFFFFF",
                            preserveObjectStacking: true
                        });
                        console.log("Fabric canvas created successfully:", fabricCanvas);
                        // Set up event listeners
                        fabricCanvas.on("object:modified", handleCanvasChange);
                        fabricCanvas.on("object:added", handleCanvasChange);
                        fabricCanvas.on("object:removed", handleCanvasChange);
                        fabricCanvas.on("selection:created", handleSelectionChange);
                        fabricCanvas.on("selection:updated", handleSelectionChange);
                        fabricCanvas.on("selection:cleared", {
                            "ProductCustomizer.useEffect.loadFabric": ()=>setActiveObject(null)
                        }["ProductCustomizer.useEffect.loadFabric"]);
                        // Load initial data if provided
                        if (normalizedInitialData?.canvasData) {
                            fabricCanvas.loadFromJSON(normalizedInitialData.canvasData, {
                                "ProductCustomizer.useEffect.loadFabric": ()=>{
                                    fabricCanvas.renderAll();
                                    saveToHistory(fabricCanvas);
                                }
                            }["ProductCustomizer.useEffect.loadFabric"]);
                        } else if (normalizedProductImage) {
                            // Load product image as background
                            fabric.Image.fromURL(normalizedProductImage, {
                                "ProductCustomizer.useEffect.loadFabric": (img)=>{
                                    // Scale image to fit canvas while maintaining aspect ratio
                                    const scale = Math.min(fabricCanvas.width / img.width, fabricCanvas.height / img.height);
                                    img.scale(scale * 0.8);
                                    // Center the image
                                    img.set({
                                        left: fabricCanvas.width / 2,
                                        top: fabricCanvas.height / 2,
                                        originX: "center",
                                        originY: "center",
                                        selectable: false,
                                        evented: false
                                    });
                                    fabricCanvas.add(img);
                                    fabricCanvas.sendToBack(img);
                                    fabricCanvas.renderAll();
                                    saveToHistory(fabricCanvas);
                                }
                            }["ProductCustomizer.useEffect.loadFabric"], {
                                crossOrigin: "anonymous"
                            });
                        }
                        setCanvas(fabricCanvas);
                        setFabricLoaded(true);
                        // Notify parent component that canvas is ready
                        if (onCanvasReady) {
                            onCanvasReady(fabricCanvas);
                        }
                        // Save initial state to history
                        saveToHistory(fabricCanvas);
                        console.log("Fabric canvas initialization complete!");
                        clearTimeout(loadingTimeout);
                    } catch (error) {
                        console.error("Error loading Fabric.js or creating canvas:", error);
                        clearTimeout(loadingTimeout);
                        // Set fabricLoaded to true anyway to show fallback
                        setFabricLoaded(true);
                    }
                }
            }["ProductCustomizer.useEffect.loadFabric"];
            loadFabric();
            // Cleanup function
            return ({
                "ProductCustomizer.useEffect": ()=>{
                    if (canvas) {
                        canvas.dispose();
                    }
                }
            })["ProductCustomizer.useEffect"];
        }
    }["ProductCustomizer.useEffect"], []);
    // Check if canvas is empty (only has background image)
    const isCanvasEmpty = ()=>{
        if (!canvas) return true;
        const objects = canvas.getObjects();
        // Consider canvas empty if it only has the background image (non-selectable)
        return objects.filter((obj)=>obj.selectable !== false).length === 0;
    };
    // Handle canvas changes
    const handleCanvasChange = ()=>{
        if (!canvas) return;
        saveToHistory(canvas);
        setHasUnsavedChanges(true);
        // Auto-save to parent component (debounced)
        if (debouncedAutoSave) {
            // Temporarily disable selection borders for clean preview
            const activeObj = canvas.getActiveObject();
            canvas.discardActiveObject();
            canvas.renderAll();
            // Get data URL for preview
            const previewUrl = canvas.toDataURL({
                format: "png",
                quality: 0.8
            });
            // Get JSON data
            const canvasData = JSON.stringify(canvas.toJSON());
            // Re-enable selection
            if (activeObj) {
                canvas.setActiveObject(activeObj);
                canvas.renderAll();
            }
            // Call debounced callback with updated data
            debouncedAutoSave({
                canvasData,
                preview: previewUrl
            });
        }
    };
    // Handle selection changes
    const handleSelectionChange = (e)=>{
        const selectedObject = e.selected[0];
        setActiveObject(selectedObject);
        // Update text properties if text is selected
        if (selectedObject && selectedObject.type === "text") {
            setTextColor(selectedObject.fill || "#000000");
            setFontSize(selectedObject.fontSize || 24);
            setFontFamily(selectedObject.fontFamily || "Arial");
            setTextAlign(selectedObject.textAlign || "left");
            setFontWeight(selectedObject.fontWeight || "normal");
            setFontStyle(selectedObject.fontStyle || "normal");
            setTextDecoration(selectedObject.textDecoration || "none");
        }
    };
    // Save canvas state to history
    const saveToHistory = (fabricCanvas)=>{
        if (!fabricCanvas) return;
        const json = JSON.stringify(fabricCanvas.toJSON());
        // If we're not at the end of the history, truncate
        if (historyIndex < canvasHistory.length - 1) {
            setCanvasHistory((prev)=>prev.slice(0, historyIndex + 1));
        }
        setCanvasHistory((prev)=>[
                ...prev,
                json
            ]);
        setHistoryIndex((prev)=>prev + 1);
    };
    // Undo action
    const handleUndo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ProductCustomizer.useCallback[handleUndo]": ()=>{
            if (historyIndex <= 0 || !canvas) return;
            const newIndex = historyIndex - 1;
            setHistoryIndex(newIndex);
            canvas.loadFromJSON(canvasHistory[newIndex], {
                "ProductCustomizer.useCallback[handleUndo]": ()=>{
                    canvas.renderAll();
                    setHasUnsavedChanges(true);
                }
            }["ProductCustomizer.useCallback[handleUndo]"]);
        }
    }["ProductCustomizer.useCallback[handleUndo]"], [
        historyIndex,
        canvas,
        canvasHistory
    ]);
    // Redo action
    const handleRedo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ProductCustomizer.useCallback[handleRedo]": ()=>{
            if (historyIndex >= canvasHistory.length - 1 || !canvas) return;
            const newIndex = historyIndex + 1;
            setHistoryIndex(newIndex);
            canvas.loadFromJSON(canvasHistory[newIndex], {
                "ProductCustomizer.useCallback[handleRedo]": ()=>{
                    canvas.renderAll();
                    setHasUnsavedChanges(true);
                }
            }["ProductCustomizer.useCallback[handleRedo]"]);
        }
    }["ProductCustomizer.useCallback[handleRedo]"], [
        historyIndex,
        canvasHistory,
        canvas
    ]);
    // Add text to canvas
    const addText = (text = "Double click to edit")=>{
        if (!canvas || !fabric) return;
        const textObject = new fabric.IText(text, {
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: "center",
            originY: "center",
            fontFamily: fontFamily,
            fontSize: fontSize,
            fill: textColor,
            textAlign: textAlign,
            fontWeight: fontWeight,
            fontStyle: fontStyle,
            textDecoration: textDecoration
        });
        canvas.add(textObject);
        canvas.setActiveObject(textObject);
        canvas.renderAll();
    };
    // Add image to canvas
    const addImage = (url)=>{
        if (!canvas || !fabric) return;
        fabric.Image.fromURL(url, (img)=>{
            // Scale image to fit canvas while maintaining aspect ratio
            const scale = Math.min(canvas.width / 3 / img.width, canvas.height / 3 / img.height);
            img.scale(scale);
            // Center the image
            img.set({
                left: canvas.width / 2,
                top: canvas.height / 2,
                originX: "center",
                originY: "center"
            });
            canvas.add(img);
            canvas.setActiveObject(img);
            canvas.renderAll();
        }, {
            crossOrigin: "anonymous"
        });
    };
    // Add rectangle to canvas
    const addRectangle = ()=>{
        if (!canvas || !fabric) return;
        const rect = new fabric.Rect({
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: "center",
            originY: "center",
            width: 100,
            height: 60,
            fill: "#3b82f6",
            stroke: "#1e40af",
            strokeWidth: 2
        });
        canvas.add(rect);
        canvas.setActiveObject(rect);
        canvas.renderAll();
    };
    // Add circle to canvas
    const addCircle = ()=>{
        if (!canvas || !fabric) return;
        const circle = new fabric.Circle({
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: "center",
            originY: "center",
            radius: 50,
            fill: "#10b981",
            stroke: "#047857",
            strokeWidth: 2
        });
        canvas.add(circle);
        canvas.setActiveObject(circle);
        canvas.renderAll();
    };
    // Add triangle to canvas
    const addTriangle = ()=>{
        if (!canvas || !fabric) return;
        const triangle = new fabric.Triangle({
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: "center",
            originY: "center",
            width: 100,
            height: 100,
            fill: "#f59e0b",
            stroke: "#d97706",
            strokeWidth: 2
        });
        canvas.add(triangle);
        canvas.setActiveObject(triangle);
        canvas.renderAll();
    };
    // Upload image
    const handleImageUpload = (e)=>{
        if (!e.target.files || !e.target.files[0]) return;
        const file = e.target.files[0];
        const reader = new FileReader();
        reader.onload = (event)=>{
            if (!event.target?.result) return;
            addImage(event.target.result);
        };
        reader.readAsDataURL(file);
        e.target.value = ""; // Reset input
    };
    // Delete selected object
    const deleteSelected = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ProductCustomizer.useCallback[deleteSelected]": ()=>{
            if (!canvas || !canvas.getActiveObject()) return;
            canvas.remove(canvas.getActiveObject());
            canvas.renderAll();
            setActiveObject(null);
        }
    }["ProductCustomizer.useCallback[deleteSelected]"], [
        canvas
    ]);
    // Update text properties
    const updateTextProperty = (property, value)=>{
        if (!canvas || !activeObject || activeObject.type !== "text") return;
        activeObject.set({
            [property]: value
        });
        canvas.renderAll();
        // Update local state
        switch(property){
            case "fill":
                setTextColor(value);
                break;
            case "fontSize":
                setFontSize(value);
                break;
            case "fontFamily":
                setFontFamily(value);
                break;
            case "textAlign":
                setTextAlign(value);
                break;
            case "fontWeight":
                setFontWeight(value);
                break;
            case "fontStyle":
                setFontStyle(value);
                break;
            case "textDecoration":
                setTextDecoration(value);
                break;
        }
    };
    // Export canvas as image
    const exportImage = ()=>{
        if (!canvas) return;
        setIsExporting(true);
        // Temporarily disable selection borders
        const activeObj = canvas.getActiveObject();
        canvas.discardActiveObject();
        canvas.renderAll();
        // Get data URL
        const dataUrl = canvas.toDataURL({
            format: "png",
            quality: 1,
            multiplier: 2
        });
        // Re-enable selection
        if (activeObj) {
            canvas.setActiveObject(activeObj);
            canvas.renderAll();
        }
        // Create download link
        const link = document.createElement("a");
        link.download = `${productName.replace(/\s+/g, "-").toLowerCase()}-design.png`;
        link.href = dataUrl;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setIsExporting(false);
    };
    // Save design
    const saveDesign = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ProductCustomizer.useCallback[saveDesign]": ()=>{
            if (!canvas || !normalizedOnSave) return;
            // Temporarily disable selection borders
            const activeObj = canvas.getActiveObject();
            canvas.discardActiveObject();
            canvas.renderAll();
            // Get data URL for preview
            const previewUrl = canvas.toDataURL({
                format: "png",
                quality: 0.8
            });
            // Get JSON data
            const canvasData = JSON.stringify(canvas.toJSON());
            // Re-enable selection
            if (activeObj) {
                canvas.setActiveObject(activeObj);
                canvas.renderAll();
            }
            // Call onSave callback
            normalizedOnSave({
                canvasData,
                preview: previewUrl
            });
            setHasUnsavedChanges(false);
        }
    }["ProductCustomizer.useCallback[saveDesign]"], [
        canvas,
        normalizedOnSave
    ]);
    // Bring object forward
    const bringForward = ()=>{
        if (!canvas || !activeObject) return;
        canvas.bringForward(activeObject);
        canvas.renderAll();
    };
    // Send object backward
    const sendBackward = ()=>{
        if (!canvas || !activeObject) return;
        canvas.sendBackward(activeObject);
        canvas.renderAll();
    };
    // Rotate object
    const rotateObject = (angle = 90)=>{
        if (!canvas || !activeObject) return;
        activeObject.rotate((activeObject.angle || 0) + angle);
        canvas.renderAll();
    };
    // Center object
    const centerObject = ()=>{
        if (!canvas || !activeObject) return;
        activeObject.center();
        canvas.renderAll();
    };
    // Set active object by ID
    const setActiveObjectById = (id)=>{
        if (!canvas) return;
        const objects = canvas.getObjects();
        const obj = objects.find((o)=>o.id === id);
        if (obj) {
            canvas.setActiveObject(obj);
            canvas.renderAll();
        }
    };
    // Keyboard shortcuts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProductCustomizer.useEffect": ()=>{
            const handleKeyDown = {
                "ProductCustomizer.useEffect.handleKeyDown": (e)=>{
                    // Only handle shortcuts when canvas is focused or no input is focused
                    const activeElement = document.activeElement;
                    const isInputFocused = activeElement?.tagName === "INPUT" || activeElement?.tagName === "TEXTAREA" || activeElement?.contentEditable === "true";
                    if (isInputFocused) return;
                    // Prevent default for our shortcuts
                    if ((e.ctrlKey || e.metaKey) && [
                        "z",
                        "y",
                        "s"
                    ].includes(e.key.toLowerCase())) {
                        e.preventDefault();
                    }
                    if (e.key === "Delete" || e.key === "Backspace") {
                        e.preventDefault();
                    }
                    // Handle shortcuts
                    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === "z" && !e.shiftKey) {
                        handleUndo();
                    } else if ((e.ctrlKey || e.metaKey) && (e.key.toLowerCase() === "y" || e.key.toLowerCase() === "z" && e.shiftKey)) {
                        handleRedo();
                    } else if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === "s") {
                        saveDesign();
                    } else if (e.key === "Delete" || e.key === "Backspace") {
                        deleteSelected();
                    } else if (e.key === "Escape") {
                        if (canvas) {
                            canvas.discardActiveObject();
                            canvas.renderAll();
                        }
                    }
                }
            }["ProductCustomizer.useEffect.handleKeyDown"];
            window.addEventListener("keydown", handleKeyDown);
            return ({
                "ProductCustomizer.useEffect": ()=>window.removeEventListener("keydown", handleKeyDown)
            })["ProductCustomizer.useEffect"];
        }
    }["ProductCustomizer.useEffect"], [
        canvas,
        historyIndex,
        canvasHistory,
        handleUndo,
        handleRedo,
        saveDesign,
        deleteSelected
    ]);
    // Show loading state while Fabric.js is loading
    if (!fabricLoaded) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-2xl shadow-xl p-6 border border-gray-100 space-y-6",
            "data-has-unsaved-changes": hasUnsavedChanges ? "true" : "false",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center h-64",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 763,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 font-medium",
                            children: "Loading Professional Designer..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 764,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-500 mt-2",
                            children: "Preparing your advanced customization tools"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 767,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 bg-blue-50 rounded-lg p-3 text-sm text-blue-700",
                            children: [
                                "💡 ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "Tip:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 771,
                                    columnNumber: 18
                                }, this),
                                " You'll be able to add text, shapes, images, and more!"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 770,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>{
                                console.log("Force loading test...");
                                setFabricLoaded(true);
                            },
                            className: "mt-4 px-4 py-2 bg-red-500 text-white rounded text-xs",
                            children: "Debug: Force Load (if stuck)"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 776,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                    lineNumber: 762,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ProductCustomizer.tsx",
                lineNumber: 761,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ProductCustomizer.tsx",
            lineNumber: 757,
            columnNumber: 7
        }, this);
    }
    // If Fabric.js failed to load, show working basic customizer
    if (fabricLoaded && !canvas && !fabric) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-2xl shadow-xl p-6 border border-gray-100",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-2xl font-bold text-gray-900 flex items-center gap-2 mb-2",
                            children: "🎨 Basic Product Customizer"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 796,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 mb-3",
                            children: "Advanced tools unavailable. Using basic customization mode."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 799,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-sm text-yellow-800",
                            children: [
                                "⚠️ ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "Note:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 803,
                                    columnNumber: 16
                                }, this),
                                " Advanced design tools couldn't load. You can still customize with basic options below."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 802,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                    lineNumber: 795,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-semibold text-gray-700",
                                    children: "Custom Text"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 811,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    placeholder: "Enter your custom text here...",
                                    className: "w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 text-lg",
                                    onChange: (e)=>{
                                        // Create basic customization data
                                        const basicData = {
                                            canvasData: JSON.stringify({
                                                text: e.target.value,
                                                type: "basic"
                                            }),
                                            preview: `data:text/plain;base64,${btoa(e.target.value || "Custom Text")}`
                                        };
                                        debouncedAutoSave(basicData);
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 814,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 810,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-semibold text-gray-700",
                                    children: "Text Color"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 836,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "color",
                                            defaultValue: "#000000",
                                            className: "w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer hover:border-blue-400 transition-colors duration-200",
                                            onChange: (e)=>{
                                                console.log("Color changed:", e.target.value);
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 840,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Choose your text color"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 848,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 839,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 835,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-3 pt-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.reload(),
                                    className: "px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",
                                    children: "🔄 Try Advanced Mode Again"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 856,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        const basicData = {
                                            canvasData: JSON.stringify({
                                                text: "Sample Text",
                                                type: "basic"
                                            }),
                                            preview: `data:text/plain;base64,${btoa("Sample Text")}`
                                        };
                                        if (normalizedOnSave) {
                                            normalizedOnSave(basicData);
                                        }
                                    },
                                    className: "px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",
                                    children: "✅ Save Basic Design"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 862,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 855,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                    lineNumber: 808,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ProductCustomizer.tsx",
            lineNumber: 794,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-2xl shadow-xl p-6 border border-gray-100",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-2xl font-bold text-gray-900 flex items-center gap-2",
                                        children: "🎨 Professional Designer"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 890,
                                        columnNumber: 13
                                    }, this),
                                    hasUnsavedChanges && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-full text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-amber-500 rounded-full animate-pulse"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 895,
                                                columnNumber: 17
                                            }, this),
                                            "Auto-saving..."
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 894,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                lineNumber: 889,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 mb-3",
                                children: "Create stunning designs with our professional-grade editor. Add text, shapes, images, and customize colors."
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                lineNumber: 900,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2 text-xs",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-green-100 text-green-700 px-2 py-1 rounded-full",
                                        children: "✓ Real-time preview"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 905,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-blue-100 text-blue-700 px-2 py-1 rounded-full",
                                        children: "✓ Undo/Redo"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 908,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-purple-100 text-purple-700 px-2 py-1 rounded-full",
                                        children: "✓ High-quality export"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 911,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-orange-100 text-orange-700 px-2 py-1 rounded-full",
                                        children: "✓ Auto-save"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 914,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-indigo-100 text-indigo-700 px-2 py-1 rounded-full",
                                        children: "✓ Keyboard shortcuts"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 917,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                lineNumber: 904,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-3 p-3 bg-gray-50 rounded-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "Keyboard Shortcuts:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 925,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: "Ctrl+Z (Undo)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 926,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: "Ctrl+Y (Redo)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 927,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: "Ctrl+S (Save)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 928,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: "Delete (Remove)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 929,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: "Esc (Deselect)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                            lineNumber: 930,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 924,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                lineNumber: 923,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                        lineNumber: 888,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col lg:flex-row gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full lg:w-64 bg-gray-50 rounded-xl overflow-hidden border border-gray-200 flex flex-col order-2 lg:order-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex border-b border-gray-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `flex-1 py-3 text-sm font-medium ${activeTab === "text" ? "bg-white text-blue-600" : "text-gray-600 hover:bg-gray-100"}`,
                                                onClick: ()=>setActiveTab("text"),
                                                children: "Text"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 941,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `flex-1 py-3 text-sm font-medium ${activeTab === "shapes" ? "bg-white text-blue-600" : "text-gray-600 hover:bg-gray-100"}`,
                                                onClick: ()=>setActiveTab("shapes"),
                                                children: "Shapes"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 951,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `flex-1 py-3 text-sm font-medium ${activeTab === "images" ? "bg-white text-blue-600" : "text-gray-600 hover:bg-gray-100"}`,
                                                onClick: ()=>setActiveTab("images"),
                                                children: "Images"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 961,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: `flex-1 py-3 text-sm font-medium ${activeTab === "colors" ? "bg-white text-blue-600" : "text-gray-600 hover:bg-gray-100"}`,
                                                onClick: ()=>setActiveTab("colors"),
                                                children: "Colors"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 971,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 940,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1 overflow-y-auto p-4",
                                        children: [
                                            activeTab === "text" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",
                                                        onClick: ()=>addText(),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 992,
                                                                columnNumber: 21
                                                            }, this),
                                                            " Add Text"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 988,
                                                        columnNumber: 19
                                                    }, this),
                                                    activeObject && activeObject.type === "text" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-4 pt-4 border-t border-gray-200",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-medium text-gray-900",
                                                                children: "Text Properties"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 997,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Font"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1003,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                                        className: "w-full border border-gray-300 rounded-lg p-2 text-sm",
                                                                        value: fontFamily,
                                                                        onChange: (e)=>updateTextProperty("fontFamily", e.target.value),
                                                                        children: fonts.map((font)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                                value: font,
                                                                                children: font
                                                                            }, font, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1014,
                                                                                columnNumber: 29
                                                                            }, this))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1006,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1002,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Size"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1023,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                        type: "range",
                                                                        min: "8",
                                                                        max: "80",
                                                                        value: fontSize,
                                                                        onChange: (e)=>updateTextProperty("fontSize", parseInt(e.target.value)),
                                                                        className: "w-full"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1026,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-xs text-gray-500 text-right",
                                                                        children: [
                                                                            fontSize,
                                                                            "px"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1039,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1022,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Color"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1046,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-6 gap-2",
                                                                        children: colors.slice(0, 12).map((color)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `w-full aspect-square rounded-full border ${textColor === color ? "border-blue-500 ring-2 ring-blue-300" : "border-gray-300"}`,
                                                                                style: {
                                                                                    backgroundColor: color
                                                                                },
                                                                                onClick: ()=>updateTextProperty("fill", color),
                                                                                "aria-label": `Color ${color}`
                                                                            }, color, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1051,
                                                                                columnNumber: 29
                                                                            }, this))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1049,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1045,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Alignment"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1068,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "flex border border-gray-300 rounded-lg overflow-hidden",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `flex-1 py-1 ${textAlign === "left" ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"}`,
                                                                                onClick: ()=>updateTextProperty("textAlign", "left"),
                                                                                children: "Left"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1072,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `flex-1 py-1 ${textAlign === "center" ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"}`,
                                                                                onClick: ()=>updateTextProperty("textAlign", "center"),
                                                                                children: "Center"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1084,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `flex-1 py-1 ${textAlign === "right" ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"}`,
                                                                                onClick: ()=>updateTextProperty("textAlign", "right"),
                                                                                children: "Right"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1096,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1071,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1067,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Style"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1113,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "flex border border-gray-300 rounded-lg overflow-hidden",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `flex-1 py-1 font-bold ${fontWeight === "bold" ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"}`,
                                                                                onClick: ()=>updateTextProperty("fontWeight", fontWeight === "bold" ? "normal" : "bold"),
                                                                                children: "B"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1117,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `flex-1 py-1 italic ${fontStyle === "italic" ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"}`,
                                                                                onClick: ()=>updateTextProperty("fontStyle", fontStyle === "italic" ? "normal" : "italic"),
                                                                                children: "I"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1132,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `flex-1 py-1 underline ${textDecoration === "underline" ? "bg-blue-100 text-blue-600" : "hover:bg-gray-100"}`,
                                                                                onClick: ()=>updateTextProperty("textDecoration", textDecoration === "underline" ? "" : "underline"),
                                                                                children: "U"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1147,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1116,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1112,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 996,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 987,
                                                columnNumber: 17
                                            }, this),
                                            activeTab === "shapes" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "grid grid-cols-1 gap-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",
                                                                onClick: addRectangle,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Square$3e$__["Square"], {
                                                                        size: 16
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1179,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    " Rectangle"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1175,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2",
                                                                onClick: addCircle,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__["Circle"], {
                                                                        size: 16
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1185,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    " Circle"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1181,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "w-full bg-yellow-600 text-white py-3 px-4 rounded-lg hover:bg-yellow-700 transition-colors flex items-center justify-center gap-2",
                                                                onClick: addTriangle,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Triangle$3e$__["Triangle"], {
                                                                        size: 16
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1191,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    " Triangle"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1187,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1174,
                                                        columnNumber: 19
                                                    }, this),
                                                    activeObject && activeObject.type !== "text" && activeObject.type !== "image" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-4 pt-4 border-t border-gray-200",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-medium text-gray-900",
                                                                children: "Shape Properties"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1199,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Fill Color"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1205,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-6 gap-2",
                                                                        children: colors.slice(0, 12).map((color)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `w-full aspect-square rounded-full border ${activeObject?.fill === color ? "border-blue-500 ring-2 ring-blue-300" : "border-gray-300"}`,
                                                                                style: {
                                                                                    backgroundColor: color
                                                                                },
                                                                                onClick: ()=>{
                                                                                    if (activeObject) {
                                                                                        activeObject.set({
                                                                                            fill: color
                                                                                        });
                                                                                        canvas.renderAll();
                                                                                    }
                                                                                },
                                                                                "aria-label": `Color ${color}`
                                                                            }, color, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1210,
                                                                                columnNumber: 31
                                                                            }, this))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1208,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1204,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Border Color"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1232,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-6 gap-2",
                                                                        children: colors.slice(0, 12).map((color)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                className: `w-full aspect-square rounded-full border ${activeObject?.stroke === color ? "border-blue-500 ring-2 ring-blue-300" : "border-gray-300"}`,
                                                                                style: {
                                                                                    backgroundColor: color
                                                                                },
                                                                                onClick: ()=>{
                                                                                    if (activeObject) {
                                                                                        activeObject.set({
                                                                                            stroke: color
                                                                                        });
                                                                                        canvas.renderAll();
                                                                                    }
                                                                                },
                                                                                "aria-label": `Border color ${color}`
                                                                            }, color, false, {
                                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                                lineNumber: 1237,
                                                                                columnNumber: 31
                                                                            }, this))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1235,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1231,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm text-gray-600 mb-1",
                                                                        children: "Border Width"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1259,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                        type: "range",
                                                                        min: "0",
                                                                        max: "10",
                                                                        value: activeObject?.strokeWidth || 0,
                                                                        onChange: (e)=>{
                                                                            if (activeObject) {
                                                                                activeObject.set({
                                                                                    strokeWidth: parseInt(e.target.value)
                                                                                });
                                                                                canvas.renderAll();
                                                                            }
                                                                        },
                                                                        className: "w-full"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1262,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-xs text-gray-500 text-right",
                                                                        children: [
                                                                            activeObject?.strokeWidth || 0,
                                                                            "px"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                        lineNumber: 1277,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1258,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1198,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1173,
                                                columnNumber: 17
                                            }, this),
                                            activeTab === "images" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",
                                                        onClick: ()=>fileInputRef.current?.click(),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1293,
                                                                columnNumber: 21
                                                            }, this),
                                                            " Upload Image"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1289,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        ref: fileInputRef,
                                                        type: "file",
                                                        accept: "image/*",
                                                        className: "hidden",
                                                        onChange: handleImageUpload
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1295,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "font-medium text-gray-900 pt-4 border-t border-gray-200",
                                                        children: "Sample Images"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1303,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "grid grid-cols-2 gap-2",
                                                        children: sampleImages.map((img, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "border border-gray-200 rounded-lg p-2 hover:border-blue-500 transition-colors",
                                                                onClick: ()=>addImage(img),
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                    src: img,
                                                                    alt: `Sample ${index + 1}`,
                                                                    className: "w-full aspect-square object-contain"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1313,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, index, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1308,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1306,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1288,
                                                columnNumber: 17
                                            }, this),
                                            activeTab === "colors" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "font-medium text-gray-900",
                                                        children: "Background Color"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1327,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "grid grid-cols-4 gap-2",
                                                        children: colors.map((color)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: `w-full aspect-square rounded-lg border ${canvas?.backgroundColor === color ? "border-blue-500 ring-2 ring-blue-300" : "border-gray-300"}`,
                                                                style: {
                                                                    backgroundColor: color
                                                                },
                                                                onClick: ()=>{
                                                                    if (canvas) {
                                                                        canvas.setBackgroundColor(color, ()=>canvas.renderAll());
                                                                        handleCanvasChange();
                                                                    }
                                                                },
                                                                "aria-label": `Background color ${color}`
                                                            }, color, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1332,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1330,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1326,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 984,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                lineNumber: 938,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 order-1 lg:order-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "lg:hidden mb-3 p-3 bg-blue-50 rounded-lg text-sm text-blue-700",
                                                children: [
                                                    "💡 ",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                        children: "Tip:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1362,
                                                        columnNumber: 20
                                                    }, this),
                                                    " Use the tools below to customize your design. Tap objects to select and edit them."
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1361,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "border border-gray-300 rounded-lg overflow-hidden bg-white shadow-inner relative",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                                                        ref: canvasRef
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1367,
                                                        columnNumber: 17
                                                    }, this),
                                                    canvas && canvas.getObjects().filter((obj)=>obj.selectable !== false).length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "absolute inset-0 flex items-center justify-center pointer-events-none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-center bg-white bg-opacity-90 p-6 rounded-xl shadow-lg max-w-sm",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-4xl mb-3",
                                                                    children: "🎨"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1377,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                    className: "font-semibold text-gray-900 mb-2",
                                                                    children: "Start Creating!"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1378,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-sm text-gray-600 mb-4",
                                                                    children: "Use the tools on the left to add text, shapes, or images to your design."
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1381,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex flex-wrap gap-2 justify-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            className: "bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700 transition-colors pointer-events-auto",
                                                                            onClick: ()=>addText("Your Text Here"),
                                                                            children: "+ Add Text"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                            lineNumber: 1386,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            className: "bg-green-600 text-white px-3 py-1 rounded-full text-xs hover:bg-green-700 transition-colors pointer-events-auto",
                                                                            onClick: addRectangle,
                                                                            children: "+ Add Shape"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                            lineNumber: 1392,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1385,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                            lineNumber: 1376,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1375,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1366,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-1 flex flex-col gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
                                                        onClick: ()=>rotateObject(),
                                                        title: "Rotate 90°",
                                                        disabled: !activeObject,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCw$3e$__["RotateCw"], {
                                                            size: 16
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                            lineNumber: 1412,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1406,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
                                                        onClick: centerObject,
                                                        title: "Center Object",
                                                        disabled: !activeObject,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Move$3e$__["Move"], {
                                                            size: 16
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                            lineNumber: 1420,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1414,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
                                                        onClick: bringForward,
                                                        title: "Bring Forward",
                                                        disabled: !activeObject,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                            xmlns: "http://www.w3.org/2000/svg",
                                                            width: "16",
                                                            height: "16",
                                                            viewBox: "0 0 24 24",
                                                            fill: "none",
                                                            stroke: "currentColor",
                                                            strokeWidth: "2",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                                    x: "7",
                                                                    y: "7",
                                                                    width: "10",
                                                                    height: "10",
                                                                    rx: "1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1439,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                                    x: "4",
                                                                    y: "4",
                                                                    width: "10",
                                                                    height: "10",
                                                                    rx: "1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1440,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                            lineNumber: 1428,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1422,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 hover:bg-gray-100 rounded-lg transition-colors",
                                                        onClick: sendBackward,
                                                        title: "Send Backward",
                                                        disabled: !activeObject,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                            xmlns: "http://www.w3.org/2000/svg",
                                                            width: "16",
                                                            height: "16",
                                                            viewBox: "0 0 24 24",
                                                            fill: "none",
                                                            stroke: "currentColor",
                                                            strokeWidth: "2",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                                    x: "7",
                                                                    y: "7",
                                                                    width: "10",
                                                                    height: "10",
                                                                    rx: "1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1460,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                                    x: "10",
                                                                    y: "10",
                                                                    width: "10",
                                                                    height: "10",
                                                                    rx: "1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                    lineNumber: 1461,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                            lineNumber: 1449,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1443,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 hover:bg-gray-100 rounded-lg transition-colors text-red-500",
                                                        onClick: deleteSelected,
                                                        title: "Delete Object",
                                                        disabled: !activeObject,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                            size: 16
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                            lineNumber: 1470,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1464,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1405,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 1359,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 flex justify-between items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm",
                                                        onClick: handleUndo,
                                                        disabled: historyIndex <= 0,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1483,
                                                                columnNumber: 19
                                                            }, this),
                                                            " Undo"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1478,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm",
                                                        onClick: handleRedo,
                                                        disabled: historyIndex >= canvasHistory.length - 1,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__["Redo"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1490,
                                                                columnNumber: 19
                                                            }, this),
                                                            " Redo"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1485,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1477,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm",
                                                        onClick: exportImage,
                                                        disabled: isExporting,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1500,
                                                                columnNumber: 19
                                                            }, this),
                                                            isExporting ? "Exporting..." : "Export"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1495,
                                                        columnNumber: 17
                                                    }, this),
                                                    onSave && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-1 text-sm",
                                                        onClick: saveDesign,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                                lineNumber: 1508,
                                                                columnNumber: 21
                                                            }, this),
                                                            " Save Design"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                        lineNumber: 1504,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                                lineNumber: 1494,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                                        lineNumber: 1476,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProductCustomizer.tsx",
                                lineNumber: 1358,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProductCustomizer.tsx",
                        lineNumber: 936,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProductCustomizer.tsx",
                lineNumber: 887,
                columnNumber: 7
            }, this),
            showUnsavedWarning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-xl p-6 max-w-md w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-xl font-bold mb-4",
                            children: "Unsaved Changes"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 1521,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-6",
                            children: "You have unsaved changes to your design. Are you sure you want to leave without saving?"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 1522,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100",
                                    onClick: ()=>setShowUnsavedWarning(false),
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 1527,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",
                                    onClick: ()=>{
                                        setShowUnsavedWarning(false);
                                    // Handle discard action
                                    },
                                    children: "Discard Changes"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                                    lineNumber: 1533,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ProductCustomizer.tsx",
                            lineNumber: 1526,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ProductCustomizer.tsx",
                    lineNumber: 1520,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ProductCustomizer.tsx",
                lineNumber: 1519,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ProductCustomizer.tsx",
        lineNumber: 886,
        columnNumber: 5
    }, this);
}
_s(ProductCustomizer, "8uEcJVVl8X1FcC4YcYg9ixndNms=");
_c = ProductCustomizer;
var _c;
__turbopack_context__.k.register(_c, "ProductCustomizer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_ProductCustomizer_tsx_5b9e524f._.js.map