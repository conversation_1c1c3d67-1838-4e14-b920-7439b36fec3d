{"name": "@types/nodemailer", "version": "6.4.17", "description": "TypeScript definitions for nodemailer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/nodemailer", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rogierschouten"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "dex4er", "url": "https://github.com/dex4er"}, {"name": "<PERSON>", "githubUsername": "bioball", "url": "https://github.com/bioball"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/nodemailer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c93eda36deb49e226aaabb03b3ab999056957e2220b681e070d58074dc0a53a4", "typeScriptVersion": "4.9"}