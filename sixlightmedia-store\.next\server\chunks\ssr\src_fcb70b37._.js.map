{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n      VERIFY_EMAIL: \"/auth/verify-email\",\n      FORGOT_PASSWORD: \"/auth/forgot-password\",\n      RESET_PASSWORD: \"/auth/reset-password\",\n      RESEND_VERIFICATION: \"/auth/resend-verification\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      UPDATE_PROFILE: \"/user/profile\",\n      UPLOAD_PROFILE_IMAGE: \"/user/upload-profile-image\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACjB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;QACvB;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,6EAAgE;IAClE,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/admin/products/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { IKContext, IKUpload } from \"imagekitio-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport default function AdminProducts() {\r\n  const [form, setForm] = useState({\r\n    name: \"\",\r\n    image: \"\",\r\n    description: \"\",\r\n    customizable: false,\r\n    slug: \"\",\r\n    categoryId: \"\",\r\n  });\r\n  const [message, setMessage] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [price, setPrice] = useState(0);\r\n  const [categories, setCategories] = useState<{ id: number; name: string }[]>(\r\n    []\r\n  );\r\n  const [categoriesLoading, setCategoriesLoading] = useState(true);\r\n  const [categoriesError, setCategoriesError] = useState(\"\");\r\n\r\n  // Fetch categories on mount\r\n  useEffect(() => {\r\n    setCategoriesLoading(true);\r\n    fetch(getApiUrl(API_CONFIG.ENDPOINTS.CATEGORIES))\r\n      .then((res) => {\r\n        if (!res.ok) throw new Error(\"Failed to fetch categories\");\r\n        return res.json();\r\n      })\r\n      .then((data) => {\r\n        setCategories(data);\r\n        setCategoriesError(\"\");\r\n      })\r\n      .catch(() => {\r\n        setCategories([]);\r\n        setCategoriesError(\"Failed to load categories\");\r\n      })\r\n      .finally(() => setCategoriesLoading(false));\r\n  }, []);\r\n\r\n  function handleChange(\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement\r\n    >\r\n  ) {\r\n    const { name, value, type } = e.target;\r\n    setForm((f) => ({\r\n      ...f,\r\n      [name]:\r\n        type === \"checkbox\" ? (e.target as HTMLInputElement).checked : value,\r\n    }));\r\n  }\r\n\r\n  async function handleSubmit(e: React.FormEvent) {\r\n    e.preventDefault();\r\n    setMessage(\"\");\r\n    setError(\"\");\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setError(\"Not authenticated\");\r\n      return;\r\n    }\r\n    const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.PRODUCTS), {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n      body: JSON.stringify({\r\n        ...form,\r\n        price,\r\n        categoryId: Number(form.categoryId),\r\n      }),\r\n    });\r\n    const data = await res.json();\r\n    if (res.ok) {\r\n      setMessage(\"Product created!\");\r\n      setForm({\r\n        name: \"\",\r\n        image: \"\",\r\n        description: \"\",\r\n        customizable: false,\r\n        slug: \"\",\r\n        categoryId: \"\",\r\n      });\r\n      setPrice(0);\r\n    } else {\r\n      setError(data.error || \"Failed to create product\");\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"max-w-xl mx-auto mt-16\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h2 className=\"text-2xl font-bold\">Add New Product</h2>\r\n        <Link\r\n          href=\"/admin/categories\"\r\n          className=\"bg-gray-200 text-gray-800 px-4 py-2 rounded-full font-semibold shadow hover:bg-gray-300 transition text-sm\"\r\n        >\r\n          Manage Categories\r\n        </Link>\r\n      </div>\r\n      <IKContext\r\n        publicKey={process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY?.replace(\r\n          /\"/g,\r\n          \"\"\r\n        )}\r\n        urlEndpoint={process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT?.replace(\r\n          /\"/g,\r\n          \"\"\r\n        )}\r\n        authenticator={async () => {\r\n          const res = await fetch(\"/api/imagekit-auth\");\r\n          return res.json();\r\n        }}\r\n      >\r\n        <form onSubmit={handleSubmit} className=\"flex flex-col gap-4\">\r\n          <input\r\n            name=\"name\"\r\n            value={form.name}\r\n            onChange={handleChange}\r\n            placeholder=\"Name\"\r\n            className=\"border rounded px-3 py-2\"\r\n            required\r\n          />\r\n          <input\r\n            name=\"slug\"\r\n            value={form.slug}\r\n            onChange={handleChange}\r\n            placeholder=\"Slug (unique)\"\r\n            className=\"border rounded px-3 py-2\"\r\n            required\r\n          />\r\n          {/* ImageKit Upload */}\r\n          <div>\r\n            <label className=\"block mb-1 font-semibold\">Product Image</label>\r\n            <IKUpload\r\n              fileName={form.slug ? `${form.slug}.jpg` : \"product.jpg\"}\r\n              onSuccess={(res: { url: string }) =>\r\n                setForm((f) => ({ ...f, image: res.url }))\r\n              }\r\n              onError={() => setError(\"Image upload failed\")}\r\n              className=\"border rounded px-3 py-2 w-full\"\r\n            />\r\n            {form.image && (\r\n              <Image\r\n                src={form.image}\r\n                alt=\"Preview\"\r\n                width={96}\r\n                height={96}\r\n                className=\"mt-2 h-24 w-auto rounded shadow\"\r\n              />\r\n            )}\r\n          </div>\r\n          <textarea\r\n            name=\"description\"\r\n            value={form.description}\r\n            onChange={handleChange}\r\n            placeholder=\"Description\"\r\n            className=\"border rounded px-3 py-2\"\r\n            required\r\n          />\r\n          <select\r\n            name=\"categoryId\"\r\n            value={form.categoryId || \"\"}\r\n            onChange={handleChange}\r\n            className=\"border rounded px-3 py-2\"\r\n            required\r\n            disabled={categoriesLoading || !!categoriesError}\r\n          >\r\n            <option value=\"\" disabled>\r\n              {categoriesLoading ? \"Loading categories...\" : \"Select category\"}\r\n            </option>\r\n            {categories.map((cat) => (\r\n              <option key={cat.id} value={cat.id}>\r\n                {cat.name}\r\n              </option>\r\n            ))}\r\n          </select>\r\n          {categoriesError && (\r\n            <div className=\"text-red-600 text-sm\">{categoriesError}</div>\r\n          )}\r\n          <label className=\"flex items-center gap-2\">\r\n            <input\r\n              type=\"checkbox\"\r\n              name=\"customizable\"\r\n              checked={form.customizable}\r\n              onChange={handleChange}\r\n            />\r\n            Customizable\r\n          </label>\r\n          <label className=\"font-semibold\">\r\n            Price\r\n            <div className=\"flex items-center mt-1\">\r\n              <span className=\"px-2 py-2 bg-gray-100 border border-r-0 rounded-l\">\r\n                K\r\n              </span>\r\n              <input\r\n                type=\"number\"\r\n                min=\"0\"\r\n                step=\"0.01\"\r\n                className=\"w-full border rounded-r px-3 py-2 focus:outline-none\"\r\n                value={price}\r\n                onChange={(e) => setPrice(Number(e.target.value))}\r\n                required\r\n              />\r\n            </div>\r\n          </label>\r\n          <button\r\n            type=\"submit\"\r\n            className=\"bg-[#1a237e] text-white font-semibold px-6 py-2 rounded-full shadow hover:bg-[#283593] transition\"\r\n          >\r\n            Add Product\r\n          </button>\r\n          {message && <div className=\"text-green-600\">{message}</div>}\r\n          {error && <div className=\"text-red-600\">{error}</div>}\r\n        </form>\r\n      </IKContext>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,MAAM;QACN,OAAO;QACP,aAAa;QACb,cAAc;QACd,MAAM;QACN,YAAY;IACd;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzC,EAAE;IAEJ,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;QACrB,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,UAAU,GAC5C,IAAI,CAAC,CAAC;YACL,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,OAAO,IAAI,IAAI;QACjB,GACC,IAAI,CAAC,CAAC;YACL,cAAc;YACd,mBAAmB;QACrB,GACC,KAAK,CAAC;YACL,cAAc,EAAE;YAChB,mBAAmB;QACrB,GACC,OAAO,CAAC,IAAM,qBAAqB;IACxC,GAAG,EAAE;IAEL,SAAS,aACP,CAEC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,QAAQ,CAAC,IAAM,CAAC;gBACd,GAAG,CAAC;gBACJ,CAAC,KAAK,EACJ,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACnE,CAAC;IACH;IAEA,eAAe,aAAa,CAAkB;QAC5C,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT;QACF;QACA,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,GAAG;YAChE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA,YAAY,OAAO,KAAK,UAAU;YACpC;QACF;QACA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,IAAI,IAAI,EAAE,EAAE;YACV,WAAW;YACX,QAAQ;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,cAAc;gBACd,MAAM;gBACN,YAAY;YACd;YACA,SAAS;QACX,OAAO;YACL,SAAS,KAAK,KAAK,IAAI;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;0BAIH,8OAAC,yKAAA,CAAA,YAAS;gBACR,oFAAwD,QACtD,MACA;gBAEF,mFAA4D,QAC1D,MACA;gBAEF,eAAe;oBACb,MAAM,MAAM,MAAM,MAAM;oBACxB,OAAO,IAAI,IAAI;gBACjB;0BAEA,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BACC,MAAK;4BACL,OAAO,KAAK,IAAI;4BAChB,UAAU;4BACV,aAAY;4BACZ,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BACC,MAAK;4BACL,OAAO,KAAK,IAAI;4BAChB,UAAU;4BACV,aAAY;4BACZ,WAAU;4BACV,QAAQ;;;;;;sCAGV,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA2B;;;;;;8CAC5C,8OAAC,yKAAA,CAAA,WAAQ;oCACP,UAAU,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG;oCAC3C,WAAW,CAAC,MACV,QAAQ,CAAC,IAAM,CAAC;gDAAE,GAAG,CAAC;gDAAE,OAAO,IAAI,GAAG;4CAAC,CAAC;oCAE1C,SAAS,IAAM,SAAS;oCACxB,WAAU;;;;;;gCAEX,KAAK,KAAK,kBACT,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,KAAK;oCACf,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;;sCAIhB,8OAAC;4BACC,MAAK;4BACL,OAAO,KAAK,WAAW;4BACvB,UAAU;4BACV,aAAY;4BACZ,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BACC,MAAK;4BACL,OAAO,KAAK,UAAU,IAAI;4BAC1B,UAAU;4BACV,WAAU;4BACV,QAAQ;4BACR,UAAU,qBAAqB,CAAC,CAAC;;8CAEjC,8OAAC;oCAAO,OAAM;oCAAG,QAAQ;8CACtB,oBAAoB,0BAA0B;;;;;;gCAEhD,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC;wCAAoB,OAAO,IAAI,EAAE;kDAC/B,IAAI,IAAI;uCADE,IAAI,EAAE;;;;;;;;;;;wBAKtB,iCACC,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCAEzC,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,SAAS,KAAK,YAAY;oCAC1B,UAAU;;;;;;gCACV;;;;;;;sCAGJ,8OAAC;4BAAM,WAAU;;gCAAgB;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAoD;;;;;;sDAGpE,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,MAAK;4CACL,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;4CAC/C,QAAQ;;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;wBAGA,yBAAW,8OAAC;4BAAI,WAAU;sCAAkB;;;;;;wBAC5C,uBAAS,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAKnD", "debugId": null}}]}