(()=>{var e={};e.id=162,e.ids=[162],e.modules={191:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9167,23))},448:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(5239),i=s(8088),a=s(8170),o=s.n(a),n=s(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6200)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\forgot-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\forgot-password\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},715:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(687),i=s(3210),a=s(3282),o=s(5814),n=s.n(o),l=s(474),d=s(9275),c=s(5336),m=s(3931),p=s(3613);let u=(0,s(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),h=d.z.object({email:d.z.string().email("Please enter a valid email address")});function g(){let[e,t]=(0,i.useState)({email:""}),[s,o]=(0,i.useState)({}),[d,g]=(0,i.useState)(!1),[x,f]=(0,i.useState)(!1),[b,y]=(0,i.useState)(""),v=(e,r)=>{t(t=>({...t,[e]:r})),s[e]&&o(t=>({...t,[e]:void 0}))},j=async t=>{t.preventDefault(),g(!0),o({}),y("");let s=h.safeParse(e);if(!s.success){g(!1);let e={};s.error.errors.forEach(t=>{t.path[0]&&(e[t.path[0]]=t.message)}),o(e);return}try{let t=await (0,a.BD)(e.email);g(!1),f(!0),y(t.message||"Password reset link sent to your email!")}catch(e){g(!1),y("Failed to send reset email. Please try again.")}};return x?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,r.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,r.jsx)(c.A,{className:"h-16 w-16 text-green-500 mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-green-700",children:"Email Sent!"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600",children:b}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,r.jsx)("strong",{children:"\uD83D\uDCE7 Check your email:"})," We've sent a password reset link to"," ",(0,r.jsx)("span",{className:"font-mono",children:e.email})]})}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,r.jsx)("strong",{children:"⏰ Link expires in 1 hour"})," for your security."]})})]}),(0,r.jsxs)("div",{className:"space-y-3 mt-6",children:[(0,r.jsx)(n(),{href:"/login",className:"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Login"}),(0,r.jsx)("button",{onClick:()=>{f(!1),t({email:""}),y("")},className:"w-full text-gray-600 hover:text-gray-800 transition-colors",children:"Send to different email"})]})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,r.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Forgot Password"}),(0,r.jsx)("p",{className:"text-gray-600 text-center mt-2",children:"Enter your email address and we'll send you a link to reset your password."})]}),(0,r.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",value:e.email,onChange:e=>v("email",e.target.value),placeholder:"Enter your email address",autoComplete:"email",className:`w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ${s.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,autoComplete:"email","aria-invalid":!!s.email,"aria-describedby":s.email?"email-error":void 0})]}),s.email&&(0,r.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,r.jsx)(p.A,{size:16}),s.email]})]}),(0,r.jsx)("button",{type:"submit",className:`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${d?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"}`,disabled:d,children:d?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,r.jsx)("span",{children:"Sending Reset Link..."})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Send Reset Link"})]})}),b&&!x&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,r.jsx)(p.A,{size:16}),b]})]}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 text-center",children:(0,r.jsxs)(n(),{href:"/login",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(u,{className:"h-4 w-4"}),"Back to Login"]})}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Don't have an account?"," ",(0,r.jsx)(n(),{href:"/register",className:"text-blue-600 hover:text-blue-800 font-medium",children:"Sign up"})]})})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3282:(e,t,s)=>{"use strict";s.d(t,{A$:()=>o,BD:()=>n,RS:()=>d,iD:()=>i,kz:()=>a,xw:()=>l});var r=s(5475);async function i(e,t){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})})).json()}async function a(e,t,s){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t,name:s})})).json()}async function o(e){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.VERIFY_EMAIL),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e})})).json()}async function n(e){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.FORGOT_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}async function l(e,t){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.RESET_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e,password:t})})).json()}async function d(e){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.RESEND_VERIFICATION),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3931:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3953:(e,t,s)=>{Promise.resolve().then(s.bind(s,6200))},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var r=s(7413),i=s(2376),a=s.n(i),o=s(8726),n=s.n(o),l=s(6162);s(1135);let d={title:{default:"Six Light Media Store - Premium Custom Products & Personalized Gifts",template:"%s | Six Light Media Store"},description:"Discover premium custom products and personalized gifts at Six Light Media Store. From engraved bottles to custom t-shirts, we create unique items tailored just for you. Fast delivery, premium quality, 100% satisfaction guaranteed.",keywords:["custom products","personalized gifts","engraved bottles","custom t-shirts","Six Light Media","premium quality","custom printing","personalized items","unique gifts","custom design","Zambia","e-commerce"],authors:[{name:"Six Light Media"}],creator:"Six Light Media",publisher:"Six Light Media",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://yourdomain.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_US",url:"/",title:"Six Light Media Store - Premium Custom Products & Personalized Gifts",description:"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.",siteName:"Six Light Media Store",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Six Light Media Store - Premium Custom Products"}]},twitter:{card:"summary_large_image",title:"Six Light Media Store - Premium Custom Products & Personalized Gifts",description:"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.",images:["/og-image.jpg"],creator:"@sixlightmedia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"},category:"e-commerce",manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"Six Light Media Store"}};function c({children:e}){return(0,r.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5"}),(0,r.jsx)("meta",{name:"theme-color",content:"#1a237e"}),(0,r.jsx)("meta",{name:"msapplication-TileColor",content:"#1a237e"}),(0,r.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Six Light Media Store"}),(0,r.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"}),(0,r.jsx)("link",{rel:"icon",href:"/icon.svg",type:"image/svg+xml"}),(0,r.jsx)("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),(0,r.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//fonts.gstatic.com"}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"Six Light Media Store",description:"Premium custom products and personalized gifts",url:"https://yourdomain.com",logo:"https://yourdomain.com/6 Light Logo.png",contactPoint:{"@type":"ContactPoint",contactType:"customer service",availableLanguage:"English"},sameAs:["https://facebook.com/sixlightmedia","https://twitter.com/sixlightmedia","https://instagram.com/sixlightmedia"]})}})]}),(0,r.jsxs)("body",{className:`${a().variable} ${n().variable} antialiased`,children:[e,process.env.NEXT_PUBLIC_GA_ID&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.default,{src:`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`,strategy:"afterInteractive"}),(0,r.jsx)(l.default,{id:"google-analytics",strategy:"afterInteractive",children:`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
              `})]})]})]})}},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5475:(e,t,s)=>{"use strict";s.d(t,{e9:()=>i,i3:()=>r});let r={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh",VERIFY_EMAIL:"/auth/verify-email",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",RESEND_VERIFICATION:"/auth/resend-verification"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",UPDATE_PROFILE:"/user/profile",UPLOAD_PROFILE_IMAGE:"/user/upload-profile-image",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};process.env.IMAGEKIT_PRIVATE_KEY,process.env.NEXT_PUBLIC_GA_ID,process.env.NEXT_PUBLIC_GA_ID;let i=e=>{let t=r.BASE_URL.replace(/\/$/,""),s=e.startsWith("/")?e:`/${e}`;return`${t}${s}`}},6200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\forgot-password\\page.tsx","default")},7105:(e,t,s)=>{Promise.resolve().then(s.bind(s,715))},7219:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},7467:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9927:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,7429,23))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[728,474,814,743],()=>s(448));module.exports=r})();