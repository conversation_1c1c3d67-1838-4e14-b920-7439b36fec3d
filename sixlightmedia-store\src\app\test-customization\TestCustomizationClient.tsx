"use client";

import React, { useState } from "react";
import dynamic from "next/dynamic";
import { User } from "@/lib/auth";

// Dynamic import of the SimpleProductCustomizer (our reliable fallback)
const SimpleProductCustomizer = dynamic(
  () => import("@/components/ProductCustomizerFallback"),
  {
    ssr: false,
    loading: () => (
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Customizer...</p>
          </div>
        </div>
      </div>
    ),
  }
);

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

interface TestCustomizationClientProps {
  user?: User;
}

export default function TestCustomizationClient({ user }: TestCustomizationClientProps) {
  const [customizationData, setCustomizationData] = useState<CustomizationData | null>(null);
  const [showData, setShowData] = useState(false);

  const handleCustomizationChange = (data: CustomizationData) => {
    setCustomizationData(data);
  };

  const downloadDesign = () => {
    if (customizationData?.preview) {
      const link = document.createElement("a");
      link.href = customizationData.preview;
      link.download = "custom-design.png";
      link.click();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-900 via-blue-900 to-purple-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
              <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                Test
              </span>
              <span className="text-white"> Customization</span>
            </h1>
            <p className="text-xl text-gray-200 mb-4 max-w-2xl mx-auto">
              Test the product customization features in a secure environment
            </p>
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-white text-sm">🔒 Secure test environment for {user?.name || user?.email}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium">✅ Server-side authentication active</span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Customizer */}
          <div className="lg:col-span-2">
            <SimpleProductCustomizer
              productImageUrl="https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=500&fit=crop"
              onCustomizationChange={handleCustomizationChange}
              initialCustomization={customizationData}
            />
          </div>

          {/* Test Panel */}
          <div className="space-y-6">
            {/* Test Controls */}
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Test Controls</h3>
              
              <div className="space-y-4">
                <button
                  onClick={() => setShowData(!showData)}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {showData ? "Hide" : "Show"} Customization Data
                </button>

                <button
                  onClick={downloadDesign}
                  disabled={!customizationData?.preview}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Download Design
                </button>

                <button
                  onClick={() => setCustomizationData(null)}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Clear Design
                </button>
              </div>
            </div>

            {/* Customization Status */}
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Status</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Has Design:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    customizationData ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {customizationData ? 'Yes' : 'No'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Has Preview:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    customizationData?.preview ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {customizationData?.preview ? 'Yes' : 'No'}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-600">User:</span>
                  <span className="text-sm text-gray-900 font-medium">
                    {user?.name || user?.email}
                  </span>
                </div>
              </div>
            </div>

            {/* Preview */}
            {customizationData?.preview && (
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Preview</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={customizationData.preview}
                    alt="Design preview"
                    className="w-full h-auto"
                  />
                </div>
              </div>
            )}

            {/* Raw Data */}
            {showData && customizationData && (
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Raw Data</h3>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-auto">
                  <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                    {JSON.stringify(customizationData, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-12 bg-blue-50 rounded-2xl p-8 border border-blue-200">
          <h3 className="text-2xl font-bold text-blue-900 mb-4">🧪 Test Instructions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-blue-800">
            <div>
              <h4 className="font-semibold mb-2">Basic Testing:</h4>
              <ul className="space-y-1 text-sm">
                <li>• Add custom text</li>
                <li>• Change text color</li>
                <li>• Adjust font size</li>
                <li>• Try different fonts</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Advanced Testing:</h4>
              <ul className="space-y-1 text-sm">
                <li>• Download the design</li>
                <li>• Check raw data output</li>
                <li>• Test clear functionality</li>
                <li>• Verify auto-save works</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
