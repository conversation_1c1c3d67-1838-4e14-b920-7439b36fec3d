(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{1615:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(5155),l=s(2115),n=s(6874),a=s.n(n),i=s(5695),o=s(2502),c=s(4615),d=s(3389);function u(e){let{isOpen:t,title:s,message:n,confirmText:a="Confirm",cancelText:i="Cancel",onConfirm:o,onCancel:c,type:d="info"}=e;if((0,l.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t&&c()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,c]),!t)return null;let u=(()=>{switch(d){case"warning":return{icon:"⚠️",iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmBg:"bg-yellow-600 hover:bg-yellow-700",titleColor:"text-yellow-800"};case"danger":return{icon:"\uD83D\uDEA8",iconBg:"bg-red-100",iconColor:"text-red-600",confirmBg:"bg-red-600 hover:bg-red-700",titleColor:"text-red-800"};case"success":return{icon:"✅",iconBg:"bg-green-100",iconColor:"text-green-600",confirmBg:"bg-green-600 hover:bg-green-700",titleColor:"text-green-800"};default:return{icon:"\uD83D\uDCB0",iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmBg:"bg-blue-600 hover:bg-blue-700",titleColor:"text-blue-800"}}})();return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out scale-100",children:[(0,r.jsx)("div",{className:"p-6 pb-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full ".concat(u.iconBg," flex items-center justify-center flex-shrink-0"),children:(0,r.jsx)("span",{className:"text-2xl",children:u.icon})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("h3",{className:"text-xl font-bold ".concat(u.titleColor," leading-tight"),children:s})})]})}),(0,r.jsx)("div",{className:"px-6 pb-6",children:(0,r.jsx)("p",{className:"text-gray-700 text-base leading-relaxed",children:n})}),(0,r.jsxs)("div",{className:"px-6 pb-6 flex gap-3 justify-end",children:[(0,r.jsx)("button",{onClick:c,className:"px-6 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300",children:i}),(0,r.jsx)("button",{onClick:o,className:"px-6 py-2.5 text-white ".concat(u.confirmBg," rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg"),children:a})]})]})})}var x=s(3843);function m(e){var t,s,n,o,m,h,g,b;let{user:f}=e,[p,v]=(0,l.useState)(null),[N,j]=(0,l.useState)(""),[w,C]=(0,l.useState)(1),[y,D]=(0,l.useState)("products"),[k,E]=(0,l.useState)(0),[S,O]=(0,l.useState)(!1),[B,A]=(0,l.useState)(!1),[L,T]=(0,l.useState)(null);(0,i.useRouter)();let _=()=>{E(JSON.parse(localStorage.getItem("cart")||"[]").length)};async function I(e){if(!e)return;let t=localStorage.getItem("token");try{if(!(await fetch((0,x.e9)("".concat(x.i3.ENDPOINTS.ADMIN.ORDER_COLLECTED,"/").concat(e,"/collected")),{method:"PUT",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to mark as collected");v(t=>{var s,r;return t&&t.orders?{...t,orders:t.orders.map(t=>t.id===e?{...t,status:"COLLECTED"}:t),stats:{...t.stats,collectedOrders:(null!=(r=null==(s=t.stats)?void 0:s.collectedOrders)?r:0)+1}}:t})}catch(e){alert("Error updating order status")}}if((0,l.useEffect)(()=>{_()},[]),(0,l.useEffect)(()=>{let e=localStorage.getItem("token");if(!e)return void j("Not authenticated");fetch((0,x.e9)(x.i3.ENDPOINTS.ADMIN.DASHBOARD),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{v(e)}).catch(()=>j("Failed to load dashboard"))},[]),N)return(0,r.jsx)("div",{className:"text-red-600 text-center mt-16",children:N});if(!p)return(0,r.jsx)("div",{className:"text-center mt-16",children:"Loading..."});let M=p.products||[];return M.length,M.slice((w-1)*5,5*w),h=null!=(m=null==(t=p.stats)?void 0:t.orders)?m:null==(s=p.orders)?void 0:s.length,b=null!=(g=null==(n=p.stats)?void 0:n.collectedOrders)?g:null==(o=p.orders)?void 0:o.filter(e=>"COLLECTED"===e.status).length,(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(c.A,{cartCount:k,onCartClick:()=>{O(!0)}}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,r.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Admin"}),(0,r.jsx)("span",{className:"text-white",children:" Dashboard"})]}),(0,r.jsxs)("p",{className:"text-xl text-gray-200 mb-4 max-w-2xl mx-auto",children:["Welcome back, ",(null==f?void 0:f.name)||(null==f?void 0:f.email),"! Manage your Six Light Media store with powerful tools."]}),(0,r.jsxs)("div",{className:"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,r.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDD12 Secure Admin Access"})]})]})})]}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"✅ Server-side authentication active"})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDEA7 Dashboard Under Construction"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"The admin dashboard is being migrated to use secure server-side authentication."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(a(),{href:"/admin/products",className:"p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCE6"}),(0,r.jsx)("div",{className:"font-semibold text-blue-900",children:"Manage Products"})]}),(0,r.jsxs)(a(),{href:"/admin/orders",className:"p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-colors",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCCB"}),(0,r.jsx)("div",{className:"font-semibold text-green-900",children:"View Orders"})]}),(0,r.jsxs)(a(),{href:"/admin/categories",className:"p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFF7️"}),(0,r.jsx)("div",{className:"font-semibold text-purple-900",children:"Categories"})]})]})]})]}),(0,r.jsx)(d.A,{isOpen:S,onClose:()=>{O(!1),_()}}),(0,r.jsx)(u,{isOpen:B,title:"Confirm Order Collection",message:"Has the customer paid and collected this order? This action will mark the order as completed.",confirmText:"Yes, Mark as Collected",cancelText:"Cancel",onConfirm:()=>{L&&I(L),A(!1),T(null)},onCancel:()=>{A(!1),T(null)},type:"info"})]})}o.t1.register(o.Bs,o.m_,o.s$)},3231:(e,t,s)=>{Promise.resolve().then(s.bind(s,1615))}},e=>{var t=t=>e(e.s=t);e.O(0,[647,766,874,342,211,441,684,358],()=>t(3231)),_N_E=e.O()}]);