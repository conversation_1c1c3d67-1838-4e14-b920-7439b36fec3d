"use client";

import React, { useState, useEffect } from "react";
import { getApiUrl, API_CONFIG } from "@/lib/config";

type CartItem = {
  id: number;
  productId: number;
  name: string;
  color?: string;
  text?: string;
  customized: boolean;
  price: number;
  quantity?: number;
};

type OrderFormProps = {
  cartItems: CartItem[];
  onOrderSuccess: () => void;
  onCancel: () => void;
};

type OrderFormData = {
  customerName: string;
  customerPhone: string;
  customerAddress: string;
};

export default function OrderForm({
  cartItems,
  onOrderSuccess,
  onCancel,
}: OrderFormProps) {
  const [formData, setFormData] = useState<OrderFormData>({
    customerName: "",
    customerPhone: "",
    customerAddress: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  useEffect(() => {
    // Get user info to pre-fill name
    const token = localStorage.getItem("token");
    if (token) {
      fetch(`${API_URL}/user/dashboard`, {
        headers: { Authorization: `Bearer ${token}` },
      })
        .then((res) => res.json())
        .then((data) => {
          if (data && data.user) {
            setFormData((prev) => ({
              ...prev,
              customerName: data.user.name || "",
            }));
          }
        })
        .catch(() => {
          // Ignore error, user can still fill form manually
        });
    }
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const calculateTotal = () => {
    return cartItems.reduce(
      (total, item) => total + item.price * (item.quantity || 1),
      0
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    const token = localStorage.getItem("token");
    if (!token) {
      setError("Please log in to place an order");
      setLoading(false);
      return;
    }

    try {
      // Create orders for each cart item
      const orderPromises = cartItems.map((item) =>
        fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.ORDERS), {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            productId: item.productId,
            customerName: formData.customerName,
            customerPhone: formData.customerPhone,
            customerAddress: formData.customerAddress,
            customColor: item.color,
            customText: item.text,
            isCustomized: item.customized,
            quantity: item.quantity || 1,
          }),
        })
      );

      const responses = await Promise.all(orderPromises);

      // Check if all orders were successful
      const allSuccessful = responses.every((res) => res.ok);

      if (allSuccessful) {
        // Clear cart
        localStorage.removeItem("cart");
        onOrderSuccess();
      } else {
        setError("Some orders failed to process. Please try again.");
      }
    } catch {
      setError("Failed to place order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#1a237e]">
              Complete Your Order
            </h2>
            <button
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700 text-2xl"
              aria-label="Close"
            >
              ×
            </button>
          </div>

          {/* Order Summary */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-3">Order Summary</h3>
            {cartItems.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0"
              >
                <div>
                  <div className="font-medium">{item.name}</div>
                  {item.customized && (
                    <div className="text-sm text-gray-600">
                      {item.color && <span>Color: {item.color}</span>}
                      {item.text && (
                        <span className="ml-2">
                          Text: &ldquo;{item.text}&rdquo;
                        </span>
                      )}
                    </div>
                  )}
                  <div className="text-sm text-gray-500">
                    Qty: {item.quantity || 1}
                  </div>
                </div>
                <div className="font-semibold">
                  K{(item.price * (item.quantity || 1)).toFixed(2)}
                </div>
              </div>
            ))}
            <div className="flex justify-between items-center pt-3 mt-3 border-t border-gray-300">
              <div className="font-bold text-lg">Total:</div>
              <div className="font-bold text-lg text-[#1a237e]">
                K{calculateTotal().toFixed(2)}
              </div>
            </div>
          </div>

          {/* Order Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="customerName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Full Name *
              </label>
              <input
                type="text"
                id="customerName"
                name="customerName"
                value={formData.customerName}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label
                htmlFor="customerPhone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Phone Number *
              </label>
              <input
                type="tel"
                id="customerPhone"
                name="customerPhone"
                value={formData.customerPhone}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent"
                placeholder="e.g. +260 971 781 907"
              />
            </div>

            <div>
              <label
                htmlFor="customerAddress"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Delivery/Pickup Address *
              </label>
              <textarea
                id="customerAddress"
                name="customerAddress"
                value={formData.customerAddress}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent"
                placeholder="Enter your full address or preferred pickup location"
              />
            </div>

            {error && (
              <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
                {error}
              </div>
            )}

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold transition ${
                  loading
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:bg-[#2a3490]"
                }`}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Placing Order...</span>
                  </div>
                ) : (
                  `Place Order - K${calculateTotal().toFixed(2)}`
                )}
              </button>
            </div>
          </form>

          <div className="mt-4 text-xs text-gray-500 text-center">
            By placing this order, you agree to our terms and conditions. You
            will be contacted for payment and delivery arrangements.
          </div>
        </div>
      </div>
    </div>
  );
}
