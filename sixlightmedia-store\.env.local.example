# Local Environment Configuration Template
# Copy this file to .env.local and customize for your local development
# .env.local takes precedence over .env.development and .env

# Backend API Configuration
# For development: http://localhost:3001
# For production: https://backendapi-sixlight.onrender.com
NEXT_PUBLIC_API_URL=http://localhost:3001

# ImageKit Configuration
NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT="https://ik.imagekit.io/fwbvmq9re"
NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY="public_kFR0vTVL7DIDI8YW9eF6/luGjB4="
IMAGEKIT_PRIVATE_KEY="private_jtsBIKXyuTkkHzHJOBSHLqbyK74="

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Analytics (Optional - Add your Google Analytics ID)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# SEO Verification (Optional - Add your verification codes)
GOOGLE_VERIFICATION_CODE=your-google-verification-code

# Additional local-only configuration can go here
# These variables will override any other environment files
