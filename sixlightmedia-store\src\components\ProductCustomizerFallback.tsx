"use client";

import React, { useState } from "react";
import { Type, Palette, Download, AlertCircle } from "lucide-react";

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

interface ProductCustomizerProps {
  productImage: string;
  onCustomizationChange: (customization: CustomizationData) => void;
  initialCustomization?: CustomizationData | null;
}

const ProductCustomizerFallback: React.FC<ProductCustomizerProps> = ({
  productImage,
  onCustomizationChange,
  initialCustomization,
}) => {
  const [text, setText] = useState("");
  const [textColor, setTextColor] = useState("#000000");
  const [fontSize, setFontSize] = useState(20);
  const [fontFamily, setFontFamily] = useState("Arial");

  const handleChange = () => {
    // Create a simple preview for basic customization
    const customizationData = {
      canvasData: JSON.stringify({
        text,
        textColor,
        fontSize,
        fontFamily,
        type: "basic",
      }),
      preview: text
        ? `data:text/plain;base64,${btoa(`${text} (${textColor})`)}`
        : undefined,
    };
    onCustomizationChange(customizationData);
  };

  React.useEffect(() => {
    handleChange();
  }, [text, textColor, fontSize, fontFamily]);

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
      <div className="mb-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          🎨 Product Customizer
        </h3>
        <p className="text-gray-600">
          Customize your product with text and colors
        </p>
      </div>

      {/* Fabric.js Installation Notice */}
      <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-xl">
        <div className="flex items-start gap-3">
          <AlertCircle className="text-amber-600 mt-1" size={20} />
          <div>
            <h4 className="font-semibold text-amber-800 mb-2">
              Advanced Customizer Available
            </h4>
            <p className="text-sm text-amber-700 mb-3">
              For full design capabilities with Fabric.js, please install the
              fabric package:
            </p>
            <div className="bg-amber-100 p-3 rounded-lg font-mono text-sm text-amber-800">
              npm install fabric
            </div>
            <p className="text-xs text-amber-600 mt-2">
              After installation, restart the development server to access
              advanced features like shapes, image upload, and canvas editing.
            </p>
          </div>
        </div>
      </div>

      {/* Product Preview */}
      <div className="mb-6">
        <div className="relative bg-gray-100 rounded-xl overflow-hidden">
          <img
            src={productImage}
            alt="Product"
            className="w-full h-64 object-contain"
            onError={(e) => {
              (e.target as HTMLImageElement).src = "/bottle-dummy.jpg";
            }}
          />
          {text && (
            <div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
              style={{
                color: textColor,
                fontSize: `${fontSize}px`,
                fontFamily: fontFamily,
                textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
              }}
            >
              {text}
            </div>
          )}
        </div>
      </div>

      {/* Basic Customization Controls */}
      <div className="space-y-6">
        {/* Text Input */}
        <div className="space-y-3">
          <label className="block text-sm font-semibold text-gray-700">
            <Type className="inline mr-2" size={16} />
            Custom Text
          </label>
          <input
            type="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Enter your custom text"
            className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200"
            maxLength={30}
          />
          <div className="text-xs text-gray-500">
            {text.length}/30 characters
          </div>
        </div>

        {/* Color Picker */}
        <div className="space-y-3">
          <label className="block text-sm font-semibold text-gray-700">
            <Palette className="inline mr-2" size={16} />
            Text Color
          </label>
          <div className="flex items-center gap-4">
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer"
            />
            <div className="flex-1">
              <div className="text-sm font-mono text-gray-600 bg-gray-100 px-3 py-2 rounded-lg">
                {textColor.toUpperCase()}
              </div>
            </div>
          </div>
        </div>

        {/* Font Size */}
        <div className="space-y-3">
          <label className="block text-sm font-semibold text-gray-700">
            Font Size: {fontSize}px
          </label>
          <input
            type="range"
            min="12"
            max="48"
            value={fontSize}
            onChange={(e) => setFontSize(Number(e.target.value))}
            className="w-full"
          />
        </div>

        {/* Font Family */}
        <div className="space-y-3">
          <label className="block text-sm font-semibold text-gray-700">
            Font Family
          </label>
          <select
            value={fontFamily}
            onChange={(e) => setFontFamily(e.target.value)}
            className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100"
          >
            <option value="Arial">Arial</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Georgia">Georgia</option>
            <option value="Verdana">Verdana</option>
            <option value="Comic Sans MS">Comic Sans MS</option>
            <option value="Impact">Impact</option>
          </select>
        </div>

        {/* Preview Info */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
          <h4 className="font-semibold text-blue-800 mb-2">
            Customization Preview
          </h4>
          <div className="text-sm text-blue-700 space-y-1">
            <div>
              <strong>Text:</strong> {text || "No text added"}
            </div>
            <div>
              <strong>Color:</strong> {textColor}
            </div>
            <div>
              <strong>Font:</strong> {fontFamily} ({fontSize}px)
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 text-sm text-gray-500 text-center">
        💡 Tip: Your customization will be saved when you add the product to
        cart
      </div>
    </div>
  );
};

export default ProductCustomizerFallback;
