(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/SmartProductCustomizer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
;
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
// Dynamically import the fallback customizer
const ProductCustomizerFallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/src/components/ProductCustomizerFallback.tsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/src/components/ProductCustomizerFallback.tsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false,
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-2xl shadow-xl p-6 border border-gray-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center h-64",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                            lineNumber: 27,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600",
                            children: "Loading Customizer..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                            lineNumber: 28,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                    lineNumber: 26,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                lineNumber: 25,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
            lineNumber: 24,
            columnNumber: 7
        }, this)
});
_c = ProductCustomizerFallback;
const SmartProductCustomizer = (props)=>{
    _s();
    const [fabricAvailable, setFabricAvailable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [AdvancedCustomizer, setAdvancedCustomizer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SmartProductCustomizer.useEffect": ()=>{
            // Check if Fabric.js is available
            const checkFabricAvailability = {
                "SmartProductCustomizer.useEffect.checkFabricAvailability": async ()=>{
                    try {
                        console.log("SmartProductCustomizer: Checking Fabric.js availability...");
                        // Try to dynamically import fabric
                        const fabricModule = await __turbopack_context__.r("[project]/node_modules/fabric/dist/fabric.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                        console.log("SmartProductCustomizer: Fabric.js imported successfully:", !!fabricModule.fabric);
                        // Verify fabric is actually usable
                        if (!fabricModule.fabric) {
                            throw new Error("Fabric.js imported but fabric object is null");
                        }
                        // If successful, try to import the advanced customizer
                        console.log("SmartProductCustomizer: Importing ProductCustomizer...");
                        const { default: ProductCustomizer } = await __turbopack_context__.r("[project]/src/components/ProductCustomizer.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                        console.log("SmartProductCustomizer: ProductCustomizer imported successfully:", !!ProductCustomizer);
                        if (!ProductCustomizer) {
                            throw new Error("ProductCustomizer imported but is null");
                        }
                        setAdvancedCustomizer({
                            "SmartProductCustomizer.useEffect.checkFabricAvailability": ()=>ProductCustomizer
                        }["SmartProductCustomizer.useEffect.checkFabricAvailability"]);
                        setFabricAvailable(true);
                        console.log("SmartProductCustomizer: Advanced customizer ready!");
                    } catch (error) {
                        // Fabric.js or advanced customizer not available
                        console.error("SmartProductCustomizer: Error loading advanced customizer:", error);
                        setFabricAvailable(false);
                    }
                }
            }["SmartProductCustomizer.useEffect.checkFabricAvailability"];
            // Add timeout to prevent infinite loading
            const timeout = setTimeout({
                "SmartProductCustomizer.useEffect.timeout": ()=>{
                    console.error("SmartProductCustomizer: Timeout - falling back to basic customizer");
                    setFabricAvailable(false);
                }
            }["SmartProductCustomizer.useEffect.timeout"], 3000); // Reduced to 3 seconds for faster fallback
            checkFabricAvailability().finally({
                "SmartProductCustomizer.useEffect": ()=>{
                    clearTimeout(timeout);
                }
            }["SmartProductCustomizer.useEffect"]);
        }
    }["SmartProductCustomizer.useEffect"], []);
    // Show loading state while checking
    if (fabricAvailable === null) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-2xl shadow-xl p-6 border border-gray-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center h-64",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                            lineNumber: 107,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600",
                            children: "Checking customizer capabilities..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                            lineNumber: 108,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-500 mt-2",
                            children: "Loading advanced design tools..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                            lineNumber: 109,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-gray-400 mt-2",
                            children: "If this takes too long, we'll automatically switch to basic mode"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                            lineNumber: 112,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                    lineNumber: 106,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/SmartProductCustomizer.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
            lineNumber: 104,
            columnNumber: 7
        }, this);
    }
    // Use advanced customizer if available, otherwise use fallback
    if (fabricAvailable && AdvancedCustomizer) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AdvancedCustomizer, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/components/SmartProductCustomizer.tsx",
            lineNumber: 123,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductCustomizerFallback, {
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/SmartProductCustomizer.tsx",
        lineNumber: 126,
        columnNumber: 10
    }, this);
};
_s(SmartProductCustomizer, "4BAcNwpXg7b1Uu8G1EyvuGILmOs=");
_c1 = SmartProductCustomizer;
const __TURBOPACK__default__export__ = SmartProductCustomizer;
var _c, _c1;
__turbopack_context__.k.register(_c, "ProductCustomizerFallback");
__turbopack_context__.k.register(_c1, "SmartProductCustomizer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/SmartProductCustomizer.tsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/SmartProductCustomizer.tsx [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=src_components_SmartProductCustomizer_tsx_c2b3c3a2._.js.map