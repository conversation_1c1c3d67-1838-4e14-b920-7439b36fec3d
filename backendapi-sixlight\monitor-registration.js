const http = require('http');

// Function to test registration and monitor response
async function monitorRegistration(email) {
  console.log('🔍 Monitoring Registration Process...');
  console.log(`📧 Testing with email: ${email}`);
  
  const testUser = {
    email: email,
    password: 'TestPassword123!',
    name: 'Test User'
  };
  
  try {
    console.log('📤 Sending registration request...');
    console.log('⏰ Timestamp:', new Date().toISOString());
    
    const postData = JSON.stringify(testUser);
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const response = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers
          });
        });
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.write(postData);
      req.end();
    });
    
    console.log(`📊 Response status: ${response.status}`);
    console.log('📋 Response headers:', response.headers);
    
    try {
      const result = JSON.parse(response.data);
      console.log('📋 Response body:', JSON.stringify(result, null, 2));
      
      if (result.user && result.user.emailVerified === false) {
        console.log('✅ User created with emailVerified: false (correct!)');
        console.log('📧 Email verification should have been sent');
        console.log('');
        console.log('🔍 Next steps:');
        console.log('1. Check your email inbox for verification email');
        console.log('2. Check spam/junk folder');
        console.log('3. Search for "Six Light Media Store" in your email');
        console.log('4. Look for <NAME_EMAIL>');
      }
      
    } catch {
      console.log('📋 Response body (raw):', response.data);
    }
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Registration API call successful!');
    } else {
      console.log('❌ Registration failed');
    }
    
  } catch (error) {
    console.error('❌ Registration test failed:', error.message);
  }
}

// Get email from command line argument or use default
const email = process.argv[2] || '<EMAIL>';
monitorRegistration(email);
