(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[839],{646:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1264:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},2420:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var a=t(5155),s=t(2115),i=t(5695),n=t(9958),l=t(6874),o=t.n(l),c=t(6766),d=t(646),u=t(9946);let h=(0,u.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=t(1264);let x=(0,u.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function p(){let[e,r]=(0,s.useState)("loading"),[t,l]=(0,s.useState)(""),[u,p]=(0,s.useState)(""),[g,f]=(0,s.useState)(!1),y=(0,i.useSearchParams)(),N=(0,i.useRouter)();(0,s.useEffect)(()=>{let e=null==y?void 0:y.get("token");if(!e){r("error"),l("Invalid verification link. Please check your email for the correct link.");return}(0,n.A$)(e).then(e=>{e.message?(r("success"),l(e.message),setTimeout(()=>{N.push("/login?message=Email verified successfully! You can now log in.")},3e3)):(r("error"),l(e.error||"Verification failed"))}).catch(e=>{var t,a;r("error"),(null==(t=e.message)?void 0:t.includes("expired"))?(r("expired"),l("Your verification link has expired. Please request a new one.")):(null==(a=e.message)?void 0:a.includes("already verified"))?(r("success"),l("Your email is already verified! You can log in now."),setTimeout(()=>{N.push("/login")},2e3)):l("Verification failed. Please try again or contact support.")})},[y,N]);let E=async()=>{if(!u)return void alert("Please enter your email address");f(!0);try{let e=await (0,n.RS)(u);alert(e.message||"Verification email sent! Please check your inbox.")}catch(e){alert("Failed to resend verification email. Please try again.")}finally{f(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,a.jsx)(c.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Email Verification"})]}),"loading"===e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Verifying your email..."})]}),"success"===e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d.A,{className:"h-16 w-16 text-green-500 mx-auto"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-green-700",children:"Email Verified!"}),(0,a.jsx)("p",{className:"text-gray-600",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to login page..."})]}),(0,a.jsx)(o(),{href:"/login",className:"inline-block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors",children:"Continue to Login"})]}),"error"===e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(h,{className:"h-16 w-16 text-red-500 mx-auto"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-red-700",children:"Verification Failed"}),(0,a.jsx)("p",{className:"text-gray-600",children:t})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(o(),{href:"/login",className:"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors",children:"Go to Login"}),(0,a.jsx)(o(),{href:"/register",className:"inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors",children:"Create New Account"})]})]}),"expired"===e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(m.A,{className:"h-16 w-16 text-orange-500 mx-auto"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-orange-700",children:"Link Expired"}),(0,a.jsx)("p",{className:"text-gray-600",children:t})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("input",{type:"email",value:u,onChange:e=>p(e.target.value),placeholder:"Enter your email address",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("button",{onClick:E,disabled:g,className:"w-full bg-orange-600 text-white py-3 px-6 rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors flex items-center justify-center gap-2",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x,{className:"h-4 w-4 animate-spin"}),"Sending..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Resend Verification Email"]})}),(0,a.jsx)(o(),{href:"/register",className:"inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors",children:"Create New Account"})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Need help?"," ",(0,a.jsx)(o(),{href:"/contact",className:"text-blue-600 hover:text-blue-800",children:"Contact Support"})]})})]})})}function g(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Verifying email..."})]})})}),children:(0,a.jsx)(p,{})})}},3843:(e,r,t)=>{"use strict";t.d(r,{e9:()=>i,i3:()=>s});var a=t(9509);let s={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh",VERIFY_EMAIL:"/auth/verify-email",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",RESEND_VERIFICATION:"/auth/resend-verification"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",UPDATE_PROFILE:"/user/profile",UPLOAD_PROFILE_IMAGE:"/user/upload-profile-image",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};a.env.IMAGEKIT_PRIVATE_KEY,a.env.NEXT_PUBLIC_GA_ID,a.env.NEXT_PUBLIC_GA_ID;let i=e=>{let r=s.BASE_URL.replace(/\/$/,""),t=e.startsWith("/")?e:"/".concat(e);return"".concat(r).concat(t)}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6286:(e,r,t)=>{Promise.resolve().then(t.bind(t,2420))},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},o=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:h,...m}=e;return(0,a.createElement)("svg",{ref:r,...c,width:s,height:s,stroke:t,strokeWidth:n?24*Number(i)/Number(s):i,className:l("lucide",d),...!u&&!o(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:o,...c}=t;return(0,a.createElement)(d,{ref:i,iconNode:r,className:l("lucide-".concat(s(n(e))),"lucide-".concat(e),o),...c})});return t.displayName=n(e),t}},9958:(e,r,t)=>{"use strict";t.d(r,{A$:()=>n,BD:()=>l,RS:()=>c,iD:()=>s,kz:()=>i,xw:()=>o});var a=t(3843);async function s(e,r){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:r})})).json()}async function i(e,r,t){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:r,name:t})})).json()}async function n(e){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.VERIFY_EMAIL),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e})})).json()}async function l(e){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.FORGOT_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}async function o(e,r){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.RESET_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e,password:r})})).json()}async function c(e){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.RESEND_VERIFICATION),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}}},e=>{var r=r=>e(e.s=r);e.O(0,[766,874,441,684,358],()=>r(6286)),_N_E=e.O()}]);