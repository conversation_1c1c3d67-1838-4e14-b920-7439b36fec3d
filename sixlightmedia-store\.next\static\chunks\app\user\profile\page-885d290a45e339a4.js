(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[614],{4416:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},7339:(e,a,t)=>{Promise.resolve().then(t.bind(t,9238))},9238:(e,a,t)=>{"use strict";t.d(a,{default:()=>u});var s=t(5155),r=t(2115),l=t(6766),o=t(6874),n=t.n(o),i=t(4615),d=t(3389),c=t(3843);function u(e){let{user:a}=e,[t,o]=(0,r.useState)(null),[u,m]=(0,r.useState)(""),[h,x]=(0,r.useState)(""),[g,f]=(0,r.useState)(!1),[p,b]=(0,r.useState)(0),[w,y]=(0,r.useState)(!1),[v,N]=(0,r.useState)(!1),[j,P]=(0,r.useState)({oldPassword:"",newPassword:"",confirmPassword:""}),[k,S]=(0,r.useState)(""),[C,A]=(0,r.useState)(""),[E,I]=(0,r.useState)(0),[D,O]=(0,r.useState)(!1),[F,R]=(0,r.useState)(!0),M=(0,r.useRef)(null),T=()=>{I(JSON.parse(localStorage.getItem("cart")||"[]").length)};(0,r.useEffect)(()=>{T()},[]),(0,r.useEffect)(()=>{let e=localStorage.getItem("token");if(!e){S("Not authenticated"),R(!1);return}fetch((0,c.e9)(c.i3.ENDPOINTS.USER.DASHBOARD),{headers:{Authorization:"Bearer ".concat(e)},credentials:"include"}).then(async e=>{if(e.ok){let a=(await e.json()).user;o(a),m(a.name||""),x(a.profileImage||"")}else{let a=await e.text();console.error("Profile API error:",e.status,a),S("Failed to load profile: ".concat(e.status))}}).catch(e=>{console.error("Profile fetch error:",e),S("Network error. Please check your connection.")}).finally(()=>R(!1))},[]);let _=async e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){if(t.size>5242880)return void S("Image size must be less than 5MB");f(!0),b(0),S(""),new FormData().append("profileImage",t);try{localStorage.getItem("token");let e=new XMLHttpRequest;e.upload.addEventListener("progress",e=>{if(e.lengthComputable){let a=e.loaded/e.total*100;b(a)}}),e.onload=()=>{if(200===e.status){let a=JSON.parse(e.responseText);x(a.profileImage),o(e=>e?{...e,profileImage:a.profileImage}:null),A("Profile image updated successfully!")}else S("Failed to upload image");f(!1),b(0)},e.onerror=()=>{S("Failed to upload image"),f(!1),b(0)},S("Image upload feature is temporarily disabled. Please update your name instead."),f(!1),b(0);return}catch(e){S("Failed to upload image"),f(!1),b(0)}}},z=async()=>{y(!0),S(""),A("");try{let e=localStorage.getItem("token"),a=await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.UPDATE_PROFILE),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},credentials:"include",body:JSON.stringify({name:u})});if(a.ok){let e=await a.json();o(e),A("Profile updated successfully!")}else S("Failed to update profile")}catch(e){S("Failed to update profile")}finally{y(!1)}},B=async()=>{if(j.newPassword!==j.confirmPassword)return void S("New passwords don't match");if(j.newPassword.length<6)return void S("New password must be at least 6 characters");N(!0),S(""),A("");try{let e=localStorage.getItem("token"),a=await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.CHANGE_PASSWORD),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},credentials:"include",body:JSON.stringify({oldPassword:j.oldPassword,newPassword:j.newPassword})});if(a.ok)P({oldPassword:"",newPassword:"",confirmPassword:""}),A("Password changed successfully!");else{let e=await a.json();S(e.message||"Failed to change password")}}catch(e){S("Failed to change password")}finally{N(!1)}};return F?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{cartCount:E,onCartClick:()=>{O(!0)}}),(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Profile"}),(0,s.jsx)("span",{className:"text-white",children:" Management"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-200 mb-4 max-w-2xl mx-auto",children:"Manage your account settings and personal information"}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsxs)("span",{className:"text-white text-sm",children:["\uD83D\uDD12 Secure profile access for ",(null==a?void 0:a.name)||(null==a?void 0:a.email)]})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"✅ Server-side authentication active"})]})}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(n(),{href:"/user/dashboard",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Dashboard"})}),k&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:k}),C&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg",children:C}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Profile Information"}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsxs)("div",{className:"relative inline-block",children:[(0,s.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 mx-auto mb-4",children:h?(0,s.jsx)(l.default,{src:h,alt:"Profile",width:128,height:128,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400 text-4xl",children:"\uD83D\uDC64"})}),g&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50 rounded-full",children:(0,s.jsxs)("div",{className:"text-white text-sm",children:[Math.round(p),"%"]})})]}),(0,s.jsx)("input",{ref:M,type:"file",accept:"image/*",onChange:_,className:"hidden"}),(0,s.jsx)("button",{onClick:()=>{var e;return null==(e=M.current)?void 0:e.click()},disabled:g,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:g?"Uploading...":"Change Photo"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,s.jsx)("input",{type:"text",value:u,onChange:e=>m(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",value:(null==t?void 0:t.email)||"",disabled:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email cannot be changed"})]}),(0,s.jsx)("button",{onClick:z,disabled:w,className:"w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:w?"Saving...":"Save Profile"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Change Password"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),(0,s.jsx)("input",{type:"password",value:j.oldPassword,onChange:e=>P(a=>({...a,oldPassword:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,s.jsx)("input",{type:"password",value:j.newPassword,onChange:e=>P(a=>({...a,newPassword:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,s.jsx)("input",{type:"password",value:j.confirmPassword,onChange:e=>P(a=>({...a,confirmPassword:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)("button",{onClick:B,disabled:v||!j.oldPassword||!j.newPassword||!j.confirmPassword,className:"w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:v?"Changing...":"Change Password"})]})]})]})]})]}),(0,s.jsx)(d.A,{isOpen:D,onClose:()=>{O(!1),T()}})]})}},9946:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var s=t(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,t)=>t?t.toUpperCase():a.toLowerCase()),o=e=>{let a=l(e);return a.charAt(0).toUpperCase()+a.slice(1)},n=function(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return a.filter((e,a,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===a).join(" ").trim()},i=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,a)=>{let{color:t="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:c="",children:u,iconNode:m,...h}=e;return(0,s.createElement)("svg",{ref:a,...d,width:r,height:r,stroke:t,strokeWidth:o?24*Number(l)/Number(r):l,className:n("lucide",c),...!u&&!i(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[a,t]=e;return(0,s.createElement)(a,t)}),...Array.isArray(u)?u:[u]])}),u=(e,a)=>{let t=(0,s.forwardRef)((t,l)=>{let{className:i,...d}=t;return(0,s.createElement)(c,{ref:l,iconNode:a,className:n("lucide-".concat(r(o(e))),"lucide-".concat(e),i),...d})});return t.displayName=o(e),t}}},e=>{var a=a=>e(e.s=a);e.O(0,[766,874,211,441,684,358],()=>a(7339)),_N_E=e.O()}]);