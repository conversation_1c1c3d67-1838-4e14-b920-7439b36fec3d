"use client";
import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import Image from "next/image";
import { IKContext, IKUpload } from "imagekitio-react";
import { getApiUrl, API_CONFIG } from "@/lib/config";

type Product = {
  id: string | number;
  name: string;
  price?: number;
  image?: string;
  slug?: string;
  description?: string;
  customizable?: boolean;
  categoryId?: number;
  modelUrl?: string;
  category?: { id: number; name: string }; // Add category type
};

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params?.id;
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState<{ id: number; name: string }[]>(
    []
  );
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState("");

  useEffect(() => {
    setCategoriesLoading(true);
    fetch(getApiUrl(API_CONFIG.ENDPOINTS.CATEGORIES))
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fetch categories");
        return res.json();
      })
      .then((data) => {
        setCategories(data);
        setCategoriesError("");
      })
      .catch(() => {
        setCategories([]);
        setCategoriesError("Failed to load categories");
      })
      .finally(() => setCategoriesLoading(false));
  }, []);

  useEffect(() => {
    if (!productId) return;
    const token = localStorage.getItem("token");
    fetch(getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS}/${productId}`), {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => {
        setProduct({
          id: data.id,
          name: data.name ?? "",
          price: data.price ?? 0,
          image: data.image ?? "",
          slug: data.slug ?? "",
          description: data.description ?? "",
          customizable: data.customizable ?? false,
          categoryId: data.categoryId ?? data.category?.id ?? undefined,
          modelUrl: data.modelUrl ?? "",
          category: data.category ?? undefined, // Add category object if present
        });
      })
      .catch(() => setError("Failed to load product"))
      .finally(() => setLoading(false));
  }, [productId]);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSaving(true);
    const token = localStorage.getItem("token");
    const updateData = { ...product };
    delete updateData.id;
    const res = await fetch(
      getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS}/${productId}`),
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      }
    );
    setSaving(false);
    if (res.ok) {
      router.push("/admin/dashboard");
    } else {
      setError("Failed to update product");
    }
  }

  if (loading) return <div className="text-center mt-16">Loading...</div>;
  if (error)
    return <div className="text-red-600 text-center mt-16">{error}</div>;
  if (!product) return null;

  return (
    <div className="max-w-lg mx-auto mt-10 bg-white rounded-2xl shadow p-8">
      <h2 className="text-2xl font-bold mb-6 text-center text-[#1a237e]">
        Edit Product
      </h2>
      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <label className="font-semibold">
          Name
          <input
            className="w-full border rounded px-3 py-2 mt-1"
            value={product.name || ""}
            onChange={(e) => setProduct({ ...product, name: e.target.value })}
            required
          />
        </label>
        <label className="font-semibold">
          Price
          <div className="flex items-center mt-1">
            <span className="px-2 py-2 bg-gray-100 border border-r-0 rounded-l">
              K
            </span>
            <input
              type="number"
              min="0"
              step="0.01"
              className="w-full border rounded-r px-3 py-2 focus:outline-none"
              value={product.price !== undefined ? product.price : 0}
              onChange={(e) =>
                setProduct({ ...product, price: Number(e.target.value) })
              }
              required
            />
          </div>
        </label>
        <div className="text-lg font-semibold text-green-700 mt-2">
          Price: <span className="font-bold">K{product.price ?? 0}</span>
        </div>
        <label className="font-semibold">
          Product Image
          <IKContext
            publicKey={process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY?.replace(
              /"/g,
              ""
            )}
            urlEndpoint={process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT?.replace(
              /"/g,
              ""
            )}
            authenticator={async () => {
              const res = await fetch("/api/imagekit-auth");
              return res.json();
            }}
          >
            <IKUpload
              fileName={product.slug ? `${product.slug}.jpg` : "product.jpg"}
              onSuccess={(res: { url: string }) =>
                setProduct({ ...product, image: res.url })
              }
              onError={() => setError("Image upload failed")}
              className="border rounded px-3 py-2 w-full"
            />
            {product.image && (
              <Image
                src={product.image}
                alt="Preview"
                width={96}
                height={96}
                className="mt-2 h-24 w-auto rounded shadow"
              />
            )}
          </IKContext>
        </label>
        <label className="font-semibold">
          Category
          {categoriesLoading ? (
            <div className="text-gray-500 text-sm mt-1">
              Loading categories...
            </div>
          ) : categoriesError ? (
            <div className="text-red-600 text-sm mt-1">{categoriesError}</div>
          ) : null}
          <select
            className="w-full border rounded px-3 py-2 mt-1"
            value={product.categoryId || ""}
            onChange={(e) =>
              setProduct({ ...product, categoryId: Number(e.target.value) })
            }
            required
            disabled={categoriesLoading || !!categoriesError}
          >
            <option value="" disabled>
              Select category
            </option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.name}
              </option>
            ))}
          </select>
          <a
            href="/admin/categories"
            className="text-blue-600 text-xs underline mt-1 inline-block hover:text-blue-800"
            target="_blank"
            rel="noopener noreferrer"
          >
            Manage categories
          </a>
        </label>
        <button
          type="submit"
          className="bg-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow hover:bg-blue-700 transition mt-4"
          disabled={saving}
        >
          {saving ? "Saving..." : "Save Changes"}
        </button>
      </form>
    </div>
  );
}
