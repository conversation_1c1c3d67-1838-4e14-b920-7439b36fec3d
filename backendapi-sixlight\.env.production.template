# Production Environment Variables Template
# Copy these to your production hosting platform's environment variables section

# CRITICAL: Update these for production
NODE_ENV="production"
FRONTEND_URL="https://your-frontend-domain.com"  # CHANGE THIS!

# Database URLs (update with your production database)
DATABASE_URL="your-production-database-url"
DIRECT_URL="your-production-direct-database-url"

# Security
JWT_SECRET="tVfn+VlANCzmunn+0AvG3WKJ+frOoPBUfLD8XsLS+bA="

# Server Configuration
PORT=3001

# Email Configuration (same as development)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="dxsl tqan xfzc vynn"

# Email Security Settings
EMAIL_VERIFICATION_EXPIRES_HOURS=24
PASSWORD_RESET_EXPIRES_HOURS=1

# IMPORTANT NOTES:
# 1. FRONTEND_URL must be your actual production frontend URL
# 2. DATABASE_URL must be your production database
# 3. Never commit this file to git - it's just a template
# 4. Set these variables in your hosting platform's dashboard
