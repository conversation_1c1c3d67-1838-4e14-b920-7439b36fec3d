(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[490],{255:(e,t,s)=>{"use strict";function r(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return r}}),s(5155),s(7650),s(5744),s(589)},1938:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(5155),a=s(6766),n=s(2115),l=s(5695),i=s(6874),o=s.n(i),d=s(5028),c=s(802),u=s(4615),m=s(3389);let x=e=>{let{isOpen:t,onClose:s}=e;return t?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:s}),(0,r.jsxs)("div",{className:"relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-1",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-t-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-amber-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),"Save Your Design"]}),(0,r.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-500 focus:outline-none",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start mb-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-amber-100 rounded-full p-2",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-amber-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-gray-700 dark:text-gray-300 mb-2",children:"Please create and save your design before adding to cart."}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:["Click the"," ",(0,r.jsx)("span",{className:"font-bold text-indigo-600 dark:text-indigo-400",children:"'Save Design'"})," ","button in the customizer to save your changes."]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Follow these steps:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm text-gray-700 dark:text-gray-300",children:[(0,r.jsx)("li",{children:"Complete your design using the customizer tools"}),(0,r.jsxs)("li",{children:["Click the"," ",(0,r.jsx)("span",{className:"font-bold text-indigo-600 dark:text-indigo-400",children:"Save Design"})," ","button at the bottom of the customizer"]}),(0,r.jsx)("li",{children:'Then click "Add to Cart" to proceed'})]})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end",children:(0,r.jsxs)("button",{onClick:s,className:"px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg text-sm hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 flex items-center",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),"Got it"]})})]})]}):null};var h=s(3843);(0,d.default)(()=>Promise.all([s.e(831),s.e(367),s.e(413),s.e(683),s.e(825),s.e(321),s.e(156)]).then(s.bind(s,9156)),{loadableGenerated:{webpack:()=>[9156]},ssr:!1}),(0,d.default)(()=>Promise.all([s.e(831),s.e(367),s.e(413),s.e(683),s.e(825),s.e(632)]).then(s.bind(s,6632)),{loadableGenerated:{webpack:()=>[6632]},ssr:!1});let g=(0,d.default)(()=>s.e(283).then(s.bind(s,2283)),{loadableGenerated:{webpack:()=>[2283]},ssr:!1,loading:()=>(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading Customizer..."})]})})})});function f(e){var t,s,i;let{params:d}=e,f=n.use(d).slug,p=(0,l.useRouter)(),[j,b]=(0,n.useState)(null),[v,y]=(0,n.useState)(!0),[N,w]=(0,n.useState)(!1),[k,C]=(0,n.useState)(!1),[L,D]=(0,n.useState)(!1),[S,P]=(0,n.useState)(!1),[z,O]=(0,n.useState)(null),[M,_]=(0,n.useState)(0),[A,E]=(0,n.useState)(!1),[B,W]=(0,n.useState)(!1),F=(0,n.useRef)(null),R=(0,n.useRef)(null);(0,n.useEffect)(()=>{y(!0),w(!1),fetch((0,h.e9)("".concat(h.i3.ENDPOINTS.PRODUCTS,"/").concat(f))).then(async e=>{if(!e.ok)throw Error("Product not found");let t=await e.json();b(t),P(t.customizable||!1),y(!1)}).catch(()=>{w(!0),y(!1)})},[f]),(0,n.useEffect)(()=>{F.current&&c.Ay.fromTo(F.current,{opacity:0,y:40},{opacity:1,y:0,duration:.7,ease:"power3.out"})},[j]),(0,n.useEffect)(()=>{R.current&&c.Ay.fromTo(R.current,{scale:.92,opacity:0},{scale:1,opacity:1,duration:.5,ease:"power2.out"})},[S,null==j?void 0:j.slug]);let T=()=>{_(JSON.parse(localStorage.getItem("cart")||"[]").length)};(0,n.useEffect)(()=>{T()},[]);let I=()=>{E(!0)};function G(){return!!localStorage.getItem("token")}async function V(e){if(e.preventDefault(),!j)return;if(!G())return void p.push("/login?redirect=/product/".concat(f));let t=document.querySelector("[data-has-unsaved-changes]"),s=(null==t?void 0:t.getAttribute("data-has-unsaved-changes"))==="true";if(S&&(!z||!z.canvasData&&!z.preview||s))return void W(!0);D(!0);try{let e,t;await new Promise(e=>setTimeout(e,500));let s=JSON.parse(localStorage.getItem("cart")||"[]");if(S&&(null==z?void 0:z.canvasData))try{let s=JSON.parse(z.canvasData);e=s.color||s.textColor,t=s.text}catch(e){}s.push({id:Date.now(),productId:j.id,name:j.name,color:e,text:t,customized:S,customizationData:z,price:j.price,quantity:1}),localStorage.setItem("cart",JSON.stringify(s)),T(),C(!0),setTimeout(()=>{C(!1),p.push("/")},1200)}catch(e){console.error("Error adding to cart:",e),alert("Failed to add item to cart. Please try again.")}finally{D(!1)}}return v?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(u.A,{cartCount:M,onCartClick:I}),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading product..."})]})})]}):N||!j?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(u.A,{cartCount:M,onCartClick:I}),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Product Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the product you're looking for."}),(0,r.jsxs)(o(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Shop"]})]})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(u.A,{cartCount:M,onCartClick:I}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 pt-8",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-8",children:[(0,r.jsx)(o(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Home"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,r.jsx)(o(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Products"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:j.name})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 pb-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{ref:R,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:S&&(null==z?void 0:z.preview)?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)("div",{className:"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden",children:(0,r.jsx)("img",{src:z.preview,alt:"Customization preview",className:"w-full h-full object-contain hover:scale-105 transition-transform duration-500"})}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-4 text-center",children:"� Your Custom Design Preview"})]}):(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)("div",{className:"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden",children:(0,r.jsx)(a.default,{src:j.image&&j.image.startsWith("http")?j.image:j.image?"/"+j.image.replace(/^\/+/,""):"/bottle-dummy.jpg",alt:j.name,width:400,height:400,className:"w-full h-full object-contain hover:scale-105 transition-transform duration-500",unoptimized:!!(j.image&&j.image.startsWith("http")),onError:e=>{e.target.src="/bottle-dummy.jpg"}})}),!j.image&&(0,r.jsx)("div",{className:"text-sm text-amber-600 mt-4 bg-amber-50 px-4 py-2 rounded-lg",children:"⚠️ No product image found. Showing fallback."})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Product Features"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Premium Quality"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Fast Delivery"})]}),j.customizable&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Customizable"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Guaranteed"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{ref:F,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("span",{className:"px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full",children:(null==(t=j.category)?void 0:t.name)||"Product"}),j.customizable&&(0,r.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-700 text-sm font-semibold rounded-full",children:"✨ Customizable"})]}),(0,r.jsx)("h1",{className:"text-4xl font-black text-gray-900 mb-4 leading-tight",children:j.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-6",children:j.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-3xl font-black text-green-600",children:["K",null!=(i=j.price)?i:0]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"\uD83D\uDCB0 Best price guaranteed"})]})]}),j.customizable&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border border-purple-200",children:[(0,r.jsx)("input",{type:"checkbox",id:"customize-toggle",checked:S,onChange:()=>P(e=>!e),className:"w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"}),(0,r.jsx)("label",{htmlFor:"customize-toggle",className:"text-lg font-semibold text-gray-900 cursor-pointer select-none",children:"\uD83C\uDFA8 Customize this product"})]}),S&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-blue-700",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"})}),(0,r.jsx)("span",{className:"font-medium",children:"Advanced Designer Enabled"}),(0,r.jsx)("span",{className:"text-blue-600",children:"- Create professional designs with our smart customization tools"})]})})]}),(0,r.jsxs)("form",{onSubmit:V,className:"space-y-6",children:[S&&(0,r.jsx)(g,{productImage:j.image&&j.image.startsWith("http")?j.image:j.image?"/"+j.image.replace(/^\/+/,""):"/bottle-dummy.jpg",onCustomizationChange:O,initialCustomization:z}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{type:"submit",className:"w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ".concat(L||k?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white hover:shadow-xl transform hover:-translate-y-1"),disabled:L||k,children:L?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Adding to Cart..."})]}):k?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,r.jsx)("span",{children:"Added to Cart!"})]}):(0,r.jsxs)("span",{children:["\uD83D\uDED2"," ",S?"Add Custom Product to Cart":"Add to Cart"]})}),(0,r.jsx)("button",{type:"button",onClick:function(){if(!G())return void p.push("/login?redirect=/product/".concat(f));alert("Order Now functionality coming soon!")},className:"w-full py-4 px-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"⚡ Order Now - Fast Delivery"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDCCB Product Information"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Category:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:(null==(s=j.category)?void 0:s.name)||"General"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Customizable:"}),(0,r.jsx)("span",{className:"font-semibold ".concat(j.customizable?"text-green-600":"text-gray-500"),children:j.customizable?"✅ Yes":"❌ No"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Delivery:"}),(0,r.jsx)("span",{className:"font-semibold text-blue-600",children:"\uD83D\uDE9A 24-48 hours"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Warranty:"}),(0,r.jsx)("span",{className:"font-semibold text-green-600",children:"✅ 30 days"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-6 border border-green-200",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDEE1️ Why Choose Us?"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Premium Quality"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"⚡"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Fast Delivery"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCAF"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"100% Guaranteed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFA8"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Custom Design"})]})]})]})]})]})}),(0,r.jsx)(m.A,{isOpen:A,onClose:()=>{E(!1),T()}}),(0,r.jsx)(x,{isOpen:B,onClose:()=>W(!1)})]})}},2146:(e,t,s)=>{"use strict";function r(e){let{reason:t,children:s}=e;return s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),s(5262)},4054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{bindSnapshot:function(){return l},createAsyncLocalStorage:function(){return n},createSnapshot:function(){return i}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function n(){return a?new a:new r}function l(e){return a?a.bind(e):r.bind(e)}function i(){return a?a.snapshot():function(e,...t){return e(...t)}}},5028:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(6645),a=s.n(r)},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},5744:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=s(7828)},6645:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=s(8229)._(s(7357));function a(e,t){var s;let a={};"function"==typeof e&&(a.loader=e);let n={...a,...t};return(0,r.default)({...n,modules:null==(s=n.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7266:(e,t,s)=>{Promise.resolve().then(s.bind(s,1938))},7357:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=s(5155),a=s(2115),n=s(2146);function l(e){return{default:e&&"default"in e?e.default:e}}s(255);let i={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},o=function(e){let t={...i,...e},s=(0,a.lazy)(()=>t.loader().then(l)),o=t.loading;function d(e){let l=o?(0,r.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,i=!t.ssr||!!t.loading,d=i?a.Suspense:a.Fragment,c=t.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(s,{...e})]}):(0,r.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(d,{...i?{fallback:l}:{},children:c})}return d.displayName="LoadableComponent",d}},7828:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,s(4054).createAsyncLocalStorage)()}},e=>{var t=t=>e(e.s=t);e.O(0,[592,766,874,795,211,441,684,358],()=>t(7266)),_N_E=e.O()}]);