{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n      VERIFY_EMAIL: \"/auth/verify-email\",\n      FORGOT_PASSWORD: \"/auth/forgot-password\",\n      RESET_PASSWORD: \"/auth/reset-password\",\n      RESEND_VERIFICATION: \"/auth/resend-verification\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      UPDATE_PROFILE: \"/user/profile\",\n      UPLOAD_PROFILE_IMAGE: \"/user/upload-profile-image\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACK;AAAtB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;QACvB;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,uCAAgC,aAAa,OAAO,CAAC;IACvD,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/api/auth.ts"], "sourcesContent": ["import { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport async function login(email: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function register(email: string, password: string, name?: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password, name }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function verifyEmail(token: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.VERIFY_EMAIL), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function forgotPassword(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.FORGOT_PASSWORD),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n\r\nexport async function resetPassword(token: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function resendVerification(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESEND_VERIFICATION),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,eAAe,MAAM,KAAa,EAAE,QAAgB;IACzD,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG;QAClE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAa;IAC3E,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;YAAU;QAAK;IAC/C;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,YAAY,KAAa;IAC7C,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG;QACzE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,eAAe,KAAa;IAChD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GACnD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,cAAc,KAAa,EAAE,QAAgB;IACjE,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,GAAG;QAC3E,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,mBAAmB,KAAa;IACpD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,GACvD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/register/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { register } from \"@/api/auth\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { z } from \"zod\";\r\nimport {\r\n  Eye,\r\n  EyeOff,\r\n  AlertCircle,\r\n  CheckCircle,\r\n  User,\r\n  Mail,\r\n  Lock,\r\n  Shield,\r\n} from \"lucide-react\";\r\n\r\n// Enhanced password validation with strength requirements\r\nconst registerSchema = z\r\n  .object({\r\n    name: z\r\n      .string()\r\n      .min(2, \"Name must be at least 2 characters\")\r\n      .max(50, \"Name must be less than 50 characters\")\r\n      .regex(/^[a-zA-Z\\s]+$/, \"Name can only contain letters and spaces\"),\r\n    email: z\r\n      .string()\r\n      .email(\"Please enter a valid email address\")\r\n      .max(100, \"Email must be less than 100 characters\"),\r\n    password: z\r\n      .string()\r\n      .min(8, \"Password must be at least 8 characters\")\r\n      .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\r\n      .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\r\n      .regex(/[0-9]/, \"Password must contain at least one number\")\r\n      .regex(\r\n        /[^A-Za-z0-9]/,\r\n        \"Password must contain at least one special character\"\r\n      ),\r\n    confirmPassword: z.string(),\r\n    terms: z.boolean().refine((val) => val === true, {\r\n      message: \"You must accept the terms and conditions\",\r\n    }),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\ntype RegisterFormData = z.infer<typeof registerSchema>;\r\n\r\n// Password strength checker\r\nconst getPasswordStrength = (password: string) => {\r\n  let score = 0;\r\n  const checks = {\r\n    length: password.length >= 8,\r\n    uppercase: /[A-Z]/.test(password),\r\n    lowercase: /[a-z]/.test(password),\r\n    number: /[0-9]/.test(password),\r\n    special: /[^A-Za-z0-9]/.test(password),\r\n  };\r\n\r\n  Object.values(checks).forEach((check) => check && score++);\r\n\r\n  return {\r\n    score,\r\n    checks,\r\n    strength: score < 2 ? \"weak\" : score < 4 ? \"medium\" : \"strong\",\r\n  };\r\n};\r\n\r\nexport default function RegisterPage() {\r\n  const [formData, setFormData] = useState<RegisterFormData>({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    confirmPassword: \"\",\r\n    terms: false,\r\n  });\r\n  const [errors, setErrors] = useState<\r\n    Partial<Record<keyof RegisterFormData, string>>\r\n  >({});\r\n  const [error, setError] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [success, setSuccess] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState(\"\");\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n\r\n  const handleInputChange = (\r\n    field: keyof RegisterFormData,\r\n    value: string | boolean\r\n  ) => {\r\n    setFormData((prev) => ({ ...prev, [field]: value }));\r\n    // Clear field-specific error when user starts typing\r\n    if (errors[field]) {\r\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\r\n    }\r\n  };\r\n\r\n  async function handleSubmit(e: React.FormEvent) {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(\"\");\r\n    setSuccess(false);\r\n    setErrors({});\r\n\r\n    // Validate form data with Zod\r\n    const validation = registerSchema.safeParse(formData);\r\n    if (!validation.success) {\r\n      setLoading(false);\r\n      const fieldErrors: Partial<Record<keyof RegisterFormData, string>> = {};\r\n      validation.error.errors.forEach((err) => {\r\n        if (err.path[0]) {\r\n          fieldErrors[err.path[0] as keyof RegisterFormData] = err.message;\r\n        }\r\n      });\r\n      setErrors(fieldErrors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const result = await register(\r\n        formData.email,\r\n        formData.password,\r\n        formData.name\r\n      );\r\n      setLoading(false);\r\n\r\n      if (result.message) {\r\n        setSuccess(true);\r\n        setSuccessMessage(result.message);\r\n        // Don't auto-login anymore - user needs to verify email first\r\n      } else if (result.access_token) {\r\n        // Fallback for old registration flow\r\n        setSuccess(true);\r\n        setSuccessMessage(\r\n          \"Registration successful! Please check your email to verify your account.\"\r\n        );\r\n      } else {\r\n        setError(result.error || \"Registration failed\");\r\n      }\r\n    } catch {\r\n      setLoading(false);\r\n      setError(\"Network error. Please try again.\");\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4\">\r\n      <div className=\"w-full max-w-lg bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100\">\r\n        <div className=\"flex flex-col items-center\">\r\n          <Image\r\n            src=\"/6 Light Logo.png\"\r\n            alt=\"6 Light Logo\"\r\n            width={64}\r\n            height={64}\r\n            className=\"mb-2\"\r\n          />\r\n        </div>\r\n        <h2 className=\"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\r\n          Create Account\r\n        </h2>\r\n        <form onSubmit={handleSubmit} className=\"flex flex-col gap-4\">\r\n          {/* Name Input */}\r\n          <div className=\"space-y-1\">\r\n            <div className=\"relative\">\r\n              <User\r\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\r\n                size={20}\r\n              />\r\n              <input\r\n                value={formData.name}\r\n                onChange={(e) => handleInputChange(\"name\", e.target.value)}\r\n                placeholder=\"Full Name\"\r\n                type=\"text\"\r\n                className={`w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ${\r\n                  errors.name\r\n                    ? \"border-red-500 focus:ring-red-300\"\r\n                    : \"border-gray-300 focus:ring-blue-300\"\r\n                }`}\r\n                autoComplete=\"name\"\r\n                aria-invalid={!!errors.name}\r\n                aria-describedby={errors.name ? \"name-error\" : undefined}\r\n              />\r\n            </div>\r\n            {errors.name && (\r\n              <div\r\n                id=\"name-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.name}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Email Input */}\r\n          <div className=\"space-y-1\">\r\n            <div className=\"relative\">\r\n              <Mail\r\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\r\n                size={20}\r\n              />\r\n              <input\r\n                value={formData.email}\r\n                onChange={(e) => handleInputChange(\"email\", e.target.value)}\r\n                placeholder=\"Email Address\"\r\n                type=\"email\"\r\n                className={`w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ${\r\n                  errors.email\r\n                    ? \"border-red-500 focus:ring-red-300\"\r\n                    : \"border-gray-300 focus:ring-blue-300\"\r\n                }`}\r\n                autoComplete=\"email\"\r\n                aria-invalid={!!errors.email}\r\n                aria-describedby={errors.email ? \"email-error\" : undefined}\r\n              />\r\n            </div>\r\n            {errors.email && (\r\n              <div\r\n                id=\"email-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.email}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Password Input with Strength Indicator */}\r\n          <div className=\"space-y-1\">\r\n            <div className=\"relative\">\r\n              <Lock\r\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\r\n                size={20}\r\n              />\r\n              <input\r\n                value={formData.password}\r\n                onChange={(e) => handleInputChange(\"password\", e.target.value)}\r\n                type={showPassword ? \"text\" : \"password\"}\r\n                placeholder=\"Password\"\r\n                className={`w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ${\r\n                  errors.password\r\n                    ? \"border-red-500 focus:ring-red-300\"\r\n                    : \"border-gray-300 focus:ring-blue-300\"\r\n                }`}\r\n                autoComplete=\"new-password\"\r\n                aria-invalid={!!errors.password}\r\n                aria-describedby={\r\n                  errors.password ? \"password-error\" : undefined\r\n                }\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowPassword(!showPassword)}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\"\r\n                aria-label={showPassword ? \"Hide password\" : \"Show password\"}\r\n              >\r\n                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\r\n              </button>\r\n            </div>\r\n\r\n            {/* Password Strength Indicator */}\r\n            {formData.password && (\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex gap-1\">\r\n                  {[1, 2, 3, 4, 5].map((level) => {\r\n                    const strength = getPasswordStrength(formData.password);\r\n                    return (\r\n                      <div\r\n                        key={level}\r\n                        className={`h-1 flex-1 rounded ${\r\n                          level <= strength.score\r\n                            ? strength.strength === \"weak\"\r\n                              ? \"bg-red-500\"\r\n                              : strength.strength === \"medium\"\r\n                              ? \"bg-yellow-500\"\r\n                              : \"bg-green-500\"\r\n                            : \"bg-gray-200\"\r\n                        }`}\r\n                      />\r\n                    );\r\n                  })}\r\n                </div>\r\n                <div className=\"text-xs space-y-1\">\r\n                  {Object.entries(\r\n                    getPasswordStrength(formData.password).checks\r\n                  ).map(([key, passed]) => (\r\n                    <div\r\n                      key={key}\r\n                      className={`flex items-center gap-1 ${\r\n                        passed ? \"text-green-600\" : \"text-gray-400\"\r\n                      }`}\r\n                    >\r\n                      {passed ? (\r\n                        <CheckCircle size={12} />\r\n                      ) : (\r\n                        <AlertCircle size={12} />\r\n                      )}\r\n                      <span>\r\n                        {key === \"length\" && \"8+ characters\"}\r\n                        {key === \"uppercase\" && \"Uppercase letter\"}\r\n                        {key === \"lowercase\" && \"Lowercase letter\"}\r\n                        {key === \"number\" && \"Number\"}\r\n                        {key === \"special\" && \"Special character\"}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {errors.password && (\r\n              <div\r\n                id=\"password-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.password}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Confirm Password Input */}\r\n          <div className=\"space-y-1\">\r\n            <div className=\"relative\">\r\n              <Shield\r\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\r\n                size={20}\r\n              />\r\n              <input\r\n                value={formData.confirmPassword}\r\n                onChange={(e) =>\r\n                  handleInputChange(\"confirmPassword\", e.target.value)\r\n                }\r\n                type={showConfirmPassword ? \"text\" : \"password\"}\r\n                placeholder=\"Confirm Password\"\r\n                className={`w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ${\r\n                  errors.confirmPassword\r\n                    ? \"border-red-500 focus:ring-red-300\"\r\n                    : \"border-gray-300 focus:ring-blue-300\"\r\n                }`}\r\n                autoComplete=\"new-password\"\r\n                aria-invalid={!!errors.confirmPassword}\r\n                aria-describedby={\r\n                  errors.confirmPassword ? \"confirm-password-error\" : undefined\r\n                }\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\"\r\n                aria-label={\r\n                  showConfirmPassword ? \"Hide password\" : \"Show password\"\r\n                }\r\n              >\r\n                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}\r\n              </button>\r\n            </div>\r\n            {errors.confirmPassword && (\r\n              <div\r\n                id=\"confirm-password-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.confirmPassword}\r\n              </div>\r\n            )}\r\n          </div>\r\n          {/* Terms and Conditions */}\r\n          <div className=\"space-y-1\">\r\n            <label className=\"flex items-start gap-3 text-sm cursor-pointer\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={formData.terms}\r\n                onChange={(e) => handleInputChange(\"terms\", e.target.checked)}\r\n                className={`mt-1 accent-blue-600 ${\r\n                  errors.terms ? \"accent-red-600\" : \"\"\r\n                }`}\r\n                aria-invalid={!!errors.terms}\r\n                aria-describedby={errors.terms ? \"terms-error\" : undefined}\r\n              />\r\n              <span className=\"text-gray-700\">\r\n                I accept the{\" \"}\r\n                <a\r\n                  href=\"#\"\r\n                  className=\"underline text-blue-600 hover:text-blue-800\"\r\n                >\r\n                  terms and conditions\r\n                </a>{\" \"}\r\n                and{\" \"}\r\n                <a\r\n                  href=\"#\"\r\n                  className=\"underline text-blue-600 hover:text-blue-800\"\r\n                >\r\n                  privacy policy\r\n                </a>\r\n              </span>\r\n            </label>\r\n            {errors.terms && (\r\n              <div\r\n                id=\"terms-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.terms}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className={`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${\r\n              loading\r\n                ? \"bg-gray-400 cursor-not-allowed\"\r\n                : \"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white\"\r\n            }`}\r\n            disabled={loading}\r\n          >\r\n            {loading ? (\r\n              <div className=\"flex items-center justify-center gap-2\">\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                <span>Creating Account...</span>\r\n              </div>\r\n            ) : (\r\n              \"Create Account\"\r\n            )}\r\n          </button>\r\n\r\n          {/* Global Error Display */}\r\n          {error && (\r\n            <div className=\"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200\">\r\n              <AlertCircle size={16} />\r\n              {error}\r\n            </div>\r\n          )}\r\n\r\n          {/* Success Message */}\r\n          {success && (\r\n            <div className=\"flex items-center gap-2 text-green-600 text-sm bg-green-50 p-3 rounded-lg border border-green-200\">\r\n              <CheckCircle size={16} />\r\n              {successMessage ||\r\n                \"Registration successful! Please check your email to verify your account.\"}\r\n            </div>\r\n          )}\r\n        </form>\r\n        <div className=\"text-center text-sm text-gray-600\">\r\n          Already have an account?{\" \"}\r\n          <Link\r\n            href=\"/login\"\r\n            className=\"text-red-700 font-semibold hover:underline\"\r\n          >\r\n            Login\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;;AAiBA,0DAA0D;AAC1D,MAAM,iBAAiB,oLAAA,CAAA,IAAC,CACrB,MAAM,CAAC;IACN,MAAM,oLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,sCACP,GAAG,CAAC,IAAI,wCACR,KAAK,CAAC,iBAAiB;IAC1B,OAAO,oLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,sCACN,GAAG,CAAC,KAAK;IACZ,UAAU,oLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,6CACf,KAAK,CACJ,gBACA;IAEJ,iBAAiB,oLAAA,CAAA,IAAC,CAAC,MAAM;IACzB,OAAO,oLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;QAC/C,SAAS;IACX;AACF,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIF,4BAA4B;AAC5B,MAAM,sBAAsB,CAAC;IAC3B,IAAI,QAAQ;IACZ,MAAM,SAAS;QACb,QAAQ,SAAS,MAAM,IAAI;QAC3B,WAAW,QAAQ,IAAI,CAAC;QACxB,WAAW,QAAQ,IAAI,CAAC;QACxB,QAAQ,QAAQ,IAAI,CAAC;QACrB,SAAS,eAAe,IAAI,CAAC;IAC/B;IAEA,OAAO,MAAM,CAAC,QAAQ,OAAO,CAAC,CAAC,QAAU,SAAS;IAElD,OAAO;QACL;QACA;QACA,UAAU,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;IACxD;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEjC,CAAC;IACH,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,oBAAoB,CACxB,OACA;QAEA,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,qDAAqD;QACrD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,eAAe,aAAa,CAAkB;QAC5C,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QACX,UAAU,CAAC;QAEX,8BAA8B;QAC9B,MAAM,aAAa,eAAe,SAAS,CAAC;QAC5C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,WAAW;YACX,MAAM,cAA+D,CAAC;YACtE,WAAW,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC/B,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;oBACf,WAAW,CAAC,IAAI,IAAI,CAAC,EAAE,CAA2B,GAAG,IAAI,OAAO;gBAClE;YACF;YACA,UAAU;YACV;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAC1B,SAAS,KAAK,EACd,SAAS,QAAQ,EACjB,SAAS,IAAI;YAEf,WAAW;YAEX,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW;gBACX,kBAAkB,OAAO,OAAO;YAChC,8DAA8D;YAChE,OAAO,IAAI,OAAO,YAAY,EAAE;gBAC9B,qCAAqC;gBACrC,WAAW;gBACX,kBACE;YAEJ,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;8BAGd,6LAAC;oBAAG,WAAU;8BAAiH;;;;;;8BAG/H,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CACH,WAAU;4CACV,MAAM;;;;;;sDAER,6LAAC;4CACC,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACzD,aAAY;4CACZ,MAAK;4CACL,WAAW,CAAC,oFAAoF,EAC9F,OAAO,IAAI,GACP,sCACA,uCACJ;4CACF,cAAa;4CACb,gBAAc,CAAC,CAAC,OAAO,IAAI;4CAC3B,oBAAkB,OAAO,IAAI,GAAG,eAAe;;;;;;;;;;;;gCAGlD,OAAO,IAAI,kBACV,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,IAAI;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CACH,WAAU;4CACV,MAAM;;;;;;sDAER,6LAAC;4CACC,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,aAAY;4CACZ,MAAK;4CACL,WAAW,CAAC,oFAAoF,EAC9F,OAAO,KAAK,GACR,sCACA,uCACJ;4CACF,cAAa;4CACb,gBAAc,CAAC,CAAC,OAAO,KAAK;4CAC5B,oBAAkB,OAAO,KAAK,GAAG,gBAAgB;;;;;;;;;;;;gCAGpD,OAAO,KAAK,kBACX,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,KAAK;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CACH,WAAU;4CACV,MAAM;;;;;;sDAER,6LAAC;4CACC,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,MAAM,eAAe,SAAS;4CAC9B,aAAY;4CACZ,WAAW,CAAC,qFAAqF,EAC/F,OAAO,QAAQ,GACX,sCACA,uCACJ;4CACF,cAAa;4CACb,gBAAc,CAAC,CAAC,OAAO,QAAQ;4CAC/B,oBACE,OAAO,QAAQ,GAAG,mBAAmB;;;;;;sDAGzC,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;4CACV,cAAY,eAAe,kBAAkB;sDAE5C,6BAAe,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;qEAAS,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;;gCAKrD,SAAS,QAAQ,kBAChB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC;gDACpB,MAAM,WAAW,oBAAoB,SAAS,QAAQ;gDACtD,qBACE,6LAAC;oDAEC,WAAW,CAAC,mBAAmB,EAC7B,SAAS,SAAS,KAAK,GACnB,SAAS,QAAQ,KAAK,SACpB,eACA,SAAS,QAAQ,KAAK,WACtB,kBACA,iBACF,eACJ;mDATG;;;;;4CAYX;;;;;;sDAEF,6LAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CACb,oBAAoB,SAAS,QAAQ,EAAE,MAAM,EAC7C,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,iBAClB,6LAAC;oDAEC,WAAW,CAAC,wBAAwB,EAClC,SAAS,mBAAmB,iBAC5B;;wDAED,uBACC,6LAAC,8NAAA,CAAA,cAAW;4DAAC,MAAM;;;;;iFAEnB,6LAAC,uNAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEAErB,6LAAC;;gEACE,QAAQ,YAAY;gEACpB,QAAQ,eAAe;gEACvB,QAAQ,eAAe;gEACvB,QAAQ,YAAY;gEACpB,QAAQ,aAAa;;;;;;;;mDAfnB;;;;;;;;;;;;;;;;gCAuBd,OAAO,QAAQ,kBACd,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,QAAQ;;;;;;;;;;;;;sCAMtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAM;;;;;;sDAER,6LAAC;4CACC,OAAO,SAAS,eAAe;4CAC/B,UAAU,CAAC,IACT,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAErD,MAAM,sBAAsB,SAAS;4CACrC,aAAY;4CACZ,WAAW,CAAC,qFAAqF,EAC/F,OAAO,eAAe,GAClB,sCACA,uCACJ;4CACF,cAAa;4CACb,gBAAc,CAAC,CAAC,OAAO,eAAe;4CACtC,oBACE,OAAO,eAAe,GAAG,2BAA2B;;;;;;sDAGxD,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,uBAAuB,CAAC;4CACvC,WAAU;4CACV,cACE,sBAAsB,kBAAkB;sDAGzC,oCAAsB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;qEAAS,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;;gCAG5D,OAAO,eAAe,kBACrB,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,eAAe;;;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,SAAS,KAAK;4CACvB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,OAAO;4CAC5D,WAAW,CAAC,qBAAqB,EAC/B,OAAO,KAAK,GAAG,mBAAmB,IAClC;4CACF,gBAAc,CAAC,CAAC,OAAO,KAAK;4CAC5B,oBAAkB,OAAO,KAAK,GAAG,gBAAgB;;;;;;sDAEnD,6LAAC;4CAAK,WAAU;;gDAAgB;gDACjB;8DACb,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;gDAEI;gDAAI;gDACL;8DACJ,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;gCAKJ,OAAO,KAAK,kBACX,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,KAAK;;;;;;;;;;;;;sCAKnB,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAC,6EAA6E,EACvF,UACI,mCACA,uGACJ;4BACF,UAAU;sCAET,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;kDAAK;;;;;;;;;;;uCAGR;;;;;;wBAKH,uBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;gCAClB;;;;;;;wBAKJ,yBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;gCAClB,kBACC;;;;;;;;;;;;;8BAIR,6LAAC;oBAAI,WAAU;;wBAAoC;wBACxB;sCACzB,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GApYwB;KAAA", "debugId": null}}]}