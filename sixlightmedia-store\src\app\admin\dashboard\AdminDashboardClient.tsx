"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  TooltipItem,
} from "chart.js";
import Header from "@/components/Header";
import Cart from "@/components/Cart";
import ConfirmationModal from "@/components/ConfirmationModal";
import { getApiUrl, API_CONFIG } from "@/lib/config";
import { User } from "@/lib/auth";

ChartJS.register(ArcElement, Tooltip, Legend);

type UserOrOrder = {
  id?: string | number;
  name?: string;
  email?: string;
  status?: string;
  user?: { id: string | number; name?: string; email: string };
  product?: {
    id: string | number;
    name: string;
    price?: number;
    image?: string;
  };
  customization?: {
    color?: string;
    text?: string;
  };
  createdAt?: string;
};

type Product = {
  id: string | number;
  name: string;
  price?: number;
  image?: string;
  category?: { id: number; name: string } | null;
};

type AdminUser = {
  id: string | number;
  name?: string;
  email: string;
  role?: string;
  createdAt?: string;
};

interface AdminDashboardClientProps {
  user?: User;
}

export default function AdminDashboardClient({ user }: AdminDashboardClientProps) {
  const [data, setData] = useState<{
    users?: AdminUser[];
    orders?: UserOrOrder[];
    products?: Product[];
    stats?: {
      users?: number;
      orders?: number;
      products?: number;
      collectedOrders?: number;
    };
  } | null>(null);
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [tab, setTab] = useState<"products" | "users" | "orders">("products");
  const [cartCount, setCartCount] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingOrderId, setPendingOrderId] = useState<string | number | null>(null);
  const ITEMS_PER_PAGE = 5;
  const router = useRouter();

  const updateCartCount = () => {
    const cart = JSON.parse(localStorage.getItem("cart") || "[]");
    setCartCount(cart.length);
  };

  const handleCartClick = () => {
    setIsCartOpen(true);
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
    updateCartCount();
  };

  const handleConfirmCollection = (orderId: string | number) => {
    setPendingOrderId(orderId);
    setShowConfirmModal(true);
  };

  const handleConfirmModalConfirm = () => {
    if (pendingOrderId) {
      handleMarkCollected(pendingOrderId);
    }
    setShowConfirmModal(false);
    setPendingOrderId(null);
  };

  const handleConfirmModalCancel = () => {
    setShowConfirmModal(false);
    setPendingOrderId(null);
  };

  useEffect(() => {
    updateCartCount();
  }, []);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      setError("Not authenticated");
      return;
    }
    fetch(getApiUrl(API_CONFIG.ENDPOINTS.ADMIN.DASHBOARD), {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((dashboardData) => {
        setData(dashboardData);
      })
      .catch(() => setError("Failed to load dashboard"));
  }, []);

  function handleEditProduct(product: Product) {
    router.push(`/admin/products/${product.id}`);
  }

  async function handleDeleteProduct(productId: string | number) {
    if (!confirm("Are you sure you want to delete this product?")) return;
    const token = localStorage.getItem("token");
    try {
      const res = await fetch(
        getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS}/${productId}`),
        {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      if (!res.ok) throw new Error("Failed to delete product");
      setData((prev: typeof data) =>
        prev && prev.products
          ? {
              ...prev,
              products: prev.products.filter((p: Product) => p.id !== productId),
            }
          : prev
      );
    } catch {
      alert("Error deleting product");
    }
  }

  async function handleMarkCollected(orderId: string | number | undefined) {
    if (!orderId) return;
    const token = localStorage.getItem("token");
    try {
      const res = await fetch(
        getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.ORDER_COLLECTED}/${orderId}/collected`),
        {
          method: "PUT",
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      if (!res.ok) throw new Error("Failed to mark as collected");
      setData((prev) => {
        if (!prev || !prev.orders) return prev;
        return {
          ...prev,
          orders: prev.orders.map((o) =>
            o.id === orderId ? { ...o, status: "COLLECTED" } : o
          ),
          stats: {
            ...prev.stats,
            collectedOrders: (prev.stats?.collectedOrders ?? 0) + 1,
          },
        };
      });
    } catch {
      alert("Error updating order status");
    }
  }

  if (error) return <div className="text-red-600 text-center mt-16">{error}</div>;
  if (!data) return <div className="text-center mt-16">Loading...</div>;

  // Always show products in the dashboard list for management
  const items: Product[] = data.products || [];
  const totalPages = Math.max(1, Math.ceil(items.length / ITEMS_PER_PAGE));
  const paginatedItems = items.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  // Chart data for order status
  const totalOrders = data.stats?.orders ?? data.orders?.length ?? 0;
  const collectedOrders =
    data.stats?.collectedOrders ??
    data.orders?.filter((o) => o.status === "COLLECTED").length ??
    0;
  const pendingOrders = totalOrders - collectedOrders;
  const doughnutData = {
    labels: ["Collected", "Pending"],
    datasets: [
      {
        data: [collectedOrders, pendingOrders],
        backgroundColor: ["#2563eb", "#fbbf24"],
        borderColor: ["#1e40af", "#f59e42"],
        borderWidth: 2,
      },
    ],
  };
  const doughnutOptions = {
    cutout: "70%",
    plugins: {
      legend: {
        display: true,
        position: "bottom" as const,
        labels: {
          color: "#1a237e",
          font: { size: 14, weight: "bold" as const },
        },
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem: TooltipItem<"doughnut">) {
            const label = tooltipItem.label || "";
            const value = tooltipItem.raw || 0;
            return `${label}: ${value}`;
          },
        },
      },
    },
    responsive: true,
    maintainAspectRatio: false,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header cartCount={cartCount} onCartClick={handleCartClick} />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
              <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                Admin
              </span>
              <span className="text-white"> Dashboard</span>
            </h1>
            <p className="text-xl text-gray-200 mb-4 max-w-2xl mx-auto">
              Welcome back, {user?.name || user?.email}! Manage your Six Light Media store with powerful tools.
            </p>
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-white text-sm">🔒 Secure Admin Access</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium">✅ Server-side authentication active</span>
          </div>
        </div>

        {/* Rest of the dashboard content will be added in the next part */}
        <div className="bg-white rounded-3xl shadow-xl p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">🚧 Dashboard Under Construction</h2>
          <p className="text-gray-600 mb-6">
            The admin dashboard is being migrated to use secure server-side authentication.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/admin/products"
              className="p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors"
            >
              <div className="text-2xl mb-2">📦</div>
              <div className="font-semibold text-blue-900">Manage Products</div>
            </Link>
            <Link
              href="/admin/orders"
              className="p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-colors"
            >
              <div className="text-2xl mb-2">📋</div>
              <div className="font-semibold text-green-900">View Orders</div>
            </Link>
            <Link
              href="/admin/categories"
              className="p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors"
            >
              <div className="text-2xl mb-2">🏷️</div>
              <div className="font-semibold text-purple-900">Categories</div>
            </Link>
          </div>
        </div>
      </div>

      {/* Cart Modal */}
      <Cart isOpen={isCartOpen} onClose={handleCartClose} />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        title="Confirm Order Collection"
        message="Has the customer paid and collected this order? This action will mark the order as completed."
        confirmText="Yes, Mark as Collected"
        cancelText="Cancel"
        onConfirm={handleConfirmModalConfirm}
        onCancel={handleConfirmModalCancel}
        type="info"
      />
    </div>
  );
}
