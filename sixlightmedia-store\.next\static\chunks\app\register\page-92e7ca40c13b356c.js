(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{646:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1264:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1319:(e,s,a)=>{Promise.resolve().then(a.bind(a,6774))},2657:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3843:(e,s,a)=>{"use strict";a.d(s,{e9:()=>n,i3:()=>t});var r=a(9509);let t={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh",VERIFY_EMAIL:"/auth/verify-email",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",RESEND_VERIFICATION:"/auth/resend-verification"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",UPDATE_PROFILE:"/user/profile",UPLOAD_PROFILE_IMAGE:"/user/upload-profile-image",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};r.env.IMAGEKIT_PRIVATE_KEY,r.env.NEXT_PUBLIC_GA_ID,r.env.NEXT_PUBLIC_GA_ID;let n=e=>{let s=t.BASE_URL.replace(/\/$/,""),a=e.startsWith("/")?e:"/".concat(e);return"".concat(s).concat(a)}},6774:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var r=a(5155),t=a(2115),n=a(9958),i=a(6874),o=a.n(i),l=a(6766),c=a(1153),d=a(9946);let m=(0,d.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var u=a(5339),p=a(1264),h=a(2919),x=a(8749),g=a(2657),f=a(646);let y=(0,d.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),b=c.z.object({name:c.z.string().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"Name can only contain letters and spaces"),email:c.z.string().email("Please enter a valid email address").max(100,"Email must be less than 100 characters"),password:c.z.string().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),confirmPassword:c.z.string(),terms:c.z.boolean().refine(e=>!0===e,{message:"You must accept the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),w=e=>{let s=0,a={length:e.length>=8,uppercase:/[A-Z]/.test(e),lowercase:/[a-z]/.test(e),number:/[0-9]/.test(e),special:/[^A-Za-z0-9]/.test(e)};return Object.values(a).forEach(e=>e&&s++),{score:s,checks:a,strength:s<2?"weak":s<4?"medium":"strong"}};function N(){let[e,s]=(0,t.useState)({name:"",email:"",password:"",confirmPassword:"",terms:!1}),[a,i]=(0,t.useState)({}),[c,d]=(0,t.useState)(""),[N,j]=(0,t.useState)(!1),[v,A]=(0,t.useState)(!1),[E,S]=(0,t.useState)(""),[P,O]=(0,t.useState)(!1),[T,R]=(0,t.useState)(!1),C=(e,r)=>{s(s=>({...s,[e]:r})),a[e]&&i(s=>({...s,[e]:void 0}))};async function k(s){s.preventDefault(),j(!0),d(""),A(!1),i({});let a=b.safeParse(e);if(!a.success){j(!1);let e={};a.error.errors.forEach(s=>{s.path[0]&&(e[s.path[0]]=s.message)}),i(e);return}try{let s=await (0,n.kz)(e.email,e.password,e.name);j(!1),s.message?(A(!0),S(s.message)):s.access_token?(A(!0),S("Registration successful! Please check your email to verify your account.")):d(s.error||"Registration failed")}catch(e){j(!1),d("Network error. Please try again.")}}return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-lg bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100",children:[(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,r.jsx)("h2",{className:"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Create Account"}),(0,r.jsxs)("form",{onSubmit:k,className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,r.jsx)("input",{id:"name",name:"name",value:e.name,onChange:e=>C("name",e.target.value),placeholder:"Full Name",type:"text",autoComplete:"name",className:"w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ".concat(a.name?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),autoComplete:"name","aria-invalid":!!a.name,"aria-describedby":a.name?"name-error":void 0})]}),a.name&&(0,r.jsxs)("div",{id:"name-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,r.jsx)(u.A,{size:16}),a.name]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,r.jsx)("input",{id:"email",name:"email",value:e.email,onChange:e=>C("email",e.target.value),placeholder:"Email Address",type:"email",autoComplete:"email",className:"w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ".concat(a.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),autoComplete:"email","aria-invalid":!!a.email,"aria-describedby":a.email?"email-error":void 0})]}),a.email&&(0,r.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,r.jsx)(u.A,{size:16}),a.email]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,r.jsx)("input",{id:"password",name:"password",value:e.password,onChange:e=>C("password",e.target.value),type:P?"text":"password",placeholder:"Password",autoComplete:"new-password",className:"w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ".concat(a.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),autoComplete:"new-password","aria-invalid":!!a.password,"aria-describedby":a.password?"password-error":void 0}),(0,r.jsx)("button",{type:"button",onClick:()=>O(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":P?"Hide password":"Show password",children:P?(0,r.jsx)(x.A,{size:20}):(0,r.jsx)(g.A,{size:20})})]}),e.password&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(s=>{let a=w(e.password);return(0,r.jsx)("div",{className:"h-1 flex-1 rounded ".concat(s<=a.score?"weak"===a.strength?"bg-red-500":"medium"===a.strength?"bg-yellow-500":"bg-green-500":"bg-gray-200")},s)})}),(0,r.jsx)("div",{className:"text-xs space-y-1",children:Object.entries(w(e.password).checks).map(e=>{let[s,a]=e;return(0,r.jsxs)("div",{className:"flex items-center gap-1 ".concat(a?"text-green-600":"text-gray-400"),children:[a?(0,r.jsx)(f.A,{size:12}):(0,r.jsx)(u.A,{size:12}),(0,r.jsxs)("span",{children:["length"===s&&"8+ characters","uppercase"===s&&"Uppercase letter","lowercase"===s&&"Lowercase letter","number"===s&&"Number","special"===s&&"Special character"]})]},s)})})]}),a.password&&(0,r.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,r.jsx)(u.A,{size:16}),a.password]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",value:e.confirmPassword,onChange:e=>C("confirmPassword",e.target.value),type:T?"text":"password",autoComplete:"new-password",placeholder:"Confirm Password",className:"w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ".concat(a.confirmPassword?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),autoComplete:"new-password","aria-invalid":!!a.confirmPassword,"aria-describedby":a.confirmPassword?"confirm-password-error":void 0}),(0,r.jsx)("button",{type:"button",onClick:()=>R(!T),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":T?"Hide password":"Show password",children:T?(0,r.jsx)(x.A,{size:20}):(0,r.jsx)(g.A,{size:20})})]}),a.confirmPassword&&(0,r.jsxs)("div",{id:"confirm-password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,r.jsx)(u.A,{size:16}),a.confirmPassword]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("label",{className:"flex items-start gap-3 text-sm cursor-pointer",children:[(0,r.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:e.terms,onChange:e=>C("terms",e.target.checked),className:"mt-1 accent-blue-600 ".concat(a.terms?"accent-red-600":""),"aria-invalid":!!a.terms,"aria-describedby":a.terms?"terms-error":void 0}),(0,r.jsxs)("span",{className:"text-gray-700",children:["I accept the"," ",(0,r.jsx)("a",{href:"#",className:"underline text-blue-600 hover:text-blue-800",children:"terms and conditions"})," ","and"," ",(0,r.jsx)("a",{href:"#",className:"underline text-blue-600 hover:text-blue-800",children:"privacy policy"})]})]}),a.terms&&(0,r.jsxs)("div",{id:"terms-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,r.jsx)(u.A,{size:16}),a.terms]})]}),(0,r.jsx)("button",{type:"submit",className:"w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ".concat(N?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"),disabled:N,children:N?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,r.jsx)("span",{children:"Creating Account..."})]}):"Create Account"}),c&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,r.jsx)(u.A,{size:16}),c]}),v&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-green-600 text-sm bg-green-50 p-3 rounded-lg border border-green-200",children:[(0,r.jsx)(f.A,{size:16}),E||"Registration successful! Please check your email to verify your account."]})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,r.jsx)(o(),{href:"/login",className:"text-red-700 font-semibold hover:underline",children:"Login"})]})]})})}},8749:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9958:(e,s,a)=>{"use strict";a.d(s,{A$:()=>i,BD:()=>o,RS:()=>c,iD:()=>t,kz:()=>n,xw:()=>l});var r=a(3843);async function t(e,s){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})})).json()}async function n(e,s,a){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s,name:a})})).json()}async function i(e){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.VERIFY_EMAIL),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e})})).json()}async function o(e){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.FORGOT_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}async function l(e,s){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.RESET_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e,password:s})})).json()}async function c(e){return(await fetch((0,r.e9)(r.i3.ENDPOINTS.AUTH.RESEND_VERIFICATION),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}}},e=>{var s=s=>e(e.s=s);e.O(0,[766,874,619,441,684,358],()=>s(1319)),_N_E=e.O()}]);