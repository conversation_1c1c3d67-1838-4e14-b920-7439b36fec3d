"use client";

import Image from "next/image";
import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import dynamic from "next/dynamic";
import gsap from "gsap";
import Header from "@/components/Header";
import Cart from "@/components/Cart";
import { getApiUrl, API_CONFIG } from "@/lib/config";

const Bottle3D = dynamic(() => import("./Bottle3D"), { ssr: false });
const Tshirt3D = dynamic(() => import("./Tshirt3D"), { ssr: false });

type Product = {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  image: string;
  customizable: boolean;
  category: { id: number; name: string };
  modelUrl?: string;
};

export default function ProductPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  // Unwrap params for Next.js 15+ compatibility
  const resolvedParams = React.use(params);
  const slug = resolvedParams.slug;
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [color, setColor] = useState("#ffffff");
  const [text, setText] = useState("");
  const [added, setAdded] = useState(false);
  const [addingToCart, setAddingToCart] = useState(false);
  const [customize, setCustomize] = useState(false);
  const [cartCount, setCartCount] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setLoading(true);
    setError(false);

    // Fetch product
    fetch(getApiUrl(`${API_CONFIG.ENDPOINTS.PRODUCTS}/${slug}`))
      .then(async (productRes) => {
        if (!productRes.ok) throw new Error("Product not found");

        const productData = await productRes.json();
        setProduct(productData);

        // Set initial customization state based on product's customizable property
        setCustomize(productData.customizable || false);

        setLoading(false);
      })
      .catch(() => {
        setError(true);
        setLoading(false);
      });
  }, [slug]);

  useEffect(() => {
    if (cardRef.current) {
      gsap.fromTo(
        cardRef.current,
        { opacity: 0, y: 40 },
        { opacity: 1, y: 0, duration: 0.7, ease: "power3.out" }
      );
    }
  }, [product]);

  useEffect(() => {
    if (previewRef.current) {
      gsap.fromTo(
        previewRef.current,
        { scale: 0.92, opacity: 0 },
        { scale: 1, opacity: 1, duration: 0.5, ease: "power2.out" }
      );
    }
  }, [customize, product?.slug]);

  // Update cart count on mount and when cart changes
  const updateCartCount = () => {
    if (typeof window !== "undefined") {
      const cart = JSON.parse(localStorage.getItem("cart") || "[]");
      setCartCount(cart.length);
    }
  };

  useEffect(() => {
    updateCartCount();
  }, []);

  const handleCartClick = () => {
    setIsCartOpen(true);
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
    updateCartCount(); // Update count when cart closes
  };

  function isAuthenticated() {
    if (typeof window === "undefined") return false;
    return !!localStorage.getItem("token");
  }

  async function handleAddToCart(e: React.FormEvent) {
    e.preventDefault();
    if (!product) return;
    if (!isAuthenticated()) {
      router.push(`/login?redirect=/product/${slug}`);
      return;
    }

    setAddingToCart(true);

    try {
      // Simulate a small delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 500));

      const cart = JSON.parse(localStorage.getItem("cart") || "[]");
      cart.push({
        id: Date.now(),
        productId: product.id,
        name: product.name,
        color: customize ? color : undefined,
        text: customize ? text : undefined,
        customized: customize,
        price: product.price,
        quantity: 1,
      });
      localStorage.setItem("cart", JSON.stringify(cart));
      updateCartCount(); // Update cart count after adding item

      setAdded(true);
      setTimeout(() => {
        setAdded(false);
        router.push("/");
      }, 1200);
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setAddingToCart(false);
    }
  }

  function handleOrderNow() {
    if (!isAuthenticated()) {
      router.push(`/login?redirect=/product/${slug}`);
      return;
    }
    // You can implement direct order logic here (e.g., open checkout modal)
    alert("Order Now functionality coming soon!");
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <Header cartCount={cartCount} onCartClick={handleCartClick} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-xl text-gray-600 font-medium">
              Loading product...
            </p>
          </div>
        </div>
      </div>
    );
  }
  if (error || !product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <Header cartCount={cartCount} onCartClick={handleCartClick} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg
                className="w-12 h-12 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Product Not Found
            </h1>
            <p className="text-gray-600 mb-8">
              Sorry, we couldn&apos;t find the product you&apos;re looking for.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Shop
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header cartCount={cartCount} onCartClick={handleCartClick} />

      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 pt-8">
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link
            href="/"
            className="hover:text-indigo-600 transition-colors duration-200"
          >
            Home
          </Link>
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
          <Link
            href="/"
            className="hover:text-indigo-600 transition-colors duration-200"
          >
            Products
          </Link>
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
          <span className="text-gray-900 font-medium">{product.name}</span>
        </nav>
      </div>

      {/* Main Product Section */}
      <div className="max-w-7xl mx-auto px-4 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Product Images/3D Preview */}
          <div className="space-y-6">
            <div
              ref={previewRef}
              className="bg-white rounded-3xl shadow-xl p-8 border border-gray-100"
            >
              {customize && product.slug === "engraved-bottle" ? (
                <div className="w-full flex flex-col items-center">
                  <Bottle3D
                    color={color}
                    text={text}
                    onColorChange={setColor}
                    modelUrl={product.modelUrl}
                  />
                  <div className="text-sm text-gray-500 mt-4 text-center">
                    🎮 3D Preview: Drag to rotate, scroll to zoom
                  </div>
                </div>
              ) : customize && product.slug === "custom-tshirt" ? (
                <div className="w-full flex flex-col items-center">
                  <Tshirt3D
                    color={color}
                    text={text}
                    onColorChange={setColor}
                  />
                  <div className="text-sm text-gray-500 mt-4 text-center">
                    🎮 3D Preview: Drag to rotate, scroll to zoom
                  </div>
                </div>
              ) : (
                <div className="w-full flex flex-col items-center">
                  <div className="aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden">
                    <Image
                      src={
                        product.image && product.image.startsWith("http")
                          ? product.image
                          : product.image
                          ? "/" + product.image.replace(/^\/+/, "")
                          : "/bottle-dummy.jpg"
                      }
                      alt={product.name}
                      width={400}
                      height={400}
                      className="w-full h-full object-contain hover:scale-105 transition-transform duration-500"
                      unoptimized={
                        !!(product.image && product.image.startsWith("http"))
                      }
                      onError={(e) => {
                        (e.target as HTMLImageElement).src =
                          "/bottle-dummy.jpg";
                      }}
                    />
                  </div>
                  {!product.image && (
                    <div className="text-sm text-amber-600 mt-4 bg-amber-50 px-4 py-2 rounded-lg">
                      ⚠️ No product image found. Showing fallback.
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Product Features */}
            <div className="bg-white rounded-3xl shadow-lg p-6 border border-gray-100">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                Product Features
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700">Premium Quality</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700">Fast Delivery</span>
                </div>
                {product.customizable && (
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-purple-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                    <span className="text-sm text-gray-700">Customizable</span>
                  </div>
                )}
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-yellow-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700">Guaranteed</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div
              ref={cardRef}
              className="bg-white rounded-3xl shadow-xl p-8 border border-gray-100"
            >
              {/* Product Header */}
              <div className="mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full">
                    {product.category?.name || "Product"}
                  </span>
                  {product.customizable && (
                    <span className="px-3 py-1 bg-purple-100 text-purple-700 text-sm font-semibold rounded-full">
                      ✨ Customizable
                    </span>
                  )}
                </div>

                <h1 className="text-4xl font-black text-gray-900 mb-4 leading-tight">
                  {product.name}
                </h1>

                <p className="text-lg text-gray-600 leading-relaxed mb-6">
                  {product.description}
                </p>

                <div className="flex items-center gap-4 mb-6">
                  <div className="text-3xl font-black text-green-600">
                    K{product.price ?? 0}
                  </div>
                  <div className="text-sm text-gray-500">
                    💰 Best price guaranteed
                  </div>
                </div>
              </div>

              {/* Customization Toggle */}
              {product.customizable && (
                <div className="mb-8">
                  <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border border-purple-200">
                    <input
                      type="checkbox"
                      id="customize-toggle"
                      checked={customize}
                      onChange={() => setCustomize((v) => !v)}
                      className="w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <label
                      htmlFor="customize-toggle"
                      className="text-lg font-semibold text-gray-900 cursor-pointer select-none"
                    >
                      🎨 Customize this product
                    </label>
                  </div>
                </div>
              )}

              {/* Customization Form */}
              <form onSubmit={handleAddToCart} className="space-y-6">
                {customize && (
                  <div className="space-y-6 p-6 bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl border border-gray-200">
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      🎨 Customize Your Product
                    </h3>

                    {/* Color Picker */}
                    <div className="space-y-3">
                      <label className="block text-sm font-semibold text-gray-700">
                        Choose Color
                      </label>
                      <div className="flex items-center gap-4">
                        <input
                          type="color"
                          name="color"
                          value={color}
                          onChange={(e) => setColor(e.target.value)}
                          className="w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer hover:border-indigo-400 transition-colors duration-200"
                          title="Choose color"
                        />
                        <div className="flex-1">
                          <div className="text-sm font-mono text-gray-600 bg-white px-3 py-2 rounded-lg border">
                            {color.toUpperCase()}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Text Input */}
                    <div className="space-y-3">
                      <label className="block text-sm font-semibold text-gray-700">
                        Text to Engrave/Print
                        <span className="text-xs text-gray-500 font-normal ml-2">
                          ({text.length}/30 characters)
                        </span>
                      </label>
                      <input
                        type="text"
                        name="text"
                        value={text}
                        onChange={(e) => setText(e.target.value)}
                        maxLength={30}
                        placeholder="e.g. Your Name or Message"
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg"
                        required
                      />
                      {text.length > 25 && (
                        <div className="text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg">
                          ⚠️ Character limit almost reached
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-4">
                  <button
                    type="submit"
                    className={`w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ${
                      addingToCart || added
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white hover:shadow-xl transform hover:-translate-y-1"
                    }`}
                    disabled={addingToCart || added}
                  >
                    {addingToCart ? (
                      <div className="flex items-center justify-center gap-3">
                        <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Adding to Cart...</span>
                      </div>
                    ) : added ? (
                      <div className="flex items-center justify-center gap-2">
                        <svg
                          className="w-6 h-6"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span>Added to Cart!</span>
                      </div>
                    ) : (
                      <span>
                        🛒{" "}
                        {customize
                          ? "Add Custom Product to Cart"
                          : "Add to Cart"}
                      </span>
                    )}
                  </button>

                  <button
                    type="button"
                    onClick={handleOrderNow}
                    className="w-full py-4 px-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                  >
                    ⚡ Order Now - Fast Delivery
                  </button>
                </div>
              </form>
            </div>

            {/* Additional Product Info */}
            <div className="bg-white rounded-3xl shadow-lg p-6 border border-gray-100">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                📋 Product Information
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Category:</span>
                  <span className="font-semibold text-gray-900">
                    {product.category?.name || "General"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Customizable:</span>
                  <span
                    className={`font-semibold ${
                      product.customizable ? "text-green-600" : "text-gray-500"
                    }`}
                  >
                    {product.customizable ? "✅ Yes" : "❌ No"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery:</span>
                  <span className="font-semibold text-blue-600">
                    🚚 24-48 hours
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Warranty:</span>
                  <span className="font-semibold text-green-600">
                    ✅ 30 days
                  </span>
                </div>
              </div>
            </div>

            {/* Trust Badges */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-6 border border-green-200">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                🛡️ Why Choose Us?
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl mb-2">🏆</div>
                  <div className="text-sm font-semibold text-gray-700">
                    Premium Quality
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">⚡</div>
                  <div className="text-sm font-semibold text-gray-700">
                    Fast Delivery
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">💯</div>
                  <div className="text-sm font-semibold text-gray-700">
                    100% Guaranteed
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">🎨</div>
                  <div className="text-sm font-semibold text-gray-700">
                    Custom Design
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cart Modal */}
      <Cart isOpen={isCartOpen} onClose={handleCartClose} />
    </div>
  );
}
