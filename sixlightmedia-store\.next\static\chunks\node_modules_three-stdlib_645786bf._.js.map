{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "EventDispatcher.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AA6BO,MAAM,gBAA2C;IAAjD,aAAA;QAEK,8BAAA;QAAA,cAAA,IAAA,EAAA;IAAA;IAAA;;;;GAAA,GAOX,iBACO,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY,IAAA,CAAK,UAAA,GAAa,CAAA;QAEvD,MAAM,YAAY,IAAA,CAAK,UAAA;QAElB,IAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,GAAY;YAE3B,SAAA,CAAA,IAAK,CAAA,GAAI,EAAA;QAErB;QAEA,IAAK,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA,GAAM;YAEzC,SAAA,CAAA,IAAK,CAAA,CAAE,IAAA,CAAM,QAAS;QAElC;IAED;IAAA;;;;MAAA,GAOG,iBACI,IAAA,EACA,QAAA,EACO;QAEb,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAmB,OAAA;QAE5C,MAAM,YAAY,IAAA,CAAK,UAAA;QAEhB,OAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,KAAa,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA;IAErF;IAAA;;;;MAAA,GAOG,oBACI,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,IAAK,CAAA;QAEtC,IAAK,kBAAkB,KAAA,GAAY;YAE5B,MAAA,QAAQ,cAAc,OAAA,CAAS,QAAS;YAE9C,IAAK,UAAU,CAAA,GAAM;gBAEN,cAAA,MAAA,CAAQ,OAAO,CAAE;YAEhC;QAED;IAED;IAAA;;;MAAA,GAMG,cAA0D,KAAA,EAA0C;QAEtG,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,MAAM,IAAK,CAAA;QAE5C,IAAK,kBAAkB,KAAA,GAAY;YAElC,MAAM,MAAA,GAAS,IAAA;YAGT,MAAA,QAAQ,cAAc,KAAA,CAAO,CAAE;YAErC,IAAA,IAAU,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAEhD,KAAA,CAAO,CAAE,CAAA,CAAE,IAAA,CAAM,IAAA,EAAM,KAAM;YAE9B;YAEA,MAAM,MAAA,GAAS;QAEhB;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "file": "OrbitControls.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgBA,MAAM,OAAA,aAAA,GAAA,uJAA2B,MAAA;AACjC,MAAM,SAAA,aAAA,GAAA,uJAA6B,QAAA;AACnC,MAAM,aAAa,KAAK,GAAA,CAAI,KAAA,CAAM,KAAK,EAAA,GAAK,GAAA,CAAI;AAShD,MAAM,mBAAmB,CAAC,QAAgB,WAAA,CAAuB,SAAS,WAAY,QAAA,IAAY;AAElG,MAAM,wLAAsB,kBAAA,CAA0C;IA6FpE,YAAY,MAAA,EAAgD,UAAA,CAA0B;QAC9E,KAAA;QA7FR,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,uCAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QAEV,sEAAA;QAAA,cAAA,IAAA,EAAA,UAAS,uJAAI,UAAA;QAEb,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc;QAEd,8DAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,WAAU;QAGV,4DAAA;QAAA,iCAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,UAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB,KAAK,EAAA;QAGrB,UAAA;QAAA,8DAAA;QAAA,0GAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB,CAAA;QAClB,UAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB;QAGlB,UAAA;QAAA,0CAAA;QAAA,gFAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,iBAAgB;QAGhB,gGAAA;QAAA,kCAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,aAAY;QAEZ,mCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,eAAc;QAEd,kCAAA;QAAA,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,sBAAqB;QACrB,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,kCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QAGf,wDAAA;QAAA,oFAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,mBAAkB;QAClB,sCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,uFAAA;QAAA,cAAA,IAAA,EAAA,0BAAyB;QACzB,6DAAA;QAAA,cAAA,IAAA,EAAA,wBAAuB;QAEvB,2DAAA;QAAA,sBAAA;QAAA,cAAA,IAAA,EAAA,QAAO;YAAE,MAAM;YAAa,IAAI;YAAW,OAAO;YAAc,QAAQ;QAAA;QAExE,gBAAA;QAAA,cAAA,IAAA,EAAA,gBAIK;YACH,MAAM,2JAAA,CAAM,MAAA;YACZ,2JAAQ,QAAA,CAAM,KAAA;YACd,0JAAO,QAAA,CAAM,GAAA;QAAA;QAGf,gBAAA;QAAA,cAAA,IAAA,EAAA,WAGK;YAAE,wJAAK,QAAA,CAAM,MAAA;YAAQ,wJAAK,QAAA,CAAM,SAAA;QAAA;QACrC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,wCAAA;QAAA,cAAA,IAAA,EAAA,wBAA4B;QAE5B,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,gFAAA;QAAA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAGA,4BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,6BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,wBAAA;QAAA,cAAA,IAAA,EAAA;QAEA,kHAAA;QAAA,cAAA,IAAA,EAAA;QAKE,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAGb,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAA,CAAM;QACvC,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;QAMpB,IAAA,CAAA,aAAA,GAAgB,IAAc,UAAU,GAAA;QAExC,IAAA,CAAA,iBAAA,GAAoB,IAAc,UAAU,KAAA;QAE5C,IAAA,CAAA,aAAA,GAAgB,CAAC,UAAwB;YAE5C,IAAI,MAAM,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC7C,IAAI,aAAa,UAAU,GAAA;YAG3B,IAAI,aAAa,GAAG,cAAc,IAAI,KAAK,EAAA;YAC3C,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,EAAA;YAC7B,IAAI,UAAU,KAAK,GAAA,CAAI,MAAM,UAAU;YACvC,IAAI,IAAI,KAAK,EAAA,GAAK,UAAU,SAAS;gBACnC,IAAI,MAAM,YAAY;oBACpB,OAAO,IAAI,KAAK,EAAA;gBAAA,OACX;oBACL,cAAc,IAAI,KAAK,EAAA;gBACzB;YACF;YACA,eAAe,GAAA,GAAM,MAAM;YAC3B,MAAM,MAAA,CAAO;QAAA;QAGV,IAAA,CAAA,iBAAA,GAAoB,CAAC,UAAwB;YAEhD,IAAI,QAAQ,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC/C,IAAI,eAAe,UAAU,KAAA;YAG7B,IAAI,eAAe,GAAG,gBAAgB,IAAI,KAAK,EAAA;YAC/C,IAAI,QAAQ,GAAG,SAAS,IAAI,KAAK,EAAA;YACjC,IAAI,YAAY,KAAK,GAAA,CAAI,QAAQ,YAAY;YAC7C,IAAI,IAAI,KAAK,EAAA,GAAK,YAAY,WAAW;gBACvC,IAAI,QAAQ,cAAc;oBACxB,SAAS,IAAI,KAAK,EAAA;gBAAA,OACb;oBACL,gBAAgB,IAAI,KAAK,EAAA;gBAC3B;YACF;YACA,eAAe,KAAA,GAAQ,QAAQ;YAC/B,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,WAAA,GAAc,IAAc,MAAM,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,MAAM,MAAM;QAEzE,IAAA,CAAA,iBAAA,GAAoB,CAACA,gBAAkC;YAC1DA,YAAW,gBAAA,CAAiB,WAAW,SAAS;YAChD,IAAA,CAAK,oBAAA,GAAuBA;QAAA;QAG9B,IAAA,CAAK,qBAAA,GAAwB,MAAY;YAClC,IAAA,CAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YAClE,IAAA,CAAK,oBAAA,GAAuB;QAAA;QAG9B,IAAA,CAAK,SAAA,GAAY,MAAY;YACrB,MAAA,OAAA,CAAQ,IAAA,CAAK,MAAM,MAAM;YAC/B,MAAM,SAAA,CAAU,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;YACpC,MAAA,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA;QAAA;QAG7B,IAAA,CAAK,KAAA,GAAQ,MAAY;YACjB,MAAA,MAAA,CAAO,IAAA,CAAK,MAAM,OAAO;YAC/B,MAAM,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,MAAM,SAAS;YACpC,MAAA,MAAA,CAAO,IAAA,GAAO,MAAM,KAAA;YAC1B,MAAM,MAAA,CAAO,sBAAA;YAGb,MAAM,aAAA,CAAc,WAAW;YAE/B,MAAM,MAAA,CAAO;YAEb,QAAQ,MAAM,IAAA;QAAA;QAIhB,IAAA,CAAK,MAAA,GAAA,CAAU,MAAoB;YAC3B,MAAA,SAAS,uJAAI,UAAA;YACnB,MAAM,KAAK,uJAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YAG9B,MAAM,OAAO,uJAAI,aAAA,GAAa,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;YAC9D,MAAM,cAAc,KAAK,KAAA,CAAM,EAAE,MAAA,CAAO;YAElC,MAAA,eAAe,uJAAI,UAAA;YACnB,MAAA,iBAAiB,uJAAI,aAAA;YAErB,MAAA,QAAQ,IAAI,KAAK,EAAA;YAEvB,OAAO,SAAS,SAAkB;gBAC1B,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;gBAGzB,KAAA,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;gBACzB,YAAA,IAAA,CAAK,IAAI,EAAE,MAAA,CAAO;gBAE9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;gBAGtC,OAAO,eAAA,CAAgB,IAAI;gBAG3B,UAAU,cAAA,CAAe,MAAM;gBAE/B,IAAI,MAAM,UAAA,IAAc,UAAU,MAAM,IAAA,EAAM;oBAC5C,WAAW,sBAAsB;gBACnC;gBAEA,IAAI,MAAM,aAAA,EAAe;oBACb,UAAA,KAAA,IAAS,eAAe,KAAA,GAAQ,MAAM,aAAA;oBACtC,UAAA,GAAA,IAAO,eAAe,GAAA,GAAM,MAAM,aAAA;gBAAA,OACvC;oBACL,UAAU,KAAA,IAAS,eAAe,KAAA;oBAClC,UAAU,GAAA,IAAO,eAAe,GAAA;gBAClC;gBAIA,IAAI,MAAM,MAAM,eAAA;gBAChB,IAAI,MAAM,MAAM,eAAA;gBAEhB,IAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;oBAC9B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE3B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE/B,IAAI,OAAO,KAAK;wBACJ,UAAA,KAAA,GAAQ,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,CAAC;oBAAA,OACzD;wBACL,UAAU,KAAA,GACR,UAAU,KAAA,GAAA,CAAS,MAAM,GAAA,IAAO,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK;oBACtG;gBACF;gBAGU,UAAA,GAAA,GAAM,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,UAAU,GAAG,CAAC;gBAC1F,UAAU,QAAA,CAAS;gBAIf,IAAA,MAAM,aAAA,KAAkB,MAAM;oBAChC,MAAM,MAAA,CAAO,eAAA,CAAgB,WAAW,MAAM,aAAa;gBAAA,OACtD;oBACC,MAAA,MAAA,CAAO,GAAA,CAAI,SAAS;gBAC5B;gBAIA,IAAK,MAAM,YAAA,IAAgB,qBAAuB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;oBAChG,UAAA,MAAA,GAAS,cAAc,UAAU,MAAM;gBAAA,OAC5C;oBACL,UAAU,MAAA,GAAS,cAAc,UAAU,MAAA,GAAS,KAAK;gBAC3D;gBAEA,OAAO,gBAAA,CAAiB,SAAS;gBAGjC,OAAO,eAAA,CAAgB,WAAW;gBAElC,SAAS,IAAA,CAAK,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM;gBAElC,IAAA,CAAC,MAAM,MAAA,CAAO,gBAAA,EAAkB,MAAM,MAAA,CAAO,YAAA;gBAC3C,MAAA,MAAA,CAAO,MAAA,CAAO,MAAM,MAAM;gBAE5B,IAAA,MAAM,aAAA,KAAkB,MAAM;oBACjB,eAAA,KAAA,IAAS,IAAI,MAAM,aAAA;oBACnB,eAAA,GAAA,IAAO,IAAI,MAAM,aAAA;oBAEtB,UAAA,cAAA,CAAe,IAAI,MAAM,aAAa;gBAAA,OAC3C;oBACU,eAAA,GAAA,CAAI,GAAG,GAAG,CAAC;oBAEhB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;gBACvB;gBAGA,IAAI,cAAc;gBACd,IAAA,MAAM,YAAA,IAAgB,mBAAmB;oBAC3C,IAAI,YAAY;oBAChB,IAAI,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;wBAG3E,MAAA,aAAa,OAAO,MAAA;wBACd,YAAA,cAAc,aAAa,KAAK;wBAE5C,MAAM,cAAc,aAAa;wBACjC,MAAM,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,gBAAgB,WAAW;wBACjE,MAAM,MAAA,CAAO,iBAAA;oBAAkB,OAAA,IACrB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;wBAEpE,MAAM,cAAc,uJAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,YAAA,SAAA,CAAU,MAAM,MAAM;wBAElC,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;wBACC,cAAA;wBAEd,MAAM,aAAa,uJAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,WAAA,SAAA,CAAU,MAAM,MAAM;wBAEjC,MAAM,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,UAAU,EAAE,GAAA,CAAI,WAAW;wBACrD,MAAM,MAAA,CAAO,iBAAA;wBAEb,YAAY,OAAO,MAAA;oBAAO,OACrB;wBACL,QAAQ,IAAA,CAAK,yFAAyF;wBACtG,MAAM,YAAA,GAAe;oBACvB;oBAGA,IAAI,cAAc,MAAM;wBACtB,IAAI,MAAM,kBAAA,EAAoB;4BAE5B,MAAM,MAAA,CACH,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EACZ,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM,EACtC,cAAA,CAAe,SAAS,EACxB,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ;wBAAA,OACvB;4BAEL,KAAK,MAAA,CAAO,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;4BACjC,KAAA,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EAAE,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM;4BAI/D,IAAA,KAAK,GAAA,CAAI,MAAM,MAAA,CAAO,EAAA,CAAG,GAAA,CAAI,KAAK,SAAS,CAAC,IAAI,YAAY;gCACvD,OAAA,MAAA,CAAO,MAAM,MAAM;4BAAA,OACrB;gCACL,OAAO,6BAAA,CAA8B,MAAM,MAAA,CAAO,EAAA,EAAI,MAAM,MAAM;gCAC7D,KAAA,cAAA,CAAe,QAAQ,MAAM,MAAM;4BAC1C;wBACF;oBACF;gBAAA,OAAA,IACS,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAC1F,cAAc,UAAU;oBAExB,IAAI,aAAa;wBACf,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;oBACf;gBACF;gBAEQ,QAAA;gBACY,oBAAA;gBAMpB,IACE,eACA,aAAa,iBAAA,CAAkB,MAAM,MAAA,CAAO,QAAQ,IAAI,OACxD,IAAA,CAAK,IAAI,eAAe,GAAA,CAAI,MAAM,MAAA,CAAO,UAAU,CAAA,IAAK,KACxD;oBAEA,MAAM,aAAA,CAAc,WAAW;oBAElB,aAAA,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;oBACxB,eAAA,IAAA,CAAK,MAAM,MAAA,CAAO,UAAU;oBAC7B,cAAA;oBAEP,OAAA;gBACT;gBAEO,OAAA;YAAA;QACT,CAAA;QAIG,IAAA,CAAA,OAAA,GAAU,CAACA,gBAAkC;YAChD,MAAM,UAAA,GAAaA;YAIb,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YAC/B,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,WAAW;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,SAAS,YAAY;QAAA;QAGzD,IAAA,CAAK,OAAA,GAAU,MAAY;;YAEzB,IAAI,MAAM,UAAA,EAAY;gBACd,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACvC;YACM,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,iBAAiB;YACjD,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,SAAS;YAC/C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;YACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YAC7D,IAAA,MAAM,oBAAA,KAAyB,MAAM;gBACjC,MAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YACrE;QAAA;QAQF,MAAM,QAAQ,IAAA;QAER,MAAA,cAAc;YAAE,MAAM;QAAA;QACtB,MAAA,aAAa;YAAE,MAAM;QAAA;QACrB,MAAA,WAAW;YAAE,MAAM;QAAA;QAEzB,MAAM,QAAQ;YACZ,MAAM,CAAA;YACN,QAAQ;YACR,OAAO;YACP,KAAK;YACL,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,oBAAoB;QAAA;QAGtB,IAAI,QAAQ,MAAM,IAAA;QAElB,MAAM,MAAM;QAGN,MAAA,YAAY,uJAAI,YAAA;QAChB,MAAA,iBAAiB,uJAAI,YAAA;QAE3B,IAAI,QAAQ;QACN,MAAA,YAAY,uJAAI,UAAA;QAEhB,MAAA,cAAc,uJAAI,UAAA;QAClB,MAAA,YAAY,uJAAI,UAAA;QAChB,MAAA,cAAc,uJAAI,UAAA;QAElB,MAAA,WAAW,uJAAI,UAAA;QACf,MAAA,SAAS,IAAI,6JAAA;QACb,MAAA,WAAW,uJAAI,UAAA;QAEf,MAAA,aAAa,uJAAI,UAAA;QACjB,MAAA,WAAW,uJAAI,UAAA;QACf,MAAA,aAAa,uJAAI,UAAA;QAEjB,MAAA,iBAAiB,uJAAI,UAAA;QACrB,MAAA,QAAQ,uJAAI,UAAA;QAClB,IAAI,oBAAoB;QAExB,MAAM,WAA2B,CAAA,CAAA;QACjC,MAAM,mBAA+C,CAAA;QAErD,SAAS,uBAA+B;YACtC,OAAS,IAAI,KAAK,EAAA,GAAM,KAAK,KAAM,MAAM,eAAA;QAC3C;QAEA,SAAS,eAAuB;YAC9B,OAAO,KAAK,GAAA,CAAI,MAAM,MAAM,SAAS;QACvC;QAEA,SAAS,WAAW,KAAA,EAAqB;YACnC,IAAA,MAAM,YAAA,IAAgB,MAAM,sBAAA,EAAwB;gBACtD,eAAe,KAAA,IAAS;YAAA,OACnB;gBACL,eAAe,KAAA,IAAS;YAC1B;QACF;QAEA,SAAS,SAAS,KAAA,EAAqB;YACjC,IAAA,MAAM,YAAA,IAAgB,MAAM,oBAAA,EAAsB;gBACpD,eAAe,GAAA,IAAO;YAAA,OACjB;gBACL,eAAe,GAAA,IAAO;YACxB;QACF;QAEA,MAAM,UAAA,CAAW,MAAM;YACf,MAAA,IAAI,uJAAI,UAAA;YAEP,OAAA,SAASC,SAAQ,QAAA,EAAkB,YAAA,EAAuB;gBAC7D,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBACnC,EAAA,cAAA,CAAe,CAAC,QAAQ;gBAE1B,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAGF,MAAM,QAAA,CAAS,MAAM;YACb,MAAA,IAAI,uJAAI,UAAA;YAEP,OAAA,SAASC,OAAM,QAAA,EAAkB,YAAA,EAAuB;gBACzD,IAAA,MAAM,kBAAA,KAAuB,MAAM;oBACnC,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBAAA,OAChC;oBACH,EAAA,mBAAA,CAAoB,cAAc,CAAC;oBACrC,EAAE,YAAA,CAAa,MAAM,MAAA,CAAO,EAAA,EAAI,CAAC;gBACnC;gBAEA,EAAE,cAAA,CAAe,QAAQ;gBAEzB,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAIF,MAAM,MAAA,CAAO,MAAM;YACX,MAAA,SAAS,uJAAI,UAAA;YAEZ,OAAA,SAASC,KAAI,MAAA,EAAgB,MAAA,EAAgB;gBAClD,MAAM,UAAU,MAAM,UAAA;gBAEtB,IAAI,WAAW,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;oBAEtF,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;oBAC9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;oBAClC,IAAA,iBAAiB,OAAO,MAAA;oBAGV,kBAAA,KAAK,GAAA,CAAM,MAAM,MAAA,CAAO,GAAA,GAAM,IAAK,KAAK,EAAA,GAAM,GAAK;oBAGrE,QAAS,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;oBACjF,MAAO,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;gBAAA,OAAA,IACtE,WAAW,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAErG,QACG,SAAA,CAAU,MAAM,MAAA,CAAO,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA,IAAS,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,WAAA,EAClF,MAAM,MAAA,CAAO,MAAA;oBAEf,MACG,SAAA,CAAU,MAAM,MAAA,CAAO,GAAA,GAAM,MAAM,MAAA,CAAO,MAAA,IAAW,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,YAAA,EAClF,MAAM,MAAA,CAAO,MAAA;gBACf,OACK;oBAEL,QAAQ,IAAA,CAAK,8EAA8E;oBAC3F,MAAM,SAAA,GAAY;gBACpB;YAAA;QACF,CAAA;QAGF,SAAS,SAAS,QAAA,EAAkB;YAE/B,IAAA,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,IAC1D,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAC5D;gBACQ,QAAA;YAAA,OACH;gBACL,QAAQ,IAAA,CAAK,qFAAqF;gBAClG,MAAM,UAAA,GAAa;YACrB;QACF;QAEA,SAAS,SAAS,UAAA,EAAoB;YACpC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,QAAQ,UAAA,EAAoB;YACnC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,sBAAsB,KAAA,EAAyB;YACtD,IAAI,CAAC,MAAM,YAAA,IAAgB,CAAC,MAAM,UAAA,EAAY;gBAC5C;YACF;YAEoB,oBAAA;YAEd,MAAA,OAAO,MAAM,UAAA,CAAW,qBAAA,CAAsB;YAC9C,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,IAAA;YACzB,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,GAAA;YAC/B,MAAM,IAAI,KAAK,KAAA;YACf,MAAM,IAAI,KAAK,MAAA;YAET,MAAA,CAAA,GAAK,IAAI,IAAK,IAAI;YACxB,MAAM,CAAA,GAAI,CAAA,CAAE,IAAI,CAAA,IAAK,IAAI;YAEzB,eAAe,GAAA,CAAI,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC,EAAE,SAAA,CAAU,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ,EAAE,SAAA;QAC7F;QAEA,SAAS,cAAc,IAAA,EAAsB;YACpC,OAAA,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,IAAI,CAAC;QACtE;QAMA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,YAAY,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC9C;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,sBAAsB,KAAK;YAC3B,WAAW,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC7C;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC3C;QAEA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,UAAU,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC1C,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;YAC1B,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC9B,WAAA,UAAA,CAAW,UAAU,UAAU;YAEtC,IAAA,WAAW,CAAA,GAAI,GAAG;gBACpB,SAAS,cAAc;YAAA,OAAA,IACd,WAAW,CAAA,GAAI,GAAG;gBAC3B,QAAQ,cAAc;YACxB;YAEA,WAAW,IAAA,CAAK,QAAQ;YACxB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,OAAO,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YACvC,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;YACpB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,iBAAiB,KAAA,EAAmB;YAC3C,sBAAsB,KAAK;YAEvB,IAAA,MAAM,MAAA,GAAS,GAAG;gBACpB,QAAQ,cAAc;YAAA,OAAA,IACb,MAAM,MAAA,GAAS,GAAG;gBAC3B,SAAS,cAAc;YACzB;YAEA,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,cAAc,KAAA,EAAsB;YAC3C,IAAI,cAAc;YAElB,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK,MAAM,IAAA,CAAK,EAAA;oBACV,IAAA,GAAG,MAAM,WAAW;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,MAAA;oBACV,IAAA,GAAG,CAAC,MAAM,WAAW;oBACX,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,IAAA;oBACV,IAAA,MAAM,WAAA,EAAa,CAAC;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,KAAA;oBACV,IAAA,CAAC,MAAM,WAAA,EAAa,CAAC;oBACX,cAAA;oBACd;YACJ;YAEA,IAAI,aAAa;gBAEf,MAAM,cAAA,CAAe;gBACrB,MAAM,MAAA,CAAO;YACf;QACF;QAEA,SAAS,yBAAyB;YAC5B,IAAA,SAAS,MAAA,IAAU,GAAG;gBACZ,YAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC/C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAErC,YAAA,GAAA,CAAI,GAAG,CAAC;YACtB;QACF;QAEA,SAAS,sBAAsB;YACzB,IAAA,SAAS,MAAA,IAAU,GAAG;gBACf,SAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC5C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAExC,SAAA,GAAA,CAAI,GAAG,CAAC;YACnB;QACF;QAEA,SAAS,wBAAwB;YAC/B,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEjC,WAAA,GAAA,CAAI,GAAG,QAAQ;QAC5B;QAEA,SAAS,2BAA2B;YAClC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,SAAA,EAA+B;QAC3C;QAEA,SAAS,8BAA8B;YACrC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,YAAA,EAAqC;QACjD;QAEA,SAAS,sBAAsB,KAAA,EAAqB;YAC9C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,UAAU,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OACjC;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBAC9B,UAAA,GAAA,CAAI,GAAG,CAAC;YACpB;YAEA,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;QAC5B;QAEA,SAAS,mBAAmB,KAAA,EAAqB;YAC3C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,OAAO,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OAC9B;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACjC,OAAA,GAAA,CAAI,GAAG,CAAC;YACjB;YAEA,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;QACtB;QAEA,SAAS,qBAAqB,KAAA,EAAqB;YAC3C,MAAA,WAAW,yBAAyB,KAAK;YACzC,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAC5B,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAClC,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEnC,SAAA,GAAA,CAAI,GAAG,QAAQ;YACb,WAAA,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,SAAS,CAAA,GAAI,WAAW,CAAA,EAAG,MAAM,SAAS,CAAC;YACtE,SAAS,WAAW,CAAC;YACrB,WAAW,IAAA,CAAK,QAAQ;QAC1B;QAEA,SAAS,wBAAwB,KAAA,EAAqB;YACpD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,SAAA,EAAW,mBAAmB,KAAK;QAC/C;QAEA,SAAS,2BAA2B,KAAA,EAAqB;YACvD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,YAAA,EAAc,sBAAsB,KAAK;QACrD;QAMA,SAAS,cAAc,KAAA,EAAqB;;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,SAAS,MAAA,KAAW,GAAG;gBACzB,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,eAAe;gBAChE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,aAAa;YAChE;YAEA,WAAW,KAAK;YAEZ,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,aAAa,KAAK;YAAA,OACb;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,cAAc,KAAA,EAAqB;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,YAAY,KAAK;YAAA,OACZ;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;;YACxC,cAAc,KAAK;YAEf,IAAA,SAAS,MAAA,KAAW,GAAG;gBACnB,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,qBAAA,CAAsB,MAAM,SAAA;gBAE9C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;gBACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YACnE;YAGA,MAAM,aAAA,CAAc,QAAQ;YAE5B,QAAQ,MAAM,IAAA;QAChB;QAEA,SAAS,YAAY,KAAA,EAAmB;YAClC,IAAA;YAEJ,OAAQ,MAAM,MAAA,EAAQ;gBACpB,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,IAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,MAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,KAAA;oBACjC;gBAEF;oBACgB,cAAA,CAAA;YAClB;YAEA,OAAQ,aAAa;gBACnB,KAAK,2JAAA,CAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B,QAAQ,MAAM,KAAA;oBACd;gBAEF,wJAAK,QAAA,CAAM,MAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAAA,OACT;wBACL,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAChB;oBACA;gBAEF,wJAAK,QAAA,CAAM,GAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAAA,OACT;wBACL,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAChB;oBACA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAmB;YACtC,IAAI,MAAM,OAAA,KAAY,OAAO;YAE7B,OAAQ,OAAO;gBACb,KAAK,MAAM,MAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B;gBAEF,KAAK,MAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B;gBAEF,KAAK,MAAM,GAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB;YACJ;QACF;QAEA,SAAS,aAAa,KAAA,EAAmB;YACnC,IAAA,MAAM,OAAA,KAAY,SAAS,MAAM,UAAA,KAAe,SAAU,UAAU,MAAM,IAAA,IAAQ,UAAU,MAAM,MAAA,EAAS;gBAC7G;YACF;YAEA,MAAM,cAAA,CAAe;YAGrB,MAAM,aAAA,CAAc,UAAU;YAE9B,iBAAiB,KAAK;YAGtB,MAAM,aAAA,CAAc,QAAQ;QAC9B;QAEA,SAAS,UAAU,KAAA,EAAsB;YACvC,IAAI,MAAM,OAAA,KAAY,SAAS,MAAM,SAAA,KAAc,OAAO;YAC1D,cAAc,KAAK;QACrB;QAEA,SAAS,aAAa,KAAA,EAAqB;YACzC,aAAa,KAAK;YAElB,OAAQ,SAAS,MAAA,EAAQ;gBACvB,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,wJAAK,QAAA,CAAM,MAAA;4BACT,IAAI,MAAM,YAAA,KAAiB,OAAO;4BACX;4BACvB,QAAQ,MAAM,YAAA;4BACd;wBAEF,wJAAK,QAAA,CAAM,GAAA;4BACT,IAAI,MAAM,SAAA,KAAc,OAAO;4BACX;4BACpB,QAAQ,MAAM,SAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,wJAAK,QAAA,CAAM,SAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;4BACpC;4BACzB,QAAQ,MAAM,eAAA;4BACd;wBAEF,wJAAK,QAAA,CAAM,YAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;4BACpC;4BAC5B,QAAQ,MAAM,kBAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;YACxC,aAAa,KAAK;YAElB,OAAQ,OAAO;gBACb,KAAK,MAAM,YAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,SAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,eAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;oBAC7D,wBAAwB,KAAK;oBAC7B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,kBAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;oBAChE,2BAA2B,KAAK;oBAChC,MAAM,MAAA,CAAO;oBACb;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;QACF;QAEA,SAAS,cAAc,KAAA,EAAc;YACnC,IAAI,MAAM,OAAA,KAAY,OAAO;YAC7B,MAAM,cAAA,CAAe;QACvB;QAEA,SAAS,WAAW,KAAA,EAAqB;YACvC,SAAS,IAAA,CAAK,KAAK;QACrB;QAEA,SAAS,cAAc,KAAA,EAAqB;YACnC,OAAA,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;gBACxC,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;oBACnC,SAAA,MAAA,CAAO,GAAG,CAAC;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,aAAa,KAAA,EAAqB;YACrC,IAAA,WAAW,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAE/C,IAAI,aAAa,KAAA,GAAW;gBAC1B,WAAW,uJAAI,UAAA;gBACE,gBAAA,CAAA,MAAM,SAAS,CAAA,GAAI;YACtC;YAEA,SAAS,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;QACvC;QAEA,SAAS,yBAAyB,KAAA,EAAqB;YAC/C,MAAA,UAAU,MAAM,SAAA,KAAc,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,GAAY,QAAA,CAAS,CAAC,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA;YAC7E,OAAA,gBAAA,CAAiB,QAAQ,SAAS,CAAA;QAC3C;QAIA,IAAA,CAAK,OAAA,GAAU,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC9C,QAAQ,UAAU;YAClB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC/C,SAAS,UAAU;YACnB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,MAAM;YACb,OAAA;QAAA;QAGJ,IAAA,CAAA,QAAA,GAAW,CAAC,aAAa;YAC5B,SAAS,QAAQ;YACjB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,YAAA,GAAe,MAAM;YACxB,OAAO,aAAa;QAAA;QAItB,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAErD,IAAA,CAAK,MAAA,CAAO;IACd;AACF;AAUA,MAAM,oBAAoB,cAAc;IACtC,YAAY,MAAA,EAAgD,UAAA,CAA0B;QACpF,KAAA,CAAM,QAAQ,UAAU;QAExB,IAAA,CAAK,kBAAA,GAAqB;QAErB,IAAA,CAAA,YAAA,CAAa,IAAA,sJAAO,QAAA,CAAM,GAAA;QAC1B,IAAA,CAAA,YAAA,CAAa,KAAA,sJAAQ,QAAA,CAAM,MAAA;QAE3B,IAAA,CAAA,OAAA,CAAQ,GAAA,sJAAM,QAAA,CAAM,GAAA;QACpB,IAAA,CAAA,OAAA,CAAQ,GAAA,sJAAM,QAAA,CAAM,YAAA;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/types/helpers.ts"], "sourcesContent": ["export const getWithKey = <T, K extends keyof T>(obj: T, key: K): T[K] => obj[key]\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa,CAAuB,KAAQ,MAAiB,GAAA,CAAI,GAAG,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "file": "BufferGeometryUtils.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/utils/BufferGeometryUtils.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Float32BufferAttribute,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  TrianglesDrawMode,\n  Vector3,\n  Mesh,\n  Line,\n  Points,\n  Material,\n  SkinnedMesh,\n} from 'three'\n\nimport { getWithKey } from '../types/helpers'\nimport type { TypedArrayConstructors, TypedArray } from '../types/shared'\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nexport const mergeBufferGeometries = (geometries: BufferGeometry[], useGroups?: boolean): BufferGeometry | null => {\n  const isIndexed = geometries[0].index !== null\n\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes))\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes))\n\n  const attributes: { [key: string]: Array<InterleavedBufferAttribute | BufferAttribute> } = {}\n  const morphAttributes: { [key: string]: Array<BufferAttribute | InterleavedBufferAttribute>[] } = {}\n\n  const morphTargetsRelative = geometries[0].morphTargetsRelative\n\n  const mergedGeometry = new BufferGeometry()\n\n  let offset = 0\n\n  geometries.forEach((geom, i) => {\n    let attributesCount = 0\n\n    // ensure that all geometries are indexed, or none\n\n    if (isIndexed !== (geom.index !== null)) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.',\n      )\n      return null\n    }\n\n    // gather attributes, exit early if they're different\n\n    for (let name in geom.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. All geometries must have compatible attributes; make sure \"' +\n            name +\n            '\" attribute exists among all geometries, or in none of them.',\n        )\n        return null\n      }\n\n      if (attributes[name] === undefined) {\n        attributes[name] = []\n      }\n\n      attributes[name].push(geom.attributes[name])\n\n      attributesCount++\n    }\n\n    // ensure geometries have the same number of attributes\n\n    if (attributesCount !== attributesUsed.size) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. Make sure all geometries have the same number of attributes.',\n      )\n      return null\n    }\n\n    // gather morph attributes, exit early if they're different\n\n    if (morphTargetsRelative !== geom.morphTargetsRelative) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. .morphTargetsRelative must be consistent throughout all geometries.',\n      )\n      return null\n    }\n\n    for (let name in geom.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '.  .morphAttributes must be consistent throughout all geometries.',\n        )\n        return null\n      }\n\n      if (morphAttributes[name] === undefined) morphAttributes[name] = []\n\n      morphAttributes[name].push(geom.morphAttributes[name])\n    }\n\n    // gather .userData\n\n    mergedGeometry.userData.mergedUserData = mergedGeometry.userData.mergedUserData || []\n    mergedGeometry.userData.mergedUserData.push(geom.userData)\n\n    if (useGroups) {\n      let count\n\n      if (geom.index) {\n        count = geom.index.count\n      } else if (geom.attributes.position !== undefined) {\n        count = geom.attributes.position.count\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. The geometry must have either an index or a position attribute',\n        )\n        return null\n      }\n\n      mergedGeometry.addGroup(offset, count, i)\n\n      offset += count\n    }\n  })\n\n  // merge indices\n\n  if (isIndexed) {\n    let indexOffset = 0\n    const mergedIndex: number[] = []\n\n    geometries.forEach((geom) => {\n      const index = geom.index as BufferAttribute\n\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset)\n      }\n\n      indexOffset += geom.attributes.position.count\n    })\n\n    mergedGeometry.setIndex(mergedIndex)\n  }\n\n  // merge attributes\n\n  for (let name in attributes) {\n    const mergedAttribute = mergeBufferAttributes(attributes[name] as BufferAttribute[])\n\n    if (!mergedAttribute) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' + name + ' attribute.',\n      )\n      return null\n    }\n\n    mergedGeometry.setAttribute(name, mergedAttribute)\n  }\n\n  // merge morph attributes\n\n  for (let name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length\n\n    if (numMorphTargets === 0) break\n\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {}\n    mergedGeometry.morphAttributes[name] = []\n\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = []\n\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i])\n      }\n\n      const mergedMorphAttribute = mergeBufferAttributes(morphAttributesToMerge as BufferAttribute[])\n\n      if (!mergedMorphAttribute) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' +\n            name +\n            ' morphAttribute.',\n        )\n        return null\n      }\n\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute)\n    }\n  }\n\n  return mergedGeometry\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nexport const mergeBufferAttributes = (attributes: BufferAttribute[]): BufferAttribute | null | undefined => {\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let itemSize: number | undefined = undefined\n  let normalized: boolean | undefined = undefined\n  let arrayLength = 0\n\n  attributes.forEach((attr) => {\n    if (TypedArray === undefined) {\n      TypedArray = attr.array.constructor\n    }\n    if (TypedArray !== attr.array.constructor) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.',\n      )\n      return null\n    }\n\n    if (itemSize === undefined) itemSize = attr.itemSize\n    if (itemSize !== attr.itemSize) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    if (normalized === undefined) normalized = attr.normalized\n    if (normalized !== attr.normalized) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    arrayLength += attr.array.length\n  })\n\n  if (TypedArray && itemSize) {\n    // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n    const array = new TypedArray(arrayLength)\n    let offset = 0\n\n    attributes.forEach((attr) => {\n      array.set(attr.array, offset)\n      offset += attr.array.length\n    })\n\n    return new BufferAttribute(array, itemSize, normalized)\n  }\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nexport const interleaveAttributes = (attributes: BufferAttribute[]): InterleavedBufferAttribute[] | null => {\n  // Interleaves the provided attributes into an InterleavedBuffer and returns\n  // a set of InterleavedBufferAttributes for each attribute\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let arrayLength = 0\n  let stride = 0\n\n  // calculate the the length and type of the interleavedBuffer\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i]\n\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('AttributeBuffers of different types cannot be interleaved')\n      return null\n    }\n\n    arrayLength += attribute.array.length\n    stride += attribute.itemSize\n  }\n\n  // Create the set of buffer attributes\n  // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride)\n  let offset = 0\n  const res = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n  const setters = ['setX', 'setY', 'setZ', 'setW']\n\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j]\n    const itemSize = attribute.itemSize\n    const count = attribute.count\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized)\n    res.push(iba)\n\n    offset += itemSize\n\n    // Move the data for each attribute into the new interleavedBuffer\n    // at the appropriate offset\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        const set = getWithKey(iba, setters[k] as keyof InterleavedBufferAttribute) as InterleavedBufferAttribute[\n          | 'setX'\n          | 'setY'\n          | 'setZ'\n          | 'setW']\n        const get = getWithKey(attribute, getters[k] as keyof BufferAttribute) as BufferAttribute[\n          | 'getX'\n          | 'getY'\n          | 'getZ'\n          | 'getW']\n        set(c, get(c))\n      }\n    }\n  }\n\n  return res\n}\n\n/**\n * @param {Array<BufferGeometry>} geometry\n * @return {number}\n */\nexport function estimateBytesUsed(geometry: BufferGeometry): number {\n  // Return the estimated memory used by this geometry in bytes\n  // Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n  // for InterleavedBufferAttributes.\n  let mem = 0\n  for (let name in geometry.attributes) {\n    const attr = geometry.getAttribute(name)\n    mem += attr.count * attr.itemSize * (attr.array as TypedArray).BYTES_PER_ELEMENT\n  }\n\n  const indices = geometry.getIndex()\n  mem += indices ? indices.count * indices.itemSize * (indices.array as TypedArray).BYTES_PER_ELEMENT : 0\n  return mem\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry>}\n */\nexport function mergeVertices(geometry: BufferGeometry, tolerance = 1e-4): BufferGeometry {\n  tolerance = Math.max(tolerance, Number.EPSILON)\n\n  // Generate an index buffer if the geometry doesn't have one, or optimize it\n  // if it's already available.\n  const hashToIndex: {\n    [key: string]: number\n  } = {}\n  const indices = geometry.getIndex()\n  const positions = geometry.getAttribute('position')\n  const vertexCount = indices ? indices.count : positions.count\n\n  // next value for triangle indices\n  let nextIndex = 0\n\n  // attributes and new attribute arrays\n  const attributeNames = Object.keys(geometry.attributes)\n  const attrArrays: {\n    [key: string]: []\n  } = {}\n  const morphAttrsArrays: {\n    [key: string]: Array<Array<BufferAttribute | InterleavedBufferAttribute>>\n  } = {}\n  const newIndices = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n\n  // initialize the arrays\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n\n    attrArrays[name] = []\n\n    const morphAttr = geometry.morphAttributes[name]\n    if (morphAttr) {\n      morphAttrsArrays[name] = new Array(morphAttr.length).fill(0).map(() => [])\n    }\n  }\n\n  // convert the error tolerance to an amount of decimal places to truncate to\n  const decimalShift = Math.log10(1 / tolerance)\n  const shiftMultiplier = Math.pow(10, decimalShift)\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i\n\n    // Generate a hash for the vertex attributes at the current index 'i'\n    let hash = ''\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j]\n      const attribute = geometry.getAttribute(name)\n      const itemSize = attribute.itemSize\n\n      for (let k = 0; k < itemSize; k++) {\n        // double tilde truncates the decimal value\n        // @ts-ignore no\n        hash += `${~~(attribute[getters[k]](index) * shiftMultiplier)},`\n      }\n    }\n\n    // Add another reference to the vertex if it's already\n    // used by another index\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash])\n    } else {\n      // copy data to the new index in the attribute arrays\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j]\n        const attribute = geometry.getAttribute(name)\n        const morphAttr = geometry.morphAttributes[name]\n        const itemSize = attribute.itemSize\n        const newarray = attrArrays[name]\n        const newMorphArrays = morphAttrsArrays[name]\n\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k]\n          // @ts-ignore\n          newarray.push(attribute[getterFunc](index))\n\n          if (morphAttr) {\n            for (let m = 0, ml = morphAttr.length; m < ml; m++) {\n              // @ts-ignore\n              newMorphArrays[m].push(morphAttr[m][getterFunc](index))\n            }\n          }\n        }\n      }\n\n      hashToIndex[hash] = nextIndex\n      newIndices.push(nextIndex)\n      nextIndex++\n    }\n  }\n\n  // Generate typed arrays from new attribute arrays and update\n  // the attributeBuffers\n  const result = geometry.clone()\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n    const oldAttribute = geometry.getAttribute(name)\n    //@ts-expect-error  something to do with functions and constructors and new\n    const buffer = new (oldAttribute.array as TypedArray).constructor(attrArrays[name])\n    const attribute = new BufferAttribute(buffer, oldAttribute.itemSize, oldAttribute.normalized)\n\n    result.setAttribute(name, attribute)\n\n    // Update the attribute arrays\n    if (name in morphAttrsArrays) {\n      for (let j = 0; j < morphAttrsArrays[name].length; j++) {\n        const oldMorphAttribute = geometry.morphAttributes[name][j]\n        //@ts-expect-error something to do with functions and constructors and new\n        const buffer = new (oldMorphAttribute.array as TypedArray).constructor(morphAttrsArrays[name][j])\n        const morphAttribute = new BufferAttribute(buffer, oldMorphAttribute.itemSize, oldMorphAttribute.normalized)\n        result.morphAttributes[name][j] = morphAttribute\n      }\n    }\n  }\n\n  // indices\n\n  result.setIndex(newIndices)\n\n  return result\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nexport function toTrianglesDrawMode(geometry: BufferGeometry, drawMode: number): BufferGeometry {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.')\n    return geometry\n  }\n\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex()\n\n    // generate index if not present\n\n    if (index === null) {\n      const indices = []\n\n      const position = geometry.getAttribute('position')\n\n      if (position !== undefined) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i)\n        }\n\n        geometry.setIndex(indices)\n        index = geometry.getIndex()\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.',\n        )\n        return geometry\n      }\n    }\n\n    //\n\n    const numberOfTriangles = (index as BufferAttribute).count - 2\n    const newIndices = []\n\n    if (index) {\n      if (drawMode === TriangleFanDrawMode) {\n        // gl.TRIANGLE_FAN\n\n        for (let i = 1; i <= numberOfTriangles; i++) {\n          newIndices.push(index.getX(0))\n          newIndices.push(index.getX(i))\n          newIndices.push(index.getX(i + 1))\n        }\n      } else {\n        // gl.TRIANGLE_STRIP\n\n        for (let i = 0; i < numberOfTriangles; i++) {\n          if (i % 2 === 0) {\n            newIndices.push(index.getX(i))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i + 2))\n          } else {\n            newIndices.push(index.getX(i + 2))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i))\n          }\n        }\n      }\n    }\n\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.')\n    }\n\n    // build final geometry\n\n    const newGeometry = geometry.clone()\n    newGeometry.setIndex(newIndices)\n    newGeometry.clearGroups()\n\n    return newGeometry\n  } else {\n    console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode)\n    return geometry\n  }\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nexport type ComputedMorphedAttribute = {\n  positionAttribute: BufferAttribute | InterleavedBufferAttribute\n  normalAttribute: BufferAttribute | InterleavedBufferAttribute\n  morphedPositionAttribute: Float32BufferAttribute\n  morphedNormalAttribute: Float32BufferAttribute\n}\n\nexport function computeMorphedAttributes(object: Mesh | Line | Points): ComputedMorphedAttribute | null {\n  if (object.geometry.isBufferGeometry !== true) {\n    console.error('THREE.BufferGeometryUtils: Geometry is not of type BufferGeometry.')\n    return null\n  }\n\n  const _vA = new Vector3()\n  const _vB = new Vector3()\n  const _vC = new Vector3()\n\n  const _tempA = new Vector3()\n  const _tempB = new Vector3()\n  const _tempC = new Vector3()\n\n  const _morphA = new Vector3()\n  const _morphB = new Vector3()\n  const _morphC = new Vector3()\n\n  function _calculateMorphedAttributeData(\n    object: Mesh | Line | Points,\n    material: Material,\n    attribute: BufferAttribute | InterleavedBufferAttribute,\n    morphAttribute: (BufferAttribute | InterleavedBufferAttribute)[],\n    morphTargetsRelative: boolean,\n    a: number,\n    b: number,\n    c: number,\n    modifiedAttributeArray: Float32Array,\n  ): void {\n    _vA.fromBufferAttribute(attribute, a)\n    _vB.fromBufferAttribute(attribute, b)\n    _vC.fromBufferAttribute(attribute, c)\n\n    const morphInfluences = object.morphTargetInfluences\n\n    if (\n      // @ts-ignore\n      material.morphTargets &&\n      morphAttribute &&\n      morphInfluences\n    ) {\n      _morphA.set(0, 0, 0)\n      _morphB.set(0, 0, 0)\n      _morphC.set(0, 0, 0)\n\n      for (let i = 0, il = morphAttribute.length; i < il; i++) {\n        const influence = morphInfluences[i]\n        const morph = morphAttribute[i]\n\n        if (influence === 0) continue\n\n        _tempA.fromBufferAttribute(morph, a)\n        _tempB.fromBufferAttribute(morph, b)\n        _tempC.fromBufferAttribute(morph, c)\n\n        if (morphTargetsRelative) {\n          _morphA.addScaledVector(_tempA, influence)\n          _morphB.addScaledVector(_tempB, influence)\n          _morphC.addScaledVector(_tempC, influence)\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence)\n          _morphB.addScaledVector(_tempB.sub(_vB), influence)\n          _morphC.addScaledVector(_tempC.sub(_vC), influence)\n        }\n      }\n\n      _vA.add(_morphA)\n      _vB.add(_morphB)\n      _vC.add(_morphC)\n    }\n\n    if ((object as SkinnedMesh).isSkinnedMesh) {\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(a, _vA)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(b, _vB)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(c, _vC)\n    }\n\n    modifiedAttributeArray[a * 3 + 0] = _vA.x\n    modifiedAttributeArray[a * 3 + 1] = _vA.y\n    modifiedAttributeArray[a * 3 + 2] = _vA.z\n    modifiedAttributeArray[b * 3 + 0] = _vB.x\n    modifiedAttributeArray[b * 3 + 1] = _vB.y\n    modifiedAttributeArray[b * 3 + 2] = _vB.z\n    modifiedAttributeArray[c * 3 + 0] = _vC.x\n    modifiedAttributeArray[c * 3 + 1] = _vC.y\n    modifiedAttributeArray[c * 3 + 2] = _vC.z\n  }\n\n  const geometry = object.geometry\n  const material = object.material\n\n  let a, b, c\n  const index = geometry.index\n  const positionAttribute = geometry.attributes.position\n  const morphPosition = geometry.morphAttributes.position\n  const morphTargetsRelative = geometry.morphTargetsRelative\n  const normalAttribute = geometry.attributes.normal\n  const morphNormal = geometry.morphAttributes.position\n\n  const groups = geometry.groups\n  const drawRange = geometry.drawRange\n  let i, j, il, jl\n  let group, groupMaterial\n  let start, end\n\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize)\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize)\n\n  if (index !== null) {\n    // indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j)\n          b = index.getX(j + 1)\n          c = index.getX(j + 2)\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(index.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i)\n        b = index.getX(i + 1)\n        c = index.getX(i + 2)\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  } else if (positionAttribute !== undefined) {\n    // non-indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j\n          b = j + 1\n          c = j + 2\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = i\n        b = i + 1\n        c = i + 2\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  }\n\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3)\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3)\n\n  return {\n    positionAttribute: positionAttribute,\n    normalAttribute: normalAttribute,\n    morphedPositionAttribute: morphedPositionAttribute,\n    morphedNormalAttribute: morphedNormalAttribute,\n  }\n}\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * Backwards compatible with code such as @react-three/drei's `<RoundedBox>`\n * which uses this method to operate on the original geometry.\n *\n * As of this writing, BufferGeometry.toNonIndexed() warns if the geometry is\n * non-indexed and returns `this`, i.e. the same geometry on which it was called:\n * `BufferGeometry is already non-indexed.`\n *\n * @param geometry\n * @param creaseAngle\n */\nexport function toCreasedNormals(geometry: BufferGeometry, creaseAngle = Math.PI / 3 /* 60 degrees */): BufferGeometry {\n  const creaseDot = Math.cos(creaseAngle)\n  const hashMultiplier = (1 + 1e-10) * 1e2\n\n  // reusable vectors\n  const verts = [new Vector3(), new Vector3(), new Vector3()]\n  const tempVec1 = new Vector3()\n  const tempVec2 = new Vector3()\n  const tempNorm = new Vector3()\n  const tempNorm2 = new Vector3()\n\n  // hashes a vector\n  function hashVertex(v: Vector3): string {\n    const x = ~~(v.x * hashMultiplier)\n    const y = ~~(v.y * hashMultiplier)\n    const z = ~~(v.z * hashMultiplier)\n    return `${x},${y},${z}`\n  }\n\n  const resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry\n  const posAttr = resultGeometry.attributes.position\n  const vertexMap: { [key: string]: Vector3[] } = {}\n\n  // find all the normals shared by commonly located vertices\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    // add the normal to the map for all vertices\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize()\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = []\n      }\n\n      vertexMap[hash].push(normal)\n    }\n  }\n\n  // average normals from all vertices that share a common location if they are within the\n  // provided crease threshold\n  const normalArray = new Float32Array(posAttr.count * 3)\n  const normAttr = new BufferAttribute(normalArray, 3, false)\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    // get the face normal for this vertex\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize()\n\n    // average all normals that meet the threshold and set the normal value\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      const otherNormals = vertexMap[hash]\n      tempNorm2.set(0, 0, 0)\n\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k]\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm)\n        }\n      }\n\n      tempNorm2.normalize()\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z)\n    }\n  }\n\n  resultGeometry.setAttribute('normal', normAttr)\n  return resultGeometry\n}\n"], "names": ["buffer", "object", "material", "morphTargetsRelative", "a", "b", "c", "i", "il"], "mappings": ";;;;;;;;;;;;;;AAyBa,MAAA,wBAAwB,CAAC,YAA8B,cAA+C;IACjH,MAAM,YAAY,UAAA,CAAW,CAAC,CAAA,CAAE,KAAA,KAAU;IAEpC,MAAA,iBAAiB,IAAI,IAAI,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,UAAU,CAAC;IAC9D,MAAA,sBAAsB,IAAI,IAAI,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,eAAe,CAAC;IAE9E,MAAM,aAAqF,CAAA;IAC3F,MAAM,kBAA4F,CAAA;IAE5F,MAAA,uBAAuB,UAAA,CAAW,CAAC,CAAA,CAAE,oBAAA;IAErC,MAAA,iBAAiB,uJAAI,iBAAA;IAE3B,IAAI,SAAS;IAEF,WAAA,OAAA,CAAQ,CAAC,MAAM,MAAM;QAC9B,IAAI,kBAAkB;QAIlB,IAAA,cAAA,CAAe,KAAK,KAAA,KAAU,IAAA,GAAO;YAC/B,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAIS,IAAA,IAAA,QAAQ,KAAK,UAAA,CAAY;YAChC,IAAI,CAAC,eAAe,GAAA,CAAI,IAAI,GAAG;gBACrB,QAAA,KAAA,CACN,uFACE,IACA,kEACA,OACA;gBAEG,OAAA;YACT;YAEI,IAAA,UAAA,CAAW,IAAI,CAAA,KAAM,KAAA,GAAW;gBACvB,UAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YACrB;YAEA,UAAA,CAAW,IAAI,CAAA,CAAE,IAAA,CAAK,KAAK,UAAA,CAAW,IAAI,CAAC;YAE3C;QACF;QAII,IAAA,oBAAoB,eAAe,IAAA,EAAM;YACnC,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAII,IAAA,yBAAyB,KAAK,oBAAA,EAAsB;YAC9C,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAES,IAAA,IAAA,QAAQ,KAAK,eAAA,CAAiB;YACrC,IAAI,CAAC,oBAAoB,GAAA,CAAI,IAAI,GAAG;gBAC1B,QAAA,KAAA,CACN,uFACE,IACA;gBAEG,OAAA;YACT;YAEI,IAAA,eAAA,CAAgB,IAAI,CAAA,KAAM,KAAA,GAA2B,eAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YAEjE,eAAA,CAAgB,IAAI,CAAA,CAAE,IAAA,CAAK,KAAK,eAAA,CAAgB,IAAI,CAAC;QACvD;QAIA,eAAe,QAAA,CAAS,cAAA,GAAiB,eAAe,QAAA,CAAS,cAAA,IAAkB,EAAA;QACnF,eAAe,QAAA,CAAS,cAAA,CAAe,IAAA,CAAK,KAAK,QAAQ;QAEzD,IAAI,WAAW;YACT,IAAA;YAEJ,IAAI,KAAK,KAAA,EAAO;gBACd,QAAQ,KAAK,KAAA,CAAM,KAAA;YACV,OAAA,IAAA,KAAK,UAAA,CAAW,QAAA,KAAa,KAAA,GAAW;gBACzC,QAAA,KAAK,UAAA,CAAW,QAAA,CAAS,KAAA;YAAA,OAC5B;gBACG,QAAA,KAAA,CACN,uFACE,IACA;gBAEG,OAAA;YACT;YAEe,eAAA,QAAA,CAAS,QAAQ,OAAO,CAAC;YAE9B,UAAA;QACZ;IAAA,CACD;IAID,IAAI,WAAW;QACb,IAAI,cAAc;QAClB,MAAM,cAAwB,CAAA,CAAA;QAEnB,WAAA,OAAA,CAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,KAAK,KAAA;YAEnB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,KAAA,EAAO,EAAE,EAAG;gBACpC,YAAY,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,IAAI,WAAW;YAC9C;YAEe,eAAA,KAAK,UAAA,CAAW,QAAA,CAAS,KAAA;QAAA,CACzC;QAED,eAAe,QAAA,CAAS,WAAW;IACrC;IAIA,IAAA,IAAS,QAAQ,WAAY;QAC3B,MAAM,kBAAkB,sBAAsB,UAAA,CAAW,IAAI,CAAsB;QAEnF,IAAI,CAAC,iBAAiB;YACZ,QAAA,KAAA,CACN,0FAA0F,OAAO;YAE5F,OAAA;QACT;QAEe,eAAA,YAAA,CAAa,MAAM,eAAe;IACnD;IAIA,IAAA,IAAS,QAAQ,gBAAiB;QAChC,MAAM,kBAAkB,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,MAAA;QAEjD,IAAI,oBAAoB,GAAG;QAEZ,eAAA,eAAA,GAAkB,eAAe,eAAA,IAAmB,CAAA;QACpD,eAAA,eAAA,CAAgB,IAAI,CAAA,GAAI,EAAA;QAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,iBAAiB,EAAE,EAAG;YACxC,MAAM,yBAAyB,CAAA,CAAA;YAEtB,IAAA,IAAA,IAAI,GAAG,IAAI,eAAA,CAAgB,IAAI,CAAA,CAAE,MAAA,EAAQ,EAAE,EAAG;gBACrD,uBAAuB,IAAA,CAAK,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAC;YACzD;YAEM,MAAA,uBAAuB,sBAAsB,sBAA2C;YAE9F,IAAI,CAAC,sBAAsB;gBACjB,QAAA,KAAA,CACN,0FACE,OACA;gBAEG,OAAA;YACT;YAEA,eAAe,eAAA,CAAgB,IAAI,CAAA,CAAE,IAAA,CAAK,oBAAoB;QAChE;IACF;IAEO,OAAA;AACT;AAMa,MAAA,wBAAwB,CAAC,eAAsE;IAC1G,IAAI,aAAiD,KAAA;IACrD,IAAI,WAA+B,KAAA;IACnC,IAAI,aAAkC,KAAA;IACtC,IAAI,cAAc;IAEP,WAAA,OAAA,CAAQ,CAAC,SAAS;QAC3B,IAAI,eAAe,KAAA,GAAW;YAC5B,aAAa,KAAK,KAAA,CAAM,WAAA;QAC1B;QACI,IAAA,eAAe,KAAK,KAAA,CAAM,WAAA,EAAa;YACjC,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,IAAI,aAAa,KAAA,GAAW,WAAW,KAAK,QAAA;QACxC,IAAA,aAAa,KAAK,QAAA,EAAU;YACtB,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,IAAI,eAAe,KAAA,GAAW,aAAa,KAAK,UAAA;QAC5C,IAAA,eAAe,KAAK,UAAA,EAAY;YAC1B,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,eAAe,KAAK,KAAA,CAAM,MAAA;IAAA,CAC3B;IAED,IAAI,cAAc,UAAU;QAEpB,MAAA,QAAQ,IAAI,WAAW,WAAW;QACxC,IAAI,SAAS;QAEF,WAAA,OAAA,CAAQ,CAAC,SAAS;YACrB,MAAA,GAAA,CAAI,KAAK,KAAA,EAAO,MAAM;YAC5B,UAAU,KAAK,KAAA,CAAM,MAAA;QAAA,CACtB;QAED,OAAO,IAAI,qKAAA,CAAgB,OAAO,UAAU,UAAU;IACxD;AACF;AAMa,MAAA,uBAAuB,CAAC,eAAuE;IAG1G,IAAI,aAAiD,KAAA;IACrD,IAAI,cAAc;IAClB,IAAI,SAAS;IAGJ,IAAA,IAAA,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAI,GAAG,EAAE,EAAG;QAC3C,MAAA,YAAY,UAAA,CAAW,CAAC,CAAA;QAE9B,IAAI,eAAe,KAAA,GAAW,aAAa,UAAU,KAAA,CAAM,WAAA;QACvD,IAAA,eAAe,UAAU,KAAA,CAAM,WAAA,EAAa;YAC9C,QAAQ,KAAA,CAAM,2DAA2D;YAClE,OAAA;QACT;QAEA,eAAe,UAAU,KAAA,CAAM,MAAA;QAC/B,UAAU,UAAU,QAAA;IACtB;IAIA,MAAM,oBAAoB,IAAI,uKAAA,CAAkB,IAAI,WAAW,WAAW,GAAG,MAAM;IACnF,IAAI,SAAS;IACb,MAAM,MAAM,CAAA,CAAA;IACZ,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAC/C,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAE/C,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC3C,MAAA,YAAY,UAAA,CAAW,CAAC,CAAA;QAC9B,MAAM,WAAW,UAAU,QAAA;QAC3B,MAAM,QAAQ,UAAU,KAAA;QACxB,MAAM,MAAM,uJAAI,6BAAA,CAA2B,mBAAmB,UAAU,QAAQ,UAAU,UAAU;QACpG,IAAI,IAAA,CAAK,GAAG;QAEF,UAAA;QAIV,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;gBACjC,MAAM,MAAM,wKAAA,EAAW,KAAK,OAAA,CAAQ,CAAC,CAAqC;gBAK1E,MAAM,iKAAM,aAAA,EAAW,WAAW,OAAA,CAAQ,CAAC,CAA0B;gBAKjE,IAAA,GAAG,IAAI,CAAC,CAAC;YACf;QACF;IACF;IAEO,OAAA;AACT;AAMO,SAAS,kBAAkB,QAAA,EAAkC;IAIlE,IAAI,MAAM;IACD,IAAA,IAAA,QAAQ,SAAS,UAAA,CAAY;QAC9B,MAAA,OAAO,SAAS,YAAA,CAAa,IAAI;QACvC,OAAO,KAAK,KAAA,GAAQ,KAAK,QAAA,GAAY,KAAK,KAAA,CAAqB,iBAAA;IACjE;IAEM,MAAA,UAAU,SAAS,QAAA;IACzB,OAAO,UAAU,QAAQ,KAAA,GAAQ,QAAQ,QAAA,GAAY,QAAQ,KAAA,CAAqB,iBAAA,GAAoB;IAC/F,OAAA;AACT;AAOgB,SAAA,cAAc,QAAA,EAA0B,YAAY,IAAA,EAAsB;IACxF,YAAY,KAAK,GAAA,CAAI,WAAW,OAAO,OAAO;IAI9C,MAAM,cAEF,CAAA;IACE,MAAA,UAAU,SAAS,QAAA;IACnB,MAAA,YAAY,SAAS,YAAA,CAAa,UAAU;IAClD,MAAM,cAAc,UAAU,QAAQ,KAAA,GAAQ,UAAU,KAAA;IAGxD,IAAI,YAAY;IAGhB,MAAM,iBAAiB,OAAO,IAAA,CAAK,SAAS,UAAU;IACtD,MAAM,aAEF,CAAA;IACJ,MAAM,mBAEF,CAAA;IACJ,MAAM,aAAa,CAAA,CAAA;IACnB,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAG/C,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;QAElB,UAAA,CAAA,IAAI,CAAA,GAAI,EAAA;QAEb,MAAA,YAAY,SAAS,eAAA,CAAgB,IAAI,CAAA;QAC/C,IAAI,WAAW;YACb,gBAAA,CAAiB,IAAI,CAAA,GAAI,IAAI,MAAM,UAAU,MAAM,EAAE,IAAA,CAAK,CAAC,EAAE,GAAA,CAAI,IAAM,CAAE,CAAA;QAC3E;IACF;IAGA,MAAM,eAAe,KAAK,KAAA,CAAM,IAAI,SAAS;IAC7C,MAAM,kBAAkB,KAAK,GAAA,CAAI,IAAI,YAAY;IACjD,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,MAAM,QAAQ,UAAU,QAAQ,IAAA,CAAK,CAAC,IAAI;QAG1C,IAAI,OAAO;QACX,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;YAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;YACvB,MAAA,YAAY,SAAS,YAAA,CAAa,IAAI;YAC5C,MAAM,WAAW,UAAU,QAAA;YAE3B,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;gBAGzB,QAAA,GAAG,CAAC,CAAA,CAAE,SAAA,CAAU,OAAA,CAAQ,CAAC,CAAC,CAAA,CAAE,KAAK,IAAI,eAAA,EAAA,CAAA,CAAA;YAC/C;QACF;QAIA,IAAI,QAAQ,aAAa;YACZ,WAAA,IAAA,CAAK,WAAA,CAAY,IAAI,CAAC;QAAA,OAC5B;YAEL,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;gBAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;gBACvB,MAAA,YAAY,SAAS,YAAA,CAAa,IAAI;gBACtC,MAAA,YAAY,SAAS,eAAA,CAAgB,IAAI,CAAA;gBAC/C,MAAM,WAAW,UAAU,QAAA;gBACrB,MAAA,WAAW,UAAA,CAAW,IAAI,CAAA;gBAC1B,MAAA,iBAAiB,gBAAA,CAAiB,IAAI,CAAA;gBAE5C,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;oBAC3B,MAAA,aAAa,OAAA,CAAQ,CAAC,CAAA;oBAE5B,SAAS,IAAA,CAAK,SAAA,CAAU,UAAU,CAAA,CAAE,KAAK,CAAC;oBAE1C,IAAI,WAAW;wBACb,IAAA,IAAS,IAAI,GAAG,KAAK,UAAU,MAAA,EAAQ,IAAI,IAAI,IAAK;4BAEnC,cAAA,CAAA,CAAC,CAAA,CAAE,IAAA,CAAK,SAAA,CAAU,CAAC,CAAA,CAAE,UAAU,CAAA,CAAE,KAAK,CAAC;wBACxD;oBACF;gBACF;YACF;YAEA,WAAA,CAAY,IAAI,CAAA,GAAI;YACpB,WAAW,IAAA,CAAK,SAAS;YACzB;QACF;IACF;IAIM,MAAA,SAAS,SAAS,KAAA;IACxB,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;QACvB,MAAA,eAAe,SAAS,YAAA,CAAa,IAAI;QAE/C,MAAM,SAAS,IAAK,aAAa,KAAA,CAAqB,WAAA,CAAY,UAAA,CAAW,IAAI,CAAC;QAClF,MAAM,YAAY,uJAAI,kBAAA,CAAgB,QAAQ,aAAa,QAAA,EAAU,aAAa,UAAU;QAErF,OAAA,YAAA,CAAa,MAAM,SAAS;QAGnC,IAAI,QAAQ,kBAAkB;YAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,gBAAA,CAAiB,IAAI,CAAA,CAAE,MAAA,EAAQ,IAAK;gBACtD,MAAM,oBAAoB,SAAS,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA;gBAEpDA,MAAAA,UAAS,IAAK,kBAAkB,KAAA,CAAqB,WAAA,CAAY,gBAAA,CAAiB,IAAI,CAAA,CAAE,CAAC,CAAC;gBAChG,MAAM,iBAAiB,uJAAI,kBAAA,CAAgBA,SAAQ,kBAAkB,QAAA,EAAU,kBAAkB,UAAU;gBAC3G,OAAO,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI;YACpC;QACF;IACF;IAIA,OAAO,QAAA,CAAS,UAAU;IAEnB,OAAA;AACT;AAOgB,SAAA,oBAAoB,QAAA,EAA0B,QAAA,EAAkC;IAC9F,IAAI,gKAAa,oBAAA,EAAmB;QAClC,QAAQ,IAAA,CAAK,yFAAyF;QAC/F,OAAA;IACT;IAEI,IAAA,gKAAa,sBAAA,IAAuB,gKAAa,wBAAA,EAAuB;QACtE,IAAA,QAAQ,SAAS,QAAA;QAIrB,IAAI,UAAU,MAAM;YAClB,MAAM,UAAU,CAAA,CAAA;YAEV,MAAA,WAAW,SAAS,YAAA,CAAa,UAAU;YAEjD,IAAI,aAAa,KAAA,GAAW;gBAC1B,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,KAAA,EAAO,IAAK;oBACvC,QAAQ,IAAA,CAAK,CAAC;gBAChB;gBAEA,SAAS,QAAA,CAAS,OAAO;gBACzB,QAAQ,SAAS,QAAA;YAAS,OACrB;gBACG,QAAA,KAAA,CACN;gBAEK,OAAA;YACT;QACF;QAIM,MAAA,oBAAqB,MAA0B,KAAA,GAAQ;QAC7D,MAAM,aAAa,CAAA,CAAA;QAEnB,IAAI,OAAO;YACT,IAAI,gKAAa,sBAAA,EAAqB;gBAGpC,IAAA,IAAS,IAAI,GAAG,KAAK,mBAAmB,IAAK;oBAC3C,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;gBACnC;YAAA,OACK;gBAGL,IAAA,IAAS,IAAI,GAAG,IAAI,mBAAmB,IAAK;oBACtC,IAAA,IAAI,MAAM,GAAG;wBACf,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;wBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;oBAAA,OAC5B;wBACL,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC/B;gBACF;YACF;QACF;QAEI,IAAA,WAAW,MAAA,GAAS,MAAM,mBAAmB;YAC/C,QAAQ,KAAA,CAAM,kGAAkG;QAClH;QAIM,MAAA,cAAc,SAAS,KAAA;QAC7B,YAAY,QAAA,CAAS,UAAU;QAC/B,YAAY,WAAA,CAAY;QAEjB,OAAA;IAAA,OACF;QACG,QAAA,KAAA,CAAM,uEAAuE,QAAQ;QACtF,OAAA;IACT;AACF;AAeO,SAAS,yBAAyB,MAAA,EAA+D;IAClG,IAAA,OAAO,QAAA,CAAS,gBAAA,KAAqB,MAAM;QAC7C,QAAQ,KAAA,CAAM,oEAAoE;QAC3E,OAAA;IACT;IAEM,MAAA,MAAM,uJAAI,UAAA;IACV,MAAA,MAAM,uJAAI,UAAA;IACV,MAAA,MAAM,uJAAI,UAAA;IAEV,MAAA,SAAS,uJAAI,UAAA;IACb,MAAA,SAAS,uJAAI,UAAA;IACb,MAAA,SAAS,uJAAI,UAAA;IAEb,MAAA,UAAU,uJAAI,UAAA;IACd,MAAA,UAAU,uJAAI,UAAA;IACd,MAAA,UAAU,uJAAI,UAAA;IAEX,SAAA,+BACPC,OAAAA,EACAC,SAAAA,EACA,SAAA,EACA,cAAA,EACAC,qBAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACA,sBAAA,EACM;QACF,IAAA,mBAAA,CAAoB,WAAWF,EAAC;QAChC,IAAA,mBAAA,CAAoB,WAAWC,EAAC;QAChC,IAAA,mBAAA,CAAoB,WAAWC,EAAC;QAEpC,MAAM,kBAAkBL,QAAO,qBAAA;QAE/B,IAAA,aAAA;QAEEC,UAAS,YAAA,IACT,kBACA,iBACA;YACQ,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YACX,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YACX,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YAEnB,IAAA,IAASK,KAAI,GAAGC,MAAK,eAAe,MAAA,EAAQD,KAAIC,KAAID,KAAK;gBACjD,MAAA,YAAY,eAAA,CAAgBA,EAAC,CAAA;gBAC7B,MAAA,QAAQ,cAAA,CAAeA,EAAC,CAAA;gBAE9B,IAAI,cAAc,GAAG;gBAEd,OAAA,mBAAA,CAAoB,OAAOH,EAAC;gBAC5B,OAAA,mBAAA,CAAoB,OAAOC,EAAC;gBAC5B,OAAA,mBAAA,CAAoB,OAAOC,EAAC;gBAEnC,IAAIH,uBAAsB;oBAChB,QAAA,eAAA,CAAgB,QAAQ,SAAS;oBACjC,QAAA,eAAA,CAAgB,QAAQ,SAAS;oBACjC,QAAA,eAAA,CAAgB,QAAQ,SAAS;gBAAA,OACpC;oBACL,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;oBAClD,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;oBAClD,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;gBACpD;YACF;YAEA,IAAI,GAAA,CAAI,OAAO;YACf,IAAI,GAAA,CAAI,OAAO;YACf,IAAI,GAAA,CAAI,OAAO;QACjB;QAEA,IAAKF,QAAuB,aAAA,EAAe;YAEzCA,QAAO,aAAA,CAAcG,IAAG,GAAG;YAE3BH,QAAO,aAAA,CAAcI,IAAG,GAAG;YAE3BJ,QAAO,aAAA,CAAcK,IAAG,GAAG;QAC7B;QAEA,sBAAA,CAAuBF,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBC,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBC,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;IAC1C;IAEA,MAAM,WAAW,OAAO,QAAA;IACxB,MAAM,WAAW,OAAO,QAAA;IAExB,IAAI,GAAG,GAAG;IACV,MAAM,QAAQ,SAAS,KAAA;IACjB,MAAA,oBAAoB,SAAS,UAAA,CAAW,QAAA;IACxC,MAAA,gBAAgB,SAAS,eAAA,CAAgB,QAAA;IAC/C,MAAM,uBAAuB,SAAS,oBAAA;IAChC,MAAA,kBAAkB,SAAS,UAAA,CAAW,MAAA;IACtC,MAAA,cAAc,SAAS,eAAA,CAAgB,QAAA;IAE7C,MAAM,SAAS,SAAS,MAAA;IACxB,MAAM,YAAY,SAAS,SAAA;IACvB,IAAA,GAAG,GAAG,IAAI;IACd,IAAI,OAAO;IACX,IAAI,OAAO;IAEX,MAAM,mBAAmB,IAAI,aAAa,kBAAkB,KAAA,GAAQ,kBAAkB,QAAQ;IAC9F,MAAM,iBAAiB,IAAI,aAAa,gBAAgB,KAAA,GAAQ,gBAAgB,QAAQ;IAExF,IAAI,UAAU,MAAM;QAGd,IAAA,MAAM,OAAA,CAAQ,QAAQ,GAAG;YAC3B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC3C,QAAQ,MAAA,CAAO,CAAC,CAAA;gBACA,gBAAA,QAAA,CAAS,MAAM,aAAuB,CAAA;gBAEtD,QAAQ,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAK;gBACvC,MAAA,KAAK,GAAA,CAAI,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;gBAE3E,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;oBACpC,IAAA,MAAM,IAAA,CAAK,CAAC;oBACZ,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;oBAChB,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;oBAEpB,+BACE,QACA,eACA,mBACA,eACA,sBACA,GACA,GACA,GACA;oBAGF,+BACE,QACA,eACA,iBACA,aACA,sBACA,GACA,GACA,GACA;gBAEJ;YACF;QAAA,OACK;YACL,QAAQ,KAAK,GAAA,CAAI,GAAG,UAAU,KAAK;YACnC,MAAM,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;YAE7D,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;gBACpC,IAAA,MAAM,IAAA,CAAK,CAAC;gBACZ,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;gBAChB,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;gBAEpB,+BACE,QACA,UACA,mBACA,eACA,sBACA,GACA,GACA,GACA;gBAGF,+BACE,QACA,UACA,iBACA,aACA,sBACA,GACA,GACA,GACA;YAEJ;QACF;IAAA,OAAA,IACS,sBAAsB,KAAA,GAAW;QAGtC,IAAA,MAAM,OAAA,CAAQ,QAAQ,GAAG;YAC3B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC3C,QAAQ,MAAA,CAAO,CAAC,CAAA;gBACA,gBAAA,QAAA,CAAS,MAAM,aAAuB,CAAA;gBAEtD,QAAQ,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAK;gBACvC,MAAA,KAAK,GAAA,CAAI,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;gBAE3E,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;oBACpC,IAAA;oBACJ,IAAI,IAAI;oBACR,IAAI,IAAI;oBAER,+BACE,QACA,eACA,mBACA,eACA,sBACA,GACA,GACA,GACA;oBAGF,+BACE,QACA,eACA,iBACA,aACA,sBACA,GACA,GACA,GACA;gBAEJ;YACF;QAAA,OACK;YACL,QAAQ,KAAK,GAAA,CAAI,GAAG,UAAU,KAAK;YACnC,MAAM,KAAK,GAAA,CAAI,kBAAkB,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;YAEzE,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;gBACpC,IAAA;gBACJ,IAAI,IAAI;gBACR,IAAI,IAAI;gBAER,+BACE,QACA,UACA,mBACA,eACA,sBACA,GACA,GACA,GACA;gBAGF,+BACE,QACA,UACA,iBACA,aACA,sBACA,GACA,GACA,GACA;YAEJ;QACF;IACF;IAEA,MAAM,2BAA2B,uJAAI,yBAAA,CAAuB,kBAAkB,CAAC;IAC/E,MAAM,yBAAyB,IAAI,4KAAA,CAAuB,gBAAgB,CAAC;IAEpE,OAAA;QACL;QACA;QACA;QACA;IAAA;AAEJ;AAiBO,SAAS,iBAAiB,QAAA,EAA0B,cAAc,KAAK,EAAA,GAAK,CAAA,EAAoC;IAC/G,MAAA,YAAY,KAAK,GAAA,CAAI,WAAW;IAChC,MAAA,iBAAA,CAAkB,IAAI,KAAA,IAAS;IAG/B,MAAA,QAAQ;QAAC,IAAI,6JAAA,CAAQ;QAAG,uJAAI,UAAA;QAAW,uJAAI,UAAA,CAAA,CAAS;KAAA;IACpD,MAAA,WAAW,uJAAI,UAAA;IACf,MAAA,WAAW,uJAAI,UAAA;IACf,MAAA,WAAW,IAAI,6JAAA;IACf,MAAA,YAAY,uJAAI,UAAA;IAGtB,SAAS,WAAW,CAAA,EAAoB;QACtC,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACnB,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACnB,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACZ,OAAA,GAAG,EAAA,CAAA,EAAK,EAAA,CAAA,EAAK,GAAA;IACtB;IAEA,MAAM,iBAAiB,SAAS,KAAA,GAAQ,SAAS,YAAA,CAAiB,IAAA;IAC5D,MAAA,UAAU,eAAe,UAAA,CAAW,QAAA;IAC1C,MAAM,YAA0C,CAAA;IAGvC,IAAA,IAAA,IAAI,GAAG,IAAI,QAAQ,KAAA,GAAQ,GAAG,IAAI,GAAG,IAAK;QACjD,MAAM,KAAK,IAAI;QACf,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QAE7C,SAAA,UAAA,CAAW,GAAG,CAAC;QACf,SAAA,UAAA,CAAW,GAAG,CAAC;QAGlB,MAAA,SAAS,uJAAI,UAAA,GAAU,YAAA,CAAa,UAAU,QAAQ,EAAE,SAAA;QAC9D,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,MAAA,OAAO,WAAW,IAAI;YACxB,IAAA,CAAA,CAAE,QAAQ,SAAA,GAAY;gBACd,SAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YACpB;YAEU,SAAA,CAAA,IAAI,CAAA,CAAE,IAAA,CAAK,MAAM;QAC7B;IACF;IAIA,MAAM,cAAc,IAAI,aAAa,QAAQ,KAAA,GAAQ,CAAC;IACtD,MAAM,WAAW,IAAI,qKAAA,CAAgB,aAAa,GAAG,KAAK;IACjD,IAAA,IAAA,IAAI,GAAG,IAAI,QAAQ,KAAA,GAAQ,GAAG,IAAI,GAAG,IAAK;QAEjD,MAAM,KAAK,IAAI;QACf,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QAE7C,SAAA,UAAA,CAAW,GAAG,CAAC;QACf,SAAA,UAAA,CAAW,GAAG,CAAC;QAExB,SAAS,YAAA,CAAa,UAAU,QAAQ,EAAE,SAAA,CAAU;QAGpD,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,MAAA,OAAO,WAAW,IAAI;YACtB,MAAA,eAAe,SAAA,CAAU,IAAI,CAAA;YACzB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YAErB,IAAA,IAAS,IAAI,GAAG,KAAK,aAAa,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC/C,MAAA,YAAY,YAAA,CAAa,CAAC,CAAA;gBAChC,IAAI,SAAS,GAAA,CAAI,SAAS,IAAI,WAAW;oBACvC,UAAU,GAAA,CAAI,SAAS;gBACzB;YACF;YAEA,UAAU,SAAA,CAAU;YACX,SAAA,MAAA,CAAO,KAAK,GAAG,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC;QAC/D;IACF;IAEe,eAAA,YAAA,CAAa,UAAU,QAAQ;IACvC,OAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/_polyfill/constants.ts"], "sourcesContent": ["import { REVISION } from 'three'\n\nexport const version = /* @__PURE__ */ (() => parseInt(REVISION.replace(/\\D+/g, '')))()\n"], "names": [], "mappings": ";;;;;AAEa,MAAA,UAAA,aAAA,GAAA,CAAA,IAAiC,4JAAS,WAAA,CAAS,OAAA,CAAQ,QAAQ,EAAE,CAAC,CAAA,EAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "file": "LoaderUtils.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/_polyfill/LoaderUtils.js"], "sourcesContent": ["export function decodeText(array) {\n  if (typeof TextDecoder !== 'undefined') {\n    return new TextDecoder().decode(array)\n  }\n\n  // Avoid the String.fromCharCode.apply(null, array) shortcut, which\n  // throws a \"maximum call stack size exceeded\" error for large arrays.\n\n  let s = ''\n\n  for (let i = 0, il = array.length; i < il; i++) {\n    // Implicitly assumes little-endian.\n    s += String.fromCharCode(array[i])\n  }\n\n  try {\n    // merges multi-byte utf-8 characters.\n\n    return decodeURIComponent(escape(s))\n  } catch (e) {\n    // see https://github.com/mrdoob/three.js/issues/16358\n\n    return s\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,WAAW,KAAA,EAAO;IAChC,IAAI,OAAO,gBAAgB,aAAa;QACtC,OAAO,IAAI,YAAW,EAAG,MAAA,CAAO,KAAK;IACtC;IAKD,IAAI,IAAI;IAER,IAAA,IAAS,IAAI,GAAG,KAAK,MAAM,MAAA,EAAQ,IAAI,IAAI,IAAK;QAE9C,KAAK,OAAO,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC;IAClC;IAED,IAAI;QAGF,OAAO,mBAAmB,OAAO,CAAC,CAAC;IACpC,EAAA,OAAQ,GAAP;QAGA,OAAO;IACR;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "file": "GLTFLoader.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/loaders/GLTFLoader.js"], "sourcesContent": ["import {\n  AnimationClip,\n  Bone,\n  Box3,\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DirectionalLight,\n  DoubleSide,\n  FileLoader,\n  FrontSide,\n  Group,\n  ImageBitmapLoader,\n  InstancedMesh,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  Interpolant,\n  InterpolateDiscrete,\n  InterpolateLinear,\n  Line,\n  LineBasicMaterial,\n  LineLoop,\n  LineSegments,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  LinearMipmapNearestFilter,\n  Loader,\n  LoaderUtils,\n  Material,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshPhysicalMaterial,\n  MeshStandardMaterial,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  NearestMipmapLinearFilter,\n  NearestMipmapNearestFilter,\n  NumberKeyframeTrack,\n  Object3D,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PointLight,\n  Points,\n  PointsMaterial,\n  PropertyBinding,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Skeleton,\n  SkinnedMesh,\n  Sphere,\n  SpotLight,\n  Texture,\n  TextureLoader,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  Vector2,\n  Vector3,\n  VectorKeyframeTrack,\n  InstancedBufferAttribute,\n} from 'three'\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils'\nimport { version } from '../_polyfill/constants'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nconst SRGBColorSpace = 'srgb'\nconst LinearSRGBColorSpace = 'srgb-linear'\nconst sRGBEncoding = 3001\nconst LinearEncoding = 3000\n\nclass GLTFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.dracoLoader = null\n    this.ktx2Loader = null\n    this.meshoptDecoder = null\n\n    this.pluginCallbacks = []\n\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsDispersionExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsBumpExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser)\n    })\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    let resourcePath\n\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath\n    } else if (this.path !== '') {\n      // If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n      // Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n      // resourcePath = 'https://my-cnd-server.com/assets/models/'\n      // referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n      // referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n      const relativeUrl = LoaderUtils.extractUrlBase(url)\n      resourcePath = LoaderUtils.resolveURL(relativeUrl, this.path)\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url)\n    }\n\n    // Tells the LoadingManager to track an extra item, which resolves after\n    // the model is fully loaded. This means the count of items loaded will\n    // be incorrect, but ensures manager.onLoad() does not fire early.\n    this.manager.itemStart(url)\n\n    const _onError = function (e) {\n      if (onError) {\n        onError(e)\n      } else {\n        console.error(e)\n      }\n\n      scope.manager.itemError(url)\n      scope.manager.itemEnd(url)\n    }\n\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (data) {\n        try {\n          scope.parse(\n            data,\n            resourcePath,\n            function (gltf) {\n              onLoad(gltf)\n\n              scope.manager.itemEnd(url)\n            },\n            _onError,\n          )\n        } catch (e) {\n          _onError(e)\n        }\n      },\n      onProgress,\n      _onError,\n    )\n  }\n\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader\n    return this\n  }\n\n  setDDSLoader() {\n    throw new Error('THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".')\n  }\n\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader\n    return this\n  }\n\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder\n    return this\n  }\n\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback)\n    }\n\n    return this\n  }\n\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1)\n    }\n\n    return this\n  }\n\n  parse(data, path, onLoad, onError) {\n    let json\n    const extensions = {}\n    const plugins = {}\n\n    if (typeof data === 'string') {\n      json = JSON.parse(data)\n    } else if (data instanceof ArrayBuffer) {\n      const magic = decodeText(new Uint8Array(data.slice(0, 4)))\n\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data)\n        } catch (error) {\n          if (onError) onError(error)\n          return\n        }\n\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content)\n      } else {\n        json = JSON.parse(decodeText(new Uint8Array(data)))\n      }\n    } else {\n      json = data\n    }\n\n    if (json.asset === undefined || json.asset.version[0] < 2) {\n      if (onError) onError(new Error('THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.'))\n      return\n    }\n\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || '',\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder,\n    })\n\n    parser.fileLoader.setRequestHeader(this.requestHeader)\n\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser)\n\n      if (!plugin.name) console.error('THREE.GLTFLoader: Invalid plugin found: missing name')\n\n      plugins[plugin.name] = plugin\n\n      // Workaround to avoid determining as unknown extension\n      // in addUnknownExtensionsToUserData().\n      // Remove this workaround if we move all the existing\n      // extension handlers to plugin system\n      extensions[plugin.name] = true\n    }\n\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i]\n        const extensionsRequired = json.extensionsRequired || []\n\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension()\n            break\n\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader)\n            break\n\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension()\n            break\n\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension()\n            break\n\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === undefined) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".')\n            }\n        }\n      }\n    }\n\n    parser.setExtensions(extensions)\n    parser.setPlugins(plugins)\n    parser.parse(onLoad, onError)\n  }\n\n  parseAsync(data, path) {\n    const scope = this\n\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject)\n    })\n  }\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n  let objects = {}\n\n  return {\n    get: function (key) {\n      return objects[key]\n    },\n\n    add: function (key, object) {\n      objects[key] = object\n    },\n\n    remove: function (key) {\n      delete objects[key]\n    },\n\n    removeAll: function () {\n      objects = {}\n    },\n  }\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: 'KHR_binary_glTF',\n  KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n  KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n  KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n  KHR_MATERIALS_DISPERSION: 'KHR_materials_dispersion',\n  KHR_MATERIALS_IOR: 'KHR_materials_ior',\n  KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n  KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n  KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n  KHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n  KHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n  KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n  KHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n  KHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n  KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n  KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n  KHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n  EXT_MATERIALS_BUMP: 'EXT_materials_bump',\n  EXT_TEXTURE_WEBP: 'EXT_texture_webp',\n  EXT_TEXTURE_AVIF: 'EXT_texture_avif',\n  EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n  EXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing',\n}\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL\n\n    // Object3D instance caches\n    this.cache = { refs: {}, uses: {} }\n  }\n\n  _markDefs() {\n    const parser = this.parser\n    const nodeDefs = this.parser.json.nodes || []\n\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex]\n\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== undefined) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light)\n      }\n    }\n  }\n\n  _loadLight(lightIndex) {\n    const parser = this.parser\n    const cacheKey = 'light:' + lightIndex\n    let dependency = parser.cache.get(cacheKey)\n\n    if (dependency) return dependency\n\n    const json = parser.json\n    const extensions = (json.extensions && json.extensions[this.name]) || {}\n    const lightDefs = extensions.lights || []\n    const lightDef = lightDefs[lightIndex]\n    let lightNode\n\n    const color = new Color(0xffffff)\n\n    if (lightDef.color !== undefined)\n      color.setRGB(lightDef.color[0], lightDef.color[1], lightDef.color[2], LinearSRGBColorSpace)\n\n    const range = lightDef.range !== undefined ? lightDef.range : 0\n\n    switch (lightDef.type) {\n      case 'directional':\n        lightNode = new DirectionalLight(color)\n        lightNode.target.position.set(0, 0, -1)\n        lightNode.add(lightNode.target)\n        break\n\n      case 'point':\n        lightNode = new PointLight(color)\n        lightNode.distance = range\n        break\n\n      case 'spot':\n        lightNode = new SpotLight(color)\n        lightNode.distance = range\n        // Handle spotlight properties.\n        lightDef.spot = lightDef.spot || {}\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0\n        lightDef.spot.outerConeAngle =\n          lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0\n        lightNode.angle = lightDef.spot.outerConeAngle\n        lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle\n        lightNode.target.position.set(0, 0, -1)\n        lightNode.add(lightNode.target)\n        break\n\n      default:\n        throw new Error('THREE.GLTFLoader: Unexpected light type: ' + lightDef.type)\n    }\n\n    // Some lights (e.g. spot) default to a position other than the origin. Reset the position\n    // here, because node-level parsing will only override position if explicitly specified.\n    lightNode.position.set(0, 0, 0)\n\n    lightNode.decay = 2\n\n    assignExtrasToUserData(lightNode, lightDef)\n\n    if (lightDef.intensity !== undefined) lightNode.intensity = lightDef.intensity\n\n    lightNode.name = parser.createUniqueName(lightDef.name || 'light_' + lightIndex)\n\n    dependency = Promise.resolve(lightNode)\n\n    parser.cache.add(cacheKey, dependency)\n\n    return dependency\n  }\n\n  getDependency(type, index) {\n    if (type !== 'light') return\n\n    return this._loadLight(index)\n  }\n\n  createNodeAttachment(nodeIndex) {\n    const self = this\n    const parser = this.parser\n    const json = parser.json\n    const nodeDef = json.nodes[nodeIndex]\n    const lightDef = (nodeDef.extensions && nodeDef.extensions[this.name]) || {}\n    const lightIndex = lightDef.light\n\n    if (lightIndex === undefined) return null\n\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self.cache, lightIndex, light)\n    })\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT\n  }\n\n  getMaterialType() {\n    return MeshBasicMaterial\n  }\n\n  extendParams(materialParams, materialDef, parser) {\n    const pending = []\n\n    materialParams.color = new Color(1.0, 1.0, 1.0)\n    materialParams.opacity = 1.0\n\n    const metallicRoughness = materialDef.pbrMetallicRoughness\n\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor\n\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace)\n        materialParams.opacity = array[3]\n      }\n\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace))\n      }\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength\n\n    if (emissiveStrength !== undefined) {\n      materialParams.emissiveIntensity = emissiveStrength\n    }\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.clearcoatFactor !== undefined) {\n      materialParams.clearcoat = extension.clearcoatFactor\n    }\n\n    if (extension.clearcoatTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatMap', extension.clearcoatTexture))\n    }\n\n    if (extension.clearcoatRoughnessFactor !== undefined) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor\n    }\n\n    if (extension.clearcoatRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture))\n    }\n\n    if (extension.clearcoatNormalTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture))\n\n      if (extension.clearcoatNormalTexture.scale !== undefined) {\n        const scale = extension.clearcoatNormalTexture.scale\n\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale)\n      }\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials dispersion Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_dispersion\n */\nclass GLTFMaterialsDispersionExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_DISPERSION\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.dispersion = extension.dispersion !== undefined ? extension.dispersion : 0\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.iridescenceFactor !== undefined) {\n      materialParams.iridescence = extension.iridescenceFactor\n    }\n\n    if (extension.iridescenceTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceMap', extension.iridescenceTexture))\n    }\n\n    if (extension.iridescenceIor !== undefined) {\n      materialParams.iridescenceIOR = extension.iridescenceIor\n    }\n\n    if (materialParams.iridescenceThicknessRange === undefined) {\n      materialParams.iridescenceThicknessRange = [100, 400]\n    }\n\n    if (extension.iridescenceThicknessMinimum !== undefined) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum\n    }\n\n    if (extension.iridescenceThicknessMaximum !== undefined) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum\n    }\n\n    if (extension.iridescenceThicknessTexture !== undefined) {\n      pending.push(\n        parser.assignTexture(materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture),\n      )\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    materialParams.sheenColor = new Color(0, 0, 0)\n    materialParams.sheenRoughness = 0\n    materialParams.sheen = 1\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.sheenColorFactor !== undefined) {\n      const colorFactor = extension.sheenColorFactor\n      materialParams.sheenColor.setRGB(colorFactor[0], colorFactor[1], colorFactor[2], LinearSRGBColorSpace)\n    }\n\n    if (extension.sheenRoughnessFactor !== undefined) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor\n    }\n\n    if (extension.sheenColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace))\n    }\n\n    if (extension.sheenRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.transmissionFactor !== undefined) {\n      materialParams.transmission = extension.transmissionFactor\n    }\n\n    if (extension.transmissionTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'transmissionMap', extension.transmissionTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0\n\n    if (extension.thicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'thicknessMap', extension.thicknessTexture))\n    }\n\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity\n\n    const colorArray = extension.attenuationColor || [1, 1, 1]\n    materialParams.attenuationColor = new Color().setRGB(\n      colorArray[0],\n      colorArray[1],\n      colorArray[2],\n      LinearSRGBColorSpace,\n    )\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0\n\n    if (extension.specularTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularIntensityMap', extension.specularTexture))\n    }\n\n    const colorArray = extension.specularColorFactor || [1, 1, 1]\n    materialParams.specularColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace)\n\n    if (extension.specularColorTexture !== undefined) {\n      pending.push(\n        parser.assignTexture(materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace),\n      )\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n */\nclass GLTFMaterialsBumpExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_MATERIALS_BUMP\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0\n\n    if (extension.bumpTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'bumpMap', extension.bumpTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.anisotropyStrength !== undefined) {\n      materialParams.anisotropy = extension.anisotropyStrength\n    }\n\n    if (extension.anisotropyRotation !== undefined) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation\n    }\n\n    if (extension.anisotropyTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'anisotropyMap', extension.anisotropyTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU\n  }\n\n  loadTexture(textureIndex) {\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[this.name]\n    const loader = parser.options.ktx2Loader\n\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error('THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures')\n      } else {\n        // Assumes that the extension is optional and that a fallback texture is present\n        return null\n      }\n    }\n\n    return parser.loadTextureImage(textureIndex, extension.source, loader)\n  }\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP\n    this.isSupported = null\n  }\n\n  loadTexture(textureIndex) {\n    const name = this.name\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[name]\n    const source = json.images[extension.source]\n\n    let loader = parser.textureLoader\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader)\n\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: WebP required by asset but unsupported.')\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex)\n    })\n  }\n\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image()\n\n        // Lossy test image. Support for lossy images doesn't guarantee support for all\n        // WebP images, unfortunately.\n        image.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'\n\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1)\n        }\n      })\n    }\n\n    return this.isSupported\n  }\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n */\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF\n    this.isSupported = null\n  }\n\n  loadTexture(textureIndex) {\n    const name = this.name\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[name]\n    const source = json.images[extension.source]\n\n    let loader = parser.textureLoader\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader)\n\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: AVIF required by asset but unsupported.')\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex)\n    })\n  }\n\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image()\n\n        // Lossy test image.\n        image.src =\n          'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI='\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1)\n        }\n      })\n    }\n\n    return this.isSupported\n  }\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n */\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION\n    this.parser = parser\n  }\n\n  loadBufferView(index) {\n    const json = this.parser.json\n    const bufferView = json.bufferViews[index]\n\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name]\n\n      const buffer = this.parser.getDependency('buffer', extensionDef.buffer)\n      const decoder = this.parser.options.meshoptDecoder\n\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error('THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files')\n        } else {\n          // Assumes that the extension is optional and that fallback buffer data is present\n          return null\n        }\n      }\n\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0\n        const byteLength = extensionDef.byteLength || 0\n\n        const count = extensionDef.count\n        const stride = extensionDef.byteStride\n\n        const source = new Uint8Array(res, byteOffset, byteLength)\n\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder\n            .decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter)\n            .then(function (res) {\n              return res.buffer\n            })\n        } else {\n          // Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride)\n            decoder.decodeGltfBuffer(\n              new Uint8Array(result),\n              count,\n              stride,\n              source,\n              extensionDef.mode,\n              extensionDef.filter,\n            )\n            return result\n          })\n        }\n      })\n    } else {\n      return null\n    }\n  }\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n */\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING\n    this.parser = parser\n  }\n\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json\n    const nodeDef = json.nodes[nodeIndex]\n\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === undefined) {\n      return null\n    }\n\n    const meshDef = json.meshes[nodeDef.mesh]\n\n    // No Points or Lines + Instancing support yet\n\n    for (const primitive of meshDef.primitives) {\n      if (\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLES &&\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP &&\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN &&\n        primitive.mode !== undefined\n      ) {\n        return null\n      }\n    }\n\n    const extensionDef = nodeDef.extensions[this.name]\n    const attributesDef = extensionDef.attributes\n\n    // @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n    const pending = []\n    const attributes = {}\n\n    for (const key in attributesDef) {\n      pending.push(\n        this.parser.getDependency('accessor', attributesDef[key]).then((accessor) => {\n          attributes[key] = accessor\n          return attributes[key]\n        }),\n      )\n    }\n\n    if (pending.length < 1) {\n      return null\n    }\n\n    pending.push(this.parser.createNodeMesh(nodeIndex))\n\n    return Promise.all(pending).then((results) => {\n      const nodeObject = results.pop()\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject]\n      const count = results[0].count // All attribute counts should be same\n      const instancedMeshes = []\n\n      for (const mesh of meshes) {\n        // Temporal variables\n        const m = new Matrix4()\n        const p = new Vector3()\n        const q = new Quaternion()\n        const s = new Vector3(1, 1, 1)\n\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count)\n\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i)\n          }\n\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i)\n          }\n\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i)\n          }\n\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s))\n        }\n\n        // Add instance attributes to the geometry, excluding TRS.\n        for (const attributeName in attributes) {\n          if (attributeName === '_COLOR_0') {\n            const attr = attributes[attributeName]\n            instancedMesh.instanceColor = new InstancedBufferAttribute(attr.array, attr.itemSize, attr.normalized)\n          } else if (attributeName !== 'TRANSLATION' && attributeName !== 'ROTATION' && attributeName !== 'SCALE') {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName])\n          }\n        }\n\n        // Just in case\n        Object3D.prototype.copy.call(instancedMesh, mesh)\n\n        this.parser.assignFinalMaterial(instancedMesh)\n\n        instancedMeshes.push(instancedMesh)\n      }\n\n      if (nodeObject.isGroup) {\n        nodeObject.clear()\n\n        nodeObject.add(...instancedMeshes)\n\n        return nodeObject\n      }\n\n      return instancedMeshes[0]\n    })\n  }\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF'\nconst BINARY_EXTENSION_HEADER_LENGTH = 12\nconst BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4e4f534a, BIN: 0x004e4942 }\n\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF\n    this.content = null\n    this.body = null\n\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH)\n\n    this.header = {\n      magic: decodeText(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true),\n    }\n\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error('THREE.GLTFLoader: Unsupported glTF-Binary header.')\n    } else if (this.header.version < 2.0) {\n      throw new Error('THREE.GLTFLoader: Legacy binary file detected.')\n    }\n\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH)\n    let chunkIndex = 0\n\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true)\n      chunkIndex += 4\n\n      const chunkType = chunkView.getUint32(chunkIndex, true)\n      chunkIndex += 4\n\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength)\n        this.content = decodeText(contentArray)\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex\n        this.body = data.slice(byteOffset, byteOffset + chunkLength)\n      }\n\n      // Clients must ignore chunks with unknown types.\n\n      chunkIndex += chunkLength\n    }\n\n    if (this.content === null) {\n      throw new Error('THREE.GLTFLoader: JSON content not found.')\n    }\n  }\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error('THREE.GLTFLoader: No DRACOLoader instance provided.')\n    }\n\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION\n    this.json = json\n    this.dracoLoader = dracoLoader\n    this.dracoLoader.preload()\n  }\n\n  decodePrimitive(primitive, parser) {\n    const json = this.json\n    const dracoLoader = this.dracoLoader\n    const bufferViewIndex = primitive.extensions[this.name].bufferView\n    const gltfAttributeMap = primitive.extensions[this.name].attributes\n    const threeAttributeMap = {}\n    const attributeNormalizedMap = {}\n    const attributeTypeMap = {}\n\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase()\n\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName]\n    }\n\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase()\n\n      if (gltfAttributeMap[attributeName] !== undefined) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]]\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n\n        attributeTypeMap[threeAttributeName] = componentType.name\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true\n      }\n    }\n\n    return parser.getDependency('bufferView', bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve, reject) {\n        dracoLoader.decodeDracoFile(\n          bufferView,\n          function (geometry) {\n            for (const attributeName in geometry.attributes) {\n              const attribute = geometry.attributes[attributeName]\n              const normalized = attributeNormalizedMap[attributeName]\n\n              if (normalized !== undefined) attribute.normalized = normalized\n            }\n\n            resolve(geometry)\n          },\n          threeAttributeMap,\n          attributeTypeMap,\n          LinearSRGBColorSpace,\n          reject,\n        )\n      })\n    })\n  }\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n */\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM\n  }\n\n  extendTexture(texture, transform) {\n    if (\n      (transform.texCoord === undefined || transform.texCoord === texture.channel) &&\n      transform.offset === undefined &&\n      transform.rotation === undefined &&\n      transform.scale === undefined\n    ) {\n      // See https://github.com/mrdoob/three.js/issues/21819.\n      return texture\n    }\n\n    texture = texture.clone()\n\n    if (transform.texCoord !== undefined) {\n      texture.channel = transform.texCoord\n    }\n\n    if (transform.offset !== undefined) {\n      texture.offset.fromArray(transform.offset)\n    }\n\n    if (transform.rotation !== undefined) {\n      texture.rotation = transform.rotation\n    }\n\n    if (transform.scale !== undefined) {\n      texture.repeat.fromArray(transform.scale)\n    }\n\n    texture.needsUpdate = true\n\n    return texture\n  }\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n */\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION\n  }\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer)\n  }\n\n  copySampleValue_(index) {\n    // Copies a sample value to the result buffer. See description of glTF\n    // CUBICSPLINE values layout in interpolate_() function below.\n\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize\n\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i]\n    }\n\n    return result\n  }\n\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer\n    const values = this.sampleValues\n    const stride = this.valueSize\n\n    const stride2 = stride * 2\n    const stride3 = stride * 3\n\n    const td = t1 - t0\n\n    const p = (t - t0) / td\n    const pp = p * p\n    const ppp = pp * p\n\n    const offset1 = i1 * stride3\n    const offset0 = offset1 - stride3\n\n    const s2 = -2 * ppp + 3 * pp\n    const s3 = ppp - pp\n    const s0 = 1 - s2\n    const s1 = s3 - pp + p\n\n    // Layout of keyframe output values for CUBICSPLINE animations:\n    //   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride] // splineVertex_k\n      const m0 = values[offset0 + i + stride2] * td // outTangent_k * (t_k+1 - t_k)\n      const p1 = values[offset1 + i + stride] // splineVertex_k+1\n      const m1 = values[offset1 + i] * td // inTangent_k+1 * (t_k+1 - t_k)\n\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1\n    }\n\n    return result\n  }\n}\n\nconst _q = /* @__PURE__ */ new Quaternion()\n\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1)\n\n    _q.fromArray(result).normalize().toArray(result)\n\n    return result\n  }\n}\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123,\n}\n\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array,\n}\n\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter,\n}\n\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping,\n}\n\nconst WEBGL_TYPE_SIZES = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16,\n}\n\nconst ATTRIBUTES = {\n  POSITION: 'position',\n  NORMAL: 'normal',\n  TANGENT: 'tangent',\n  // uv => uv1, 4 uv channels\n  // https://github.com/mrdoob/three.js/pull/25943\n  // https://github.com/mrdoob/three.js/pull/25788\n  ...(version >= 152\n    ? {\n        TEXCOORD_0: 'uv',\n        TEXCOORD_1: 'uv1',\n        TEXCOORD_2: 'uv2',\n        TEXCOORD_3: 'uv3',\n      }\n    : {\n        TEXCOORD_0: 'uv',\n        TEXCOORD_1: 'uv2',\n      }),\n\n  COLOR_0: 'color',\n  WEIGHTS_0: 'skinWeight',\n  JOINTS_0: 'skinIndex',\n}\n\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  translation: 'position',\n  rotation: 'quaternion',\n  weights: 'morphTargetInfluences',\n}\n\nconst INTERPOLATION = {\n  CUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete,\n}\n\nconst ALPHA_MODES = {\n  OPAQUE: 'OPAQUE',\n  MASK: 'MASK',\n  BLEND: 'BLEND',\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n */\nfunction createDefaultMaterial(cache) {\n  if (cache['DefaultMaterial'] === undefined) {\n    cache['DefaultMaterial'] = new MeshStandardMaterial({\n      color: 0xffffff,\n      emissive: 0x000000,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide,\n    })\n  }\n\n  return cache['DefaultMaterial']\n}\n\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  // Add unknown glTF extensions to an object's userData.\n\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === undefined) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {}\n      object.userData.gltfExtensions[name] = objectDef.extensions[name]\n    }\n  }\n}\n\n/**\n * @param {Object3D|Material|BufferGeometry} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== undefined) {\n    if (typeof gltfDef.extras === 'object') {\n      Object.assign(object.userData, gltfDef.extras)\n    } else {\n      console.warn('THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras)\n    }\n  }\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false\n  let hasMorphNormal = false\n  let hasMorphColor = false\n\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i]\n\n    if (target.POSITION !== undefined) hasMorphPosition = true\n    if (target.NORMAL !== undefined) hasMorphNormal = true\n    if (target.COLOR_0 !== undefined) hasMorphColor = true\n\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break\n  }\n\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry)\n\n  const pendingPositionAccessors = []\n  const pendingNormalAccessors = []\n  const pendingColorAccessors = []\n\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i]\n\n    if (hasMorphPosition) {\n      const pendingAccessor =\n        target.POSITION !== undefined ? parser.getDependency('accessor', target.POSITION) : geometry.attributes.position\n\n      pendingPositionAccessors.push(pendingAccessor)\n    }\n\n    if (hasMorphNormal) {\n      const pendingAccessor =\n        target.NORMAL !== undefined ? parser.getDependency('accessor', target.NORMAL) : geometry.attributes.normal\n\n      pendingNormalAccessors.push(pendingAccessor)\n    }\n\n    if (hasMorphColor) {\n      const pendingAccessor =\n        target.COLOR_0 !== undefined ? parser.getDependency('accessor', target.COLOR_0) : geometry.attributes.color\n\n      pendingColorAccessors.push(pendingAccessor)\n    }\n  }\n\n  return Promise.all([\n    Promise.all(pendingPositionAccessors),\n    Promise.all(pendingNormalAccessors),\n    Promise.all(pendingColorAccessors),\n  ]).then(function (accessors) {\n    const morphPositions = accessors[0]\n    const morphNormals = accessors[1]\n    const morphColors = accessors[2]\n\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors\n    geometry.morphTargetsRelative = true\n\n    return geometry\n  })\n}\n\n/**\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets()\n\n  if (meshDef.weights !== undefined) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i]\n    }\n  }\n\n  // .extras has user-defined data, so check that .extras.targetNames is an array.\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames\n\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {}\n\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.')\n    }\n  }\n}\n\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey\n\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]\n\n  if (dracoExtension) {\n    geometryKey =\n      'draco:' +\n      dracoExtension.bufferView +\n      ':' +\n      dracoExtension.indices +\n      ':' +\n      createAttributesKey(dracoExtension.attributes)\n  } else {\n    geometryKey = primitiveDef.indices + ':' + createAttributesKey(primitiveDef.attributes) + ':' + primitiveDef.mode\n  }\n\n  if (primitiveDef.targets !== undefined) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += ':' + createAttributesKey(primitiveDef.targets[i])\n    }\n  }\n\n  return geometryKey\n}\n\nfunction createAttributesKey(attributes) {\n  let attributesKey = ''\n\n  const keys = Object.keys(attributes).sort()\n\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + ':' + attributes[keys[i]] + ';'\n  }\n\n  return attributesKey\n}\n\nfunction getNormalizedComponentScale(constructor) {\n  // Reference:\n  // https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127\n\n    case Uint8Array:\n      return 1 / 255\n\n    case Int16Array:\n      return 1 / 32767\n\n    case Uint16Array:\n      return 1 / 65535\n\n    default:\n      throw new Error('THREE.GLTFLoader: Unsupported normalized accessor component type.')\n  }\n}\n\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return 'image/jpeg'\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return 'image/webp'\n\n  return 'image/png'\n}\n\nconst _identityMatrix = /* @__PURE__ */ new Matrix4()\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json\n    this.extensions = {}\n    this.plugins = {}\n    this.options = options\n\n    // loader object cache\n    this.cache = new GLTFRegistry()\n\n    // associations between Three.js objects and glTF elements\n    this.associations = new Map()\n\n    // BufferGeometry caching\n    this.primitiveCache = {}\n\n    // Node cache\n    this.nodeCache = {}\n\n    // Object3D instance caches\n    this.meshCache = { refs: {}, uses: {} }\n    this.cameraCache = { refs: {}, uses: {} }\n    this.lightCache = { refs: {}, uses: {} }\n\n    this.sourceCache = {}\n    this.textureCache = {}\n\n    // Track node names, to ensure no duplicates\n    this.nodeNamesUsed = {}\n\n    // Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n    // expensive work of uploading a texture to the GPU off the main thread.\n\n    let isSafari = false\n    let isFirefox = false\n    let firefoxVersion = -1\n\n    if (typeof navigator !== 'undefined' && typeof navigator.userAgent !== 'undefined') {\n      isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) === true\n      isFirefox = navigator.userAgent.indexOf('Firefox') > -1\n      firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1\n    }\n\n    if (typeof createImageBitmap === 'undefined' || isSafari || (isFirefox && firefoxVersion < 98)) {\n      this.textureLoader = new TextureLoader(this.options.manager)\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager)\n    }\n\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin)\n    this.textureLoader.setRequestHeader(this.options.requestHeader)\n\n    this.fileLoader = new FileLoader(this.options.manager)\n    this.fileLoader.setResponseType('arraybuffer')\n\n    if (this.options.crossOrigin === 'use-credentials') {\n      this.fileLoader.setWithCredentials(true)\n    }\n  }\n\n  setExtensions(extensions) {\n    this.extensions = extensions\n  }\n\n  setPlugins(plugins) {\n    this.plugins = plugins\n  }\n\n  parse(onLoad, onError) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n\n    // Clear the loader cache\n    this.cache.removeAll()\n    this.nodeCache = {}\n\n    // Mark the special nodes/meshes in json for efficient parse\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs()\n    })\n\n    Promise.all(\n      this._invokeAll(function (ext) {\n        return ext.beforeRoot && ext.beforeRoot()\n      }),\n    )\n      .then(function () {\n        return Promise.all([\n          parser.getDependencies('scene'),\n          parser.getDependencies('animation'),\n          parser.getDependencies('camera'),\n        ])\n      })\n      .then(function (dependencies) {\n        const result = {\n          scene: dependencies[0][json.scene || 0],\n          scenes: dependencies[0],\n          animations: dependencies[1],\n          cameras: dependencies[2],\n          asset: json.asset,\n          parser: parser,\n          userData: {},\n        }\n\n        addUnknownExtensionsToUserData(extensions, result, json)\n\n        assignExtrasToUserData(result, json)\n\n        return Promise.all(\n          parser._invokeAll(function (ext) {\n            return ext.afterRoot && ext.afterRoot(result)\n          }),\n        ).then(function () {\n          for (const scene of result.scenes) {\n            scene.updateMatrixWorld()\n          }\n\n          onLoad(result)\n        })\n      })\n      .catch(onError)\n  }\n\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || []\n    const skinDefs = this.json.skins || []\n    const meshDefs = this.json.meshes || []\n\n    // Nothing in the node definition indicates whether it is a Bone or an\n    // Object3D. Use the skins' joint references to mark bones.\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints\n\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true\n      }\n    }\n\n    // Iterate over all nodes, marking references to shared resources,\n    // as well as skeleton joints.\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex]\n\n      if (nodeDef.mesh !== undefined) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh)\n\n        // Nothing in the mesh definition indicates whether it is\n        // a SkinnedMesh or Mesh. Use the node's mesh reference\n        // to mark SkinnedMesh if node has skin.\n        if (nodeDef.skin !== undefined) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true\n        }\n      }\n\n      if (nodeDef.camera !== undefined) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera)\n      }\n    }\n  }\n\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   */\n  _addNodeRef(cache, index) {\n    if (index === undefined) return\n\n    if (cache.refs[index] === undefined) {\n      cache.refs[index] = cache.uses[index] = 0\n    }\n\n    cache.refs[index]++\n  }\n\n  /** Returns a reference to a shared resource, cloning it if necessary. */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object\n\n    const ref = object.clone()\n\n    // Propagates mappings to the cloned object, prevents mappings on the\n    // original object from being lost.\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original)\n      if (mappings != null) {\n        this.associations.set(clone, mappings)\n      }\n\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i])\n      }\n    }\n\n    updateMappings(object, ref)\n\n    ref.name += '_instance_' + cache.uses[index]++\n\n    return ref\n  }\n\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins)\n    extensions.push(this)\n\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i])\n\n      if (result) return result\n    }\n\n    return null\n  }\n\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins)\n    extensions.unshift(this)\n\n    const pending = []\n\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i])\n\n      if (result) pending.push(result)\n    }\n\n    return pending\n  }\n\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + ':' + index\n    let dependency = this.cache.get(cacheKey)\n\n    if (!dependency) {\n      switch (type) {\n        case 'scene':\n          dependency = this.loadScene(index)\n          break\n\n        case 'node':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index)\n          })\n          break\n\n        case 'mesh':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index)\n          })\n          break\n\n        case 'accessor':\n          dependency = this.loadAccessor(index)\n          break\n\n        case 'bufferView':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index)\n          })\n          break\n\n        case 'buffer':\n          dependency = this.loadBuffer(index)\n          break\n\n        case 'material':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index)\n          })\n          break\n\n        case 'texture':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index)\n          })\n          break\n\n        case 'skin':\n          dependency = this.loadSkin(index)\n          break\n\n        case 'animation':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index)\n          })\n          break\n\n        case 'camera':\n          dependency = this.loadCamera(index)\n          break\n\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index)\n          })\n\n          if (!dependency) {\n            throw new Error('Unknown type: ' + type)\n          }\n\n          break\n      }\n\n      this.cache.add(cacheKey, dependency)\n    }\n\n    return dependency\n  }\n\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type)\n\n    if (!dependencies) {\n      const parser = this\n      const defs = this.json[type + (type === 'mesh' ? 'es' : 's')] || []\n\n      dependencies = Promise.all(\n        defs.map(function (def, index) {\n          return parser.getDependency(type, index)\n        }),\n      )\n\n      this.cache.add(type, dependencies)\n    }\n\n    return dependencies\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex]\n    const loader = this.fileLoader\n\n    if (bufferDef.type && bufferDef.type !== 'arraybuffer') {\n      throw new Error('THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.')\n    }\n\n    // If present, GLB container is required to be the first buffer.\n    if (bufferDef.uri === undefined && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body)\n    }\n\n    const options = this.options\n\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, undefined, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'))\n      })\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex]\n\n    return this.getDependency('buffer', bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0\n      const byteOffset = bufferViewDef.byteOffset || 0\n      return buffer.slice(byteOffset, byteOffset + byteLength)\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this\n    const json = this.json\n\n    const accessorDef = this.json.accessors[accessorIndex]\n\n    if (accessorDef.bufferView === undefined && accessorDef.sparse === undefined) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type]\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n      const normalized = accessorDef.normalized === true\n\n      const array = new TypedArray(accessorDef.count * itemSize)\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized))\n    }\n\n    const pendingBufferViews = []\n\n    if (accessorDef.bufferView !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.bufferView))\n    } else {\n      pendingBufferViews.push(null)\n    }\n\n    if (accessorDef.sparse !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.indices.bufferView))\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.values.bufferView))\n    }\n\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0]\n\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type]\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n\n      // For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT\n      const itemBytes = elementBytes * itemSize\n      const byteOffset = accessorDef.byteOffset || 0\n      const byteStride =\n        accessorDef.bufferView !== undefined ? json.bufferViews[accessorDef.bufferView].byteStride : undefined\n      const normalized = accessorDef.normalized === true\n      let array, bufferAttribute\n\n      // The buffer is not interleaved if the stride is the item size in bytes.\n      if (byteStride && byteStride !== itemBytes) {\n        // Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n        // This makes sure that IBA.count reflects accessor.count properly\n        const ibSlice = Math.floor(byteOffset / byteStride)\n        const ibCacheKey =\n          'InterleavedBuffer:' +\n          accessorDef.bufferView +\n          ':' +\n          accessorDef.componentType +\n          ':' +\n          ibSlice +\n          ':' +\n          accessorDef.count\n        let ib = parser.cache.get(ibCacheKey)\n\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, (accessorDef.count * byteStride) / elementBytes)\n\n          // Integer parameters to IB/IBA are in array elements, not bytes.\n          ib = new InterleavedBuffer(array, byteStride / elementBytes)\n\n          parser.cache.add(ibCacheKey, ib)\n        }\n\n        bufferAttribute = new InterleavedBufferAttribute(\n          ib,\n          itemSize,\n          (byteOffset % byteStride) / elementBytes,\n          normalized,\n        )\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize)\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize)\n        }\n\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized)\n      }\n\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n      if (accessorDef.sparse !== undefined) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType]\n\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0\n\n        const sparseIndices = new TypedArrayIndices(\n          bufferViews[1],\n          byteOffsetIndices,\n          accessorDef.sparse.count * itemSizeIndices,\n        )\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize)\n\n        if (bufferView !== null) {\n          // Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n          bufferAttribute = new BufferAttribute(\n            bufferAttribute.array.slice(),\n            bufferAttribute.itemSize,\n            bufferAttribute.normalized,\n          )\n        }\n\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i]\n\n          bufferAttribute.setX(index, sparseValues[i * itemSize])\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1])\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2])\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3])\n          if (itemSize >= 5) throw new Error('THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.')\n        }\n      }\n\n      return bufferAttribute\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json\n    const options = this.options\n    const textureDef = json.textures[textureIndex]\n    const sourceIndex = textureDef.source\n    const sourceDef = json.images[sourceIndex]\n\n    let loader = this.textureLoader\n\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.loadTextureImage(textureIndex, sourceIndex, loader)\n  }\n\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this\n    const json = this.json\n\n    const textureDef = json.textures[textureIndex]\n    const sourceDef = json.images[sourceIndex]\n\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + ':' + textureDef.sampler\n\n    if (this.textureCache[cacheKey]) {\n      // See https://github.com/mrdoob/three.js/issues/21559.\n      return this.textureCache[cacheKey]\n    }\n\n    const promise = this.loadImageSource(sourceIndex, loader)\n      .then(function (texture) {\n        texture.flipY = false\n\n        texture.name = textureDef.name || sourceDef.name || ''\n\n        if (\n          texture.name === '' &&\n          typeof sourceDef.uri === 'string' &&\n          sourceDef.uri.startsWith('data:image/') === false\n        ) {\n          texture.name = sourceDef.uri\n        }\n\n        const samplers = json.samplers || {}\n        const sampler = samplers[textureDef.sampler] || {}\n\n        texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter\n        texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter\n        texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping\n        texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping\n\n        parser.associations.set(texture, { textures: textureIndex })\n\n        return texture\n      })\n      .catch(function () {\n        return null\n      })\n\n    this.textureCache[cacheKey] = promise\n\n    return promise\n  }\n\n  loadImageSource(sourceIndex, loader) {\n    const parser = this\n    const json = this.json\n    const options = this.options\n\n    if (this.sourceCache[sourceIndex] !== undefined) {\n      return this.sourceCache[sourceIndex].then((texture) => texture.clone())\n    }\n\n    const sourceDef = json.images[sourceIndex]\n\n    const URL = self.URL || self.webkitURL\n\n    let sourceURI = sourceDef.uri || ''\n    let isObjectURL = false\n\n    if (sourceDef.bufferView !== undefined) {\n      // Load binary image data from bufferView, if provided.\n\n      sourceURI = parser.getDependency('bufferView', sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true\n        const blob = new Blob([bufferView], { type: sourceDef.mimeType })\n        sourceURI = URL.createObjectURL(blob)\n        return sourceURI\n      })\n    } else if (sourceDef.uri === undefined) {\n      throw new Error('THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView')\n    }\n\n    const promise = Promise.resolve(sourceURI)\n      .then(function (sourceURI) {\n        return new Promise(function (resolve, reject) {\n          let onLoad = resolve\n\n          if (loader.isImageBitmapLoader === true) {\n            onLoad = function (imageBitmap) {\n              const texture = new Texture(imageBitmap)\n              texture.needsUpdate = true\n\n              resolve(texture)\n            }\n          }\n\n          loader.load(LoaderUtils.resolveURL(sourceURI, options.path), onLoad, undefined, reject)\n        })\n      })\n      .then(function (texture) {\n        // Clean up resources and configure Texture.\n\n        if (isObjectURL === true) {\n          URL.revokeObjectURL(sourceURI)\n        }\n\n        assignExtrasToUserData(texture, sourceDef)\n\n        texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri)\n\n        return texture\n      })\n      .catch(function (error) {\n        console.error(\"THREE.GLTFLoader: Couldn't load texture\", sourceURI)\n        throw error\n      })\n\n    this.sourceCache[sourceIndex] = promise\n    return promise\n  }\n\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this\n\n    return this.getDependency('texture', mapDef.index).then(function (texture) {\n      if (!texture) return null\n\n      if (mapDef.texCoord !== undefined && mapDef.texCoord > 0) {\n        texture = texture.clone()\n        texture.channel = mapDef.texCoord\n      }\n\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform =\n          mapDef.extensions !== undefined ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : undefined\n\n        if (transform) {\n          const gltfReference = parser.associations.get(texture)\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform)\n          parser.associations.set(texture, gltfReference)\n        }\n      }\n\n      if (colorSpace !== undefined) {\n        // Convert from legacy encoding to colorSpace\n        if (typeof colorSpace === 'number')\n          colorSpace = colorSpace === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace\n\n        // Set colorSpace if able, fallback to legacy encoding\n        if ('colorSpace' in texture) texture.colorSpace = colorSpace\n        else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n      }\n\n      materialParams[mapName] = texture\n\n      return texture\n    })\n  }\n\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   * @param  {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry\n    let material = mesh.material\n\n    const useDerivativeTangents = geometry.attributes.tangent === undefined\n    const useVertexColors = geometry.attributes.color !== undefined\n    const useFlatShading = geometry.attributes.normal === undefined\n\n    if (mesh.isPoints) {\n      const cacheKey = 'PointsMaterial:' + material.uuid\n\n      let pointsMaterial = this.cache.get(cacheKey)\n\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial()\n        Material.prototype.copy.call(pointsMaterial, material)\n        pointsMaterial.color.copy(material.color)\n        pointsMaterial.map = material.map\n        pointsMaterial.sizeAttenuation = false // glTF spec says points should be 1px\n\n        this.cache.add(cacheKey, pointsMaterial)\n      }\n\n      material = pointsMaterial\n    } else if (mesh.isLine) {\n      const cacheKey = 'LineBasicMaterial:' + material.uuid\n\n      let lineMaterial = this.cache.get(cacheKey)\n\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial()\n        Material.prototype.copy.call(lineMaterial, material)\n        lineMaterial.color.copy(material.color)\n        lineMaterial.map = material.map\n\n        this.cache.add(cacheKey, lineMaterial)\n      }\n\n      material = lineMaterial\n    }\n\n    // Clone the material if it will be modified\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = 'ClonedMaterial:' + material.uuid + ':'\n\n      if (useDerivativeTangents) cacheKey += 'derivative-tangents:'\n      if (useVertexColors) cacheKey += 'vertex-colors:'\n      if (useFlatShading) cacheKey += 'flat-shading:'\n\n      let cachedMaterial = this.cache.get(cacheKey)\n\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone()\n\n        if (useVertexColors) cachedMaterial.vertexColors = true\n        if (useFlatShading) cachedMaterial.flatShading = true\n\n        if (useDerivativeTangents) {\n          // https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1\n        }\n\n        this.cache.add(cacheKey, cachedMaterial)\n\n        this.associations.set(cachedMaterial, this.associations.get(material))\n      }\n\n      material = cachedMaterial\n    }\n\n    mesh.material = material\n  }\n\n  getMaterialType(/* materialIndex */) {\n    return MeshStandardMaterial\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n    const materialDef = json.materials[materialIndex]\n\n    let materialType\n    const materialParams = {}\n    const materialExtensions = materialDef.extensions || {}\n\n    const pending = []\n\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT]\n      materialType = kmuExtension.getMaterialType()\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser))\n    } else {\n      // Specification:\n      // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {}\n\n      materialParams.color = new Color(1.0, 1.0, 1.0)\n      materialParams.opacity = 1.0\n\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor\n\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace)\n        materialParams.opacity = array[3]\n      }\n\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace))\n      }\n\n      materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0\n      materialParams.roughness =\n        metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0\n\n      if (metallicRoughness.metallicRoughnessTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture))\n        pending.push(parser.assignTexture(materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture))\n      }\n\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex)\n      })\n\n      pending.push(\n        Promise.all(\n          this._invokeAll(function (ext) {\n            return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams)\n          }),\n        ),\n      )\n    }\n\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide\n    }\n\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE\n\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true\n\n      // See: https://github.com/mrdoob/three.js/issues/17706\n      materialParams.depthWrite = false\n    } else {\n      materialParams.transparent = false\n\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5\n      }\n    }\n\n    if (materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'normalMap', materialDef.normalTexture))\n\n      materialParams.normalScale = new Vector2(1, 1)\n\n      if (materialDef.normalTexture.scale !== undefined) {\n        const scale = materialDef.normalTexture.scale\n\n        materialParams.normalScale.set(scale, scale)\n      }\n    }\n\n    if (materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'aoMap', materialDef.occlusionTexture))\n\n      if (materialDef.occlusionTexture.strength !== undefined) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength\n      }\n    }\n\n    if (materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial) {\n      const emissiveFactor = materialDef.emissiveFactor\n      materialParams.emissive = new Color().setRGB(\n        emissiveFactor[0],\n        emissiveFactor[1],\n        emissiveFactor[2],\n        LinearSRGBColorSpace,\n      )\n    }\n\n    if (materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace))\n    }\n\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams)\n\n      if (materialDef.name) material.name = materialDef.name\n\n      assignExtrasToUserData(material, materialDef)\n\n      parser.associations.set(material, { materials: materialIndex })\n\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef)\n\n      return material\n    })\n  }\n\n  /** When Object3D instances are targeted by animation, they need unique names. */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || '')\n\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + '_' + ++this.nodeNamesUsed[sanitizedName]\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0\n\n      return sanitizedName\n    }\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this\n    const extensions = this.extensions\n    const cache = this.primitiveCache\n\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]\n        .decodePrimitive(primitive, parser)\n        .then(function (geometry) {\n          return addPrimitiveAttributes(geometry, primitive, parser)\n        })\n    }\n\n    const pending = []\n\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i]\n      const cacheKey = createPrimitiveKey(primitive)\n\n      // See if we've already created this geometry\n      const cached = cache[cacheKey]\n\n      if (cached) {\n        // Use the cached geometry if it exists\n        pending.push(cached.promise)\n      } else {\n        let geometryPromise\n\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          // Use DRACO geometry if available\n          geometryPromise = createDracoPrimitive(primitive)\n        } else {\n          // Otherwise create a new geometry\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser)\n        }\n\n        // Cache this geometry\n        cache[cacheKey] = { primitive: primitive, promise: geometryPromise }\n\n        pending.push(geometryPromise)\n      }\n    }\n\n    return Promise.all(pending)\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n\n    const meshDef = json.meshes[meshIndex]\n    const primitives = meshDef.primitives\n\n    const pending = []\n\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material =\n        primitives[i].material === undefined\n          ? createDefaultMaterial(this.cache)\n          : this.getDependency('material', primitives[i].material)\n\n      pending.push(material)\n    }\n\n    pending.push(parser.loadGeometries(primitives))\n\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1)\n      const geometries = results[results.length - 1]\n\n      const meshes = []\n\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i]\n        const primitive = primitives[i]\n\n        // 1. create Mesh\n\n        let mesh\n\n        const material = materials[i]\n\n        if (\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||\n          primitive.mode === undefined\n        ) {\n          // .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material)\n\n          if (mesh.isSkinnedMesh === true) {\n            // normalize skin weights to fix malformed assets (see #15319)\n            mesh.normalizeSkinWeights()\n          }\n\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode)\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode)\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material)\n        } else {\n          throw new Error('THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode)\n        }\n\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef)\n        }\n\n        mesh.name = parser.createUniqueName(meshDef.name || 'mesh_' + meshIndex)\n\n        assignExtrasToUserData(mesh, meshDef)\n\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive)\n\n        parser.assignFinalMaterial(mesh)\n\n        meshes.push(mesh)\n      }\n\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i,\n        })\n      }\n\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef)\n\n        return meshes[0]\n      }\n\n      const group = new Group()\n\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef)\n\n      parser.associations.set(group, { meshes: meshIndex })\n\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i])\n      }\n\n      return group\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera\n    const cameraDef = this.json.cameras[cameraIndex]\n    const params = cameraDef[cameraDef.type]\n\n    if (!params) {\n      console.warn('THREE.GLTFLoader: Missing camera parameters.')\n      return\n    }\n\n    if (cameraDef.type === 'perspective') {\n      camera = new PerspectiveCamera(\n        MathUtils.radToDeg(params.yfov),\n        params.aspectRatio || 1,\n        params.znear || 1,\n        params.zfar || 2e6,\n      )\n    } else if (cameraDef.type === 'orthographic') {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar)\n    }\n\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name)\n\n    assignExtrasToUserData(camera, cameraDef)\n\n    return Promise.resolve(camera)\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex]\n\n    const pending = []\n\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]))\n    }\n\n    if (skinDef.inverseBindMatrices !== undefined) {\n      pending.push(this.getDependency('accessor', skinDef.inverseBindMatrices))\n    } else {\n      pending.push(null)\n    }\n\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop()\n      const jointNodes = results\n\n      // Note that bones (joint nodes) may or may not be in the\n      // scene graph at this time.\n\n      const bones = []\n      const boneInverses = []\n\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i]\n\n        if (jointNode) {\n          bones.push(jointNode)\n\n          const mat = new Matrix4()\n\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16)\n          }\n\n          boneInverses.push(mat)\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i])\n        }\n      }\n\n      return new Skeleton(bones, boneInverses)\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json\n    const parser = this\n\n    const animationDef = json.animations[animationIndex]\n    const animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex\n\n    const pendingNodes = []\n    const pendingInputAccessors = []\n    const pendingOutputAccessors = []\n    const pendingSamplers = []\n    const pendingTargets = []\n\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i]\n      const sampler = animationDef.samplers[channel.sampler]\n      const target = channel.target\n      const name = target.node\n      const input = animationDef.parameters !== undefined ? animationDef.parameters[sampler.input] : sampler.input\n      const output = animationDef.parameters !== undefined ? animationDef.parameters[sampler.output] : sampler.output\n\n      if (target.node === undefined) continue\n\n      pendingNodes.push(this.getDependency('node', name))\n      pendingInputAccessors.push(this.getDependency('accessor', input))\n      pendingOutputAccessors.push(this.getDependency('accessor', output))\n      pendingSamplers.push(sampler)\n      pendingTargets.push(target)\n    }\n\n    return Promise.all([\n      Promise.all(pendingNodes),\n      Promise.all(pendingInputAccessors),\n      Promise.all(pendingOutputAccessors),\n      Promise.all(pendingSamplers),\n      Promise.all(pendingTargets),\n    ]).then(function (dependencies) {\n      const nodes = dependencies[0]\n      const inputAccessors = dependencies[1]\n      const outputAccessors = dependencies[2]\n      const samplers = dependencies[3]\n      const targets = dependencies[4]\n\n      const tracks = []\n\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i]\n        const inputAccessor = inputAccessors[i]\n        const outputAccessor = outputAccessors[i]\n        const sampler = samplers[i]\n        const target = targets[i]\n\n        if (node === undefined) continue\n\n        if (node.updateMatrix) {\n          node.updateMatrix()\n        }\n\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target)\n\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k])\n          }\n        }\n      }\n\n      return new AnimationClip(animationName, undefined, tracks)\n    })\n  }\n\n  createNodeMesh(nodeIndex) {\n    const json = this.json\n    const parser = this\n    const nodeDef = json.nodes[nodeIndex]\n\n    if (nodeDef.mesh === undefined) return null\n\n    return parser.getDependency('mesh', nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh)\n\n      // if weights are provided on the node, override weights on the mesh.\n      if (nodeDef.weights !== undefined) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return\n\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i]\n          }\n        })\n      }\n\n      return node\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json\n    const parser = this\n\n    const nodeDef = json.nodes[nodeIndex]\n\n    const nodePending = parser._loadNodeShallow(nodeIndex)\n\n    const childPending = []\n    const childrenDef = nodeDef.children || []\n\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency('node', childrenDef[i]))\n    }\n\n    const skeletonPending =\n      nodeDef.skin === undefined ? Promise.resolve(null) : parser.getDependency('skin', nodeDef.skin)\n\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0]\n      const children = results[1]\n      const skeleton = results[2]\n\n      if (skeleton !== null) {\n        // This full traverse should be fine because\n        // child glTF nodes have not been added to this node yet.\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return\n\n          mesh.bind(skeleton, _identityMatrix)\n        })\n      }\n\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i])\n      }\n\n      return node\n    })\n  }\n\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json\n    const extensions = this.extensions\n    const parser = this\n\n    // This method is called from .loadNode() and .loadSkin().\n    // Cache a node to avoid duplication.\n\n    if (this.nodeCache[nodeIndex] !== undefined) {\n      return this.nodeCache[nodeIndex]\n    }\n\n    const nodeDef = json.nodes[nodeIndex]\n\n    // reserve node's name before its dependencies, so the root has the intended name.\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : ''\n\n    const pending = []\n\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex)\n    })\n\n    if (meshPromise) {\n      pending.push(meshPromise)\n    }\n\n    if (nodeDef.camera !== undefined) {\n      pending.push(\n        parser.getDependency('camera', nodeDef.camera).then(function (camera) {\n          return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera)\n        }),\n      )\n    }\n\n    parser\n      ._invokeAll(function (ext) {\n        return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex)\n      })\n      .forEach(function (promise) {\n        pending.push(promise)\n      })\n\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node\n\n      // .isBone isn't in glTF spec. See ._markDefs\n      if (nodeDef.isBone === true) {\n        node = new Bone()\n      } else if (objects.length > 1) {\n        node = new Group()\n      } else if (objects.length === 1) {\n        node = objects[0]\n      } else {\n        node = new Object3D()\n      }\n\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i])\n        }\n      }\n\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name\n        node.name = nodeName\n      }\n\n      assignExtrasToUserData(node, nodeDef)\n\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef)\n\n      if (nodeDef.matrix !== undefined) {\n        const matrix = new Matrix4()\n        matrix.fromArray(nodeDef.matrix)\n        node.applyMatrix4(matrix)\n      } else {\n        if (nodeDef.translation !== undefined) {\n          node.position.fromArray(nodeDef.translation)\n        }\n\n        if (nodeDef.rotation !== undefined) {\n          node.quaternion.fromArray(nodeDef.rotation)\n        }\n\n        if (nodeDef.scale !== undefined) {\n          node.scale.fromArray(nodeDef.scale)\n        }\n      }\n\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {})\n      }\n\n      parser.associations.get(node).nodes = nodeIndex\n\n      return node\n    })\n\n    return this.nodeCache[nodeIndex]\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions\n    const sceneDef = this.json.scenes[sceneIndex]\n    const parser = this\n\n    // Loader returns Group, not Scene.\n    // See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n    const scene = new Group()\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name)\n\n    assignExtrasToUserData(scene, sceneDef)\n\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef)\n\n    const nodeIds = sceneDef.nodes || []\n\n    const pending = []\n\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency('node', nodeIds[i]))\n    }\n\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i])\n      }\n\n      // Removes dangling associations, associations that reference a node that\n      // didn't make it into the scene.\n      const reduceAssociations = (node) => {\n        const reducedAssociations = new Map()\n\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value)\n          }\n        }\n\n        node.traverse((node) => {\n          const mappings = parser.associations.get(node)\n\n          if (mappings != null) {\n            reducedAssociations.set(node, mappings)\n          }\n        })\n\n        return reducedAssociations\n      }\n\n      parser.associations = reduceAssociations(scene)\n\n      return scene\n    })\n  }\n\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = []\n\n    const targetName = node.name ? node.name : node.uuid\n    const targetNames = []\n\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid)\n        }\n      })\n    } else {\n      targetNames.push(targetName)\n    }\n\n    let TypedKeyframeTrack\n\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack\n        break\n\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack\n        break\n\n      case PATH_PROPERTIES.position:\n      case PATH_PROPERTIES.scale:\n        TypedKeyframeTrack = VectorKeyframeTrack\n        break\n\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack\n            break\n          case 2:\n          case 3:\n          default:\n            TypedKeyframeTrack = VectorKeyframeTrack\n            break\n        }\n\n        break\n    }\n\n    const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[sampler.interpolation] : InterpolateLinear\n\n    const outputArray = this._getArrayFromAccessor(outputAccessor)\n\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(\n        targetNames[j] + '.' + PATH_PROPERTIES[target.path],\n        inputAccessor.array,\n        outputArray,\n        interpolation,\n      )\n\n      // Override interpolation with custom factory method.\n      if (sampler.interpolation === 'CUBICSPLINE') {\n        this._createCubicSplineTrackInterpolant(track)\n      }\n\n      tracks.push(track)\n    }\n\n    return tracks\n  }\n\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array\n\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor)\n      const scaled = new Float32Array(outputArray.length)\n\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale\n      }\n\n      outputArray = scaled\n    }\n\n    return outputArray\n  }\n\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      // A CUBICSPLINE keyframe in glTF has three output values for each input value,\n      // representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n      // must be divided by three to get the interpolant's sampleSize argument.\n\n      const interpolantType =\n        this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant\n\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result)\n    }\n\n    // Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true\n  }\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes\n\n  const box = new Box3()\n\n  if (attributes.POSITION !== undefined) {\n    const accessor = parser.json.accessors[attributes.POSITION]\n\n    const min = accessor.min\n    const max = accessor.max\n\n    // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n    if (min !== undefined && max !== undefined) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]))\n\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType])\n        box.min.multiplyScalar(boxScale)\n        box.max.multiplyScalar(boxScale)\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.')\n\n      return\n    }\n  } else {\n    return\n  }\n\n  const targets = primitiveDef.targets\n\n  if (targets !== undefined) {\n    const maxDisplacement = new Vector3()\n    const vector = new Vector3()\n\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i]\n\n      if (target.POSITION !== undefined) {\n        const accessor = parser.json.accessors[target.POSITION]\n        const min = accessor.min\n        const max = accessor.max\n\n        // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n        if (min !== undefined && max !== undefined) {\n          // we need to get max of absolute components because target weight is [-1,1]\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])))\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])))\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])))\n\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType])\n            vector.multiplyScalar(boxScale)\n          }\n\n          // Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n          // to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n          // are used to implement key-frame animations and as such only two are active at a time - this results in very large\n          // boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n          maxDisplacement.max(vector)\n        } else {\n          console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.')\n        }\n      }\n    }\n\n    // As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n    box.expandByVector(maxDisplacement)\n  }\n\n  geometry.boundingBox = box\n\n  const sphere = new Sphere()\n\n  box.getCenter(sphere.center)\n  sphere.radius = box.min.distanceTo(box.max) / 2\n\n  geometry.boundingSphere = sphere\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes\n\n  const pending = []\n\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency('accessor', accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor)\n    })\n  }\n\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase()\n\n    // Skip attributes already provided by e.g. Draco extension.\n    if (threeAttributeName in geometry.attributes) continue\n\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName))\n  }\n\n  if (primitiveDef.indices !== undefined && !geometry.index) {\n    const accessor = parser.getDependency('accessor', primitiveDef.indices).then(function (accessor) {\n      geometry.setIndex(accessor)\n    })\n\n    pending.push(accessor)\n  }\n\n  assignExtrasToUserData(geometry, primitiveDef)\n\n  computeBounds(geometry, primitiveDef, parser)\n\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== undefined ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry\n  })\n}\n\nexport { GLTFLoader }\n"], "names": ["self", "res", "sourceURI", "node", "accessor"], "mappings": ";;;;;;;;;;;AAoEA,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAC7B,MAAM,eAAe;AACrB,MAAM,iBAAiB;AAEvB,MAAM,sKAAmB,SAAA,CAAO;IAC9B,YAAY,OAAA,CAAS;QACnB,KAAA,CAAM,OAAO;QAEb,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,cAAA,GAAiB;QAEtB,IAAA,CAAK,eAAA,GAAkB,CAAE,CAAA;QAEzB,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,gCAAgC,MAAM;QACvD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,iCAAiC,MAAM;QACxD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,2BAA2B,MAAM;QAClD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,yBAAyB,MAAM;QAChD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,yBAAyB,MAAM;QAChD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,4BAA4B,MAAM;QACnD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,mCAAmC,MAAM;QAC1D,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,6BAA6B,MAAM;QACpD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,0BAA0B,MAAM;QACjD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,uCAAuC,MAAM;QAC9D,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,+BAA+B,MAAM;QACtD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,kCAAkC,MAAM;QACzD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,iCAAiC,MAAM;QACxD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,2BAA2B,MAAM;QAClD,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,oBAAoB,MAAM;QAC3C,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,uBAAuB,MAAM;QAC9C,CAAK;QAED,IAAA,CAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;YAC9B,OAAO,IAAI,sBAAsB,MAAM;QAC7C,CAAK;IACF;IAED,KAAK,GAAA,EAAK,MAAA,EAAQ,UAAA,EAAY,OAAA,EAAS;QACrC,MAAM,QAAQ,IAAA;QAEd,IAAI;QAEJ,IAAI,IAAA,CAAK,YAAA,KAAiB,IAAI;YAC5B,eAAe,IAAA,CAAK,YAAA;QAC1B,OAAA,IAAe,IAAA,CAAK,IAAA,KAAS,IAAI;YAM3B,MAAM,iKAAc,cAAA,CAAY,cAAA,CAAe,GAAG;YAClD,kKAAe,cAAA,CAAY,UAAA,CAAW,aAAa,IAAA,CAAK,IAAI;QAClE,OAAW;YACL,kKAAe,cAAA,CAAY,cAAA,CAAe,GAAG;QAC9C;QAKD,IAAA,CAAK,OAAA,CAAQ,SAAA,CAAU,GAAG;QAE1B,MAAM,WAAW,SAAU,CAAA,EAAG;YAC5B,IAAI,SAAS;gBACX,QAAQ,CAAC;YACjB,OAAa;gBACL,QAAQ,KAAA,CAAM,CAAC;YAChB;YAED,MAAM,OAAA,CAAQ,SAAA,CAAU,GAAG;YAC3B,MAAM,OAAA,CAAQ,OAAA,CAAQ,GAAG;QAC1B;QAED,MAAM,SAAS,IAAI,gKAAA,CAAW,IAAA,CAAK,OAAO;QAE1C,OAAO,OAAA,CAAQ,IAAA,CAAK,IAAI;QACxB,OAAO,eAAA,CAAgB,aAAa;QACpC,OAAO,gBAAA,CAAiB,IAAA,CAAK,aAAa;QAC1C,OAAO,kBAAA,CAAmB,IAAA,CAAK,eAAe;QAE9C,OAAO,IAAA,CACL,KACA,SAAU,IAAA,EAAM;YACd,IAAI;gBACF,MAAM,KAAA,CACJ,MACA,cACA,SAAU,IAAA,EAAM;oBACd,OAAO,IAAI;oBAEX,MAAM,OAAA,CAAQ,OAAA,CAAQ,GAAG;gBAC1B,GACD;YAEH,EAAA,OAAQ,GAAP;gBACA,SAAS,CAAC;YACX;QACF,GACD,YACA;IAEH;IAED,eAAe,WAAA,EAAa;QAC1B,IAAA,CAAK,WAAA,GAAc;QACnB,OAAO,IAAA;IACR;IAED,eAAe;QACb,MAAM,IAAI,MAAM,kGAAkG;IACnH;IAED,cAAc,UAAA,EAAY;QACxB,IAAA,CAAK,UAAA,GAAa;QAClB,OAAO,IAAA;IACR;IAED,kBAAkB,cAAA,EAAgB;QAChC,IAAA,CAAK,cAAA,GAAiB;QACtB,OAAO,IAAA;IACR;IAED,SAAS,QAAA,EAAU;QACjB,IAAI,IAAA,CAAK,eAAA,CAAgB,OAAA,CAAQ,QAAQ,MAAM,CAAA,GAAI;YACjD,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,QAAQ;QACnC;QAED,OAAO,IAAA;IACR;IAED,WAAW,QAAA,EAAU;QACnB,IAAI,IAAA,CAAK,eAAA,CAAgB,OAAA,CAAQ,QAAQ,MAAM,CAAA,GAAI;YACjD,IAAA,CAAK,eAAA,CAAgB,MAAA,CAAO,IAAA,CAAK,eAAA,CAAgB,OAAA,CAAQ,QAAQ,GAAG,CAAC;QACtE;QAED,OAAO,IAAA;IACR;IAED,MAAM,IAAA,EAAM,IAAA,EAAM,MAAA,EAAQ,OAAA,EAAS;QACjC,IAAI;QACJ,MAAM,aAAa,CAAE;QACrB,MAAM,UAAU,CAAE;QAElB,IAAI,OAAO,SAAS,UAAU;YAC5B,OAAO,KAAK,KAAA,CAAM,IAAI;QAC5B,OAAA,IAAe,gBAAgB,aAAa;YACtC,MAAM,2KAAQ,aAAA,EAAW,IAAI,WAAW,KAAK,KAAA,CAAM,GAAG,CAAC,CAAC,CAAC;YAEzD,IAAI,UAAU,+BAA+B;gBAC3C,IAAI;oBACF,UAAA,CAAW,WAAW,eAAe,CAAA,GAAI,IAAI,oBAAoB,IAAI;gBACtE,EAAA,OAAQ,OAAP;oBACA,IAAI,SAAS,QAAQ,KAAK;oBAC1B;gBACD;gBAED,OAAO,KAAK,KAAA,CAAM,UAAA,CAAW,WAAW,eAAe,CAAA,CAAE,OAAO;YACxE,OAAa;gBACL,OAAO,KAAK,KAAA,EAAM,+KAAA,EAAW,IAAI,WAAW,IAAI,CAAC,CAAC;YACnD;QACP,OAAW;YACL,OAAO;QACR;QAED,IAAI,KAAK,KAAA,KAAU,KAAA,KAAa,KAAK,KAAA,CAAM,OAAA,CAAQ,CAAC,CAAA,GAAI,GAAG;YACzD,IAAI,SAAS,QAAQ,IAAI,MAAM,yEAAyE,CAAC;YACzG;QACD;QAED,MAAM,SAAS,IAAI,WAAW,MAAM;YAClC,MAAM,QAAQ,IAAA,CAAK,YAAA,IAAgB;YACnC,aAAa,IAAA,CAAK,WAAA;YAClB,eAAe,IAAA,CAAK,aAAA;YACpB,SAAS,IAAA,CAAK,OAAA;YACd,YAAY,IAAA,CAAK,UAAA;YACjB,gBAAgB,IAAA,CAAK,cAAA;QAC3B,CAAK;QAED,OAAO,UAAA,CAAW,gBAAA,CAAiB,IAAA,CAAK,aAAa;QAErD,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,eAAA,CAAgB,MAAA,EAAQ,IAAK;YACpD,MAAM,SAAS,IAAA,CAAK,eAAA,CAAgB,CAAC,CAAA,CAAE,MAAM;YAE7C,IAAI,CAAC,OAAO,IAAA,EAAM,QAAQ,KAAA,CAAM,sDAAsD;YAEtF,OAAA,CAAQ,OAAO,IAAI,CAAA,GAAI;YAMvB,UAAA,CAAW,OAAO,IAAI,CAAA,GAAI;QAC3B;QAED,IAAI,KAAK,cAAA,EAAgB;YACvB,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,cAAA,CAAe,MAAA,EAAQ,EAAE,EAAG;gBACnD,MAAM,gBAAgB,KAAK,cAAA,CAAe,CAAC,CAAA;gBAC3C,MAAM,qBAAqB,KAAK,kBAAA,IAAsB,CAAE,CAAA;gBAExD,OAAQ,eAAa;oBACnB,KAAK,WAAW,mBAAA;wBACd,UAAA,CAAW,aAAa,CAAA,GAAI,IAAI,4BAA6B;wBAC7D;oBAEF,KAAK,WAAW,0BAAA;wBACd,UAAA,CAAW,aAAa,CAAA,GAAI,IAAI,kCAAkC,MAAM,IAAA,CAAK,WAAW;wBACxF;oBAEF,KAAK,WAAW,qBAAA;wBACd,UAAA,CAAW,aAAa,CAAA,GAAI,IAAI,8BAA+B;wBAC/D;oBAEF,KAAK,WAAW,qBAAA;wBACd,UAAA,CAAW,aAAa,CAAA,GAAI,IAAI,8BAA+B;wBAC/D;oBAEF;wBACE,IAAI,mBAAmB,OAAA,CAAQ,aAAa,KAAK,KAAK,OAAA,CAAQ,aAAa,CAAA,KAAM,KAAA,GAAW;4BAC1F,QAAQ,IAAA,CAAK,0CAA0C,gBAAgB,IAAI;wBAC5E;gBACJ;YACF;QACF;QAED,OAAO,aAAA,CAAc,UAAU;QAC/B,OAAO,UAAA,CAAW,OAAO;QACzB,OAAO,KAAA,CAAM,QAAQ,OAAO;IAC7B;IAED,WAAW,IAAA,EAAM,IAAA,EAAM;QACrB,MAAM,QAAQ,IAAA;QAEd,OAAO,IAAI,QAAQ,SAAU,OAAA,EAAS,MAAA,EAAQ;YAC5C,MAAM,KAAA,CAAM,MAAM,MAAM,SAAS,MAAM;QAC7C,CAAK;IACF;AACH;AAIA,SAAS,eAAe;IACtB,IAAI,UAAU,CAAE;IAEhB,OAAO;QACL,KAAK,SAAU,GAAA,EAAK;YAClB,OAAO,OAAA,CAAQ,GAAG,CAAA;QACnB;QAED,KAAK,SAAU,GAAA,EAAK,MAAA,EAAQ;YAC1B,OAAA,CAAQ,GAAG,CAAA,GAAI;QAChB;QAED,QAAQ,SAAU,GAAA,EAAK;YACrB,OAAO,OAAA,CAAQ,GAAG,CAAA;QACnB;QAED,WAAW,WAAY;YACrB,UAAU,CAAE;QACb;IACF;AACH;AAMA,MAAM,aAAa;IACjB,iBAAiB;IACjB,4BAA4B;IAC5B,qBAAqB;IACrB,yBAAyB;IACzB,0BAA0B;IAC1B,mBAAmB;IACnB,qBAAqB;IACrB,wBAAwB;IACxB,4BAA4B;IAC5B,2BAA2B;IAC3B,0BAA0B;IAC1B,qBAAqB;IACrB,sBAAsB;IACtB,oBAAoB;IACpB,uBAAuB;IACvB,uBAAuB;IACvB,iCAAiC;IACjC,oBAAoB;IACpB,kBAAkB;IAClB,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;AAC3B;AAOA,MAAM,oBAAoB;IACxB,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,mBAAA;QAGvB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM,CAAA;YAAI,MAAM,CAAA;QAAI;IACpC;IAED,YAAY;QACV,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,KAAA,IAAS,CAAE,CAAA;QAE7C,IAAA,IAAS,YAAY,GAAG,aAAa,SAAS,MAAA,EAAQ,YAAY,YAAY,YAAa;YACzF,MAAM,UAAU,QAAA,CAAS,SAAS,CAAA;YAElC,IAAI,QAAQ,UAAA,IAAc,QAAQ,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,IAAK,QAAQ,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,CAAE,KAAA,KAAU,KAAA,GAAW;gBAC5G,OAAO,WAAA,CAAY,IAAA,CAAK,KAAA,EAAO,QAAQ,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,CAAE,KAAK;YACnE;QACF;IACF;IAED,WAAW,UAAA,EAAY;QACrB,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,WAAW,WAAW;QAC5B,IAAI,aAAa,OAAO,KAAA,CAAM,GAAA,CAAI,QAAQ;QAE1C,IAAI,YAAY,OAAO;QAEvB,MAAM,OAAO,OAAO,IAAA;QACpB,MAAM,aAAc,KAAK,UAAA,IAAc,KAAK,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,IAAM,CAAE;QACxE,MAAM,YAAY,WAAW,MAAA,IAAU,CAAE,CAAA;QACzC,MAAM,WAAW,SAAA,CAAU,UAAU,CAAA;QACrC,IAAI;QAEJ,MAAM,QAAQ,IAAI,2JAAA,CAAM,QAAQ;QAEhC,IAAI,SAAS,KAAA,KAAU,KAAA,GACrB,MAAM,MAAA,CAAO,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,oBAAoB;QAE5F,MAAM,QAAQ,SAAS,KAAA,KAAU,KAAA,IAAY,SAAS,KAAA,GAAQ;QAE9D,OAAQ,SAAS,IAAA,EAAI;YACnB,KAAK;gBACH,YAAY,uJAAI,mBAAA,CAAiB,KAAK;gBACtC,UAAU,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE;gBACtC,UAAU,GAAA,CAAI,UAAU,MAAM;gBAC9B;YAEF,KAAK;gBACH,YAAY,uJAAI,aAAA,CAAW,KAAK;gBAChC,UAAU,QAAA,GAAW;gBACrB;YAEF,KAAK;gBACH,YAAY,uJAAI,YAAA,CAAU,KAAK;gBAC/B,UAAU,QAAA,GAAW;gBAErB,SAAS,IAAA,GAAO,SAAS,IAAA,IAAQ,CAAE;gBACnC,SAAS,IAAA,CAAK,cAAA,GAAiB,SAAS,IAAA,CAAK,cAAA,KAAmB,KAAA,IAAY,SAAS,IAAA,CAAK,cAAA,GAAiB;gBAC3G,SAAS,IAAA,CAAK,cAAA,GACZ,SAAS,IAAA,CAAK,cAAA,KAAmB,KAAA,IAAY,SAAS,IAAA,CAAK,cAAA,GAAiB,KAAK,EAAA,GAAK;gBACxF,UAAU,KAAA,GAAQ,SAAS,IAAA,CAAK,cAAA;gBAChC,UAAU,QAAA,GAAW,IAAM,SAAS,IAAA,CAAK,cAAA,GAAiB,SAAS,IAAA,CAAK,cAAA;gBACxE,UAAU,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE;gBACtC,UAAU,GAAA,CAAI,UAAU,MAAM;gBAC9B;YAEF;gBACE,MAAM,IAAI,MAAM,8CAA8C,SAAS,IAAI;QAC9E;QAID,UAAU,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAC;QAE9B,UAAU,KAAA,GAAQ;QAElB,uBAAuB,WAAW,QAAQ;QAE1C,IAAI,SAAS,SAAA,KAAc,KAAA,GAAW,UAAU,SAAA,GAAY,SAAS,SAAA;QAErE,UAAU,IAAA,GAAO,OAAO,gBAAA,CAAiB,SAAS,IAAA,IAAQ,WAAW,UAAU;QAE/E,aAAa,QAAQ,OAAA,CAAQ,SAAS;QAEtC,OAAO,KAAA,CAAM,GAAA,CAAI,UAAU,UAAU;QAErC,OAAO;IACR;IAED,cAAc,IAAA,EAAM,KAAA,EAAO;QACzB,IAAI,SAAS,SAAS;QAEtB,OAAO,IAAA,CAAK,UAAA,CAAW,KAAK;IAC7B;IAED,qBAAqB,SAAA,EAAW;QAC9B,MAAMA,QAAO,IAAA;QACb,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,OAAO,OAAO,IAAA;QACpB,MAAM,UAAU,KAAK,KAAA,CAAM,SAAS,CAAA;QACpC,MAAM,WAAY,QAAQ,UAAA,IAAc,QAAQ,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,IAAM,CAAE;QAC5E,MAAM,aAAa,SAAS,KAAA;QAE5B,IAAI,eAAe,KAAA,GAAW,OAAO;QAErC,OAAO,IAAA,CAAK,UAAA,CAAW,UAAU,EAAE,IAAA,CAAK,SAAU,KAAA,EAAO;YACvD,OAAO,OAAO,WAAA,CAAYA,MAAK,KAAA,EAAO,YAAY,KAAK;QAC7D,CAAK;IACF;AACH;AAOA,MAAM,4BAA4B;IAChC,aAAc;QACZ,IAAA,CAAK,IAAA,GAAO,WAAW,mBAAA;IACxB;IAED,kBAAkB;QAChB,0JAAO,oBAAA;IACR;IAED,aAAa,cAAA,EAAgB,WAAA,EAAa,MAAA,EAAQ;QAChD,MAAM,UAAU,CAAE,CAAA;QAElB,eAAe,KAAA,GAAQ,uJAAI,QAAA,CAAM,GAAK,GAAK,CAAG;QAC9C,eAAe,OAAA,GAAU;QAEzB,MAAM,oBAAoB,YAAY,oBAAA;QAEtC,IAAI,mBAAmB;YACrB,IAAI,MAAM,OAAA,CAAQ,kBAAkB,eAAe,GAAG;gBACpD,MAAM,QAAQ,kBAAkB,eAAA;gBAEhC,eAAe,KAAA,CAAM,MAAA,CAAO,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA,EAAG,oBAAoB;gBAC9E,eAAe,OAAA,GAAU,KAAA,CAAM,CAAC,CAAA;YACjC;YAED,IAAI,kBAAkB,gBAAA,KAAqB,KAAA,GAAW;gBACpD,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,OAAO,kBAAkB,gBAAA,EAAkB,cAAc,CAAC;YAC7G;QACF;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,uCAAuC;IAC3C,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,+BAAA;IACxB;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,mBAAmB,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,CAAE,gBAAA;QAE3D,IAAI,qBAAqB,KAAA,GAAW;YAClC,eAAe,iBAAA,GAAoB;QACpC;QAED,OAAO,QAAQ,OAAA,CAAS;IACzB;AACH;AAOA,MAAM,gCAAgC;IACpC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,uBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,IAAI,UAAU,eAAA,KAAoB,KAAA,GAAW;YAC3C,eAAe,SAAA,GAAY,UAAU,eAAA;QACtC;QAED,IAAI,UAAU,gBAAA,KAAqB,KAAA,GAAW;YAC5C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,gBAAgB,UAAU,gBAAgB,CAAC;QAC9F;QAED,IAAI,UAAU,wBAAA,KAA6B,KAAA,GAAW;YACpD,eAAe,kBAAA,GAAqB,UAAU,wBAAA;QAC/C;QAED,IAAI,UAAU,yBAAA,KAA8B,KAAA,GAAW;YACrD,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,yBAAyB,UAAU,yBAAyB,CAAC;QAChH;QAED,IAAI,UAAU,sBAAA,KAA2B,KAAA,GAAW;YAClD,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,sBAAsB,UAAU,sBAAsB,CAAC;YAEzG,IAAI,UAAU,sBAAA,CAAuB,KAAA,KAAU,KAAA,GAAW;gBACxD,MAAM,QAAQ,UAAU,sBAAA,CAAuB,KAAA;gBAE/C,eAAe,oBAAA,GAAuB,uJAAI,UAAA,CAAQ,OAAO,KAAK;YAC/D;QACF;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,iCAAiC;IACrC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,wBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,OAAO,0KAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,eAAe,UAAA,GAAa,UAAU,UAAA,KAAe,KAAA,IAAY,UAAU,UAAA,GAAa;QAExF,OAAO,QAAQ,OAAA,CAAS;IACzB;AACH;AAOA,MAAM,kCAAkC;IACtC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,yBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,IAAI,UAAU,iBAAA,KAAsB,KAAA,GAAW;YAC7C,eAAe,WAAA,GAAc,UAAU,iBAAA;QACxC;QAED,IAAI,UAAU,kBAAA,KAAuB,KAAA,GAAW;YAC9C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,kBAAkB,UAAU,kBAAkB,CAAC;QAClG;QAED,IAAI,UAAU,cAAA,KAAmB,KAAA,GAAW;YAC1C,eAAe,cAAA,GAAiB,UAAU,cAAA;QAC3C;QAED,IAAI,eAAe,yBAAA,KAA8B,KAAA,GAAW;YAC1D,eAAe,yBAAA,GAA4B;gBAAC;gBAAK,GAAG;aAAA;QACrD;QAED,IAAI,UAAU,2BAAA,KAAgC,KAAA,GAAW;YACvD,eAAe,yBAAA,CAA0B,CAAC,CAAA,GAAI,UAAU,2BAAA;QACzD;QAED,IAAI,UAAU,2BAAA,KAAgC,KAAA,GAAW;YACvD,eAAe,yBAAA,CAA0B,CAAC,CAAA,GAAI,UAAU,2BAAA;QACzD;QAED,IAAI,UAAU,2BAAA,KAAgC,KAAA,GAAW;YACvD,QAAQ,IAAA,CACN,OAAO,aAAA,CAAc,gBAAgB,2BAA2B,UAAU,2BAA2B;QAExG;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,4BAA4B;IAChC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,mBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,eAAe,UAAA,GAAa,uJAAI,QAAA,CAAM,GAAG,GAAG,CAAC;QAC7C,eAAe,cAAA,GAAiB;QAChC,eAAe,KAAA,GAAQ;QAEvB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,IAAI,UAAU,gBAAA,KAAqB,KAAA,GAAW;YAC5C,MAAM,cAAc,UAAU,gBAAA;YAC9B,eAAe,UAAA,CAAW,MAAA,CAAO,WAAA,CAAY,CAAC,CAAA,EAAG,WAAA,CAAY,CAAC,CAAA,EAAG,WAAA,CAAY,CAAC,CAAA,EAAG,oBAAoB;QACtG;QAED,IAAI,UAAU,oBAAA,KAAyB,KAAA,GAAW;YAChD,eAAe,cAAA,GAAiB,UAAU,oBAAA;QAC3C;QAED,IAAI,UAAU,iBAAA,KAAsB,KAAA,GAAW;YAC7C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,iBAAiB,UAAU,iBAAA,EAAmB,cAAc,CAAC;QAChH;QAED,IAAI,UAAU,qBAAA,KAA0B,KAAA,GAAW;YACjD,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,qBAAqB,UAAU,qBAAqB,CAAC;QACxG;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAQA,MAAM,mCAAmC;IACvC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,0BAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,IAAI,UAAU,kBAAA,KAAuB,KAAA,GAAW;YAC9C,eAAe,YAAA,GAAe,UAAU,kBAAA;QACzC;QAED,IAAI,UAAU,mBAAA,KAAwB,KAAA,GAAW;YAC/C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,mBAAmB,UAAU,mBAAmB,CAAC;QACpG;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,6BAA6B;IACjC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,oBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,eAAe,SAAA,GAAY,UAAU,eAAA,KAAoB,KAAA,IAAY,UAAU,eAAA,GAAkB;QAEjG,IAAI,UAAU,gBAAA,KAAqB,KAAA,GAAW;YAC5C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,gBAAgB,UAAU,gBAAgB,CAAC;QAC9F;QAED,eAAe,mBAAA,GAAsB,UAAU,mBAAA,IAAuB;QAEtE,MAAM,aAAa,UAAU,gBAAA,IAAoB;YAAC;YAAG;YAAG,CAAC;SAAA;QACzD,eAAe,gBAAA,GAAmB,uJAAI,QAAA,CAAK,EAAG,MAAA,CAC5C,UAAA,CAAW,CAAC,CAAA,EACZ,UAAA,CAAW,CAAC,CAAA,EACZ,UAAA,CAAW,CAAC,CAAA,EACZ;QAGF,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,0BAA0B;IAC9B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,iBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,eAAe,GAAA,GAAM,UAAU,GAAA,KAAQ,KAAA,IAAY,UAAU,GAAA,GAAM;QAEnE,OAAO,QAAQ,OAAA,CAAS;IACzB;AACH;AAOA,MAAM,+BAA+B;IACnC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,sBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,OAAO,0KAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,eAAe,iBAAA,GAAoB,UAAU,cAAA,KAAmB,KAAA,IAAY,UAAU,cAAA,GAAiB;QAEvG,IAAI,UAAU,eAAA,KAAoB,KAAA,GAAW;YAC3C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,wBAAwB,UAAU,eAAe,CAAC;QACrG;QAED,MAAM,aAAa,UAAU,mBAAA,IAAuB;YAAC;YAAG;YAAG,CAAC;SAAA;QAC5D,eAAe,aAAA,GAAgB,uJAAI,QAAA,CAAK,EAAG,MAAA,CAAO,UAAA,CAAW,CAAC,CAAA,EAAG,UAAA,CAAW,CAAC,CAAA,EAAG,UAAA,CAAW,CAAC,CAAA,EAAG,oBAAoB;QAEnH,IAAI,UAAU,oBAAA,KAAyB,KAAA,GAAW;YAChD,QAAQ,IAAA,CACN,OAAO,aAAA,CAAc,gBAAgB,oBAAoB,UAAU,oBAAA,EAAsB,cAAc;QAE1G;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,2BAA2B;IAC/B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,kBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,eAAe,SAAA,GAAY,UAAU,UAAA,KAAe,KAAA,IAAY,UAAU,UAAA,GAAa;QAEvF,IAAI,UAAU,WAAA,KAAgB,KAAA,GAAW;YACvC,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,WAAW,UAAU,WAAW,CAAC;QACpF;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,iCAAiC;IACrC,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,wBAAA;IACxB;IAED,gBAAgB,aAAA,EAAe;QAC7B,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG,OAAO;QAE1E,0JAAO,uBAAA;IACR;IAED,qBAAqB,aAAA,EAAe,cAAA,EAAgB;QAClD,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,cAAc,OAAO,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAEvD,IAAI,CAAC,YAAY,UAAA,IAAc,CAAC,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YACjE,OAAO,QAAQ,OAAA,CAAS;QACzB;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,YAAY,YAAY,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QAElD,IAAI,UAAU,kBAAA,KAAuB,KAAA,GAAW;YAC9C,eAAe,UAAA,GAAa,UAAU,kBAAA;QACvC;QAED,IAAI,UAAU,kBAAA,KAAuB,KAAA,GAAW;YAC9C,eAAe,kBAAA,GAAqB,UAAU,kBAAA;QAC/C;QAED,IAAI,UAAU,iBAAA,KAAsB,KAAA,GAAW;YAC7C,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,iBAAiB,UAAU,iBAAiB,CAAC;QAChG;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;AACH;AAOA,MAAM,2BAA2B;IAC/B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,kBAAA;IACxB;IAED,YAAY,YAAA,EAAc;QACxB,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,OAAO,OAAO,IAAA;QAEpB,MAAM,aAAa,KAAK,QAAA,CAAS,YAAY,CAAA;QAE7C,IAAI,CAAC,WAAW,UAAA,IAAc,CAAC,WAAW,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YAC/D,OAAO;QACR;QAED,MAAM,YAAY,WAAW,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QACjD,MAAM,SAAS,OAAO,OAAA,CAAQ,UAAA;QAE9B,IAAI,CAAC,QAAQ;YACX,IAAI,KAAK,kBAAA,IAAsB,KAAK,kBAAA,CAAmB,OAAA,CAAQ,IAAA,CAAK,IAAI,KAAK,GAAG;gBAC9E,MAAM,IAAI,MAAM,6EAA6E;YACrG,OAAa;gBAEL,OAAO;YACR;QACF;QAED,OAAO,OAAO,gBAAA,CAAiB,cAAc,UAAU,MAAA,EAAQ,MAAM;IACtE;AACH;AAOA,MAAM,yBAAyB;IAC7B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,gBAAA;QACvB,IAAA,CAAK,WAAA,GAAc;IACpB;IAED,YAAY,YAAA,EAAc;QACxB,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,OAAO,OAAO,IAAA;QAEpB,MAAM,aAAa,KAAK,QAAA,CAAS,YAAY,CAAA;QAE7C,IAAI,CAAC,WAAW,UAAA,IAAc,CAAC,WAAW,UAAA,CAAW,IAAI,CAAA,EAAG;YAC1D,OAAO;QACR;QAED,MAAM,YAAY,WAAW,UAAA,CAAW,IAAI,CAAA;QAC5C,MAAM,SAAS,KAAK,MAAA,CAAO,UAAU,MAAM,CAAA;QAE3C,IAAI,SAAS,OAAO,aAAA;QACpB,IAAI,OAAO,GAAA,EAAK;YACd,MAAM,UAAU,OAAO,OAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,OAAO,GAAG;YAC5D,IAAI,YAAY,MAAM,SAAS;QAChC;QAED,OAAO,IAAA,CAAK,aAAA,CAAa,EAAG,IAAA,CAAK,SAAU,WAAA,EAAa;YACtD,IAAI,aAAa,OAAO,OAAO,gBAAA,CAAiB,cAAc,UAAU,MAAA,EAAQ,MAAM;YAEtF,IAAI,KAAK,kBAAA,IAAsB,KAAK,kBAAA,CAAmB,OAAA,CAAQ,IAAI,KAAK,GAAG;gBACzE,MAAM,IAAI,MAAM,2DAA2D;YAC5E;YAGD,OAAO,OAAO,WAAA,CAAY,YAAY;QAC5C,CAAK;IACF;IAED,gBAAgB;QACd,IAAI,CAAC,IAAA,CAAK,WAAA,EAAa;YACrB,IAAA,CAAK,WAAA,GAAc,IAAI,QAAQ,SAAU,OAAA,EAAS;gBAChD,MAAM,QAAQ,IAAI,MAAO;gBAIzB,MAAM,GAAA,GAAM;gBAEZ,MAAM,MAAA,GAAS,MAAM,OAAA,GAAU,WAAY;oBACzC,QAAQ,MAAM,MAAA,KAAW,CAAC;gBAC3B;YACT,CAAO;QACF;QAED,OAAO,IAAA,CAAK,WAAA;IACb;AACH;AAOA,MAAM,yBAAyB;IAC7B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,WAAW,gBAAA;QACvB,IAAA,CAAK,WAAA,GAAc;IACpB;IAED,YAAY,YAAA,EAAc;QACxB,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,SAAS,IAAA,CAAK,MAAA;QACpB,MAAM,OAAO,OAAO,IAAA;QAEpB,MAAM,aAAa,KAAK,QAAA,CAAS,YAAY,CAAA;QAE7C,IAAI,CAAC,WAAW,UAAA,IAAc,CAAC,WAAW,UAAA,CAAW,IAAI,CAAA,EAAG;YAC1D,OAAO;QACR;QAED,MAAM,YAAY,WAAW,UAAA,CAAW,IAAI,CAAA;QAC5C,MAAM,SAAS,KAAK,MAAA,CAAO,UAAU,MAAM,CAAA;QAE3C,IAAI,SAAS,OAAO,aAAA;QACpB,IAAI,OAAO,GAAA,EAAK;YACd,MAAM,UAAU,OAAO,OAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,OAAO,GAAG;YAC5D,IAAI,YAAY,MAAM,SAAS;QAChC;QAED,OAAO,IAAA,CAAK,aAAA,CAAa,EAAG,IAAA,CAAK,SAAU,WAAA,EAAa;YACtD,IAAI,aAAa,OAAO,OAAO,gBAAA,CAAiB,cAAc,UAAU,MAAA,EAAQ,MAAM;YAEtF,IAAI,KAAK,kBAAA,IAAsB,KAAK,kBAAA,CAAmB,OAAA,CAAQ,IAAI,KAAK,GAAG;gBACzE,MAAM,IAAI,MAAM,2DAA2D;YAC5E;YAGD,OAAO,OAAO,WAAA,CAAY,YAAY;QAC5C,CAAK;IACF;IAED,gBAAgB;QACd,IAAI,CAAC,IAAA,CAAK,WAAA,EAAa;YACrB,IAAA,CAAK,WAAA,GAAc,IAAI,QAAQ,SAAU,OAAA,EAAS;gBAChD,MAAM,QAAQ,IAAI,MAAO;gBAGzB,MAAM,GAAA,GACJ;gBACF,MAAM,MAAA,GAAS,MAAM,OAAA,GAAU,WAAY;oBACzC,QAAQ,MAAM,MAAA,KAAW,CAAC;gBAC3B;YACT,CAAO;QACF;QAED,OAAO,IAAA,CAAK,WAAA;IACb;AACH;AAOA,MAAM,uBAAuB;IAC3B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,IAAA,GAAO,WAAW,uBAAA;QACvB,IAAA,CAAK,MAAA,GAAS;IACf;IAED,eAAe,KAAA,EAAO;QACpB,MAAM,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA;QACzB,MAAM,aAAa,KAAK,WAAA,CAAY,KAAK,CAAA;QAEzC,IAAI,WAAW,UAAA,IAAc,WAAW,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,EAAG;YAC7D,MAAM,eAAe,WAAW,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;YAEpD,MAAM,SAAS,IAAA,CAAK,MAAA,CAAO,aAAA,CAAc,UAAU,aAAa,MAAM;YACtE,MAAM,UAAU,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,cAAA;YAEpC,IAAI,CAAC,WAAW,CAAC,QAAQ,SAAA,EAAW;gBAClC,IAAI,KAAK,kBAAA,IAAsB,KAAK,kBAAA,CAAmB,OAAA,CAAQ,IAAA,CAAK,IAAI,KAAK,GAAG;oBAC9E,MAAM,IAAI,MAAM,oFAAoF;gBAC9G,OAAe;oBAEL,OAAO;gBACR;YACF;YAED,OAAO,OAAO,IAAA,CAAK,SAAU,GAAA,EAAK;gBAChC,MAAM,aAAa,aAAa,UAAA,IAAc;gBAC9C,MAAM,aAAa,aAAa,UAAA,IAAc;gBAE9C,MAAM,QAAQ,aAAa,KAAA;gBAC3B,MAAM,SAAS,aAAa,UAAA;gBAE5B,MAAM,SAAS,IAAI,WAAW,KAAK,YAAY,UAAU;gBAEzD,IAAI,QAAQ,qBAAA,EAAuB;oBACjC,OAAO,QACJ,qBAAA,CAAsB,OAAO,QAAQ,QAAQ,aAAa,IAAA,EAAM,aAAa,MAAM,EACnF,IAAA,CAAK,SAAUC,IAAAA,EAAK;wBACnB,OAAOA,KAAI,MAAA;oBACzB,CAAa;gBACb,OAAe;oBAEL,OAAO,QAAQ,KAAA,CAAM,IAAA,CAAK,WAAY;wBACpC,MAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;wBAC7C,QAAQ,gBAAA,CACN,IAAI,WAAW,MAAM,GACrB,OACA,QACA,QACA,aAAa,IAAA,EACb,aAAa,MAAA;wBAEf,OAAO;oBACnB,CAAW;gBACF;YACT,CAAO;QACP,OAAW;YACL,OAAO;QACR;IACF;AACH;AAQA,MAAM,sBAAsB;IAC1B,YAAY,MAAA,CAAQ;QAClB,IAAA,CAAK,IAAA,GAAO,WAAW,uBAAA;QACvB,IAAA,CAAK,MAAA,GAAS;IACf;IAED,eAAe,SAAA,EAAW;QACxB,MAAM,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA;QACzB,MAAM,UAAU,KAAK,KAAA,CAAM,SAAS,CAAA;QAEpC,IAAI,CAAC,QAAQ,UAAA,IAAc,CAAC,QAAQ,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,IAAK,QAAQ,IAAA,KAAS,KAAA,GAAW;YACvF,OAAO;QACR;QAED,MAAM,UAAU,KAAK,MAAA,CAAO,QAAQ,IAAI,CAAA;QAIxC,KAAA,MAAW,aAAa,QAAQ,UAAA,CAAY;YAC1C,IACE,UAAU,IAAA,KAAS,gBAAgB,SAAA,IACnC,UAAU,IAAA,KAAS,gBAAgB,cAAA,IACnC,UAAU,IAAA,KAAS,gBAAgB,YAAA,IACnC,UAAU,IAAA,KAAS,KAAA,GACnB;gBACA,OAAO;YACR;QACF;QAED,MAAM,eAAe,QAAQ,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA;QACjD,MAAM,gBAAgB,aAAa,UAAA;QAInC,MAAM,UAAU,CAAE,CAAA;QAClB,MAAM,aAAa,CAAE;QAErB,IAAA,MAAW,OAAO,cAAe;YAC/B,QAAQ,IAAA,CACN,IAAA,CAAK,MAAA,CAAO,aAAA,CAAc,YAAY,aAAA,CAAc,GAAG,CAAC,EAAE,IAAA,CAAK,CAAC,aAAa;gBAC3E,UAAA,CAAW,GAAG,CAAA,GAAI;gBAClB,OAAO,UAAA,CAAW,GAAG,CAAA;YAC/B,CAAS;QAEJ;QAED,IAAI,QAAQ,MAAA,GAAS,GAAG;YACtB,OAAO;QACR;QAED,QAAQ,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,cAAA,CAAe,SAAS,CAAC;QAElD,OAAO,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,CAAC,YAAY;YAC5C,MAAM,aAAa,QAAQ,GAAA,CAAK;YAChC,MAAM,SAAS,WAAW,OAAA,GAAU,WAAW,QAAA,GAAW;gBAAC,UAAU;aAAA;YACrE,MAAM,QAAQ,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA;YACzB,MAAM,kBAAkB,CAAE,CAAA;YAE1B,KAAA,MAAW,QAAQ,OAAQ;gBAEzB,MAAM,IAAI,uJAAI,UAAA,CAAS;gBACvB,MAAM,IAAI,uJAAI,UAAA,CAAS;gBACvB,MAAM,IAAI,uJAAI,aAAA,CAAY;gBAC1B,MAAM,IAAI,uJAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;gBAE7B,MAAM,gBAAgB,uJAAI,gBAAA,CAAc,KAAK,QAAA,EAAU,KAAK,QAAA,EAAU,KAAK;gBAE3E,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;oBAC9B,IAAI,WAAW,WAAA,EAAa;wBAC1B,EAAE,mBAAA,CAAoB,WAAW,WAAA,EAAa,CAAC;oBAChD;oBAED,IAAI,WAAW,QAAA,EAAU;wBACvB,EAAE,mBAAA,CAAoB,WAAW,QAAA,EAAU,CAAC;oBAC7C;oBAED,IAAI,WAAW,KAAA,EAAO;wBACpB,EAAE,mBAAA,CAAoB,WAAW,KAAA,EAAO,CAAC;oBAC1C;oBAED,cAAc,WAAA,CAAY,GAAG,EAAE,OAAA,CAAQ,GAAG,GAAG,CAAC,CAAC;gBAChD;gBAGD,IAAA,MAAW,iBAAiB,WAAY;oBACtC,IAAI,kBAAkB,YAAY;wBAChC,MAAM,OAAO,UAAA,CAAW,aAAa,CAAA;wBACrC,cAAc,aAAA,GAAgB,uJAAI,2BAAA,CAAyB,KAAK,KAAA,EAAO,KAAK,QAAA,EAAU,KAAK,UAAU;oBACjH,OAAA,IAAqB,kBAAkB,iBAAiB,kBAAkB,cAAc,kBAAkB,SAAS;wBACvG,KAAK,QAAA,CAAS,YAAA,CAAa,eAAe,UAAA,CAAW,aAAa,CAAC;oBACpE;gBACF;gBAGD,kJAAA,CAAA,WAAA,CAAS,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,eAAe,IAAI;gBAEhD,IAAA,CAAK,MAAA,CAAO,mBAAA,CAAoB,aAAa;gBAE7C,gBAAgB,IAAA,CAAK,aAAa;YACnC;YAED,IAAI,WAAW,OAAA,EAAS;gBACtB,WAAW,KAAA,CAAO;gBAElB,WAAW,GAAA,CAAI,GAAG,eAAe;gBAEjC,OAAO;YACR;YAED,OAAO,eAAA,CAAgB,CAAC,CAAA;QAC9B,CAAK;IACF;AACH;AAGA,MAAM,gCAAgC;AACtC,MAAM,iCAAiC;AACvC,MAAM,+BAA+B;IAAE,MAAM;IAAY,KAAK;AAAY;AAE1E,MAAM,oBAAoB;IACxB,YAAY,IAAA,CAAM;QAChB,IAAA,CAAK,IAAA,GAAO,WAAW,eAAA;QACvB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,IAAA,GAAO;QAEZ,MAAM,aAAa,IAAI,SAAS,MAAM,GAAG,8BAA8B;QAEvE,IAAA,CAAK,MAAA,GAAS;YACZ,0KAAO,aAAA,EAAW,IAAI,WAAW,KAAK,KAAA,CAAM,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,WAAW,SAAA,CAAU,GAAG,IAAI;YACrC,QAAQ,WAAW,SAAA,CAAU,GAAG,IAAI;QACrC;QAED,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,+BAA+B;YACvD,MAAM,IAAI,MAAM,mDAAmD;QACpE,OAAA,IAAU,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU,GAAK;YACpC,MAAM,IAAI,MAAM,gDAAgD;QACjE;QAED,MAAM,sBAAsB,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS;QACjD,MAAM,YAAY,IAAI,SAAS,MAAM,8BAA8B;QACnE,IAAI,aAAa;QAEjB,MAAO,aAAa,oBAAqB;YACvC,MAAM,cAAc,UAAU,SAAA,CAAU,YAAY,IAAI;YACxD,cAAc;YAEd,MAAM,YAAY,UAAU,SAAA,CAAU,YAAY,IAAI;YACtD,cAAc;YAEd,IAAI,cAAc,6BAA6B,IAAA,EAAM;gBACnD,MAAM,eAAe,IAAI,WAAW,MAAM,iCAAiC,YAAY,WAAW;gBAClG,IAAA,CAAK,OAAA,sKAAU,aAAA,EAAW,YAAY;YAC9C,OAAA,IAAiB,cAAc,6BAA6B,GAAA,EAAK;gBACzD,MAAM,aAAa,iCAAiC;gBACpD,IAAA,CAAK,IAAA,GAAO,KAAK,KAAA,CAAM,YAAY,aAAa,WAAW;YAC5D;YAID,cAAc;QACf;QAED,IAAI,IAAA,CAAK,OAAA,KAAY,MAAM;YACzB,MAAM,IAAI,MAAM,2CAA2C;QAC5D;IACF;AACH;AAOA,MAAM,kCAAkC;IACtC,YAAY,IAAA,EAAM,WAAA,CAAa;QAC7B,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM,qDAAqD;QACtE;QAED,IAAA,CAAK,IAAA,GAAO,WAAW,0BAAA;QACvB,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,WAAA,CAAY,OAAA,CAAS;IAC3B;IAED,gBAAgB,SAAA,EAAW,MAAA,EAAQ;QACjC,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,cAAc,IAAA,CAAK,WAAA;QACzB,MAAM,kBAAkB,UAAU,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,CAAE,UAAA;QACxD,MAAM,mBAAmB,UAAU,UAAA,CAAW,IAAA,CAAK,IAAI,CAAA,CAAE,UAAA;QACzD,MAAM,oBAAoB,CAAE;QAC5B,MAAM,yBAAyB,CAAE;QACjC,MAAM,mBAAmB,CAAE;QAE3B,IAAA,MAAW,iBAAiB,iBAAkB;YAC5C,MAAM,qBAAqB,UAAA,CAAW,aAAa,CAAA,IAAK,cAAc,WAAA,CAAa;YAEnF,iBAAA,CAAkB,kBAAkB,CAAA,GAAI,gBAAA,CAAiB,aAAa,CAAA;QACvE;QAED,IAAA,MAAW,iBAAiB,UAAU,UAAA,CAAY;YAChD,MAAM,qBAAqB,UAAA,CAAW,aAAa,CAAA,IAAK,cAAc,WAAA,CAAa;YAEnF,IAAI,gBAAA,CAAiB,aAAa,CAAA,KAAM,KAAA,GAAW;gBACjD,MAAM,cAAc,KAAK,SAAA,CAAU,UAAU,UAAA,CAAW,aAAa,CAAC,CAAA;gBACtE,MAAM,gBAAgB,qBAAA,CAAsB,YAAY,aAAa,CAAA;gBAErE,gBAAA,CAAiB,kBAAkB,CAAA,GAAI,cAAc,IAAA;gBACrD,sBAAA,CAAuB,kBAAkB,CAAA,GAAI,YAAY,UAAA,KAAe;YACzE;QACF;QAED,OAAO,OAAO,aAAA,CAAc,cAAc,eAAe,EAAE,IAAA,CAAK,SAAU,UAAA,EAAY;YACpF,OAAO,IAAI,QAAQ,SAAU,OAAA,EAAS,MAAA,EAAQ;gBAC5C,YAAY,eAAA,CACV,YACA,SAAU,QAAA,EAAU;oBAClB,IAAA,MAAW,iBAAiB,SAAS,UAAA,CAAY;wBAC/C,MAAM,YAAY,SAAS,UAAA,CAAW,aAAa,CAAA;wBACnD,MAAM,aAAa,sBAAA,CAAuB,aAAa,CAAA;wBAEvD,IAAI,eAAe,KAAA,GAAW,UAAU,UAAA,GAAa;oBACtD;oBAED,QAAQ,QAAQ;gBACjB,GACD,mBACA,kBACA,sBACA;YAEV,CAAO;QACP,CAAK;IACF;AACH;AAOA,MAAM,8BAA8B;IAClC,aAAc;QACZ,IAAA,CAAK,IAAA,GAAO,WAAW,qBAAA;IACxB;IAED,cAAc,OAAA,EAAS,SAAA,EAAW;QAChC,IAAA,CACG,UAAU,QAAA,KAAa,KAAA,KAAa,UAAU,QAAA,KAAa,QAAQ,OAAA,KACpE,UAAU,MAAA,KAAW,KAAA,KACrB,UAAU,QAAA,KAAa,KAAA,KACvB,UAAU,KAAA,KAAU,KAAA,GACpB;YAEA,OAAO;QACR;QAED,UAAU,QAAQ,KAAA,CAAO;QAEzB,IAAI,UAAU,QAAA,KAAa,KAAA,GAAW;YACpC,QAAQ,OAAA,GAAU,UAAU,QAAA;QAC7B;QAED,IAAI,UAAU,MAAA,KAAW,KAAA,GAAW;YAClC,QAAQ,MAAA,CAAO,SAAA,CAAU,UAAU,MAAM;QAC1C;QAED,IAAI,UAAU,QAAA,KAAa,KAAA,GAAW;YACpC,QAAQ,QAAA,GAAW,UAAU,QAAA;QAC9B;QAED,IAAI,UAAU,KAAA,KAAU,KAAA,GAAW;YACjC,QAAQ,MAAA,CAAO,SAAA,CAAU,UAAU,KAAK;QACzC;QAED,QAAQ,WAAA,GAAc;QAEtB,OAAO;IACR;AACH;AAOA,MAAM,8BAA8B;IAClC,aAAc;QACZ,IAAA,CAAK,IAAA,GAAO,WAAW,qBAAA;IACxB;AACH;AAQA,MAAM,sLAAmC,cAAA,CAAY;IACnD,YAAY,kBAAA,EAAoB,YAAA,EAAc,UAAA,EAAY,YAAA,CAAc;QACtE,KAAA,CAAM,oBAAoB,cAAc,YAAY,YAAY;IACjE;IAED,iBAAiB,KAAA,EAAO;QAItB,MAAM,SAAS,IAAA,CAAK,YAAA,EAClB,SAAS,IAAA,CAAK,YAAA,EACd,YAAY,IAAA,CAAK,SAAA,EACjB,SAAS,QAAQ,YAAY,IAAI;QAEnC,IAAA,IAAS,IAAI,GAAG,MAAM,WAAW,IAAK;YACpC,MAAA,CAAO,CAAC,CAAA,GAAI,MAAA,CAAO,SAAS,CAAC,CAAA;QAC9B;QAED,OAAO;IACR;IAED,aAAa,EAAA,EAAI,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI;QAC1B,MAAM,SAAS,IAAA,CAAK,YAAA;QACpB,MAAM,SAAS,IAAA,CAAK,YAAA;QACpB,MAAM,SAAS,IAAA,CAAK,SAAA;QAEpB,MAAM,UAAU,SAAS;QACzB,MAAM,UAAU,SAAS;QAEzB,MAAM,KAAK,KAAK;QAEhB,MAAM,IAAA,CAAK,IAAI,EAAA,IAAM;QACrB,MAAM,KAAK,IAAI;QACf,MAAM,MAAM,KAAK;QAEjB,MAAM,UAAU,KAAK;QACrB,MAAM,UAAU,UAAU;QAE1B,MAAM,KAAK,CAAA,IAAK,MAAM,IAAI;QAC1B,MAAM,KAAK,MAAM;QACjB,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,KAAK,KAAK;QAIrB,IAAA,IAAS,IAAI,GAAG,MAAM,QAAQ,IAAK;YACjC,MAAM,KAAK,MAAA,CAAO,UAAU,IAAI,MAAM,CAAA;YACtC,MAAM,KAAK,MAAA,CAAO,UAAU,IAAI,OAAO,CAAA,GAAI;YAC3C,MAAM,KAAK,MAAA,CAAO,UAAU,IAAI,MAAM,CAAA;YACtC,MAAM,KAAK,MAAA,CAAO,UAAU,CAAC,CAAA,GAAI;YAEjC,MAAA,CAAO,CAAC,CAAA,GAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;QAChD;QAED,OAAO;IACR;AACH;AAEA,MAAM,KAAqB,aAAA,GAAA,IAAI,gKAAA,CAAY;AAE3C,MAAM,6CAA6C,2BAA2B;IAC5E,aAAa,EAAA,EAAI,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI;QAC1B,MAAM,SAAS,KAAA,CAAM,aAAa,IAAI,IAAI,GAAG,EAAE;QAE/C,GAAG,SAAA,CAAU,MAAM,EAAE,SAAA,CAAW,EAAC,OAAA,CAAQ,MAAM;QAE/C,OAAO;IACR;AACH;AAQA,MAAM,kBAAkB;IACtB,OAAO;IAAA,oBAAA;IAEP,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,WAAW;IACX,YAAY;IACZ,WAAW;IACX,gBAAgB;IAChB,cAAc;IACd,eAAe;IACf,gBAAgB;AAClB;AAEA,MAAM,wBAAwB;IAC5B,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEA,MAAM,gBAAgB;IACpB,yJAAM,gBAAA;IACN,yJAAM,eAAA;IACN,yJAAM,6BAAA;IACN,yJAAM,4BAAA;IACN,yJAAM,4BAAA;IACN,yJAAM,2BAAA;AACR;AAEA,MAAM,kBAAkB;IACtB,OAAO,yKAAA;IACP,OAAO,4KAAA;IACP,0JAAO,iBAAA;AACT;AAEA,MAAM,mBAAmB;IACvB,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEA,MAAM,aAAa;IACjB,UAAU;IACV,QAAQ;IACR,SAAS;IAAA,2BAAA;IAAA,gDAAA;IAAA,gDAAA;IAIT,gKAAI,UAAA,IAAW,MACX;QACE,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;IACb,IACD;QACE,YAAY;QACZ,YAAY;IACpB,CAAA;IAEE,SAAS;IACT,WAAW;IACX,UAAU;AACZ;AAEA,MAAM,kBAAkB;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;AACX;AAEA,MAAM,gBAAgB;IACpB,aAAa,KAAA;IAAA,0FAAA;IAAA,uFAAA;IAEb,QAAQ,uKAAA;IACR,yJAAM,sBAAA;AACR;AAEA,MAAM,cAAc;IAClB,QAAQ;IACR,MAAM;IACN,OAAO;AACT;AAKA,SAAS,sBAAsB,KAAA,EAAO;IACpC,IAAI,KAAA,CAAM,iBAAiB,CAAA,KAAM,KAAA,GAAW;QAC1C,KAAA,CAAM,iBAAiB,CAAA,GAAI,uJAAI,uBAAA,CAAqB;YAClD,OAAO;YACP,UAAU;YACV,WAAW;YACX,WAAW;YACX,aAAa;YACb,WAAW;YACX,yJAAM,YAAA;QACZ,CAAK;IACF;IAED,OAAO,KAAA,CAAM,iBAAiB,CAAA;AAChC;AAEA,SAAS,+BAA+B,eAAA,EAAiB,MAAA,EAAQ,SAAA,EAAW;IAG1E,IAAA,MAAW,QAAQ,UAAU,UAAA,CAAY;QACvC,IAAI,eAAA,CAAgB,IAAI,CAAA,KAAM,KAAA,GAAW;YACvC,OAAO,QAAA,CAAS,cAAA,GAAiB,OAAO,QAAA,CAAS,cAAA,IAAkB,CAAE;YACrE,OAAO,QAAA,CAAS,cAAA,CAAe,IAAI,CAAA,GAAI,UAAU,UAAA,CAAW,IAAI,CAAA;QACjE;IACF;AACH;AAMA,SAAS,uBAAuB,MAAA,EAAQ,OAAA,EAAS;IAC/C,IAAI,QAAQ,MAAA,KAAW,KAAA,GAAW;QAChC,IAAI,OAAO,QAAQ,MAAA,KAAW,UAAU;YACtC,OAAO,MAAA,CAAO,OAAO,QAAA,EAAU,QAAQ,MAAM;QACnD,OAAW;YACL,QAAQ,IAAA,CAAK,wDAAwD,QAAQ,MAAM;QACpF;IACF;AACH;AAUA,SAAS,gBAAgB,QAAA,EAAU,OAAA,EAAS,MAAA,EAAQ;IAClD,IAAI,mBAAmB;IACvB,IAAI,iBAAiB;IACrB,IAAI,gBAAgB;IAEpB,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;QAChD,MAAM,SAAS,OAAA,CAAQ,CAAC,CAAA;QAExB,IAAI,OAAO,QAAA,KAAa,KAAA,GAAW,mBAAmB;QACtD,IAAI,OAAO,MAAA,KAAW,KAAA,GAAW,iBAAiB;QAClD,IAAI,OAAO,OAAA,KAAY,KAAA,GAAW,gBAAgB;QAElD,IAAI,oBAAoB,kBAAkB,eAAe;IAC1D;IAED,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,eAAe,OAAO,QAAQ,OAAA,CAAQ,QAAQ;IAE3F,MAAM,2BAA2B,CAAE,CAAA;IACnC,MAAM,yBAAyB,CAAE,CAAA;IACjC,MAAM,wBAAwB,CAAE,CAAA;IAEhC,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;QAChD,MAAM,SAAS,OAAA,CAAQ,CAAC,CAAA;QAExB,IAAI,kBAAkB;YACpB,MAAM,kBACJ,OAAO,QAAA,KAAa,KAAA,IAAY,OAAO,aAAA,CAAc,YAAY,OAAO,QAAQ,IAAI,SAAS,UAAA,CAAW,QAAA;YAE1G,yBAAyB,IAAA,CAAK,eAAe;QAC9C;QAED,IAAI,gBAAgB;YAClB,MAAM,kBACJ,OAAO,MAAA,KAAW,KAAA,IAAY,OAAO,aAAA,CAAc,YAAY,OAAO,MAAM,IAAI,SAAS,UAAA,CAAW,MAAA;YAEtG,uBAAuB,IAAA,CAAK,eAAe;QAC5C;QAED,IAAI,eAAe;YACjB,MAAM,kBACJ,OAAO,OAAA,KAAY,KAAA,IAAY,OAAO,aAAA,CAAc,YAAY,OAAO,OAAO,IAAI,SAAS,UAAA,CAAW,KAAA;YAExG,sBAAsB,IAAA,CAAK,eAAe;QAC3C;IACF;IAED,OAAO,QAAQ,GAAA,CAAI;QACjB,QAAQ,GAAA,CAAI,wBAAwB;QACpC,QAAQ,GAAA,CAAI,sBAAsB;QAClC,QAAQ,GAAA,CAAI,qBAAqB;KAClC,EAAE,IAAA,CAAK,SAAU,SAAA,EAAW;QAC3B,MAAM,iBAAiB,SAAA,CAAU,CAAC,CAAA;QAClC,MAAM,eAAe,SAAA,CAAU,CAAC,CAAA;QAChC,MAAM,cAAc,SAAA,CAAU,CAAC,CAAA;QAE/B,IAAI,kBAAkB,SAAS,eAAA,CAAgB,QAAA,GAAW;QAC1D,IAAI,gBAAgB,SAAS,eAAA,CAAgB,MAAA,GAAS;QACtD,IAAI,eAAe,SAAS,eAAA,CAAgB,KAAA,GAAQ;QACpD,SAAS,oBAAA,GAAuB;QAEhC,OAAO;IACX,CAAG;AACH;AAMA,SAAS,mBAAmB,IAAA,EAAM,OAAA,EAAS;IACzC,KAAK,kBAAA,CAAoB;IAEzB,IAAI,QAAQ,OAAA,KAAY,KAAA,GAAW;QACjC,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,OAAA,CAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;YACxD,KAAK,qBAAA,CAAsB,CAAC,CAAA,GAAI,QAAQ,OAAA,CAAQ,CAAC,CAAA;QAClD;IACF;IAGD,IAAI,QAAQ,MAAA,IAAU,MAAM,OAAA,CAAQ,QAAQ,MAAA,CAAO,WAAW,GAAG;QAC/D,MAAM,cAAc,QAAQ,MAAA,CAAO,WAAA;QAEnC,IAAI,KAAK,qBAAA,CAAsB,MAAA,KAAW,YAAY,MAAA,EAAQ;YAC5D,KAAK,qBAAA,GAAwB,CAAE;YAE/B,IAAA,IAAS,IAAI,GAAG,KAAK,YAAY,MAAA,EAAQ,IAAI,IAAI,IAAK;gBACpD,KAAK,qBAAA,CAAsB,WAAA,CAAY,CAAC,CAAC,CAAA,GAAI;YAC9C;QACP,OAAW;YACL,QAAQ,IAAA,CAAK,sEAAsE;QACpF;IACF;AACH;AAEA,SAAS,mBAAmB,YAAA,EAAc;IACxC,IAAI;IAEJ,MAAM,iBAAiB,aAAa,UAAA,IAAc,aAAa,UAAA,CAAW,WAAW,0BAA0B,CAAA;IAE/G,IAAI,gBAAgB;QAClB,cACE,WACA,eAAe,UAAA,GACf,MACA,eAAe,OAAA,GACf,MACA,oBAAoB,eAAe,UAAU;IACnD,OAAS;QACL,cAAc,aAAa,OAAA,GAAU,MAAM,oBAAoB,aAAa,UAAU,IAAI,MAAM,aAAa,IAAA;IAC9G;IAED,IAAI,aAAa,OAAA,KAAY,KAAA,GAAW;QACtC,IAAA,IAAS,IAAI,GAAG,KAAK,aAAa,OAAA,CAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;YAC7D,eAAe,MAAM,oBAAoB,aAAa,OAAA,CAAQ,CAAC,CAAC;QACjE;IACF;IAED,OAAO;AACT;AAEA,SAAS,oBAAoB,UAAA,EAAY;IACvC,IAAI,gBAAgB;IAEpB,MAAM,OAAO,OAAO,IAAA,CAAK,UAAU,EAAE,IAAA,CAAM;IAE3C,IAAA,IAAS,IAAI,GAAG,KAAK,KAAK,MAAA,EAAQ,IAAI,IAAI,IAAK;QAC7C,iBAAiB,IAAA,CAAK,CAAC,CAAA,GAAI,MAAM,UAAA,CAAW,IAAA,CAAK,CAAC,CAAC,CAAA,GAAI;IACxD;IAED,OAAO;AACT;AAEA,SAAS,4BAA4B,WAAA,EAAa;IAIhD,OAAQ,aAAW;QACjB,KAAK;YACH,OAAO,IAAI;QAEb,KAAK;YACH,OAAO,IAAI;QAEb,KAAK;YACH,OAAO,IAAI;QAEb,KAAK;YACH,OAAO,IAAI;QAEb;YACE,MAAM,IAAI,MAAM,mEAAmE;IACtF;AACH;AAEA,SAAS,oBAAoB,GAAA,EAAK;IAChC,IAAI,IAAI,MAAA,CAAO,gBAAgB,IAAI,KAAK,IAAI,MAAA,CAAO,oBAAoB,MAAM,GAAG,OAAO;IACvF,IAAI,IAAI,MAAA,CAAO,eAAe,IAAI,KAAK,IAAI,MAAA,CAAO,oBAAoB,MAAM,GAAG,OAAO;IAEtF,OAAO;AACT;AAEA,MAAM,kBAAkC,aAAA,GAAA,uJAAI,UAAA,CAAS;AAIrD,MAAM,WAAW;IACf,YAAY,OAAO,CAAA,CAAA,EAAI,UAAU,CAAA,CAAA,CAAI;QACnC,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,UAAA,GAAa,CAAE;QACpB,IAAA,CAAK,OAAA,GAAU,CAAE;QACjB,IAAA,CAAK,OAAA,GAAU;QAGf,IAAA,CAAK,KAAA,GAAQ,IAAI,aAAc;QAG/B,IAAA,CAAK,YAAA,GAAe,aAAA,GAAA,IAAI,IAAK;QAG7B,IAAA,CAAK,cAAA,GAAiB,CAAE;QAGxB,IAAA,CAAK,SAAA,GAAY,CAAE;QAGnB,IAAA,CAAK,SAAA,GAAY;YAAE,MAAM,CAAA;YAAI,MAAM,CAAA;QAAI;QACvC,IAAA,CAAK,WAAA,GAAc;YAAE,MAAM,CAAA;YAAI,MAAM,CAAA;QAAI;QACzC,IAAA,CAAK,UAAA,GAAa;YAAE,MAAM,CAAA;YAAI,MAAM,CAAA;QAAI;QAExC,IAAA,CAAK,WAAA,GAAc,CAAE;QACrB,IAAA,CAAK,YAAA,GAAe,CAAE;QAGtB,IAAA,CAAK,aAAA,GAAgB,CAAE;QAKvB,IAAI,WAAW;QACf,IAAI,YAAY;QAChB,IAAI,iBAAiB,CAAA;QAErB,IAAI,OAAO,cAAc,eAAe,OAAO,UAAU,SAAA,KAAc,aAAa;YAClF,WAAW,iCAAiC,IAAA,CAAK,UAAU,SAAS,MAAM;YAC1E,YAAY,UAAU,SAAA,CAAU,OAAA,CAAQ,SAAS,IAAI,CAAA;YACrD,iBAAiB,YAAY,UAAU,SAAA,CAAU,KAAA,CAAM,qBAAqB,CAAA,CAAE,CAAC,CAAA,GAAI,CAAA;QACpF;QAED,IAAI,OAAO,sBAAsB,eAAe,YAAa,aAAa,iBAAiB,IAAK;YAC9F,IAAA,CAAK,aAAA,GAAgB,uJAAI,gBAAA,CAAc,IAAA,CAAK,OAAA,CAAQ,OAAO;QACjE,OAAW;YACL,IAAA,CAAK,aAAA,GAAgB,uJAAI,oBAAA,CAAkB,IAAA,CAAK,OAAA,CAAQ,OAAO;QAChE;QAED,IAAA,CAAK,aAAA,CAAc,cAAA,CAAe,IAAA,CAAK,OAAA,CAAQ,WAAW;QAC1D,IAAA,CAAK,aAAA,CAAc,gBAAA,CAAiB,IAAA,CAAK,OAAA,CAAQ,aAAa;QAE9D,IAAA,CAAK,UAAA,GAAa,uJAAI,aAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,OAAO;QACrD,IAAA,CAAK,UAAA,CAAW,eAAA,CAAgB,aAAa;QAE7C,IAAI,IAAA,CAAK,OAAA,CAAQ,WAAA,KAAgB,mBAAmB;YAClD,IAAA,CAAK,UAAA,CAAW,kBAAA,CAAmB,IAAI;QACxC;IACF;IAED,cAAc,UAAA,EAAY;QACxB,IAAA,CAAK,UAAA,GAAa;IACnB;IAED,WAAW,OAAA,EAAS;QAClB,IAAA,CAAK,OAAA,GAAU;IAChB;IAED,MAAM,MAAA,EAAQ,OAAA,EAAS;QACrB,MAAM,SAAS,IAAA;QACf,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,aAAa,IAAA,CAAK,UAAA;QAGxB,IAAA,CAAK,KAAA,CAAM,SAAA,CAAW;QACtB,IAAA,CAAK,SAAA,GAAY,CAAE;QAGnB,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;YAC7B,OAAO,IAAI,SAAA,IAAa,IAAI,SAAA,CAAW;QAC7C,CAAK;QAED,QAAQ,GAAA,CACN,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;YAC7B,OAAO,IAAI,UAAA,IAAc,IAAI,UAAA,CAAY;QACjD,CAAO,GAEA,IAAA,CAAK,WAAY;YAChB,OAAO,QAAQ,GAAA,CAAI;gBACjB,OAAO,eAAA,CAAgB,OAAO;gBAC9B,OAAO,eAAA,CAAgB,WAAW;gBAClC,OAAO,eAAA,CAAgB,QAAQ;aAChC;QACT,CAAO,EACA,IAAA,CAAK,SAAU,YAAA,EAAc;YAC5B,MAAM,SAAS;gBACb,OAAO,YAAA,CAAa,CAAC,CAAA,CAAE,KAAK,KAAA,IAAS,CAAC,CAAA;gBACtC,QAAQ,YAAA,CAAa,CAAC,CAAA;gBACtB,YAAY,YAAA,CAAa,CAAC,CAAA;gBAC1B,SAAS,YAAA,CAAa,CAAC,CAAA;gBACvB,OAAO,KAAK,KAAA;gBACZ;gBACA,UAAU,CAAE;YACb;YAED,+BAA+B,YAAY,QAAQ,IAAI;YAEvD,uBAAuB,QAAQ,IAAI;YAEnC,OAAO,QAAQ,GAAA,CACb,OAAO,UAAA,CAAW,SAAU,GAAA,EAAK;gBAC/B,OAAO,IAAI,SAAA,IAAa,IAAI,SAAA,CAAU,MAAM;YACxD,CAAW,GACD,IAAA,CAAK,WAAY;gBACjB,KAAA,MAAW,SAAS,OAAO,MAAA,CAAQ;oBACjC,MAAM,iBAAA,CAAmB;gBAC1B;gBAED,OAAO,MAAM;YACvB,CAAS;QACT,CAAO,EACA,KAAA,CAAM,OAAO;IACjB;IAAA;;GAAA,GAKD,YAAY;QACV,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,KAAA,IAAS,CAAE,CAAA;QACtC,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,KAAA,IAAS,CAAE,CAAA;QACtC,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,MAAA,IAAU,CAAE,CAAA;QAIvC,IAAA,IAAS,YAAY,GAAG,aAAa,SAAS,MAAA,EAAQ,YAAY,YAAY,YAAa;YACzF,MAAM,SAAS,QAAA,CAAS,SAAS,CAAA,CAAE,MAAA;YAEnC,IAAA,IAAS,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC/C,QAAA,CAAS,MAAA,CAAO,CAAC,CAAC,CAAA,CAAE,MAAA,GAAS;YAC9B;QACF;QAID,IAAA,IAAS,YAAY,GAAG,aAAa,SAAS,MAAA,EAAQ,YAAY,YAAY,YAAa;YACzF,MAAM,UAAU,QAAA,CAAS,SAAS,CAAA;YAElC,IAAI,QAAQ,IAAA,KAAS,KAAA,GAAW;gBAC9B,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,SAAA,EAAW,QAAQ,IAAI;gBAK7C,IAAI,QAAQ,IAAA,KAAS,KAAA,GAAW;oBAC9B,QAAA,CAAS,QAAQ,IAAI,CAAA,CAAE,aAAA,GAAgB;gBACxC;YACF;YAED,IAAI,QAAQ,MAAA,KAAW,KAAA,GAAW;gBAChC,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,WAAA,EAAa,QAAQ,MAAM;YAClD;QACF;IACF;IAAA;;;;;;;;GAAA,GAWD,YAAY,KAAA,EAAO,KAAA,EAAO;QACxB,IAAI,UAAU,KAAA,GAAW;QAEzB,IAAI,MAAM,IAAA,CAAK,KAAK,CAAA,KAAM,KAAA,GAAW;YACnC,MAAM,IAAA,CAAK,KAAK,CAAA,GAAI,MAAM,IAAA,CAAK,KAAK,CAAA,GAAI;QACzC;QAED,MAAM,IAAA,CAAK,KAAK,CAAA;IACjB;IAAA,uEAAA,GAGD,YAAY,KAAA,EAAO,KAAA,EAAO,MAAA,EAAQ;QAChC,IAAI,MAAM,IAAA,CAAK,KAAK,CAAA,IAAK,GAAG,OAAO;QAEnC,MAAM,MAAM,OAAO,KAAA,CAAO;QAI1B,MAAM,iBAAiB,CAAC,UAAU,UAAU;YAC1C,MAAM,WAAW,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,QAAQ;YAC/C,IAAI,YAAY,MAAM;gBACpB,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,OAAO,QAAQ;YACtC;YAED,KAAA,MAAW,CAAC,GAAG,KAAK,CAAA,IAAK,SAAS,QAAA,CAAS,OAAA,GAAW;gBACpD,eAAe,OAAO,MAAM,QAAA,CAAS,CAAC,CAAC;YACxC;QACF;QAED,eAAe,QAAQ,GAAG;QAE1B,IAAI,IAAA,IAAQ,eAAe,MAAM,IAAA,CAAK,KAAK,CAAA;QAE3C,OAAO;IACR;IAED,WAAW,IAAA,EAAM;QACf,MAAM,aAAa,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO;QAC7C,WAAW,IAAA,CAAK,IAAI;QAEpB,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAK;YAC1C,MAAM,SAAS,KAAK,UAAA,CAAW,CAAC,CAAC;YAEjC,IAAI,QAAQ,OAAO;QACpB;QAED,OAAO;IACR;IAED,WAAW,IAAA,EAAM;QACf,MAAM,aAAa,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO;QAC7C,WAAW,OAAA,CAAQ,IAAI;QAEvB,MAAM,UAAU,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAK;YAC1C,MAAM,SAAS,KAAK,UAAA,CAAW,CAAC,CAAC;YAEjC,IAAI,QAAQ,QAAQ,IAAA,CAAK,MAAM;QAChC;QAED,OAAO;IACR;IAAA;;;;;GAAA,GAQD,cAAc,IAAA,EAAM,KAAA,EAAO;QACzB,MAAM,WAAW,OAAO,MAAM;QAC9B,IAAI,aAAa,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,QAAQ;QAExC,IAAI,CAAC,YAAY;YACf,OAAQ,MAAI;gBACV,KAAK;oBACH,aAAa,IAAA,CAAK,SAAA,CAAU,KAAK;oBACjC;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,IAAI,QAAA,IAAY,IAAI,QAAA,CAAS,KAAK;oBACrD,CAAW;oBACD;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,IAAI,QAAA,IAAY,IAAI,QAAA,CAAS,KAAK;oBACrD,CAAW;oBACD;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,YAAA,CAAa,KAAK;oBACpC;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,IAAI,cAAA,IAAkB,IAAI,cAAA,CAAe,KAAK;oBACjE,CAAW;oBACD;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,KAAK;oBAClC;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,IAAI,YAAA,IAAgB,IAAI,YAAA,CAAa,KAAK;oBAC7D,CAAW;oBACD;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,IAAI,WAAA,IAAe,IAAI,WAAA,CAAY,KAAK;oBAC3D,CAAW;oBACD;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;oBAChC;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,IAAI,aAAA,IAAiB,IAAI,aAAA,CAAc,KAAK;oBAC/D,CAAW;oBACD;gBAEF,KAAK;oBACH,aAAa,IAAA,CAAK,UAAA,CAAW,KAAK;oBAClC;gBAEF;oBACE,aAAa,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;wBAC1C,OAAO,OAAO,IAAA,IAAQ,IAAI,aAAA,IAAiB,IAAI,aAAA,CAAc,MAAM,KAAK;oBACpF,CAAW;oBAED,IAAI,CAAC,YAAY;wBACf,MAAM,IAAI,MAAM,mBAAmB,IAAI;oBACxC;oBAED;YACH;YAED,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,UAAU,UAAU;QACpC;QAED,OAAO;IACR;IAAA;;;;GAAA,GAOD,gBAAgB,IAAA,EAAM;QACpB,IAAI,eAAe,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,IAAI;QAEtC,IAAI,CAAC,cAAc;YACjB,MAAM,SAAS,IAAA;YACf,MAAM,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,SAAS,SAAS,OAAO,GAAA,CAAI,CAAA,IAAK,CAAE,CAAA;YAEnE,eAAe,QAAQ,GAAA,CACrB,KAAK,GAAA,CAAI,SAAU,GAAA,EAAK,KAAA,EAAO;gBAC7B,OAAO,OAAO,aAAA,CAAc,MAAM,KAAK;YACjD,CAAS;YAGH,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,MAAM,YAAY;QAClC;QAED,OAAO;IACR;IAAA;;;;GAAA,GAOD,WAAW,WAAA,EAAa;QACtB,MAAM,YAAY,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,WAAW,CAAA;QAC/C,MAAM,SAAS,IAAA,CAAK,UAAA;QAEpB,IAAI,UAAU,IAAA,IAAQ,UAAU,IAAA,KAAS,eAAe;YACtD,MAAM,IAAI,MAAM,uBAAuB,UAAU,IAAA,GAAO,gCAAgC;QACzF;QAGD,IAAI,UAAU,GAAA,KAAQ,KAAA,KAAa,gBAAgB,GAAG;YACpD,OAAO,QAAQ,OAAA,CAAQ,IAAA,CAAK,UAAA,CAAW,WAAW,eAAe,CAAA,CAAE,IAAI;QACxE;QAED,MAAM,UAAU,IAAA,CAAK,OAAA;QAErB,OAAO,IAAI,QAAQ,SAAU,OAAA,EAAS,MAAA,EAAQ;YAC5C,OAAO,IAAA,oJAAK,cAAA,CAAY,UAAA,CAAW,UAAU,GAAA,EAAK,QAAQ,IAAI,GAAG,SAAS,KAAA,GAAW,WAAY;gBAC/F,OAAO,IAAI,MAAM,8CAA8C,UAAU,GAAA,GAAM,IAAI,CAAC;YAC5F,CAAO;QACP,CAAK;IACF;IAAA;;;;GAAA,GAOD,eAAe,eAAA,EAAiB;QAC9B,MAAM,gBAAgB,IAAA,CAAK,IAAA,CAAK,WAAA,CAAY,eAAe,CAAA;QAE3D,OAAO,IAAA,CAAK,aAAA,CAAc,UAAU,cAAc,MAAM,EAAE,IAAA,CAAK,SAAU,MAAA,EAAQ;YAC/E,MAAM,aAAa,cAAc,UAAA,IAAc;YAC/C,MAAM,aAAa,cAAc,UAAA,IAAc;YAC/C,OAAO,OAAO,KAAA,CAAM,YAAY,aAAa,UAAU;QAC7D,CAAK;IACF;IAAA;;;;GAAA,GAOD,aAAa,aAAA,EAAe;QAC1B,MAAM,SAAS,IAAA;QACf,MAAM,OAAO,IAAA,CAAK,IAAA;QAElB,MAAM,cAAc,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,aAAa,CAAA;QAErD,IAAI,YAAY,UAAA,KAAe,KAAA,KAAa,YAAY,MAAA,KAAW,KAAA,GAAW;YAC5E,MAAM,WAAW,gBAAA,CAAiB,YAAY,IAAI,CAAA;YAClD,MAAM,aAAa,qBAAA,CAAsB,YAAY,aAAa,CAAA;YAClE,MAAM,aAAa,YAAY,UAAA,KAAe;YAE9C,MAAM,QAAQ,IAAI,WAAW,YAAY,KAAA,GAAQ,QAAQ;YACzD,OAAO,QAAQ,OAAA,CAAQ,uJAAI,kBAAA,CAAgB,OAAO,UAAU,UAAU,CAAC;QACxE;QAED,MAAM,qBAAqB,CAAE,CAAA;QAE7B,IAAI,YAAY,UAAA,KAAe,KAAA,GAAW;YACxC,mBAAmB,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,cAAc,YAAY,UAAU,CAAC;QACtF,OAAW;YACL,mBAAmB,IAAA,CAAK,IAAI;QAC7B;QAED,IAAI,YAAY,MAAA,KAAW,KAAA,GAAW;YACpC,mBAAmB,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,cAAc,YAAY,MAAA,CAAO,OAAA,CAAQ,UAAU,CAAC;YAC/F,mBAAmB,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,cAAc,YAAY,MAAA,CAAO,MAAA,CAAO,UAAU,CAAC;QAC/F;QAED,OAAO,QAAQ,GAAA,CAAI,kBAAkB,EAAE,IAAA,CAAK,SAAU,WAAA,EAAa;YACjE,MAAM,aAAa,WAAA,CAAY,CAAC,CAAA;YAEhC,MAAM,WAAW,gBAAA,CAAiB,YAAY,IAAI,CAAA;YAClD,MAAM,aAAa,qBAAA,CAAsB,YAAY,aAAa,CAAA;YAGlE,MAAM,eAAe,WAAW,iBAAA;YAChC,MAAM,YAAY,eAAe;YACjC,MAAM,aAAa,YAAY,UAAA,IAAc;YAC7C,MAAM,aACJ,YAAY,UAAA,KAAe,KAAA,IAAY,KAAK,WAAA,CAAY,YAAY,UAAU,CAAA,CAAE,UAAA,GAAa,KAAA;YAC/F,MAAM,aAAa,YAAY,UAAA,KAAe;YAC9C,IAAI,OAAO;YAGX,IAAI,cAAc,eAAe,WAAW;gBAG1C,MAAM,UAAU,KAAK,KAAA,CAAM,aAAa,UAAU;gBAClD,MAAM,aACJ,uBACA,YAAY,UAAA,GACZ,MACA,YAAY,aAAA,GACZ,MACA,UACA,MACA,YAAY,KAAA;gBACd,IAAI,KAAK,OAAO,KAAA,CAAM,GAAA,CAAI,UAAU;gBAEpC,IAAI,CAAC,IAAI;oBACP,QAAQ,IAAI,WAAW,YAAY,UAAU,YAAa,YAAY,KAAA,GAAQ,aAAc,YAAY;oBAGxG,KAAK,uJAAI,oBAAA,CAAkB,OAAO,aAAa,YAAY;oBAE3D,OAAO,KAAA,CAAM,GAAA,CAAI,YAAY,EAAE;gBAChC;gBAED,kBAAkB,uJAAI,6BAAA,CACpB,IACA,UACC,aAAa,aAAc,cAC5B;YAEV,OAAa;gBACL,IAAI,eAAe,MAAM;oBACvB,QAAQ,IAAI,WAAW,YAAY,KAAA,GAAQ,QAAQ;gBAC7D,OAAe;oBACL,QAAQ,IAAI,WAAW,YAAY,YAAY,YAAY,KAAA,GAAQ,QAAQ;gBAC5E;gBAED,kBAAkB,uJAAI,kBAAA,CAAgB,OAAO,UAAU,UAAU;YAClE;YAGD,IAAI,YAAY,MAAA,KAAW,KAAA,GAAW;gBACpC,MAAM,kBAAkB,iBAAiB,MAAA;gBACzC,MAAM,oBAAoB,qBAAA,CAAsB,YAAY,MAAA,CAAO,OAAA,CAAQ,aAAa,CAAA;gBAExF,MAAM,oBAAoB,YAAY,MAAA,CAAO,OAAA,CAAQ,UAAA,IAAc;gBACnE,MAAM,mBAAmB,YAAY,MAAA,CAAO,MAAA,CAAO,UAAA,IAAc;gBAEjE,MAAM,gBAAgB,IAAI,kBACxB,WAAA,CAAY,CAAC,CAAA,EACb,mBACA,YAAY,MAAA,CAAO,KAAA,GAAQ;gBAE7B,MAAM,eAAe,IAAI,WAAW,WAAA,CAAY,CAAC,CAAA,EAAG,kBAAkB,YAAY,MAAA,CAAO,KAAA,GAAQ,QAAQ;gBAEzG,IAAI,eAAe,MAAM;oBAEvB,kBAAkB,uJAAI,kBAAA,CACpB,gBAAgB,KAAA,CAAM,KAAA,CAAO,GAC7B,gBAAgB,QAAA,EAChB,gBAAgB,UAAA;gBAEnB;gBAED,IAAA,IAAS,IAAI,GAAG,KAAK,cAAc,MAAA,EAAQ,IAAI,IAAI,IAAK;oBACtD,MAAM,QAAQ,aAAA,CAAc,CAAC,CAAA;oBAE7B,gBAAgB,IAAA,CAAK,OAAO,YAAA,CAAa,IAAI,QAAQ,CAAC;oBACtD,IAAI,YAAY,GAAG,gBAAgB,IAAA,CAAK,OAAO,YAAA,CAAa,IAAI,WAAW,CAAC,CAAC;oBAC7E,IAAI,YAAY,GAAG,gBAAgB,IAAA,CAAK,OAAO,YAAA,CAAa,IAAI,WAAW,CAAC,CAAC;oBAC7E,IAAI,YAAY,GAAG,gBAAgB,IAAA,CAAK,OAAO,YAAA,CAAa,IAAI,WAAW,CAAC,CAAC;oBAC7E,IAAI,YAAY,GAAG,MAAM,IAAI,MAAM,mEAAmE;gBACvG;YACF;YAED,OAAO;QACb,CAAK;IACF;IAAA;;;;GAAA,GAOD,YAAY,YAAA,EAAc;QACxB,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,UAAU,IAAA,CAAK,OAAA;QACrB,MAAM,aAAa,KAAK,QAAA,CAAS,YAAY,CAAA;QAC7C,MAAM,cAAc,WAAW,MAAA;QAC/B,MAAM,YAAY,KAAK,MAAA,CAAO,WAAW,CAAA;QAEzC,IAAI,SAAS,IAAA,CAAK,aAAA;QAElB,IAAI,UAAU,GAAA,EAAK;YACjB,MAAM,UAAU,QAAQ,OAAA,CAAQ,UAAA,CAAW,UAAU,GAAG;YACxD,IAAI,YAAY,MAAM,SAAS;QAChC;QAED,OAAO,IAAA,CAAK,gBAAA,CAAiB,cAAc,aAAa,MAAM;IAC/D;IAED,iBAAiB,YAAA,EAAc,WAAA,EAAa,MAAA,EAAQ;QAClD,MAAM,SAAS,IAAA;QACf,MAAM,OAAO,IAAA,CAAK,IAAA;QAElB,MAAM,aAAa,KAAK,QAAA,CAAS,YAAY,CAAA;QAC7C,MAAM,YAAY,KAAK,MAAA,CAAO,WAAW,CAAA;QAEzC,MAAM,WAAA,CAAY,UAAU,GAAA,IAAO,UAAU,UAAA,IAAc,MAAM,WAAW,OAAA;QAE5E,IAAI,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG;YAE/B,OAAO,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA;QAClC;QAED,MAAM,UAAU,IAAA,CAAK,eAAA,CAAgB,aAAa,MAAM,EACrD,IAAA,CAAK,SAAU,OAAA,EAAS;YACvB,QAAQ,KAAA,GAAQ;YAEhB,QAAQ,IAAA,GAAO,WAAW,IAAA,IAAQ,UAAU,IAAA,IAAQ;YAEpD,IACE,QAAQ,IAAA,KAAS,MACjB,OAAO,UAAU,GAAA,KAAQ,YACzB,UAAU,GAAA,CAAI,UAAA,CAAW,aAAa,MAAM,OAC5C;gBACA,QAAQ,IAAA,GAAO,UAAU,GAAA;YAC1B;YAED,MAAM,WAAW,KAAK,QAAA,IAAY,CAAE;YACpC,MAAM,UAAU,QAAA,CAAS,WAAW,OAAO,CAAA,IAAK,CAAE;YAElD,QAAQ,SAAA,GAAY,aAAA,CAAc,QAAQ,SAAS,CAAA,uJAAK,eAAA;YACxD,QAAQ,SAAA,GAAY,aAAA,CAAc,QAAQ,SAAS,CAAA,uJAAK,2BAAA;YACxD,QAAQ,KAAA,GAAQ,eAAA,CAAgB,QAAQ,KAAK,CAAA,IAAK,oKAAA;YAClD,QAAQ,KAAA,GAAQ,eAAA,CAAgB,QAAQ,KAAK,CAAA,uJAAK,iBAAA;YAElD,OAAO,YAAA,CAAa,GAAA,CAAI,SAAS;gBAAE,UAAU;YAAA,CAAc;YAE3D,OAAO;QACf,CAAO,EACA,KAAA,CAAM,WAAY;YACjB,OAAO;QACf,CAAO;QAEH,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,GAAI;QAE9B,OAAO;IACR;IAED,gBAAgB,WAAA,EAAa,MAAA,EAAQ;QACnC,MAAM,SAAS,IAAA;QACf,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,UAAU,IAAA,CAAK,OAAA;QAErB,IAAI,IAAA,CAAK,WAAA,CAAY,WAAW,CAAA,KAAM,KAAA,GAAW;YAC/C,OAAO,IAAA,CAAK,WAAA,CAAY,WAAW,CAAA,CAAE,IAAA,CAAK,CAAC,UAAY,QAAQ,KAAA,EAAO;QACvE;QAED,MAAM,YAAY,KAAK,MAAA,CAAO,WAAW,CAAA;QAEzC,MAAM,MAAM,KAAK,GAAA,IAAO,KAAK,SAAA;QAE7B,IAAI,YAAY,UAAU,GAAA,IAAO;QACjC,IAAI,cAAc;QAElB,IAAI,UAAU,UAAA,KAAe,KAAA,GAAW;YAGtC,YAAY,OAAO,aAAA,CAAc,cAAc,UAAU,UAAU,EAAE,IAAA,CAAK,SAAU,UAAA,EAAY;gBAC9F,cAAc;gBACd,MAAM,OAAO,IAAI,KAAK;oBAAC,UAAU;iBAAA,EAAG;oBAAE,MAAM,UAAU,QAAA;gBAAA,CAAU;gBAChE,YAAY,IAAI,eAAA,CAAgB,IAAI;gBACpC,OAAO;YACf,CAAO;QACP,OAAA,IAAe,UAAU,GAAA,KAAQ,KAAA,GAAW;YACtC,MAAM,IAAI,MAAM,6BAA6B,cAAc,gCAAgC;QAC5F;QAED,MAAM,UAAU,QAAQ,OAAA,CAAQ,SAAS,EACtC,IAAA,CAAK,SAAUC,UAAAA,EAAW;YACzB,OAAO,IAAI,QAAQ,SAAU,OAAA,EAAS,MAAA,EAAQ;gBAC5C,IAAI,SAAS;gBAEb,IAAI,OAAO,mBAAA,KAAwB,MAAM;oBACvC,SAAS,SAAU,WAAA,EAAa;wBAC9B,MAAM,UAAU,uJAAI,UAAA,CAAQ,WAAW;wBACvC,QAAQ,WAAA,GAAc;wBAEtB,QAAQ,OAAO;oBAChB;gBACF;gBAED,OAAO,IAAA,oJAAK,cAAA,CAAY,UAAA,CAAWA,YAAW,QAAQ,IAAI,GAAG,QAAQ,KAAA,GAAW,MAAM;YAChG,CAAS;QACT,CAAO,EACA,IAAA,CAAK,SAAU,OAAA,EAAS;YAGvB,IAAI,gBAAgB,MAAM;gBACxB,IAAI,eAAA,CAAgB,SAAS;YAC9B;YAED,uBAAuB,SAAS,SAAS;YAEzC,QAAQ,QAAA,CAAS,QAAA,GAAW,UAAU,QAAA,IAAY,oBAAoB,UAAU,GAAG;YAEnF,OAAO;QACf,CAAO,EACA,KAAA,CAAM,SAAU,KAAA,EAAO;YACtB,QAAQ,KAAA,CAAM,2CAA2C,SAAS;YAClE,MAAM;QACd,CAAO;QAEH,IAAA,CAAK,WAAA,CAAY,WAAW,CAAA,GAAI;QAChC,OAAO;IACR;IAAA;;;;;;GAAA,GASD,cAAc,cAAA,EAAgB,OAAA,EAAS,MAAA,EAAQ,UAAA,EAAY;QACzD,MAAM,SAAS,IAAA;QAEf,OAAO,IAAA,CAAK,aAAA,CAAc,WAAW,OAAO,KAAK,EAAE,IAAA,CAAK,SAAU,OAAA,EAAS;YACzE,IAAI,CAAC,SAAS,OAAO;YAErB,IAAI,OAAO,QAAA,KAAa,KAAA,KAAa,OAAO,QAAA,GAAW,GAAG;gBACxD,UAAU,QAAQ,KAAA,CAAO;gBACzB,QAAQ,OAAA,GAAU,OAAO,QAAA;YAC1B;YAED,IAAI,OAAO,UAAA,CAAW,WAAW,qBAAqB,CAAA,EAAG;gBACvD,MAAM,YACJ,OAAO,UAAA,KAAe,KAAA,IAAY,OAAO,UAAA,CAAW,WAAW,qBAAqB,CAAA,GAAI,KAAA;gBAE1F,IAAI,WAAW;oBACb,MAAM,gBAAgB,OAAO,YAAA,CAAa,GAAA,CAAI,OAAO;oBACrD,UAAU,OAAO,UAAA,CAAW,WAAW,qBAAqB,CAAA,CAAE,aAAA,CAAc,SAAS,SAAS;oBAC9F,OAAO,YAAA,CAAa,GAAA,CAAI,SAAS,aAAa;gBAC/C;YACF;YAED,IAAI,eAAe,KAAA,GAAW;gBAE5B,IAAI,OAAO,eAAe,UACxB,aAAa,eAAe,eAAe,iBAAiB;gBAG9D,IAAI,gBAAgB,SAAS,QAAQ,UAAA,GAAa;qBAC7C,QAAQ,QAAA,GAAW,eAAe,iBAAiB,eAAe;YACxE;YAED,cAAA,CAAe,OAAO,CAAA,GAAI;YAE1B,OAAO;QACb,CAAK;IACF;IAAA;;;;;;;GAAA,GAUD,oBAAoB,IAAA,EAAM;QACxB,MAAM,WAAW,KAAK,QAAA;QACtB,IAAI,WAAW,KAAK,QAAA;QAEpB,MAAM,wBAAwB,SAAS,UAAA,CAAW,OAAA,KAAY,KAAA;QAC9D,MAAM,kBAAkB,SAAS,UAAA,CAAW,KAAA,KAAU,KAAA;QACtD,MAAM,iBAAiB,SAAS,UAAA,CAAW,MAAA,KAAW,KAAA;QAEtD,IAAI,KAAK,QAAA,EAAU;YACjB,MAAM,WAAW,oBAAoB,SAAS,IAAA;YAE9C,IAAI,iBAAiB,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,QAAQ;YAE5C,IAAI,CAAC,gBAAgB;gBACnB,iBAAiB,uJAAI,iBAAA,CAAgB;gBACrC,kJAAA,CAAA,WAAA,CAAS,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAgB,QAAQ;gBACrD,eAAe,KAAA,CAAM,IAAA,CAAK,SAAS,KAAK;gBACxC,eAAe,GAAA,GAAM,SAAS,GAAA;gBAC9B,eAAe,eAAA,GAAkB;gBAEjC,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,UAAU,cAAc;YACxC;YAED,WAAW;QACjB,OAAA,IAAe,KAAK,MAAA,EAAQ;YACtB,MAAM,WAAW,uBAAuB,SAAS,IAAA;YAEjD,IAAI,eAAe,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,QAAQ;YAE1C,IAAI,CAAC,cAAc;gBACjB,eAAe,uJAAI,oBAAA,CAAmB;gBACtC,kJAAA,CAAA,WAAA,CAAS,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,cAAc,QAAQ;gBACnD,aAAa,KAAA,CAAM,IAAA,CAAK,SAAS,KAAK;gBACtC,aAAa,GAAA,GAAM,SAAS,GAAA;gBAE5B,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,UAAU,YAAY;YACtC;YAED,WAAW;QACZ;QAGD,IAAI,yBAAyB,mBAAmB,gBAAgB;YAC9D,IAAI,WAAW,oBAAoB,SAAS,IAAA,GAAO;YAEnD,IAAI,uBAAuB,YAAY;YACvC,IAAI,iBAAiB,YAAY;YACjC,IAAI,gBAAgB,YAAY;YAEhC,IAAI,iBAAiB,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,QAAQ;YAE5C,IAAI,CAAC,gBAAgB;gBACnB,iBAAiB,SAAS,KAAA,CAAO;gBAEjC,IAAI,iBAAiB,eAAe,YAAA,GAAe;gBACnD,IAAI,gBAAgB,eAAe,WAAA,GAAc;gBAEjD,IAAI,uBAAuB;oBAEzB,IAAI,eAAe,WAAA,EAAa,eAAe,WAAA,CAAY,CAAA,IAAK,CAAA;oBAChE,IAAI,eAAe,oBAAA,EAAsB,eAAe,oBAAA,CAAqB,CAAA,IAAK,CAAA;gBACnF;gBAED,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,UAAU,cAAc;gBAEvC,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,gBAAgB,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,QAAQ,CAAC;YACtE;YAED,WAAW;QACZ;QAED,KAAK,QAAA,GAAW;IACjB;IAED,kBAAqC;QACnC,OAAO,0KAAA;IACR;IAAA;;;;GAAA,GAOD,aAAa,aAAA,EAAe;QAC1B,MAAM,SAAS,IAAA;QACf,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,aAAa,IAAA,CAAK,UAAA;QACxB,MAAM,cAAc,KAAK,SAAA,CAAU,aAAa,CAAA;QAEhD,IAAI;QACJ,MAAM,iBAAiB,CAAE;QACzB,MAAM,qBAAqB,YAAY,UAAA,IAAc,CAAE;QAEvD,MAAM,UAAU,CAAE,CAAA;QAElB,IAAI,kBAAA,CAAmB,WAAW,mBAAmB,CAAA,EAAG;YACtD,MAAM,eAAe,UAAA,CAAW,WAAW,mBAAmB,CAAA;YAC9D,eAAe,aAAa,eAAA,CAAiB;YAC7C,QAAQ,IAAA,CAAK,aAAa,YAAA,CAAa,gBAAgB,aAAa,MAAM,CAAC;QACjF,OAAW;YAIL,MAAM,oBAAoB,YAAY,oBAAA,IAAwB,CAAE;YAEhE,eAAe,KAAA,GAAQ,uJAAI,QAAA,CAAM,GAAK,GAAK,CAAG;YAC9C,eAAe,OAAA,GAAU;YAEzB,IAAI,MAAM,OAAA,CAAQ,kBAAkB,eAAe,GAAG;gBACpD,MAAM,QAAQ,kBAAkB,eAAA;gBAEhC,eAAe,KAAA,CAAM,MAAA,CAAO,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA,EAAG,oBAAoB;gBAC9E,eAAe,OAAA,GAAU,KAAA,CAAM,CAAC,CAAA;YACjC;YAED,IAAI,kBAAkB,gBAAA,KAAqB,KAAA,GAAW;gBACpD,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,OAAO,kBAAkB,gBAAA,EAAkB,cAAc,CAAC;YAC7G;YAED,eAAe,SAAA,GAAY,kBAAkB,cAAA,KAAmB,KAAA,IAAY,kBAAkB,cAAA,GAAiB;YAC/G,eAAe,SAAA,GACb,kBAAkB,eAAA,KAAoB,KAAA,IAAY,kBAAkB,eAAA,GAAkB;YAExF,IAAI,kBAAkB,wBAAA,KAA6B,KAAA,GAAW;gBAC5D,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,gBAAgB,kBAAkB,wBAAwB,CAAC;gBAC7G,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,gBAAgB,kBAAkB,wBAAwB,CAAC;YAC9G;YAED,eAAe,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;gBAC5C,OAAO,IAAI,eAAA,IAAmB,IAAI,eAAA,CAAgB,aAAa;YACvE,CAAO;YAED,QAAQ,IAAA,CACN,QAAQ,GAAA,CACN,IAAA,CAAK,UAAA,CAAW,SAAU,GAAA,EAAK;gBAC7B,OAAO,IAAI,oBAAA,IAAwB,IAAI,oBAAA,CAAqB,eAAe,cAAc;YACrG,CAAW;QAGN;QAED,IAAI,YAAY,WAAA,KAAgB,MAAM;YACpC,eAAe,IAAA,sJAAO,aAAA;QACvB;QAED,MAAM,YAAY,YAAY,SAAA,IAAa,YAAY,MAAA;QAEvD,IAAI,cAAc,YAAY,KAAA,EAAO;YACnC,eAAe,WAAA,GAAc;YAG7B,eAAe,UAAA,GAAa;QAClC,OAAW;YACL,eAAe,WAAA,GAAc;YAE7B,IAAI,cAAc,YAAY,IAAA,EAAM;gBAClC,eAAe,SAAA,GAAY,YAAY,WAAA,KAAgB,KAAA,IAAY,YAAY,WAAA,GAAc;YAC9F;QACF;QAED,IAAI,YAAY,aAAA,KAAkB,KAAA,KAAa,oKAAiB,oBAAA,EAAmB;YACjF,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,aAAa,YAAY,aAAa,CAAC;YAEzF,eAAe,WAAA,GAAc,uJAAI,UAAA,CAAQ,GAAG,CAAC;YAE7C,IAAI,YAAY,aAAA,CAAc,KAAA,KAAU,KAAA,GAAW;gBACjD,MAAM,QAAQ,YAAY,aAAA,CAAc,KAAA;gBAExC,eAAe,WAAA,CAAY,GAAA,CAAI,OAAO,KAAK;YAC5C;QACF;QAED,IAAI,YAAY,gBAAA,KAAqB,KAAA,KAAa,oKAAiB,oBAAA,EAAmB;YACpF,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,SAAS,YAAY,gBAAgB,CAAC;YAExF,IAAI,YAAY,gBAAA,CAAiB,QAAA,KAAa,KAAA,GAAW;gBACvD,eAAe,cAAA,GAAiB,YAAY,gBAAA,CAAiB,QAAA;YAC9D;QACF;QAED,IAAI,YAAY,cAAA,KAAmB,KAAA,KAAa,oKAAiB,oBAAA,EAAmB;YAClF,MAAM,iBAAiB,YAAY,cAAA;YACnC,eAAe,QAAA,GAAW,IAAI,2JAAA,CAAK,EAAG,MAAA,CACpC,cAAA,CAAe,CAAC,CAAA,EAChB,cAAA,CAAe,CAAC,CAAA,EAChB,cAAA,CAAe,CAAC,CAAA,EAChB;QAEH;QAED,IAAI,YAAY,eAAA,KAAoB,KAAA,KAAa,oKAAiB,oBAAA,EAAmB;YACnF,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,gBAAgB,eAAe,YAAY,eAAA,EAAiB,cAAc,CAAC;QAC9G;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,WAAY;YAC3C,MAAM,WAAW,IAAI,aAAa,cAAc;YAEhD,IAAI,YAAY,IAAA,EAAM,SAAS,IAAA,GAAO,YAAY,IAAA;YAElD,uBAAuB,UAAU,WAAW;YAE5C,OAAO,YAAA,CAAa,GAAA,CAAI,UAAU;gBAAE,WAAW;YAAA,CAAe;YAE9D,IAAI,YAAY,UAAA,EAAY,+BAA+B,YAAY,UAAU,WAAW;YAE5F,OAAO;QACb,CAAK;IACF;IAAA,+EAAA,GAGD,iBAAiB,YAAA,EAAc;QAC7B,MAAM,mKAAgB,kBAAA,CAAgB,gBAAA,CAAiB,gBAAgB,EAAE;QAEzE,IAAI,iBAAiB,IAAA,CAAK,aAAA,EAAe;YACvC,OAAO,gBAAgB,MAAM,EAAE,IAAA,CAAK,aAAA,CAAc,aAAa,CAAA;QACrE,OAAW;YACL,IAAA,CAAK,aAAA,CAAc,aAAa,CAAA,GAAI;YAEpC,OAAO;QACR;IACF;IAAA;;;;;;;GAAA,GAUD,eAAe,UAAA,EAAY;QACzB,MAAM,SAAS,IAAA;QACf,MAAM,aAAa,IAAA,CAAK,UAAA;QACxB,MAAM,QAAQ,IAAA,CAAK,cAAA;QAEnB,SAAS,qBAAqB,SAAA,EAAW;YACvC,OAAO,UAAA,CAAW,WAAW,0BAA0B,CAAA,CACpD,eAAA,CAAgB,WAAW,MAAM,EACjC,IAAA,CAAK,SAAU,QAAA,EAAU;gBACxB,OAAO,uBAAuB,UAAU,WAAW,MAAM;YACnE,CAAS;QACJ;QAED,MAAM,UAAU,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,KAAK,WAAW,MAAA,EAAQ,IAAI,IAAI,IAAK;YACnD,MAAM,YAAY,UAAA,CAAW,CAAC,CAAA;YAC9B,MAAM,WAAW,mBAAmB,SAAS;YAG7C,MAAM,SAAS,KAAA,CAAM,QAAQ,CAAA;YAE7B,IAAI,QAAQ;gBAEV,QAAQ,IAAA,CAAK,OAAO,OAAO;YACnC,OAAa;gBACL,IAAI;gBAEJ,IAAI,UAAU,UAAA,IAAc,UAAU,UAAA,CAAW,WAAW,0BAA0B,CAAA,EAAG;oBAEvF,kBAAkB,qBAAqB,SAAS;gBAC1D,OAAe;oBAEL,kBAAkB,uBAAuB,uJAAI,iBAAA,CAAc,GAAI,WAAW,MAAM;gBACjF;gBAGD,KAAA,CAAM,QAAQ,CAAA,GAAI;oBAAE;oBAAsB,SAAS;gBAAiB;gBAEpE,QAAQ,IAAA,CAAK,eAAe;YAC7B;QACF;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO;IAC3B;IAAA;;;;GAAA,GAOD,SAAS,SAAA,EAAW;QAClB,MAAM,SAAS,IAAA;QACf,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,aAAa,IAAA,CAAK,UAAA;QAExB,MAAM,UAAU,KAAK,MAAA,CAAO,SAAS,CAAA;QACrC,MAAM,aAAa,QAAQ,UAAA;QAE3B,MAAM,UAAU,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,KAAK,WAAW,MAAA,EAAQ,IAAI,IAAI,IAAK;YACnD,MAAM,WACJ,UAAA,CAAW,CAAC,CAAA,CAAE,QAAA,KAAa,KAAA,IACvB,sBAAsB,IAAA,CAAK,KAAK,IAChC,IAAA,CAAK,aAAA,CAAc,YAAY,UAAA,CAAW,CAAC,CAAA,CAAE,QAAQ;YAE3D,QAAQ,IAAA,CAAK,QAAQ;QACtB;QAED,QAAQ,IAAA,CAAK,OAAO,cAAA,CAAe,UAAU,CAAC;QAE9C,OAAO,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,SAAU,OAAA,EAAS;YAClD,MAAM,YAAY,QAAQ,KAAA,CAAM,GAAG,QAAQ,MAAA,GAAS,CAAC;YACrD,MAAM,aAAa,OAAA,CAAQ,QAAQ,MAAA,GAAS,CAAC,CAAA;YAE7C,MAAM,SAAS,CAAE,CAAA;YAEjB,IAAA,IAAS,IAAI,GAAG,KAAK,WAAW,MAAA,EAAQ,IAAI,IAAI,IAAK;gBACnD,MAAM,WAAW,UAAA,CAAW,CAAC,CAAA;gBAC7B,MAAM,YAAY,UAAA,CAAW,CAAC,CAAA;gBAI9B,IAAI;gBAEJ,MAAM,WAAW,SAAA,CAAU,CAAC,CAAA;gBAE5B,IACE,UAAU,IAAA,KAAS,gBAAgB,SAAA,IACnC,UAAU,IAAA,KAAS,gBAAgB,cAAA,IACnC,UAAU,IAAA,KAAS,gBAAgB,YAAA,IACnC,UAAU,IAAA,KAAS,KAAA,GACnB;oBAEA,OAAO,QAAQ,aAAA,KAAkB,OAAO,uJAAI,cAAA,CAAY,UAAU,QAAQ,IAAI,uJAAI,OAAA,CAAK,UAAU,QAAQ;oBAEzG,IAAI,KAAK,aAAA,KAAkB,MAAM;wBAE/B,KAAK,oBAAA,CAAsB;oBAC5B;oBAED,IAAI,UAAU,IAAA,KAAS,gBAAgB,cAAA,EAAgB;wBACrD,KAAK,QAAA,0KAAW,sBAAA,EAAoB,KAAK,QAAA,qJAAU,wBAAqB;oBACzE,OAAA,IAAU,UAAU,IAAA,KAAS,gBAAgB,YAAA,EAAc;wBAC1D,KAAK,QAAA,0KAAW,sBAAA,EAAoB,KAAK,QAAA,qJAAU,sBAAmB;oBACvE;gBACF,OAAA,IAAU,UAAU,IAAA,KAAS,gBAAgB,KAAA,EAAO;oBACnD,OAAO,uJAAI,eAAA,CAAa,UAAU,QAAQ;gBAC3C,OAAA,IAAU,UAAU,IAAA,KAAS,gBAAgB,UAAA,EAAY;oBACxD,OAAO,IAAI,0JAAA,CAAK,UAAU,QAAQ;gBACnC,OAAA,IAAU,UAAU,IAAA,KAAS,gBAAgB,SAAA,EAAW;oBACvD,OAAO,uJAAI,WAAA,CAAS,UAAU,QAAQ;gBACvC,OAAA,IAAU,UAAU,IAAA,KAAS,gBAAgB,MAAA,EAAQ;oBACpD,OAAO,uJAAI,SAAA,CAAO,UAAU,QAAQ;gBAC9C,OAAe;oBACL,MAAM,IAAI,MAAM,mDAAmD,UAAU,IAAI;gBAClF;gBAED,IAAI,OAAO,IAAA,CAAK,KAAK,QAAA,CAAS,eAAe,EAAE,MAAA,GAAS,GAAG;oBACzD,mBAAmB,MAAM,OAAO;gBACjC;gBAED,KAAK,IAAA,GAAO,OAAO,gBAAA,CAAiB,QAAQ,IAAA,IAAQ,UAAU,SAAS;gBAEvE,uBAAuB,MAAM,OAAO;gBAEpC,IAAI,UAAU,UAAA,EAAY,+BAA+B,YAAY,MAAM,SAAS;gBAEpF,OAAO,mBAAA,CAAoB,IAAI;gBAE/B,OAAO,IAAA,CAAK,IAAI;YACjB;YAED,IAAA,IAAS,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC/C,OAAO,YAAA,CAAa,GAAA,CAAI,MAAA,CAAO,CAAC,CAAA,EAAG;oBACjC,QAAQ;oBACR,YAAY;gBACtB,CAAS;YACF;YAED,IAAI,OAAO,MAAA,KAAW,GAAG;gBACvB,IAAI,QAAQ,UAAA,EAAY,+BAA+B,YAAY,MAAA,CAAO,CAAC,CAAA,EAAG,OAAO;gBAErF,OAAO,MAAA,CAAO,CAAC,CAAA;YAChB;YAED,MAAM,QAAQ,uJAAI,QAAA,CAAO;YAEzB,IAAI,QAAQ,UAAA,EAAY,+BAA+B,YAAY,OAAO,OAAO;YAEjF,OAAO,YAAA,CAAa,GAAA,CAAI,OAAO;gBAAE,QAAQ;YAAA,CAAW;YAEpD,IAAA,IAAS,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC/C,MAAM,GAAA,CAAI,MAAA,CAAO,CAAC,CAAC;YACpB;YAED,OAAO;QACb,CAAK;IACF;IAAA;;;;GAAA,GAOD,WAAW,WAAA,EAAa;QACtB,IAAI;QACJ,MAAM,YAAY,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,WAAW,CAAA;QAC/C,MAAM,SAAS,SAAA,CAAU,UAAU,IAAI,CAAA;QAEvC,IAAI,CAAC,QAAQ;YACX,QAAQ,IAAA,CAAK,8CAA8C;YAC3D;QACD;QAED,IAAI,UAAU,IAAA,KAAS,eAAe;YACpC,SAAS,uJAAI,oBAAA,CACX,+JAAA,CAAU,QAAA,CAAS,OAAO,IAAI,GAC9B,OAAO,WAAA,IAAe,GACtB,OAAO,KAAA,IAAS,GAChB,OAAO,IAAA,IAAQ;QAEvB,OAAA,IAAe,UAAU,IAAA,KAAS,gBAAgB;YAC5C,SAAS,uJAAI,qBAAA,CAAmB,CAAC,OAAO,IAAA,EAAM,OAAO,IAAA,EAAM,OAAO,IAAA,EAAM,CAAC,OAAO,IAAA,EAAM,OAAO,KAAA,EAAO,OAAO,IAAI;QAChH;QAED,IAAI,UAAU,IAAA,EAAM,OAAO,IAAA,GAAO,IAAA,CAAK,gBAAA,CAAiB,UAAU,IAAI;QAEtE,uBAAuB,QAAQ,SAAS;QAExC,OAAO,QAAQ,OAAA,CAAQ,MAAM;IAC9B;IAAA;;;;GAAA,GAOD,SAAS,SAAA,EAAW;QAClB,MAAM,UAAU,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,SAAS,CAAA;QAEzC,MAAM,UAAU,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,MAAA,CAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;YACvD,QAAQ,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,QAAQ,MAAA,CAAO,CAAC,CAAC,CAAC;QACtD;QAED,IAAI,QAAQ,mBAAA,KAAwB,KAAA,GAAW;YAC7C,QAAQ,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,YAAY,QAAQ,mBAAmB,CAAC;QAC9E,OAAW;YACL,QAAQ,IAAA,CAAK,IAAI;QAClB;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,SAAU,OAAA,EAAS;YAClD,MAAM,sBAAsB,QAAQ,GAAA,CAAK;YACzC,MAAM,aAAa;YAKnB,MAAM,QAAQ,CAAE,CAAA;YAChB,MAAM,eAAe,CAAE,CAAA;YAEvB,IAAA,IAAS,IAAI,GAAG,KAAK,WAAW,MAAA,EAAQ,IAAI,IAAI,IAAK;gBACnD,MAAM,YAAY,UAAA,CAAW,CAAC,CAAA;gBAE9B,IAAI,WAAW;oBACb,MAAM,IAAA,CAAK,SAAS;oBAEpB,MAAM,MAAM,uJAAI,UAAA,CAAS;oBAEzB,IAAI,wBAAwB,MAAM;wBAChC,IAAI,SAAA,CAAU,oBAAoB,KAAA,EAAO,IAAI,EAAE;oBAChD;oBAED,aAAa,IAAA,CAAK,GAAG;gBAC/B,OAAe;oBACL,QAAQ,IAAA,CAAK,oDAAoD,QAAQ,MAAA,CAAO,CAAC,CAAC;gBACnF;YACF;YAED,OAAO,uJAAI,WAAA,CAAS,OAAO,YAAY;QAC7C,CAAK;IACF;IAAA;;;;GAAA,GAOD,cAAc,cAAA,EAAgB;QAC5B,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,SAAS,IAAA;QAEf,MAAM,eAAe,KAAK,UAAA,CAAW,cAAc,CAAA;QACnD,MAAM,gBAAgB,aAAa,IAAA,GAAO,aAAa,IAAA,GAAO,eAAe;QAE7E,MAAM,eAAe,CAAE,CAAA;QACvB,MAAM,wBAAwB,CAAE,CAAA;QAChC,MAAM,yBAAyB,CAAE,CAAA;QACjC,MAAM,kBAAkB,CAAE,CAAA;QAC1B,MAAM,iBAAiB,CAAE,CAAA;QAEzB,IAAA,IAAS,IAAI,GAAG,KAAK,aAAa,QAAA,CAAS,MAAA,EAAQ,IAAI,IAAI,IAAK;YAC9D,MAAM,UAAU,aAAa,QAAA,CAAS,CAAC,CAAA;YACvC,MAAM,UAAU,aAAa,QAAA,CAAS,QAAQ,OAAO,CAAA;YACrD,MAAM,SAAS,QAAQ,MAAA;YACvB,MAAM,OAAO,OAAO,IAAA;YACpB,MAAM,QAAQ,aAAa,UAAA,KAAe,KAAA,IAAY,aAAa,UAAA,CAAW,QAAQ,KAAK,CAAA,GAAI,QAAQ,KAAA;YACvG,MAAM,SAAS,aAAa,UAAA,KAAe,KAAA,IAAY,aAAa,UAAA,CAAW,QAAQ,MAAM,CAAA,GAAI,QAAQ,MAAA;YAEzG,IAAI,OAAO,IAAA,KAAS,KAAA,GAAW;YAE/B,aAAa,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,QAAQ,IAAI,CAAC;YAClD,sBAAsB,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,YAAY,KAAK,CAAC;YAChE,uBAAuB,IAAA,CAAK,IAAA,CAAK,aAAA,CAAc,YAAY,MAAM,CAAC;YAClE,gBAAgB,IAAA,CAAK,OAAO;YAC5B,eAAe,IAAA,CAAK,MAAM;QAC3B;QAED,OAAO,QAAQ,GAAA,CAAI;YACjB,QAAQ,GAAA,CAAI,YAAY;YACxB,QAAQ,GAAA,CAAI,qBAAqB;YACjC,QAAQ,GAAA,CAAI,sBAAsB;YAClC,QAAQ,GAAA,CAAI,eAAe;YAC3B,QAAQ,GAAA,CAAI,cAAc;SAC3B,EAAE,IAAA,CAAK,SAAU,YAAA,EAAc;YAC9B,MAAM,QAAQ,YAAA,CAAa,CAAC,CAAA;YAC5B,MAAM,iBAAiB,YAAA,CAAa,CAAC,CAAA;YACrC,MAAM,kBAAkB,YAAA,CAAa,CAAC,CAAA;YACtC,MAAM,WAAW,YAAA,CAAa,CAAC,CAAA;YAC/B,MAAM,UAAU,YAAA,CAAa,CAAC,CAAA;YAE9B,MAAM,SAAS,CAAE,CAAA;YAEjB,IAAA,IAAS,IAAI,GAAG,KAAK,MAAM,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC9C,MAAM,OAAO,KAAA,CAAM,CAAC,CAAA;gBACpB,MAAM,gBAAgB,cAAA,CAAe,CAAC,CAAA;gBACtC,MAAM,iBAAiB,eAAA,CAAgB,CAAC,CAAA;gBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;gBAC1B,MAAM,SAAS,OAAA,CAAQ,CAAC,CAAA;gBAExB,IAAI,SAAS,KAAA,GAAW;gBAExB,IAAI,KAAK,YAAA,EAAc;oBACrB,KAAK,YAAA,CAAc;gBACpB;gBAED,MAAM,gBAAgB,OAAO,sBAAA,CAAuB,MAAM,eAAe,gBAAgB,SAAS,MAAM;gBAExG,IAAI,eAAe;oBACjB,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,MAAA,EAAQ,IAAK;wBAC7C,OAAO,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC;oBAC7B;gBACF;YACF;YAED,OAAO,uJAAI,gBAAA,CAAc,eAAe,KAAA,GAAW,MAAM;QAC/D,CAAK;IACF;IAED,eAAe,SAAA,EAAW;QACxB,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,SAAS,IAAA;QACf,MAAM,UAAU,KAAK,KAAA,CAAM,SAAS,CAAA;QAEpC,IAAI,QAAQ,IAAA,KAAS,KAAA,GAAW,OAAO;QAEvC,OAAO,OAAO,aAAA,CAAc,QAAQ,QAAQ,IAAI,EAAE,IAAA,CAAK,SAAU,IAAA,EAAM;YACrE,MAAM,OAAO,OAAO,WAAA,CAAY,OAAO,SAAA,EAAW,QAAQ,IAAA,EAAM,IAAI;YAGpE,IAAI,QAAQ,OAAA,KAAY,KAAA,GAAW;gBACjC,KAAK,QAAA,CAAS,SAAU,CAAA,EAAG;oBACzB,IAAI,CAAC,EAAE,MAAA,EAAQ;oBAEf,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,OAAA,CAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;wBACxD,EAAE,qBAAA,CAAsB,CAAC,CAAA,GAAI,QAAQ,OAAA,CAAQ,CAAC,CAAA;oBAC/C;gBACX,CAAS;YACF;YAED,OAAO;QACb,CAAK;IACF;IAAA;;;;GAAA,GAOD,SAAS,SAAA,EAAW;QAClB,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,SAAS,IAAA;QAEf,MAAM,UAAU,KAAK,KAAA,CAAM,SAAS,CAAA;QAEpC,MAAM,cAAc,OAAO,gBAAA,CAAiB,SAAS;QAErD,MAAM,eAAe,CAAE,CAAA;QACvB,MAAM,cAAc,QAAQ,QAAA,IAAY,CAAE,CAAA;QAE1C,IAAA,IAAS,IAAI,GAAG,KAAK,YAAY,MAAA,EAAQ,IAAI,IAAI,IAAK;YACpD,aAAa,IAAA,CAAK,OAAO,aAAA,CAAc,QAAQ,WAAA,CAAY,CAAC,CAAC,CAAC;QAC/D;QAED,MAAM,kBACJ,QAAQ,IAAA,KAAS,KAAA,IAAY,QAAQ,OAAA,CAAQ,IAAI,IAAI,OAAO,aAAA,CAAc,QAAQ,QAAQ,IAAI;QAEhG,OAAO,QAAQ,GAAA,CAAI;YAAC;YAAa,QAAQ,GAAA,CAAI,YAAY;YAAG,eAAe;SAAC,EAAE,IAAA,CAAK,SAAU,OAAA,EAAS;YACpG,MAAM,OAAO,OAAA,CAAQ,CAAC,CAAA;YACtB,MAAM,WAAW,OAAA,CAAQ,CAAC,CAAA;YAC1B,MAAM,WAAW,OAAA,CAAQ,CAAC,CAAA;YAE1B,IAAI,aAAa,MAAM;gBAGrB,KAAK,QAAA,CAAS,SAAU,IAAA,EAAM;oBAC5B,IAAI,CAAC,KAAK,aAAA,EAAe;oBAEzB,KAAK,IAAA,CAAK,UAAU,eAAe;gBAC7C,CAAS;YACF;YAED,IAAA,IAAS,IAAI,GAAG,KAAK,SAAS,MAAA,EAAQ,IAAI,IAAI,IAAK;gBACjD,KAAK,GAAA,CAAI,QAAA,CAAS,CAAC,CAAC;YACrB;YAED,OAAO;QACb,CAAK;IACF;IAAA,4CAAA;IAAA,6EAAA;IAID,iBAAiB,SAAA,EAAW;QAC1B,MAAM,OAAO,IAAA,CAAK,IAAA;QAClB,MAAM,aAAa,IAAA,CAAK,UAAA;QACxB,MAAM,SAAS,IAAA;QAKf,IAAI,IAAA,CAAK,SAAA,CAAU,SAAS,CAAA,KAAM,KAAA,GAAW;YAC3C,OAAO,IAAA,CAAK,SAAA,CAAU,SAAS,CAAA;QAChC;QAED,MAAM,UAAU,KAAK,KAAA,CAAM,SAAS,CAAA;QAGpC,MAAM,WAAW,QAAQ,IAAA,GAAO,OAAO,gBAAA,CAAiB,QAAQ,IAAI,IAAI;QAExE,MAAM,UAAU,CAAE,CAAA;QAElB,MAAM,cAAc,OAAO,UAAA,CAAW,SAAU,GAAA,EAAK;YACnD,OAAO,IAAI,cAAA,IAAkB,IAAI,cAAA,CAAe,SAAS;QAC/D,CAAK;QAED,IAAI,aAAa;YACf,QAAQ,IAAA,CAAK,WAAW;QACzB;QAED,IAAI,QAAQ,MAAA,KAAW,KAAA,GAAW;YAChC,QAAQ,IAAA,CACN,OAAO,aAAA,CAAc,UAAU,QAAQ,MAAM,EAAE,IAAA,CAAK,SAAU,MAAA,EAAQ;gBACpE,OAAO,OAAO,WAAA,CAAY,OAAO,WAAA,EAAa,QAAQ,MAAA,EAAQ,MAAM;YAC9E,CAAS;QAEJ;QAED,OACG,UAAA,CAAW,SAAU,GAAA,EAAK;YACzB,OAAO,IAAI,oBAAA,IAAwB,IAAI,oBAAA,CAAqB,SAAS;QAC7E,CAAO,EACA,OAAA,CAAQ,SAAU,OAAA,EAAS;YAC1B,QAAQ,IAAA,CAAK,OAAO;QAC5B,CAAO;QAEH,IAAA,CAAK,SAAA,CAAU,SAAS,CAAA,GAAI,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,SAAU,OAAA,EAAS;YACvE,IAAI;YAGJ,IAAI,QAAQ,MAAA,KAAW,MAAM;gBAC3B,OAAO,uJAAI,OAAA,CAAM;YACzB,OAAA,IAAiB,QAAQ,MAAA,GAAS,GAAG;gBAC7B,OAAO,uJAAI,QAAA,CAAO;YAC1B,OAAA,IAAiB,QAAQ,MAAA,KAAW,GAAG;gBAC/B,OAAO,OAAA,CAAQ,CAAC,CAAA;YACxB,OAAa;gBACL,OAAO,uJAAI,WAAA,CAAU;YACtB;YAED,IAAI,SAAS,OAAA,CAAQ,CAAC,CAAA,EAAG;gBACvB,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;oBAChD,KAAK,GAAA,CAAI,OAAA,CAAQ,CAAC,CAAC;gBACpB;YACF;YAED,IAAI,QAAQ,IAAA,EAAM;gBAChB,KAAK,QAAA,CAAS,IAAA,GAAO,QAAQ,IAAA;gBAC7B,KAAK,IAAA,GAAO;YACb;YAED,uBAAuB,MAAM,OAAO;YAEpC,IAAI,QAAQ,UAAA,EAAY,+BAA+B,YAAY,MAAM,OAAO;YAEhF,IAAI,QAAQ,MAAA,KAAW,KAAA,GAAW;gBAChC,MAAM,SAAS,uJAAI,UAAA,CAAS;gBAC5B,OAAO,SAAA,CAAU,QAAQ,MAAM;gBAC/B,KAAK,YAAA,CAAa,MAAM;YAChC,OAAa;gBACL,IAAI,QAAQ,WAAA,KAAgB,KAAA,GAAW;oBACrC,KAAK,QAAA,CAAS,SAAA,CAAU,QAAQ,WAAW;gBAC5C;gBAED,IAAI,QAAQ,QAAA,KAAa,KAAA,GAAW;oBAClC,KAAK,UAAA,CAAW,SAAA,CAAU,QAAQ,QAAQ;gBAC3C;gBAED,IAAI,QAAQ,KAAA,KAAU,KAAA,GAAW;oBAC/B,KAAK,KAAA,CAAM,SAAA,CAAU,QAAQ,KAAK;gBACnC;YACF;YAED,IAAI,CAAC,OAAO,YAAA,CAAa,GAAA,CAAI,IAAI,GAAG;gBAClC,OAAO,YAAA,CAAa,GAAA,CAAI,MAAM,CAAA,CAAE;YACjC;YAED,OAAO,YAAA,CAAa,GAAA,CAAI,IAAI,EAAE,KAAA,GAAQ;YAEtC,OAAO;QACb,CAAK;QAED,OAAO,IAAA,CAAK,SAAA,CAAU,SAAS,CAAA;IAChC;IAAA;;;;GAAA,GAOD,UAAU,UAAA,EAAY;QACpB,MAAM,aAAa,IAAA,CAAK,UAAA;QACxB,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAU,CAAA;QAC5C,MAAM,SAAS,IAAA;QAIf,MAAM,QAAQ,uJAAI,QAAA,CAAO;QACzB,IAAI,SAAS,IAAA,EAAM,MAAM,IAAA,GAAO,OAAO,gBAAA,CAAiB,SAAS,IAAI;QAErE,uBAAuB,OAAO,QAAQ;QAEtC,IAAI,SAAS,UAAA,EAAY,+BAA+B,YAAY,OAAO,QAAQ;QAEnF,MAAM,UAAU,SAAS,KAAA,IAAS,CAAE,CAAA;QAEpC,MAAM,UAAU,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;YAChD,QAAQ,IAAA,CAAK,OAAO,aAAA,CAAc,QAAQ,OAAA,CAAQ,CAAC,CAAC,CAAC;QACtD;QAED,OAAO,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,SAAU,KAAA,EAAO;YAChD,IAAA,IAAS,IAAI,GAAG,KAAK,MAAM,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC9C,MAAM,GAAA,CAAI,KAAA,CAAM,CAAC,CAAC;YACnB;YAID,MAAM,qBAAqB,CAAC,SAAS;gBACnC,MAAM,sBAAsB,aAAA,GAAA,IAAI,IAAK;gBAErC,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,YAAA,CAAc;oBAC9C,IAAI,kKAAe,WAAA,IAAY,kKAAe,UAAA,EAAS;wBACrD,oBAAoB,GAAA,CAAI,KAAK,KAAK;oBACnC;gBACF;gBAED,KAAK,QAAA,CAAS,CAACC,UAAS;oBACtB,MAAM,WAAW,OAAO,YAAA,CAAa,GAAA,CAAIA,KAAI;oBAE7C,IAAI,YAAY,MAAM;wBACpB,oBAAoB,GAAA,CAAIA,OAAM,QAAQ;oBACvC;gBACX,CAAS;gBAED,OAAO;YACR;YAED,OAAO,YAAA,GAAe,mBAAmB,KAAK;YAE9C,OAAO;QACb,CAAK;IACF;IAED,uBAAuB,IAAA,EAAM,aAAA,EAAe,cAAA,EAAgB,OAAA,EAAS,MAAA,EAAQ;QAC3E,MAAM,SAAS,CAAE,CAAA;QAEjB,MAAM,aAAa,KAAK,IAAA,GAAO,KAAK,IAAA,GAAO,KAAK,IAAA;QAChD,MAAM,cAAc,CAAE,CAAA;QAEtB,IAAI,eAAA,CAAgB,OAAO,IAAI,CAAA,KAAM,gBAAgB,OAAA,EAAS;YAC5D,KAAK,QAAA,CAAS,SAAU,MAAA,EAAQ;gBAC9B,IAAI,OAAO,qBAAA,EAAuB;oBAChC,YAAY,IAAA,CAAK,OAAO,IAAA,GAAO,OAAO,IAAA,GAAO,OAAO,IAAI;gBACzD;YACT,CAAO;QACP,OAAW;YACL,YAAY,IAAA,CAAK,UAAU;QAC5B;QAED,IAAI;QAEJ,OAAQ,eAAA,CAAgB,OAAO,IAAI,CAAA,EAAC;YAClC,KAAK,gBAAgB,OAAA;gBACnB,wKAAqB,sBAAA;gBACrB;YAEF,KAAK,gBAAgB,QAAA;gBACnB,uKAAqB,2BAAA;gBACrB;YAEF,KAAK,gBAAgB,QAAA;YACrB,KAAK,gBAAgB,KAAA;gBACnB,wKAAqB,sBAAA;gBACrB;YAEF;gBACE,OAAQ,eAAe,QAAA,EAAQ;oBAC7B,KAAK;wBACH,wKAAqB,sBAAA;wBACrB;oBACF,KAAK;oBACL,KAAK;oBACL;wBACE,wKAAqB,sBAAA;wBACrB;gBACH;gBAED;QACH;QAED,MAAM,gBAAgB,QAAQ,aAAA,KAAkB,KAAA,IAAY,aAAA,CAAc,QAAQ,aAAa,CAAA,sJAAI,oBAAA;QAEnG,MAAM,cAAc,IAAA,CAAK,qBAAA,CAAsB,cAAc;QAE7D,IAAA,IAAS,IAAI,GAAG,KAAK,YAAY,MAAA,EAAQ,IAAI,IAAI,IAAK;YACpD,MAAM,QAAQ,IAAI,mBAChB,WAAA,CAAY,CAAC,CAAA,GAAI,MAAM,eAAA,CAAgB,OAAO,IAAI,CAAA,EAClD,cAAc,KAAA,EACd,aACA;YAIF,IAAI,QAAQ,aAAA,KAAkB,eAAe;gBAC3C,IAAA,CAAK,kCAAA,CAAmC,KAAK;YAC9C;YAED,OAAO,IAAA,CAAK,KAAK;QAClB;QAED,OAAO;IACR;IAED,sBAAsB,QAAA,EAAU;QAC9B,IAAI,cAAc,SAAS,KAAA;QAE3B,IAAI,SAAS,UAAA,EAAY;YACvB,MAAM,QAAQ,4BAA4B,YAAY,WAAW;YACjE,MAAM,SAAS,IAAI,aAAa,YAAY,MAAM;YAElD,IAAA,IAAS,IAAI,GAAG,KAAK,YAAY,MAAA,EAAQ,IAAI,IAAI,IAAK;gBACpD,MAAA,CAAO,CAAC,CAAA,GAAI,WAAA,CAAY,CAAC,CAAA,GAAI;YAC9B;YAED,cAAc;QACf;QAED,OAAO;IACR;IAED,mCAAmC,KAAA,EAAO;QACxC,MAAM,iBAAA,GAAoB,SAAS,wCAAwC,MAAA,EAAQ;YAKjF,MAAM,kBACJ,IAAA,+JAAgB,0BAAA,GAA0B,uCAAuC;YAEnF,OAAO,IAAI,gBAAgB,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,YAAA,CAAY,IAAK,GAAG,MAAM;QACpF;QAGD,MAAM,iBAAA,CAAkB,yCAAA,GAA4C;IACrE;AACH;AAOA,SAAS,cAAc,QAAA,EAAU,YAAA,EAAc,MAAA,EAAQ;IACrD,MAAM,aAAa,aAAa,UAAA;IAEhC,MAAM,MAAM,uJAAI,OAAA,CAAM;IAEtB,IAAI,WAAW,QAAA,KAAa,KAAA,GAAW;QACrC,MAAM,WAAW,OAAO,IAAA,CAAK,SAAA,CAAU,WAAW,QAAQ,CAAA;QAE1D,MAAM,MAAM,SAAS,GAAA;QACrB,MAAM,MAAM,SAAS,GAAA;QAIrB,IAAI,QAAQ,KAAA,KAAa,QAAQ,KAAA,GAAW;YAC1C,IAAI,GAAA,CAAI,uJAAI,UAAA,CAAQ,GAAA,CAAI,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,CAAC,GAAG,IAAI,6JAAA,CAAQ,GAAA,CAAI,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,CAAC,CAAC;YAEhF,IAAI,SAAS,UAAA,EAAY;gBACvB,MAAM,WAAW,4BAA4B,qBAAA,CAAsB,SAAS,aAAa,CAAC;gBAC1F,IAAI,GAAA,CAAI,cAAA,CAAe,QAAQ;gBAC/B,IAAI,GAAA,CAAI,cAAA,CAAe,QAAQ;YAChC;QACP,OAAW;YACL,QAAQ,IAAA,CAAK,qEAAqE;YAElF;QACD;IACL,OAAS;QACL;IACD;IAED,MAAM,UAAU,aAAa,OAAA;IAE7B,IAAI,YAAY,KAAA,GAAW;QACzB,MAAM,kBAAkB,uJAAI,UAAA,CAAS;QACrC,MAAM,SAAS,uJAAI,UAAA,CAAS;QAE5B,IAAA,IAAS,IAAI,GAAG,KAAK,QAAQ,MAAA,EAAQ,IAAI,IAAI,IAAK;YAChD,MAAM,SAAS,OAAA,CAAQ,CAAC,CAAA;YAExB,IAAI,OAAO,QAAA,KAAa,KAAA,GAAW;gBACjC,MAAM,WAAW,OAAO,IAAA,CAAK,SAAA,CAAU,OAAO,QAAQ,CAAA;gBACtD,MAAM,MAAM,SAAS,GAAA;gBACrB,MAAM,MAAM,SAAS,GAAA;gBAIrB,IAAI,QAAQ,KAAA,KAAa,QAAQ,KAAA,GAAW;oBAE1C,OAAO,IAAA,CAAK,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,GAAG,KAAK,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,CAAC,CAAC;oBACxD,OAAO,IAAA,CAAK,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,GAAG,KAAK,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,CAAC,CAAC;oBACxD,OAAO,IAAA,CAAK,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,GAAG,KAAK,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,CAAC,CAAC;oBAExD,IAAI,SAAS,UAAA,EAAY;wBACvB,MAAM,WAAW,4BAA4B,qBAAA,CAAsB,SAAS,aAAa,CAAC;wBAC1F,OAAO,cAAA,CAAe,QAAQ;oBAC/B;oBAMD,gBAAgB,GAAA,CAAI,MAAM;gBACpC,OAAe;oBACL,QAAQ,IAAA,CAAK,qEAAqE;gBACnF;YACF;QACF;QAGD,IAAI,cAAA,CAAe,eAAe;IACnC;IAED,SAAS,WAAA,GAAc;IAEvB,MAAM,SAAS,sJAAI,UAAA,CAAQ;IAE3B,IAAI,SAAA,CAAU,OAAO,MAAM;IAC3B,OAAO,MAAA,GAAS,IAAI,GAAA,CAAI,UAAA,CAAW,IAAI,GAAG,IAAI;IAE9C,SAAS,cAAA,GAAiB;AAC5B;AAQA,SAAS,uBAAuB,QAAA,EAAU,YAAA,EAAc,MAAA,EAAQ;IAC9D,MAAM,aAAa,aAAa,UAAA;IAEhC,MAAM,UAAU,CAAE,CAAA;IAElB,SAAS,wBAAwB,aAAA,EAAe,aAAA,EAAe;QAC7D,OAAO,OAAO,aAAA,CAAc,YAAY,aAAa,EAAE,IAAA,CAAK,SAAU,QAAA,EAAU;YAC9E,SAAS,YAAA,CAAa,eAAe,QAAQ;QACnD,CAAK;IACF;IAED,IAAA,MAAW,qBAAqB,WAAY;QAC1C,MAAM,qBAAqB,UAAA,CAAW,iBAAiB,CAAA,IAAK,kBAAkB,WAAA,CAAa;QAG3F,IAAI,sBAAsB,SAAS,UAAA,EAAY;QAE/C,QAAQ,IAAA,CAAK,wBAAwB,UAAA,CAAW,iBAAiB,CAAA,EAAG,kBAAkB,CAAC;IACxF;IAED,IAAI,aAAa,OAAA,KAAY,KAAA,KAAa,CAAC,SAAS,KAAA,EAAO;QACzD,MAAM,WAAW,OAAO,aAAA,CAAc,YAAY,aAAa,OAAO,EAAE,IAAA,CAAK,SAAUC,SAAAA,EAAU;YAC/F,SAAS,QAAA,CAASA,SAAQ;QAChC,CAAK;QAED,QAAQ,IAAA,CAAK,QAAQ;IACtB;IAED,uBAAuB,UAAU,YAAY;IAE7C,cAAc,UAAU,cAAc,MAAM;IAE5C,OAAO,QAAQ,GAAA,CAAI,OAAO,EAAE,IAAA,CAAK,WAAY;QAC3C,OAAO,aAAa,OAAA,KAAY,KAAA,IAAY,gBAAgB,UAAU,aAAa,OAAA,EAAS,MAAM,IAAI;IAC1G,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4076, "column": 0}, "map": {"version": 3, "file": "DRACOLoader.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/loaders/DRACOLoader.js"], "sourcesContent": ["import { BufferAttribute, <PERSON>ufferGeometry, FileLoader, Loader } from 'three'\n\nconst _taskCache = new WeakMap()\n\nclass DRACOLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.decoderPath = ''\n    this.decoderConfig = {}\n    this.decoderBinary = null\n    this.decoderPending = null\n\n    this.workerLimit = 4\n    this.workerPool = []\n    this.workerNextTaskID = 1\n    this.workerSourceURL = ''\n\n    this.defaultAttributeIDs = {\n      position: 'POSITION',\n      normal: 'NORMAL',\n      color: 'COLOR',\n      uv: 'TEX_COORD',\n    }\n    this.defaultAttributeTypes = {\n      position: 'Float32Array',\n      normal: 'Float32Array',\n      color: 'Float32Array',\n      uv: 'Float32Array',\n    }\n  }\n\n  setDecoderPath(path) {\n    this.decoderPath = path\n\n    return this\n  }\n\n  setDecoderConfig(config) {\n    this.decoderConfig = config\n\n    return this\n  }\n\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit\n\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      (buffer) => {\n        const taskConfig = {\n          attributeIDs: this.defaultAttributeIDs,\n          attributeTypes: this.defaultAttributeTypes,\n          useUniqueIDs: false,\n        }\n\n        this.decodeGeometry(buffer, taskConfig).then(onLoad).catch(onError)\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /** @deprecated Kept for backward-compatibility with previous DRACOLoader versions. */\n  decodeDracoFile(buffer, callback, attributeIDs, attributeTypes) {\n    const taskConfig = {\n      attributeIDs: attributeIDs || this.defaultAttributeIDs,\n      attributeTypes: attributeTypes || this.defaultAttributeTypes,\n      useUniqueIDs: !!attributeIDs,\n    }\n\n    this.decodeGeometry(buffer, taskConfig).then(callback)\n  }\n\n  decodeGeometry(buffer, taskConfig) {\n    // TODO: For backward-compatibility, support 'attributeTypes' objects containing\n    // references (rather than names) to typed array constructors. These must be\n    // serialized before sending them to the worker.\n    for (const attribute in taskConfig.attributeTypes) {\n      const type = taskConfig.attributeTypes[attribute]\n\n      if (type.BYTES_PER_ELEMENT !== undefined) {\n        taskConfig.attributeTypes[attribute] = type.name\n      }\n    }\n\n    //\n\n    const taskKey = JSON.stringify(taskConfig)\n\n    // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n    // again from this thread.\n    if (_taskCache.has(buffer)) {\n      const cachedTask = _taskCache.get(buffer)\n\n      if (cachedTask.key === taskKey) {\n        return cachedTask.promise\n      } else if (buffer.byteLength === 0) {\n        // Technically, it would be possible to wait for the previous task to complete,\n        // transfer the buffer back, and decode again with the second configuration. That\n        // is complex, and I don't know of any reason to decode a Draco buffer twice in\n        // different ways, so this is left unimplemented.\n        throw new Error(\n          'THREE.DRACOLoader: Unable to re-decode a buffer with different ' +\n            'settings. Buffer has already been transferred.',\n        )\n      }\n    }\n\n    //\n\n    let worker\n    const taskID = this.workerNextTaskID++\n    const taskCost = buffer.byteLength\n\n    // Obtain a worker and assign a task, and construct a geometry instance\n    // when the task completes.\n    const geometryPending = this._getWorker(taskID, taskCost)\n      .then((_worker) => {\n        worker = _worker\n\n        return new Promise((resolve, reject) => {\n          worker._callbacks[taskID] = { resolve, reject }\n\n          worker.postMessage({ type: 'decode', id: taskID, taskConfig, buffer }, [buffer])\n\n          // this.debug();\n        })\n      })\n      .then((message) => this._createGeometry(message.geometry))\n\n    // Remove task from the task list.\n    // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n    geometryPending\n      .catch(() => true)\n      .then(() => {\n        if (worker && taskID) {\n          this._releaseTask(worker, taskID)\n\n          // this.debug();\n        }\n      })\n\n    // Cache the task result.\n    _taskCache.set(buffer, {\n      key: taskKey,\n      promise: geometryPending,\n    })\n\n    return geometryPending\n  }\n\n  _createGeometry(geometryData) {\n    const geometry = new BufferGeometry()\n\n    if (geometryData.index) {\n      geometry.setIndex(new BufferAttribute(geometryData.index.array, 1))\n    }\n\n    for (let i = 0; i < geometryData.attributes.length; i++) {\n      const attribute = geometryData.attributes[i]\n      const name = attribute.name\n      const array = attribute.array\n      const itemSize = attribute.itemSize\n\n      geometry.setAttribute(name, new BufferAttribute(array, itemSize))\n    }\n\n    return geometry\n  }\n\n  _loadLibrary(url, responseType) {\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.decoderPath)\n    loader.setResponseType(responseType)\n    loader.setWithCredentials(this.withCredentials)\n\n    return new Promise((resolve, reject) => {\n      loader.load(url, resolve, undefined, reject)\n    })\n  }\n\n  preload() {\n    this._initDecoder()\n\n    return this\n  }\n\n  _initDecoder() {\n    if (this.decoderPending) return this.decoderPending\n\n    const useJS = typeof WebAssembly !== 'object' || this.decoderConfig.type === 'js'\n    const librariesPending = []\n\n    if (useJS) {\n      librariesPending.push(this._loadLibrary('draco_decoder.js', 'text'))\n    } else {\n      librariesPending.push(this._loadLibrary('draco_wasm_wrapper.js', 'text'))\n      librariesPending.push(this._loadLibrary('draco_decoder.wasm', 'arraybuffer'))\n    }\n\n    this.decoderPending = Promise.all(librariesPending).then((libraries) => {\n      const jsContent = libraries[0]\n\n      if (!useJS) {\n        this.decoderConfig.wasmBinary = libraries[1]\n      }\n\n      const fn = DRACOWorker.toString()\n\n      const body = [\n        '/* draco decoder */',\n        jsContent,\n        '',\n        '/* worker */',\n        fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n      ].join('\\n')\n\n      this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n    })\n\n    return this.decoderPending\n  }\n\n  _getWorker(taskID, taskCost) {\n    return this._initDecoder().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker = new Worker(this.workerSourceURL)\n\n        worker._callbacks = {}\n        worker._taskCosts = {}\n        worker._taskLoad = 0\n\n        worker.postMessage({ type: 'init', decoderConfig: this.decoderConfig })\n\n        worker.onmessage = function (e) {\n          const message = e.data\n\n          switch (message.type) {\n            case 'decode':\n              worker._callbacks[message.id].resolve(message)\n              break\n\n            case 'error':\n              worker._callbacks[message.id].reject(message)\n              break\n\n            default:\n              console.error('THREE.DRACOLoader: Unexpected message, \"' + message.type + '\"')\n          }\n        }\n\n        this.workerPool.push(worker)\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1\n        })\n      }\n\n      const worker = this.workerPool[this.workerPool.length - 1]\n      worker._taskCosts[taskID] = taskCost\n      worker._taskLoad += taskCost\n      return worker\n    })\n  }\n\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID]\n    delete worker._callbacks[taskID]\n    delete worker._taskCosts[taskID]\n  }\n\n  debug() {\n    console.log(\n      'Task load: ',\n      this.workerPool.map((worker) => worker._taskLoad),\n    )\n  }\n\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate()\n    }\n\n    this.workerPool.length = 0\n\n    return this\n  }\n}\n\n/* WEB WORKER */\n\nfunction DRACOWorker() {\n  let decoderConfig\n  let decoderPending\n\n  onmessage = function (e) {\n    const message = e.data\n\n    switch (message.type) {\n      case 'init':\n        decoderConfig = message.decoderConfig\n        decoderPending = new Promise(function (resolve /*, reject*/) {\n          decoderConfig.onModuleLoaded = function (draco) {\n            // Module is Promise-like. Wrap before resolving to avoid loop.\n            resolve({ draco: draco })\n          }\n\n          DracoDecoderModule(decoderConfig)\n        })\n        break\n\n      case 'decode':\n        const buffer = message.buffer\n        const taskConfig = message.taskConfig\n        decoderPending.then((module) => {\n          const draco = module.draco\n          const decoder = new draco.Decoder()\n          const decoderBuffer = new draco.DecoderBuffer()\n          decoderBuffer.Init(new Int8Array(buffer), buffer.byteLength)\n\n          try {\n            const geometry = decodeGeometry(draco, decoder, decoderBuffer, taskConfig)\n\n            const buffers = geometry.attributes.map((attr) => attr.array.buffer)\n\n            if (geometry.index) buffers.push(geometry.index.array.buffer)\n\n            self.postMessage({ type: 'decode', id: message.id, geometry }, buffers)\n          } catch (error) {\n            console.error(error)\n\n            self.postMessage({ type: 'error', id: message.id, error: error.message })\n          } finally {\n            draco.destroy(decoderBuffer)\n            draco.destroy(decoder)\n          }\n        })\n        break\n    }\n  }\n\n  function decodeGeometry(draco, decoder, decoderBuffer, taskConfig) {\n    const attributeIDs = taskConfig.attributeIDs\n    const attributeTypes = taskConfig.attributeTypes\n\n    let dracoGeometry\n    let decodingStatus\n\n    const geometryType = decoder.GetEncodedGeometryType(decoderBuffer)\n\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      dracoGeometry = new draco.Mesh()\n      decodingStatus = decoder.DecodeBufferToMesh(decoderBuffer, dracoGeometry)\n    } else if (geometryType === draco.POINT_CLOUD) {\n      dracoGeometry = new draco.PointCloud()\n      decodingStatus = decoder.DecodeBufferToPointCloud(decoderBuffer, dracoGeometry)\n    } else {\n      throw new Error('THREE.DRACOLoader: Unexpected geometry type.')\n    }\n\n    if (!decodingStatus.ok() || dracoGeometry.ptr === 0) {\n      throw new Error('THREE.DRACOLoader: Decoding failed: ' + decodingStatus.error_msg())\n    }\n\n    const geometry = { index: null, attributes: [] }\n\n    // Gather all vertex attributes.\n    for (const attributeName in attributeIDs) {\n      const attributeType = self[attributeTypes[attributeName]]\n\n      let attribute\n      let attributeID\n\n      // A Draco file may be created with default vertex attributes, whose attribute IDs\n      // are mapped 1:1 from their semantic name (POSITION, NORMAL, ...). Alternatively,\n      // a Draco file may contain a custom set of attributes, identified by known unique\n      // IDs. glTF files always do the latter, and `.drc` files typically do the former.\n      if (taskConfig.useUniqueIDs) {\n        attributeID = attributeIDs[attributeName]\n        attribute = decoder.GetAttributeByUniqueId(dracoGeometry, attributeID)\n      } else {\n        attributeID = decoder.GetAttributeId(dracoGeometry, draco[attributeIDs[attributeName]])\n\n        if (attributeID === -1) continue\n\n        attribute = decoder.GetAttribute(dracoGeometry, attributeID)\n      }\n\n      geometry.attributes.push(decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute))\n    }\n\n    // Add index.\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      geometry.index = decodeIndex(draco, decoder, dracoGeometry)\n    }\n\n    draco.destroy(dracoGeometry)\n\n    return geometry\n  }\n\n  function decodeIndex(draco, decoder, dracoGeometry) {\n    const numFaces = dracoGeometry.num_faces()\n    const numIndices = numFaces * 3\n    const byteLength = numIndices * 4\n\n    const ptr = draco._malloc(byteLength)\n    decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr)\n    const index = new Uint32Array(draco.HEAPF32.buffer, ptr, numIndices).slice()\n    draco._free(ptr)\n\n    return { array: index, itemSize: 1 }\n  }\n\n  function decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute) {\n    const numComponents = attribute.num_components()\n    const numPoints = dracoGeometry.num_points()\n    const numValues = numPoints * numComponents\n    const byteLength = numValues * attributeType.BYTES_PER_ELEMENT\n    const dataType = getDracoDataType(draco, attributeType)\n\n    const ptr = draco._malloc(byteLength)\n    decoder.GetAttributeDataArrayForAllPoints(dracoGeometry, attribute, dataType, byteLength, ptr)\n    const array = new attributeType(draco.HEAPF32.buffer, ptr, numValues).slice()\n    draco._free(ptr)\n\n    return {\n      name: attributeName,\n      array: array,\n      itemSize: numComponents,\n    }\n  }\n\n  function getDracoDataType(draco, attributeType) {\n    switch (attributeType) {\n      case Float32Array:\n        return draco.DT_FLOAT32\n      case Int8Array:\n        return draco.DT_INT8\n      case Int16Array:\n        return draco.DT_INT16\n      case Int32Array:\n        return draco.DT_INT32\n      case Uint8Array:\n        return draco.DT_UINT8\n      case Uint16Array:\n        return draco.DT_UINT16\n      case Uint32Array:\n        return draco.DT_UINT32\n    }\n  }\n}\n\nexport { DRACOLoader }\n"], "names": ["worker"], "mappings": ";;;;;AAEA,MAAM,aAAa,aAAA,GAAA,IAAI,QAAS;AAEhC,MAAM,uKAAoB,SAAA,CAAO;IAC/B,YAAY,OAAA,CAAS;QACnB,KAAA,CAAM,OAAO;QAEb,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,aAAA,GAAgB,CAAE;QACvB,IAAA,CAAK,aAAA,GAAgB;QACrB,IAAA,CAAK,cAAA,GAAiB;QAEtB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,UAAA,GAAa,CAAE,CAAA;QACpB,IAAA,CAAK,gBAAA,GAAmB;QACxB,IAAA,CAAK,eAAA,GAAkB;QAEvB,IAAA,CAAK,mBAAA,GAAsB;YACzB,UAAU;YACV,QAAQ;YACR,OAAO;YACP,IAAI;QACL;QACD,IAAA,CAAK,qBAAA,GAAwB;YAC3B,UAAU;YACV,QAAQ;YACR,OAAO;YACP,IAAI;QACL;IACF;IAED,eAAe,IAAA,EAAM;QACnB,IAAA,CAAK,WAAA,GAAc;QAEnB,OAAO,IAAA;IACR;IAED,iBAAiB,MAAA,EAAQ;QACvB,IAAA,CAAK,aAAA,GAAgB;QAErB,OAAO,IAAA;IACR;IAED,eAAe,WAAA,EAAa;QAC1B,IAAA,CAAK,WAAA,GAAc;QAEnB,OAAO,IAAA;IACR;IAED,KAAK,GAAA,EAAK,MAAA,EAAQ,UAAA,EAAY,OAAA,EAAS;QACrC,MAAM,SAAS,uJAAI,aAAA,CAAW,IAAA,CAAK,OAAO;QAE1C,OAAO,OAAA,CAAQ,IAAA,CAAK,IAAI;QACxB,OAAO,eAAA,CAAgB,aAAa;QACpC,OAAO,gBAAA,CAAiB,IAAA,CAAK,aAAa;QAC1C,OAAO,kBAAA,CAAmB,IAAA,CAAK,eAAe;QAE9C,OAAO,IAAA,CACL,KACA,CAAC,WAAW;YACV,MAAM,aAAa;gBACjB,cAAc,IAAA,CAAK,mBAAA;gBACnB,gBAAgB,IAAA,CAAK,qBAAA;gBACrB,cAAc;YACf;YAED,IAAA,CAAK,cAAA,CAAe,QAAQ,UAAU,EAAE,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,OAAO;QACnE,GACD,YACA;IAEH;IAAA,oFAAA,GAGD,gBAAgB,MAAA,EAAQ,QAAA,EAAU,YAAA,EAAc,cAAA,EAAgB;QAC9D,MAAM,aAAa;YACjB,cAAc,gBAAgB,IAAA,CAAK,mBAAA;YACnC,gBAAgB,kBAAkB,IAAA,CAAK,qBAAA;YACvC,cAAc,CAAC,CAAC;QACjB;QAED,IAAA,CAAK,cAAA,CAAe,QAAQ,UAAU,EAAE,IAAA,CAAK,QAAQ;IACtD;IAED,eAAe,MAAA,EAAQ,UAAA,EAAY;QAIjC,IAAA,MAAW,aAAa,WAAW,cAAA,CAAgB;YACjD,MAAM,OAAO,WAAW,cAAA,CAAe,SAAS,CAAA;YAEhD,IAAI,KAAK,iBAAA,KAAsB,KAAA,GAAW;gBACxC,WAAW,cAAA,CAAe,SAAS,CAAA,GAAI,KAAK,IAAA;YAC7C;QACF;QAID,MAAM,UAAU,KAAK,SAAA,CAAU,UAAU;QAIzC,IAAI,WAAW,GAAA,CAAI,MAAM,GAAG;YAC1B,MAAM,aAAa,WAAW,GAAA,CAAI,MAAM;YAExC,IAAI,WAAW,GAAA,KAAQ,SAAS;gBAC9B,OAAO,WAAW,OAAA;YAC1B,OAAA,IAAiB,OAAO,UAAA,KAAe,GAAG;gBAKlC,MAAM,IAAI,MACR;YAGH;QACF;QAID,IAAI;QACJ,MAAM,SAAS,IAAA,CAAK,gBAAA;QACpB,MAAM,WAAW,OAAO,UAAA;QAIxB,MAAM,kBAAkB,IAAA,CAAK,UAAA,CAAW,QAAQ,QAAQ,EACrD,IAAA,CAAK,CAAC,YAAY;YACjB,SAAS;YAET,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;gBACtC,OAAO,UAAA,CAAW,MAAM,CAAA,GAAI;oBAAE;oBAAS;gBAAQ;gBAE/C,OAAO,WAAA,CAAY;oBAAE,MAAM;oBAAU,IAAI;oBAAQ;oBAAY;gBAAA,GAAU;oBAAC,MAAM;iBAAC;YAGzF,CAAS;QACT,CAAO,EACA,IAAA,CAAK,CAAC,UAAY,IAAA,CAAK,eAAA,CAAgB,QAAQ,QAAQ,CAAC;QAI3D,gBACG,KAAA,CAAM,IAAM,IAAI,EAChB,IAAA,CAAK,MAAM;YACV,IAAI,UAAU,QAAQ;gBACpB,IAAA,CAAK,YAAA,CAAa,QAAQ,MAAM;YAGjC;QACT,CAAO;QAGH,WAAW,GAAA,CAAI,QAAQ;YACrB,KAAK;YACL,SAAS;QACf,CAAK;QAED,OAAO;IACR;IAED,gBAAgB,YAAA,EAAc;QAC5B,MAAM,WAAW,uJAAI,iBAAA,CAAgB;QAErC,IAAI,aAAa,KAAA,EAAO;YACtB,SAAS,QAAA,CAAS,uJAAI,kBAAA,CAAgB,aAAa,KAAA,CAAM,KAAA,EAAO,CAAC,CAAC;QACnE;QAED,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,UAAA,CAAW,MAAA,EAAQ,IAAK;YACvD,MAAM,YAAY,aAAa,UAAA,CAAW,CAAC,CAAA;YAC3C,MAAM,OAAO,UAAU,IAAA;YACvB,MAAM,QAAQ,UAAU,KAAA;YACxB,MAAM,WAAW,UAAU,QAAA;YAE3B,SAAS,YAAA,CAAa,MAAM,uJAAI,kBAAA,CAAgB,OAAO,QAAQ,CAAC;QACjE;QAED,OAAO;IACR;IAED,aAAa,GAAA,EAAK,YAAA,EAAc;QAC9B,MAAM,SAAS,uJAAI,aAAA,CAAW,IAAA,CAAK,OAAO;QAC1C,OAAO,OAAA,CAAQ,IAAA,CAAK,WAAW;QAC/B,OAAO,eAAA,CAAgB,YAAY;QACnC,OAAO,kBAAA,CAAmB,IAAA,CAAK,eAAe;QAE9C,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;YACtC,OAAO,IAAA,CAAK,KAAK,SAAS,KAAA,GAAW,MAAM;QACjD,CAAK;IACF;IAED,UAAU;QACR,IAAA,CAAK,YAAA,CAAc;QAEnB,OAAO,IAAA;IACR;IAED,eAAe;QACb,IAAI,IAAA,CAAK,cAAA,EAAgB,OAAO,IAAA,CAAK,cAAA;QAErC,MAAM,QAAQ,OAAO,gBAAgB,YAAY,IAAA,CAAK,aAAA,CAAc,IAAA,KAAS;QAC7E,MAAM,mBAAmB,CAAE,CAAA;QAE3B,IAAI,OAAO;YACT,iBAAiB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,oBAAoB,MAAM,CAAC;QACzE,OAAW;YACL,iBAAiB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,yBAAyB,MAAM,CAAC;YACxE,iBAAiB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,sBAAsB,aAAa,CAAC;QAC7E;QAED,IAAA,CAAK,cAAA,GAAiB,QAAQ,GAAA,CAAI,gBAAgB,EAAE,IAAA,CAAK,CAAC,cAAc;YACtE,MAAM,YAAY,SAAA,CAAU,CAAC,CAAA;YAE7B,IAAI,CAAC,OAAO;gBACV,IAAA,CAAK,aAAA,CAAc,UAAA,GAAa,SAAA,CAAU,CAAC,CAAA;YAC5C;YAED,MAAM,KAAK,YAAY,QAAA,CAAU;YAEjC,MAAM,OAAO;gBACX;gBACA;gBACA;gBACA;gBACA,GAAG,SAAA,CAAU,GAAG,OAAA,CAAQ,GAAG,IAAI,GAAG,GAAG,WAAA,CAAY,GAAG,CAAC;aAC7D,CAAQ,IAAA,CAAK,IAAI;YAEX,IAAA,CAAK,eAAA,GAAkB,IAAI,eAAA,CAAgB,IAAI,KAAK;gBAAC,IAAI;aAAC,CAAC;QACjE,CAAK;QAED,OAAO,IAAA,CAAK,cAAA;IACb;IAED,WAAW,MAAA,EAAQ,QAAA,EAAU;QAC3B,OAAO,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,MAAM;YACpC,IAAI,IAAA,CAAK,UAAA,CAAW,MAAA,GAAS,IAAA,CAAK,WAAA,EAAa;gBAC7C,MAAMA,UAAS,IAAI,OAAO,IAAA,CAAK,eAAe;gBAE9CA,QAAO,UAAA,GAAa,CAAE;gBACtBA,QAAO,UAAA,GAAa,CAAE;gBACtBA,QAAO,SAAA,GAAY;gBAEnBA,QAAO,WAAA,CAAY;oBAAE,MAAM;oBAAQ,eAAe,IAAA,CAAK,aAAA;gBAAA,CAAe;gBAEtEA,QAAO,SAAA,GAAY,SAAU,CAAA,EAAG;oBAC9B,MAAM,UAAU,EAAE,IAAA;oBAElB,OAAQ,QAAQ,IAAA,EAAI;wBAClB,KAAK;4BACHA,QAAO,UAAA,CAAW,QAAQ,EAAE,CAAA,CAAE,OAAA,CAAQ,OAAO;4BAC7C;wBAEF,KAAK;4BACHA,QAAO,UAAA,CAAW,QAAQ,EAAE,CAAA,CAAE,MAAA,CAAO,OAAO;4BAC5C;wBAEF;4BACE,QAAQ,KAAA,CAAM,6CAA6C,QAAQ,IAAA,GAAO,GAAG;oBAChF;gBACF;gBAED,IAAA,CAAK,UAAA,CAAW,IAAA,CAAKA,OAAM;YACnC,OAAa;gBACL,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,SAAU,CAAA,EAAG,CAAA,EAAG;oBACnC,OAAO,EAAE,SAAA,GAAY,EAAE,SAAA,GAAY,CAAA,IAAK;gBAClD,CAAS;YACF;YAED,MAAM,SAAS,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,UAAA,CAAW,MAAA,GAAS,CAAC,CAAA;YACzD,OAAO,UAAA,CAAW,MAAM,CAAA,GAAI;YAC5B,OAAO,SAAA,IAAa;YACpB,OAAO;QACb,CAAK;IACF;IAED,aAAa,MAAA,EAAQ,MAAA,EAAQ;QAC3B,OAAO,SAAA,IAAa,OAAO,UAAA,CAAW,MAAM,CAAA;QAC5C,OAAO,OAAO,UAAA,CAAW,MAAM,CAAA;QAC/B,OAAO,OAAO,UAAA,CAAW,MAAM,CAAA;IAChC;IAED,QAAQ;QACN,QAAQ,GAAA,CACN,eACA,IAAA,CAAK,UAAA,CAAW,GAAA,CAAI,CAAC,SAAW,OAAO,SAAS;IAEnD;IAED,UAAU;QACR,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,UAAA,CAAW,MAAA,EAAQ,EAAE,EAAG;YAC/C,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,SAAA,CAAW;QAC/B;QAED,IAAA,CAAK,UAAA,CAAW,MAAA,GAAS;QAEzB,OAAO,IAAA;IACR;AACH;AAIA,SAAS,cAAc;IACrB,IAAI;IACJ,IAAI;IAEJ,YAAY,SAAU,CAAA,EAAG;QACvB,MAAM,UAAU,EAAE,IAAA;QAElB,OAAQ,QAAQ,IAAA,EAAI;YAClB,KAAK;gBACH,gBAAgB,QAAQ,aAAA;gBACxB,iBAAiB,IAAI,QAAQ,SAAU,OAAA,EAAsB;oBAC3D,cAAc,cAAA,GAAiB,SAAU,KAAA,EAAO;wBAE9C,QAAQ;4BAAE;wBAAA,CAAc;oBACzB;oBAED,mBAAmB,aAAa;gBAC1C,CAAS;gBACD;YAEF,KAAK;gBACH,MAAM,SAAS,QAAQ,MAAA;gBACvB,MAAM,aAAa,QAAQ,UAAA;gBAC3B,eAAe,IAAA,CAAK,CAAC,WAAW;oBAC9B,MAAM,QAAQ,OAAO,KAAA;oBACrB,MAAM,UAAU,IAAI,MAAM,OAAA,CAAS;oBACnC,MAAM,gBAAgB,IAAI,MAAM,aAAA,CAAe;oBAC/C,cAAc,IAAA,CAAK,IAAI,UAAU,MAAM,GAAG,OAAO,UAAU;oBAE3D,IAAI;wBACF,MAAM,WAAW,eAAe,OAAO,SAAS,eAAe,UAAU;wBAEzE,MAAM,UAAU,SAAS,UAAA,CAAW,GAAA,CAAI,CAAC,OAAS,KAAK,KAAA,CAAM,MAAM;wBAEnE,IAAI,SAAS,KAAA,EAAO,QAAQ,IAAA,CAAK,SAAS,KAAA,CAAM,KAAA,CAAM,MAAM;wBAE5D,KAAK,WAAA,CAAY;4BAAE,MAAM;4BAAU,IAAI,QAAQ,EAAA;4BAAI;wBAAU,GAAE,OAAO;oBACvE,EAAA,OAAQ,OAAP;wBACA,QAAQ,KAAA,CAAM,KAAK;wBAEnB,KAAK,WAAA,CAAY;4BAAE,MAAM;4BAAS,IAAI,QAAQ,EAAA;4BAAI,OAAO,MAAM,OAAA;wBAAO,CAAE;oBACpF,SAAoB;wBACR,MAAM,OAAA,CAAQ,aAAa;wBAC3B,MAAM,OAAA,CAAQ,OAAO;oBACtB;gBACX,CAAS;gBACD;QACH;IACF;IAED,SAAS,eAAe,KAAA,EAAO,OAAA,EAAS,aAAA,EAAe,UAAA,EAAY;QACjE,MAAM,eAAe,WAAW,YAAA;QAChC,MAAM,iBAAiB,WAAW,cAAA;QAElC,IAAI;QACJ,IAAI;QAEJ,MAAM,eAAe,QAAQ,sBAAA,CAAuB,aAAa;QAEjE,IAAI,iBAAiB,MAAM,eAAA,EAAiB;YAC1C,gBAAgB,IAAI,MAAM,IAAA,CAAM;YAChC,iBAAiB,QAAQ,kBAAA,CAAmB,eAAe,aAAa;QAC9E,OAAA,IAAe,iBAAiB,MAAM,WAAA,EAAa;YAC7C,gBAAgB,IAAI,MAAM,UAAA,CAAY;YACtC,iBAAiB,QAAQ,wBAAA,CAAyB,eAAe,aAAa;QACpF,OAAW;YACL,MAAM,IAAI,MAAM,8CAA8C;QAC/D;QAED,IAAI,CAAC,eAAe,EAAA,CAAE,KAAM,cAAc,GAAA,KAAQ,GAAG;YACnD,MAAM,IAAI,MAAM,yCAAyC,eAAe,SAAA,CAAS,CAAE;QACpF;QAED,MAAM,WAAW;YAAE,OAAO;YAAM,YAAY,CAAA,CAAA;QAAI;QAGhD,IAAA,MAAW,iBAAiB,aAAc;YACxC,MAAM,gBAAgB,IAAA,CAAK,cAAA,CAAe,aAAa,CAAC,CAAA;YAExD,IAAI;YACJ,IAAI;YAMJ,IAAI,WAAW,YAAA,EAAc;gBAC3B,cAAc,YAAA,CAAa,aAAa,CAAA;gBACxC,YAAY,QAAQ,sBAAA,CAAuB,eAAe,WAAW;YAC7E,OAAa;gBACL,cAAc,QAAQ,cAAA,CAAe,eAAe,KAAA,CAAM,YAAA,CAAa,aAAa,CAAC,CAAC;gBAEtF,IAAI,gBAAgB,CAAA,GAAI;gBAExB,YAAY,QAAQ,YAAA,CAAa,eAAe,WAAW;YAC5D;YAED,SAAS,UAAA,CAAW,IAAA,CAAK,gBAAgB,OAAO,SAAS,eAAe,eAAe,eAAe,SAAS,CAAC;QACjH;QAGD,IAAI,iBAAiB,MAAM,eAAA,EAAiB;YAC1C,SAAS,KAAA,GAAQ,YAAY,OAAO,SAAS,aAAa;QAC3D;QAED,MAAM,OAAA,CAAQ,aAAa;QAE3B,OAAO;IACR;IAED,SAAS,YAAY,KAAA,EAAO,OAAA,EAAS,aAAA,EAAe;QAClD,MAAM,WAAW,cAAc,SAAA,CAAW;QAC1C,MAAM,aAAa,WAAW;QAC9B,MAAM,aAAa,aAAa;QAEhC,MAAM,MAAM,MAAM,OAAA,CAAQ,UAAU;QACpC,QAAQ,uBAAA,CAAwB,eAAe,YAAY,GAAG;QAC9D,MAAM,QAAQ,IAAI,YAAY,MAAM,OAAA,CAAQ,MAAA,EAAQ,KAAK,UAAU,EAAE,KAAA,CAAO;QAC5E,MAAM,KAAA,CAAM,GAAG;QAEf,OAAO;YAAE,OAAO;YAAO,UAAU;QAAG;IACrC;IAED,SAAS,gBAAgB,KAAA,EAAO,OAAA,EAAS,aAAA,EAAe,aAAA,EAAe,aAAA,EAAe,SAAA,EAAW;QAC/F,MAAM,gBAAgB,UAAU,cAAA,CAAgB;QAChD,MAAM,YAAY,cAAc,UAAA,CAAY;QAC5C,MAAM,YAAY,YAAY;QAC9B,MAAM,aAAa,YAAY,cAAc,iBAAA;QAC7C,MAAM,WAAW,iBAAiB,OAAO,aAAa;QAEtD,MAAM,MAAM,MAAM,OAAA,CAAQ,UAAU;QACpC,QAAQ,iCAAA,CAAkC,eAAe,WAAW,UAAU,YAAY,GAAG;QAC7F,MAAM,QAAQ,IAAI,cAAc,MAAM,OAAA,CAAQ,MAAA,EAAQ,KAAK,SAAS,EAAE,KAAA,CAAO;QAC7E,MAAM,KAAA,CAAM,GAAG;QAEf,OAAO;YACL,MAAM;YACN;YACA,UAAU;QACX;IACF;IAED,SAAS,iBAAiB,KAAA,EAAO,aAAA,EAAe;QAC9C,OAAQ,eAAa;YACnB,KAAK;gBACH,OAAO,MAAM,UAAA;YACf,KAAK;gBACH,OAAO,MAAM,OAAA;YACf,KAAK;gBACH,OAAO,MAAM,QAAA;YACf,KAAK;gBACH,OAAO,MAAM,QAAA;YACf,KAAK;gBACH,OAAO,MAAM,QAAA;YACf,KAAK;gBACH,OAAO,MAAM,SAAA;YACf,KAAK;gBACH,OAAO,MAAM,SAAA;QAChB;IACF;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4443, "column": 0}, "map": {"version": 3, "file": "MeshoptDecoder.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/libs/MeshoptDecoder.ts"], "sourcesContent": ["// This file is part of meshoptimizer library and is distributed under the terms of MIT License.\n// Copyright (C) 2016-2020, by <PERSON><PERSON><PERSON> (<EMAIL>)\n\ntype API = {\n  ready: Promise<void>\n  supported: boolean\n  decodeVertexBuffer: (target: Uint8Array, count: number, size: number, source: Uint8Array, filter?: string) => void\n  decodeIndexBuffer: (target: Uint8Array, count: number, size: number, source: Uint8Array) => void\n  decodeIndexSequence: (target: Uint8Array, count: number, size: number, source: Uint8Array) => void\n  decodeGltfBuffer: (\n    target: Uint8Array,\n    count: number,\n    size: number,\n    source: Uint8Array,\n    mode: string,\n    filter?: string,\n  ) => void\n}\n\nlet generated: API\n\nconst MeshoptDecoder = () => {\n  if (generated) return generated\n\n  // Built with clang version 11.0.0 (https://github.com/llvm/llvm-project.git 0160ad802e899c2922bc9b29564080c22eb0908c)\n  // Built from meshoptimizer 0.14\n  const wasm_base =\n    '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'\n  const wasm_simd =\n    '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'\n\n  // Uses bulk-memory and simd extensions\n  const detector = new Uint8Array([\n    0,\n    97,\n    115,\n    109,\n    1,\n    0,\n    0,\n    0,\n    1,\n    4,\n    1,\n    96,\n    0,\n    0,\n    3,\n    3,\n    2,\n    0,\n    0,\n    5,\n    3,\n    1,\n    0,\n    1,\n    12,\n    1,\n    0,\n    10,\n    22,\n    2,\n    12,\n    0,\n    65,\n    0,\n    65,\n    0,\n    65,\n    0,\n    252,\n    10,\n    0,\n    0,\n    11,\n    7,\n    0,\n    65,\n    0,\n    253,\n    15,\n    26,\n    11,\n  ])\n\n  // Used to unpack wasm\n  const wasmpack = new Uint8Array([\n    32,\n    0,\n    65,\n    253,\n    3,\n    1,\n    2,\n    34,\n    4,\n    106,\n    6,\n    5,\n    11,\n    8,\n    7,\n    20,\n    13,\n    33,\n    12,\n    16,\n    128,\n    9,\n    116,\n    64,\n    19,\n    113,\n    127,\n    15,\n    10,\n    21,\n    22,\n    14,\n    255,\n    66,\n    24,\n    54,\n    136,\n    107,\n    18,\n    23,\n    192,\n    26,\n    114,\n    118,\n    132,\n    17,\n    77,\n    101,\n    130,\n    144,\n    27,\n    87,\n    131,\n    44,\n    45,\n    74,\n    156,\n    154,\n    70,\n    167,\n  ])\n\n  if (typeof WebAssembly !== 'object') {\n    // This module requires WebAssembly to function\n    return {\n      supported: false,\n    }\n  }\n\n  let wasm = wasm_base\n\n  if (WebAssembly.validate(detector)) {\n    wasm = wasm_simd\n  }\n\n  let instance: any // WebAssembly.Instance\n\n  const promise = WebAssembly.instantiate(unpack(wasm), {}).then((result) => {\n    instance = result.instance\n    instance.exports.__wasm_call_ctors()\n  })\n\n  function unpack(data: string) {\n    const result = new Uint8Array(data.length)\n    for (let i = 0; i < data.length; ++i) {\n      const ch = data.charCodeAt(i)\n      result[i] = ch > 96 ? ch - 71 : ch > 64 ? ch - 65 : ch > 47 ? ch + 4 : ch > 46 ? 63 : 62\n    }\n    let write = 0\n    for (let i = 0; i < data.length; ++i) {\n      result[write++] = result[i] < 60 ? wasmpack[result[i]] : (result[i] - 60) * 64 + result[++i]\n    }\n    return result.buffer.slice(0, write)\n  }\n\n  function decode(\n    fun: Function,\n    target: Uint8Array,\n    count: number,\n    size: number,\n    source: Uint8Array,\n    filter?: Function,\n  ) {\n    const sbrk = instance.exports.sbrk\n    const count4 = (count + 3) & ~3 // pad for SIMD filter\n    const tp = sbrk(count4 * size)\n    const sp = sbrk(source.length)\n    const heap = new Uint8Array(instance.exports.memory.buffer)\n    heap.set(source, sp)\n    const res = fun(tp, count, size, sp, source.length)\n    if (res === 0 && filter) {\n      filter(tp, count4, size)\n    }\n    target.set(heap.subarray(tp, tp + count * size))\n    sbrk(tp - sbrk(0))\n    if (res !== 0) {\n      throw new Error(`Malformed buffer data: ${res}`)\n    }\n  }\n\n  const filters = {\n    // legacy index-based enums for glTF\n    0: '',\n    1: 'meshopt_decodeFilterOct',\n    2: 'meshopt_decodeFilterQuat',\n    3: 'meshopt_decodeFilterExp',\n    // string-based enums for glTF\n    NONE: '',\n    OCTAHEDRAL: 'meshopt_decodeFilterOct',\n    QUATERNION: 'meshopt_decodeFilterQuat',\n    EXPONENTIAL: 'meshopt_decodeFilterExp',\n  }\n\n  const decoders = {\n    // legacy index-based enums for glTF\n    0: 'meshopt_decodeVertexBuffer',\n    1: 'meshopt_decodeIndexBuffer',\n    2: 'meshopt_decodeIndexSequence',\n    // string-based enums for glTF\n    ATTRIBUTES: 'meshopt_decodeVertexBuffer',\n    TRIANGLES: 'meshopt_decodeIndexBuffer',\n    INDICES: 'meshopt_decodeIndexSequence',\n  }\n\n  generated = {\n    ready: promise,\n    supported: true,\n    decodeVertexBuffer(target, count, size, source, filter) {\n      decode(\n        instance.exports.meshopt_decodeVertexBuffer,\n        target,\n        count,\n        size,\n        source,\n        instance.exports[filters[filter as keyof typeof filters]],\n      )\n    },\n    decodeIndexBuffer(target, count, size, source) {\n      decode(instance.exports.meshopt_decodeIndexBuffer, target, count, size, source)\n    },\n    decodeIndexSequence(target, count, size, source) {\n      decode(instance.exports.meshopt_decodeIndexSequence, target, count, size, source)\n    },\n    decodeGltfBuffer(target, count, size, source, mode, filter) {\n      decode(\n        instance.exports[decoders[mode as keyof typeof decoders]],\n        target,\n        count,\n        size,\n        source,\n        instance.exports[filters[filter as keyof typeof filters]],\n      )\n    },\n  }\n\n  return generated\n}\n\nexport { MeshoptDecoder }\n"], "names": [], "mappings": ";;;AAmBA,IAAI;AAEJ,MAAM,iBAAiB,MAAM;IACvB,IAAA,WAAkB,OAAA;IAItB,MAAM,YACJ;IACF,MAAM,YACJ;IAGI,MAAA,WAAW,IAAI,WAAW;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGK,MAAA,WAAW,IAAI,WAAW;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAEG,IAAA,OAAO,gBAAgB,UAAU;QAE5B,OAAA;YACL,WAAW;QAAA;IAEf;IAEA,IAAI,OAAO;IAEP,IAAA,YAAY,QAAA,CAAS,QAAQ,GAAG;QAC3B,OAAA;IACT;IAEI,IAAA;IAEE,MAAA,UAAU,YAAY,WAAA,CAAY,OAAO,IAAI,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,CAAC,WAAW;QACzE,WAAW,OAAO,QAAA;QAClB,SAAS,OAAA,CAAQ,iBAAA;IAAkB,CACpC;IAED,SAAS,OAAO,IAAA,EAAc;QAC5B,MAAM,SAAS,IAAI,WAAW,KAAK,MAAM;QACzC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,EAAE,EAAG;YAC9B,MAAA,KAAK,KAAK,UAAA,CAAW,CAAC;YAC5B,MAAA,CAAO,CAAC,CAAA,GAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;QACxF;QACA,IAAI,QAAQ;QACZ,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,EAAE,EAAG;YACpC,MAAA,CAAO,OAAO,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,GAAI,KAAK,QAAA,CAAS,MAAA,CAAO,CAAC,CAAC,CAAA,GAAA,CAAK,MAAA,CAAO,CAAC,CAAA,GAAI,EAAA,IAAM,KAAK,MAAA,CAAO,EAAE,CAAC,CAAA;QAC7F;QACA,OAAO,OAAO,MAAA,CAAO,KAAA,CAAM,GAAG,KAAK;IACrC;IAEA,SAAS,OACP,GAAA,EACA,MAAA,EACA,KAAA,EACA,IAAA,EACA,MAAA,EACA,MAAA,EACA;QACM,MAAA,OAAO,SAAS,OAAA,CAAQ,IAAA;QACxB,MAAA,SAAU,QAAQ,IAAK,CAAC;QACxB,MAAA,KAAK,KAAK,SAAS,IAAI;QACvB,MAAA,KAAK,KAAK,OAAO,MAAM;QAC7B,MAAM,OAAO,IAAI,WAAW,SAAS,OAAA,CAAQ,MAAA,CAAO,MAAM;QACrD,KAAA,GAAA,CAAI,QAAQ,EAAE;QACnB,MAAM,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;QAC9C,IAAA,QAAQ,KAAK,QAAQ;YAChB,OAAA,IAAI,QAAQ,IAAI;QACzB;QACA,OAAO,GAAA,CAAI,KAAK,QAAA,CAAS,IAAI,KAAK,QAAQ,IAAI,CAAC;QAC1C,KAAA,KAAK,KAAK,CAAC,CAAC;QACjB,IAAI,QAAQ,GAAG;YACP,MAAA,IAAI,MAAM,CAAA,uBAAA,EAA0B,KAAK;QACjD;IACF;IAEA,MAAM,UAAU;QAAA,oCAAA;QAEd,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QAAA,8BAAA;QAEH,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,aAAa;IAAA;IAGf,MAAM,WAAW;QAAA,oCAAA;QAEf,GAAG;QACH,GAAG;QACH,GAAG;QAAA,8BAAA;QAEH,YAAY;QACZ,WAAW;QACX,SAAS;IAAA;IAGC,YAAA;QACV,OAAO;QACP,WAAW;QACX,oBAAmB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ,MAAA,EAAQ;YACtD,OACE,SAAS,OAAA,CAAQ,0BAAA,EACjB,QACA,OACA,MACA,QACA,SAAS,OAAA,CAAQ,OAAA,CAAQ,MAA8B,CAAC,CAAA;QAE5D;QACA,mBAAkB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ;YAC7C,OAAO,SAAS,OAAA,CAAQ,yBAAA,EAA2B,QAAQ,OAAO,MAAM,MAAM;QAChF;QACA,qBAAoB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ;YAC/C,OAAO,SAAS,OAAA,CAAQ,2BAAA,EAA6B,QAAQ,OAAO,MAAM,MAAM;QAClF;QACA,kBAAiB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,MAAA,EAAQ;YAC1D,OACE,SAAS,OAAA,CAAQ,QAAA,CAAS,IAA6B,CAAC,CAAA,EACxD,QACA,OACA,MACA,QACA,SAAS,OAAA,CAAQ,OAAA,CAAQ,MAA8B,CAAC,CAAA;QAE5D;IAAA;IAGK,OAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4657, "column": 0}, "map": {"version": 3, "file": "SkeletonUtils.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/utils/SkeletonUtils.js"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  QuaternionKeyframeTrack,\n  SkeletonHelper,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\n\nfunction retarget(target, source, options = {}) {\n  const pos = new Vector3(),\n    quat = new Quaternion(),\n    scale = new Vector3(),\n    bindBoneMatrix = new Matrix4(),\n    relativeMatrix = new Matrix4(),\n    globalMatrix = new Matrix4()\n\n  options.preserveMatrix = options.preserveMatrix !== undefined ? options.preserveMatrix : true\n  options.preservePosition = options.preservePosition !== undefined ? options.preservePosition : true\n  options.preserveHipPosition = options.preserveHipPosition !== undefined ? options.preserveHipPosition : false\n  options.useTargetMatrix = options.useTargetMatrix !== undefined ? options.useTargetMatrix : false\n  options.hip = options.hip !== undefined ? options.hip : 'hip'\n  options.names = options.names || {}\n\n  const sourceBones = source.isObject3D ? source.skeleton.bones : getBones(source),\n    bones = target.isObject3D ? target.skeleton.bones : getBones(target)\n\n  let bindBones, bone, name, boneTo, bonesPosition\n\n  // reset bones\n\n  if (target.isObject3D) {\n    target.skeleton.pose()\n  } else {\n    options.useTargetMatrix = true\n    options.preserveMatrix = false\n  }\n\n  if (options.preservePosition) {\n    bonesPosition = []\n\n    for (let i = 0; i < bones.length; i++) {\n      bonesPosition.push(bones[i].position.clone())\n    }\n  }\n\n  if (options.preserveMatrix) {\n    // reset matrix\n\n    target.updateMatrixWorld()\n\n    target.matrixWorld.identity()\n\n    // reset children matrix\n\n    for (let i = 0; i < target.children.length; ++i) {\n      target.children[i].updateMatrixWorld(true)\n    }\n  }\n\n  if (options.offsets) {\n    bindBones = []\n\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i]\n      name = options.names[bone.name] || bone.name\n\n      if (options.offsets[name]) {\n        bone.matrix.multiply(options.offsets[name])\n\n        bone.matrix.decompose(bone.position, bone.quaternion, bone.scale)\n\n        bone.updateMatrixWorld()\n      }\n\n      bindBones.push(bone.matrixWorld.clone())\n    }\n  }\n\n  for (let i = 0; i < bones.length; ++i) {\n    bone = bones[i]\n    name = options.names[bone.name] || bone.name\n\n    boneTo = getBoneByName(name, sourceBones)\n\n    globalMatrix.copy(bone.matrixWorld)\n\n    if (boneTo) {\n      boneTo.updateMatrixWorld()\n\n      if (options.useTargetMatrix) {\n        relativeMatrix.copy(boneTo.matrixWorld)\n      } else {\n        relativeMatrix.copy(target.matrixWorld).invert()\n        relativeMatrix.multiply(boneTo.matrixWorld)\n      }\n\n      // ignore scale to extract rotation\n\n      scale.setFromMatrixScale(relativeMatrix)\n      relativeMatrix.scale(scale.set(1 / scale.x, 1 / scale.y, 1 / scale.z))\n\n      // apply to global matrix\n\n      globalMatrix.makeRotationFromQuaternion(quat.setFromRotationMatrix(relativeMatrix))\n\n      if (target.isObject3D) {\n        const boneIndex = bones.indexOf(bone),\n          wBindMatrix = bindBones\n            ? bindBones[boneIndex]\n            : bindBoneMatrix.copy(target.skeleton.boneInverses[boneIndex]).invert()\n\n        globalMatrix.multiply(wBindMatrix)\n      }\n\n      globalMatrix.copyPosition(relativeMatrix)\n    }\n\n    if (bone.parent && bone.parent.isBone) {\n      bone.matrix.copy(bone.parent.matrixWorld).invert()\n      bone.matrix.multiply(globalMatrix)\n    } else {\n      bone.matrix.copy(globalMatrix)\n    }\n\n    if (options.preserveHipPosition && name === options.hip) {\n      bone.matrix.setPosition(pos.set(0, bone.position.y, 0))\n    }\n\n    bone.matrix.decompose(bone.position, bone.quaternion, bone.scale)\n\n    bone.updateMatrixWorld()\n  }\n\n  if (options.preservePosition) {\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i]\n      name = options.names[bone.name] || bone.name\n\n      if (name !== options.hip) {\n        bone.position.copy(bonesPosition[i])\n      }\n    }\n  }\n\n  if (options.preserveMatrix) {\n    // restore matrix\n\n    target.updateMatrixWorld(true)\n  }\n}\n\nfunction retargetClip(target, source, clip, options = {}) {\n  options.useFirstFramePosition = options.useFirstFramePosition !== undefined ? options.useFirstFramePosition : false\n  options.fps = options.fps !== undefined ? options.fps : 30\n  options.names = options.names || []\n\n  if (!source.isObject3D) {\n    source = getHelperFromSkeleton(source)\n  }\n\n  const numFrames = Math.round(clip.duration * (options.fps / 1000) * 1000),\n    delta = 1 / options.fps,\n    convertedTracks = [],\n    mixer = new AnimationMixer(source),\n    bones = getBones(target.skeleton),\n    boneDatas = []\n  let positionOffset, bone, boneTo, boneData, name\n\n  mixer.clipAction(clip).play()\n  mixer.update(0)\n\n  source.updateMatrixWorld()\n\n  for (let i = 0; i < numFrames; ++i) {\n    const time = i * delta\n\n    retarget(target, source, options)\n\n    for (let j = 0; j < bones.length; ++j) {\n      name = options.names[bones[j].name] || bones[j].name\n\n      boneTo = getBoneByName(name, source.skeleton)\n\n      if (boneTo) {\n        bone = bones[j]\n        boneData = boneDatas[j] = boneDatas[j] || { bone: bone }\n\n        if (options.hip === name) {\n          if (!boneData.pos) {\n            boneData.pos = {\n              times: new Float32Array(numFrames),\n              values: new Float32Array(numFrames * 3),\n            }\n          }\n\n          if (options.useFirstFramePosition) {\n            if (i === 0) {\n              positionOffset = bone.position.clone()\n            }\n\n            bone.position.sub(positionOffset)\n          }\n\n          boneData.pos.times[i] = time\n\n          bone.position.toArray(boneData.pos.values, i * 3)\n        }\n\n        if (!boneData.quat) {\n          boneData.quat = {\n            times: new Float32Array(numFrames),\n            values: new Float32Array(numFrames * 4),\n          }\n        }\n\n        boneData.quat.times[i] = time\n\n        bone.quaternion.toArray(boneData.quat.values, i * 4)\n      }\n    }\n\n    mixer.update(delta)\n\n    source.updateMatrixWorld()\n  }\n\n  for (let i = 0; i < boneDatas.length; ++i) {\n    boneData = boneDatas[i]\n\n    if (boneData) {\n      if (boneData.pos) {\n        convertedTracks.push(\n          new VectorKeyframeTrack(\n            '.bones[' + boneData.bone.name + '].position',\n            boneData.pos.times,\n            boneData.pos.values,\n          ),\n        )\n      }\n\n      convertedTracks.push(\n        new QuaternionKeyframeTrack(\n          '.bones[' + boneData.bone.name + '].quaternion',\n          boneData.quat.times,\n          boneData.quat.values,\n        ),\n      )\n    }\n  }\n\n  mixer.uncacheAction(clip)\n\n  return new AnimationClip(clip.name, -1, convertedTracks)\n}\n\nfunction clone(source) {\n  const sourceLookup = new Map()\n  const cloneLookup = new Map()\n\n  const clone = source.clone()\n\n  parallelTraverse(source, clone, function (sourceNode, clonedNode) {\n    sourceLookup.set(clonedNode, sourceNode)\n    cloneLookup.set(sourceNode, clonedNode)\n  })\n\n  clone.traverse(function (node) {\n    if (!node.isSkinnedMesh) return\n\n    const clonedMesh = node\n    const sourceMesh = sourceLookup.get(node)\n    const sourceBones = sourceMesh.skeleton.bones\n\n    clonedMesh.skeleton = sourceMesh.skeleton.clone()\n    clonedMesh.bindMatrix.copy(sourceMesh.bindMatrix)\n\n    clonedMesh.skeleton.bones = sourceBones.map(function (bone) {\n      return cloneLookup.get(bone)\n    })\n\n    clonedMesh.bind(clonedMesh.skeleton, clonedMesh.bindMatrix)\n  })\n\n  return clone\n}\n\n// internal helper\n\nfunction getBoneByName(name, skeleton) {\n  for (let i = 0, bones = getBones(skeleton); i < bones.length; i++) {\n    if (name === bones[i].name) return bones[i]\n  }\n}\n\nfunction getBones(skeleton) {\n  return Array.isArray(skeleton) ? skeleton : skeleton.bones\n}\n\nfunction getHelperFromSkeleton(skeleton) {\n  const source = new SkeletonHelper(skeleton.bones[0])\n  source.skeleton = skeleton\n\n  return source\n}\n\nfunction parallelTraverse(a, b, callback) {\n  callback(a, b)\n\n  for (let i = 0; i < a.children.length; i++) {\n    parallelTraverse(a.children[i], b.children[i], callback)\n  }\n}\n\nexport const SkeletonUtils = { retarget, retargetClip, clone }\n"], "names": ["clone"], "mappings": ";;;;;AAWA,SAAS,SAAS,MAAA,EAAQ,MAAA,EAAQ,UAAU,CAAA,CAAA,EAAI;IAC9C,MAAM,MAAM,uJAAI,UAAA,CAAS,GACvB,OAAO,uJAAI,aAAA,CAAY,GACvB,QAAQ,uJAAI,UAAA,CAAS,GACrB,iBAAiB,uJAAI,UAAA,CAAS,GAC9B,iBAAiB,uJAAI,UAAA,CAAS,GAC9B,eAAe,uJAAI,UAAA,CAAS;IAE9B,QAAQ,cAAA,GAAiB,QAAQ,cAAA,KAAmB,KAAA,IAAY,QAAQ,cAAA,GAAiB;IACzF,QAAQ,gBAAA,GAAmB,QAAQ,gBAAA,KAAqB,KAAA,IAAY,QAAQ,gBAAA,GAAmB;IAC/F,QAAQ,mBAAA,GAAsB,QAAQ,mBAAA,KAAwB,KAAA,IAAY,QAAQ,mBAAA,GAAsB;IACxG,QAAQ,eAAA,GAAkB,QAAQ,eAAA,KAAoB,KAAA,IAAY,QAAQ,eAAA,GAAkB;IAC5F,QAAQ,GAAA,GAAM,QAAQ,GAAA,KAAQ,KAAA,IAAY,QAAQ,GAAA,GAAM;IACxD,QAAQ,KAAA,GAAQ,QAAQ,KAAA,IAAS,CAAE;IAEnC,MAAM,cAAc,OAAO,UAAA,GAAa,OAAO,QAAA,CAAS,KAAA,GAAQ,SAAS,MAAM,GAC7E,QAAQ,OAAO,UAAA,GAAa,OAAO,QAAA,CAAS,KAAA,GAAQ,SAAS,MAAM;IAErE,IAAI,WAAW,MAAM,MAAM,QAAQ;IAInC,IAAI,OAAO,UAAA,EAAY;QACrB,OAAO,QAAA,CAAS,IAAA,CAAM;IAC1B,OAAS;QACL,QAAQ,eAAA,GAAkB;QAC1B,QAAQ,cAAA,GAAiB;IAC1B;IAED,IAAI,QAAQ,gBAAA,EAAkB;QAC5B,gBAAgB,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;YACrC,cAAc,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAAE,QAAA,CAAS,KAAA,EAAO;QAC7C;IACF;IAED,IAAI,QAAQ,cAAA,EAAgB;QAG1B,OAAO,iBAAA,CAAmB;QAE1B,OAAO,WAAA,CAAY,QAAA,CAAU;QAI7B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,QAAA,CAAS,MAAA,EAAQ,EAAE,EAAG;YAC/C,OAAO,QAAA,CAAS,CAAC,CAAA,CAAE,iBAAA,CAAkB,IAAI;QAC1C;IACF;IAED,IAAI,QAAQ,OAAA,EAAS;QACnB,YAAY,CAAE,CAAA;QAEd,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;YACrC,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,OAAO,QAAQ,KAAA,CAAM,KAAK,IAAI,CAAA,IAAK,KAAK,IAAA;YAExC,IAAI,QAAQ,OAAA,CAAQ,IAAI,CAAA,EAAG;gBACzB,KAAK,MAAA,CAAO,QAAA,CAAS,QAAQ,OAAA,CAAQ,IAAI,CAAC;gBAE1C,KAAK,MAAA,CAAO,SAAA,CAAU,KAAK,QAAA,EAAU,KAAK,UAAA,EAAY,KAAK,KAAK;gBAEhE,KAAK,iBAAA,CAAmB;YACzB;YAED,UAAU,IAAA,CAAK,KAAK,WAAA,CAAY,KAAA,CAAK,CAAE;QACxC;IACF;IAED,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;QACrC,OAAO,KAAA,CAAM,CAAC,CAAA;QACd,OAAO,QAAQ,KAAA,CAAM,KAAK,IAAI,CAAA,IAAK,KAAK,IAAA;QAExC,SAAS,cAAc,MAAM,WAAW;QAExC,aAAa,IAAA,CAAK,KAAK,WAAW;QAElC,IAAI,QAAQ;YACV,OAAO,iBAAA,CAAmB;YAE1B,IAAI,QAAQ,eAAA,EAAiB;gBAC3B,eAAe,IAAA,CAAK,OAAO,WAAW;YAC9C,OAAa;gBACL,eAAe,IAAA,CAAK,OAAO,WAAW,EAAE,MAAA,CAAQ;gBAChD,eAAe,QAAA,CAAS,OAAO,WAAW;YAC3C;YAID,MAAM,kBAAA,CAAmB,cAAc;YACvC,eAAe,KAAA,CAAM,MAAM,GAAA,CAAI,IAAI,MAAM,CAAA,EAAG,IAAI,MAAM,CAAA,EAAG,IAAI,MAAM,CAAC,CAAC;YAIrE,aAAa,0BAAA,CAA2B,KAAK,qBAAA,CAAsB,cAAc,CAAC;YAElF,IAAI,OAAO,UAAA,EAAY;gBACrB,MAAM,YAAY,MAAM,OAAA,CAAQ,IAAI,GAClC,cAAc,YACV,SAAA,CAAU,SAAS,CAAA,GACnB,eAAe,IAAA,CAAK,OAAO,QAAA,CAAS,YAAA,CAAa,SAAS,CAAC,EAAE,MAAA,CAAQ;gBAE3E,aAAa,QAAA,CAAS,WAAW;YAClC;YAED,aAAa,YAAA,CAAa,cAAc;QACzC;QAED,IAAI,KAAK,MAAA,IAAU,KAAK,MAAA,CAAO,MAAA,EAAQ;YACrC,KAAK,MAAA,CAAO,IAAA,CAAK,KAAK,MAAA,CAAO,WAAW,EAAE,MAAA,CAAQ;YAClD,KAAK,MAAA,CAAO,QAAA,CAAS,YAAY;QACvC,OAAW;YACL,KAAK,MAAA,CAAO,IAAA,CAAK,YAAY;QAC9B;QAED,IAAI,QAAQ,mBAAA,IAAuB,SAAS,QAAQ,GAAA,EAAK;YACvD,KAAK,MAAA,CAAO,WAAA,CAAY,IAAI,GAAA,CAAI,GAAG,KAAK,QAAA,CAAS,CAAA,EAAG,CAAC,CAAC;QACvD;QAED,KAAK,MAAA,CAAO,SAAA,CAAU,KAAK,QAAA,EAAU,KAAK,UAAA,EAAY,KAAK,KAAK;QAEhE,KAAK,iBAAA,CAAmB;IACzB;IAED,IAAI,QAAQ,gBAAA,EAAkB;QAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;YACrC,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,OAAO,QAAQ,KAAA,CAAM,KAAK,IAAI,CAAA,IAAK,KAAK,IAAA;YAExC,IAAI,SAAS,QAAQ,GAAA,EAAK;gBACxB,KAAK,QAAA,CAAS,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC;YACpC;QACF;IACF;IAED,IAAI,QAAQ,cAAA,EAAgB;QAG1B,OAAO,iBAAA,CAAkB,IAAI;IAC9B;AACH;AAEA,SAAS,aAAa,MAAA,EAAQ,MAAA,EAAQ,IAAA,EAAM,UAAU,CAAA,CAAA,EAAI;IACxD,QAAQ,qBAAA,GAAwB,QAAQ,qBAAA,KAA0B,KAAA,IAAY,QAAQ,qBAAA,GAAwB;IAC9G,QAAQ,GAAA,GAAM,QAAQ,GAAA,KAAQ,KAAA,IAAY,QAAQ,GAAA,GAAM;IACxD,QAAQ,KAAA,GAAQ,QAAQ,KAAA,IAAS,CAAE,CAAA;IAEnC,IAAI,CAAC,OAAO,UAAA,EAAY;QACtB,SAAS,sBAAsB,MAAM;IACtC;IAED,MAAM,YAAY,KAAK,KAAA,CAAM,KAAK,QAAA,GAAA,CAAY,QAAQ,GAAA,GAAM,GAAA,IAAQ,GAAI,GACtE,QAAQ,IAAI,QAAQ,GAAA,EACpB,kBAAkB,CAAE,CAAA,EACpB,QAAQ,uJAAI,iBAAA,CAAe,MAAM,GACjC,QAAQ,SAAS,OAAO,QAAQ,GAChC,YAAY,CAAE,CAAA;IAChB,IAAI,gBAAgB,MAAM,QAAQ,UAAU;IAE5C,MAAM,UAAA,CAAW,IAAI,EAAE,IAAA,CAAM;IAC7B,MAAM,MAAA,CAAO,CAAC;IAEd,OAAO,iBAAA,CAAmB;IAE1B,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,EAAE,EAAG;QAClC,MAAM,OAAO,IAAI;QAEjB,SAAS,QAAQ,QAAQ,OAAO;QAEhC,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;YACrC,OAAO,QAAQ,KAAA,CAAM,KAAA,CAAM,CAAC,CAAA,CAAE,IAAI,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA;YAEhD,SAAS,cAAc,MAAM,OAAO,QAAQ;YAE5C,IAAI,QAAQ;gBACV,OAAO,KAAA,CAAM,CAAC,CAAA;gBACd,WAAW,SAAA,CAAU,CAAC,CAAA,GAAI,SAAA,CAAU,CAAC,CAAA,IAAK;oBAAE;gBAAY;gBAExD,IAAI,QAAQ,GAAA,KAAQ,MAAM;oBACxB,IAAI,CAAC,SAAS,GAAA,EAAK;wBACjB,SAAS,GAAA,GAAM;4BACb,OAAO,IAAI,aAAa,SAAS;4BACjC,QAAQ,IAAI,aAAa,YAAY,CAAC;wBACvC;oBACF;oBAED,IAAI,QAAQ,qBAAA,EAAuB;wBACjC,IAAI,MAAM,GAAG;4BACX,iBAAiB,KAAK,QAAA,CAAS,KAAA,CAAO;wBACvC;wBAED,KAAK,QAAA,CAAS,GAAA,CAAI,cAAc;oBACjC;oBAED,SAAS,GAAA,CAAI,KAAA,CAAM,CAAC,CAAA,GAAI;oBAExB,KAAK,QAAA,CAAS,OAAA,CAAQ,SAAS,GAAA,CAAI,MAAA,EAAQ,IAAI,CAAC;gBACjD;gBAED,IAAI,CAAC,SAAS,IAAA,EAAM;oBAClB,SAAS,IAAA,GAAO;wBACd,OAAO,IAAI,aAAa,SAAS;wBACjC,QAAQ,IAAI,aAAa,YAAY,CAAC;oBACvC;gBACF;gBAED,SAAS,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,GAAI;gBAEzB,KAAK,UAAA,CAAW,OAAA,CAAQ,SAAS,IAAA,CAAK,MAAA,EAAQ,IAAI,CAAC;YACpD;QACF;QAED,MAAM,MAAA,CAAO,KAAK;QAElB,OAAO,iBAAA,CAAmB;IAC3B;IAED,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,EAAE,EAAG;QACzC,WAAW,SAAA,CAAU,CAAC,CAAA;QAEtB,IAAI,UAAU;YACZ,IAAI,SAAS,GAAA,EAAK;gBAChB,gBAAgB,IAAA,CACd,uJAAI,sBAAA,CACF,YAAY,SAAS,IAAA,CAAK,IAAA,GAAO,cACjC,SAAS,GAAA,CAAI,KAAA,EACb,SAAS,GAAA,CAAI,MAAA;YAGlB;YAED,gBAAgB,IAAA,CACd,uJAAI,0BAAA,CACF,YAAY,SAAS,IAAA,CAAK,IAAA,GAAO,gBACjC,SAAS,IAAA,CAAK,KAAA,EACd,SAAS,IAAA,CAAK,MAAA;QAGnB;IACF;IAED,MAAM,aAAA,CAAc,IAAI;IAExB,OAAO,uJAAI,gBAAA,CAAc,KAAK,IAAA,EAAM,CAAA,GAAI,eAAe;AACzD;AAEA,SAAS,MAAM,MAAA,EAAQ;IACrB,MAAM,eAAe,aAAA,GAAA,IAAI,IAAK;IAC9B,MAAM,cAAc,aAAA,GAAA,IAAI,IAAK;IAE7B,MAAMA,SAAQ,OAAO,KAAA,CAAO;IAE5B,iBAAiB,QAAQA,QAAO,SAAU,UAAA,EAAY,UAAA,EAAY;QAChE,aAAa,GAAA,CAAI,YAAY,UAAU;QACvC,YAAY,GAAA,CAAI,YAAY,UAAU;IAC1C,CAAG;IAEDA,OAAM,QAAA,CAAS,SAAU,IAAA,EAAM;QAC7B,IAAI,CAAC,KAAK,aAAA,EAAe;QAEzB,MAAM,aAAa;QACnB,MAAM,aAAa,aAAa,GAAA,CAAI,IAAI;QACxC,MAAM,cAAc,WAAW,QAAA,CAAS,KAAA;QAExC,WAAW,QAAA,GAAW,WAAW,QAAA,CAAS,KAAA,CAAO;QACjD,WAAW,UAAA,CAAW,IAAA,CAAK,WAAW,UAAU;QAEhD,WAAW,QAAA,CAAS,KAAA,GAAQ,YAAY,GAAA,CAAI,SAAU,IAAA,EAAM;YAC1D,OAAO,YAAY,GAAA,CAAI,IAAI;QACjC,CAAK;QAED,WAAW,IAAA,CAAK,WAAW,QAAA,EAAU,WAAW,UAAU;IAC9D,CAAG;IAED,OAAOA;AACT;AAIA,SAAS,cAAc,IAAA,EAAM,QAAA,EAAU;IACrC,IAAA,IAAS,IAAI,GAAG,QAAQ,SAAS,QAAQ,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACjE,IAAI,SAAS,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA,EAAM,OAAO,KAAA,CAAM,CAAC,CAAA;IAC3C;AACH;AAEA,SAAS,SAAS,QAAA,EAAU;IAC1B,OAAO,MAAM,OAAA,CAAQ,QAAQ,IAAI,WAAW,SAAS,KAAA;AACvD;AAEA,SAAS,sBAAsB,QAAA,EAAU;IACvC,MAAM,SAAS,uJAAI,iBAAA,CAAe,SAAS,KAAA,CAAM,CAAC,CAAC;IACnD,OAAO,QAAA,GAAW;IAElB,OAAO;AACT;AAEA,SAAS,iBAAiB,CAAA,EAAG,CAAA,EAAG,QAAA,EAAU;IACxC,SAAS,GAAG,CAAC;IAEb,IAAA,IAAS,IAAI,GAAG,IAAI,EAAE,QAAA,CAAS,MAAA,EAAQ,IAAK;QAC1C,iBAAiB,EAAE,QAAA,CAAS,CAAC,CAAA,EAAG,EAAE,QAAA,CAAS,CAAC,CAAA,EAAG,QAAQ;IACxD;AACH;AAEY,MAAC,gBAAgB;IAAE;IAAU;IAAc;AAAK", "ignoreList": [0], "debugId": null}}]}