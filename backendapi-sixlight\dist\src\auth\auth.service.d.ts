import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma.service';
import { $Enums } from '@prisma/client';
export declare class AuthService {
    private prisma;
    private jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    validateUser(email: string, pass: string): Promise<{
        id: number;
        email: string;
        name: string | null;
        role: $Enums.Role;
        createdAt: Date;
        updatedAt: Date;
        profileImage: string | null;
    } | null>;
    login(user: any): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            role: any;
            name: any;
        };
    }>;
    register(data: {
        email: string;
        password: string;
        name?: string;
        role?: string;
    }): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            role: any;
            name: any;
        };
    }>;
}
