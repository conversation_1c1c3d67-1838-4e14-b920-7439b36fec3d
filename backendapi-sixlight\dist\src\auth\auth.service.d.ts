import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma.service';
import { EmailService } from '../email/email.service';
import { $Enums } from '@prisma/client';
export declare class AuthService {
    private prisma;
    private jwtService;
    private emailService;
    constructor(prisma: PrismaService, jwtService: JwtService, emailService: EmailService);
    validateUser(email: string, pass: string): Promise<{
        id: number;
        email: string;
        name: string | null;
        role: $Enums.Role;
        createdAt: Date;
        updatedAt: Date;
        profileImage: string | null;
        emailVerified: boolean;
        emailVerificationToken: string | null;
        emailVerificationExpires: Date | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
    } | null>;
    login(user: any): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            role: any;
            name: any;
        };
    }>;
    register(data: {
        email: string;
        password: string;
        name?: string;
        role?: string;
    }): Promise<{
        message: string;
        user: {
            id: number;
            email: string;
            name: string | null;
            emailVerified: boolean;
        };
    }>;
    verifyEmail(token: string): Promise<{
        message: string;
    }>;
    requestPasswordReset(email: string): Promise<{
        message: string;
    }>;
    resetPassword(token: string, newPassword: string): Promise<{
        message: string;
    }>;
    resendVerificationEmail(email: string): Promise<{
        message: string;
    }>;
}
