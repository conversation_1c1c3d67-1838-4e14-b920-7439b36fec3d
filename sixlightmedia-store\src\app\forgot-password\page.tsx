"use client";
import { useState } from "react";
import { forgotPassword } from "@/api/auth";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";
import { Mail, ArrowLeft, CheckCircle, AlertCircle } from "lucide-react";

// Zod validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const [formData, setFormData] = useState<ForgotPasswordFormData>({
    email: "",
  });
  const [errors, setErrors] = useState<Partial<ForgotPasswordFormData>>({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [message, setMessage] = useState("");

  const handleInputChange = (
    field: keyof ForgotPasswordFormData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});
    setMessage("");

    // Validate form data with Zod
    const validation = forgotPasswordSchema.safeParse(formData);
    if (!validation.success) {
      setLoading(false);
      const fieldErrors: Partial<ForgotPasswordFormData> = {};
      validation.error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as keyof ForgotPasswordFormData] =
            err.message;
        }
      });
      setErrors(fieldErrors);
      return;
    }

    try {
      const result = await forgotPassword(formData.email);
      setLoading(false);
      setSuccess(true);
      setMessage(result.message || "Password reset link sent to your email!");
    } catch (error) {
      setLoading(false);
      setMessage("Failed to send reset email. Please try again.");
    }
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-4">
        <div className="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100">
          <div className="flex flex-col items-center mb-6">
            <Image
              src="/6 Light Logo.png"
              alt="6 Light Logo"
              width={64}
              height={64}
              className="mb-4"
            />
            <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            <h1 className="text-2xl font-bold text-green-700">Email Sent!</h1>
          </div>

          <div className="space-y-4">
            <p className="text-gray-600">{message}</p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-800">
                <strong>📧 Check your email:</strong> We've sent a password
                reset link to{" "}
                <span className="font-mono">{formData.email}</span>
              </p>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                <strong>⏰ Link expires in 1 hour</strong> for your security.
              </p>
            </div>
          </div>

          <div className="space-y-3 mt-6">
            <Link
              href="/login"
              className="inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Login
            </Link>
            <button
              onClick={() => {
                setSuccess(false);
                setFormData({ email: "" });
                setMessage("");
              }}
              className="w-full text-gray-600 hover:text-gray-800 transition-colors"
            >
              Send to different email
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
        <div className="flex flex-col items-center mb-6">
          <Image
            src="/6 Light Logo.png"
            alt="6 Light Logo"
            width={64}
            height={64}
            className="mb-4"
          />
          <h1 className="text-2xl font-bold text-gray-900">Forgot Password</h1>
          <p className="text-gray-600 text-center mt-2">
            Enter your email address and we'll send you a link to reset your
            password.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-1">
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="Enter your email address"
                autoComplete="email"
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ${
                  errors.email
                    ? "border-red-500 focus:ring-red-300"
                    : "border-gray-300 focus:ring-blue-300"
                }`}
                autoComplete="email"
                aria-invalid={!!errors.email}
                aria-describedby={errors.email ? "email-error" : undefined}
              />
            </div>
            {errors.email && (
              <div
                id="email-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.email}
              </div>
            )}
          </div>

          <button
            type="submit"
            className={`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${
              loading
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            }`}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Sending Reset Link...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <Mail className="h-4 w-4" />
                <span>Send Reset Link</span>
              </div>
            )}
          </button>

          {message && !success && (
            <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200">
              <AlertCircle size={16} />
              {message}
            </div>
          )}
        </form>

        <div className="mt-6 pt-6 border-t border-gray-200 text-center">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Login
          </Link>
        </div>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-500">
            Don't have an account?{" "}
            <Link
              href="/register"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
