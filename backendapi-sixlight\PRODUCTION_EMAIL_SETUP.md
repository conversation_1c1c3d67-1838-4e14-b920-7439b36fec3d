# 📧 Production Email Verification Setup Guide

## 🚨 Critical Issues Fixed

Your `.env` file had several issues that prevent email verification from working in production:

### ❌ Problems Found:
1. **FRONTEND_URL** pointed to `localhost:3000` (won't work for users in production)
2. **Missing CORS configuration** for production
3. **Missing email service configuration**
4. **No production-specific settings**

### ✅ What Was Added/Fixed:

## 📋 New Environment Variables Added

```env
# Frontend/Backend URLs
BACKEND_URL="http://localhost:3001"
CORS_ORIGIN="http://localhost:3000"

# Email Service Configuration
EMAIL_FROM_NAME="Six Light Media Store"
EMAIL_FROM_ADDRESS="<EMAIL>"

# Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SESSION_SECRET="your-session-secret-here"
COOKIE_SECURE="false"
```

## 🚀 Production Deployment Steps

### Step 1: Update Your Production Environment Variables

In your hosting platform (Render, Hero<PERSON>, Vercel, etc.), set these variables:

```env
# CRITICAL: Update these URLs
NODE_ENV="production"
FRONTEND_URL="https://your-actual-frontend-domain.com"
BACKEND_URL="https://your-actual-backend-domain.com"
CORS_ORIGIN="https://your-actual-frontend-domain.com"

# Email Configuration (same as development)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="dxsl tqan xfzc vynn"
EMAIL_FROM_NAME="Six Light Media Store"
EMAIL_FROM_ADDRESS="<EMAIL>"

# Security (production values)
COOKIE_SECURE="true"
TRUST_PROXY="true"
SESSION_SECRET="generate-new-secret-for-production"

# Database (your existing production database)
DATABASE_URL="your-production-database-url"
DIRECT_URL="your-production-direct-url"
JWT_SECRET="tVfn+VlANCzmunn+0AvG3WKJ+frOoPBUfLD8XsLS+bA="
```

### Step 2: Platform-Specific Instructions

#### For Render.com:
1. Go to your backend service dashboard
2. Click "Environment" tab
3. Add each variable manually
4. Deploy the service

#### For Heroku:
```bash
heroku config:set FRONTEND_URL=https://your-frontend.vercel.app
heroku config:set NODE_ENV=production
heroku config:set CORS_ORIGIN=https://your-frontend.vercel.app
# ... add all other variables
```

#### For Railway/Vercel:
- Add variables in the dashboard under "Environment Variables"

### Step 3: Test Production Email

1. Deploy your backend with new environment variables
2. Register a new user in production
3. Check if verification email arrives with correct links

## 🔍 Debugging Production Issues

### Run the Debug Script:
```bash
node debug-production-email.js
```

### Common Issues:

1. **Email links point to localhost**
   - Fix: Update `FRONTEND_URL` to production domain

2. **CORS errors in browser**
   - Fix: Update `CORS_ORIGIN` to match frontend domain

3. **No emails sent**
   - Check: SMTP credentials are set in production
   - Check: Production logs for email errors

4. **SSL/TLS errors**
   - Fix: Ensure `COOKIE_SECURE="true"` in production

## 📧 Email URL Structure

Development: `http://localhost:3000/verify-email?token=abc123`
Production: `https://your-domain.com/verify-email?token=abc123`

## ✅ Verification Checklist

- [ ] FRONTEND_URL points to production domain
- [ ] CORS_ORIGIN matches frontend domain  
- [ ] NODE_ENV set to "production"
- [ ] SMTP credentials configured
- [ ] SSL settings enabled (COOKIE_SECURE=true)
- [ ] Test email verification flow
- [ ] Check production logs for errors

## 🆘 Still Having Issues?

1. Check production logs for email service errors
2. Verify SMTP credentials work (test with debug script)
3. Ensure frontend can reach backend API
4. Check if emails are going to spam folder
5. Verify DNS/domain configuration

## 📞 Quick Fix Commands

Test SMTP in production:
```bash
node debug-production-email.js
```

Check environment variables:
```bash
echo $FRONTEND_URL
echo $NODE_ENV
echo $SMTP_USER
```
