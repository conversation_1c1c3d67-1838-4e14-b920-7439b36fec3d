(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{1319:(e,t,s)=>{Promise.resolve().then(s.bind(s,6616))},6616:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),r=s(2115),n=s(9958),o=s(6874),i=s.n(o),l=s(6766),c=s(1153);let d=c.z.object({name:c.z.string().min(2,"Name is required"),email:c.z.string().email("Invalid email address"),password:c.z.string().min(6,"Password must be at least 6 characters"),terms:c.z.literal(!0,{errorMap:()=>({message:"You must accept the terms and conditions"})})});function u(){let[e,t]=(0,r.useState)(""),[s,o]=(0,r.useState)(""),[c,u]=(0,r.useState)(""),[m,g]=(0,r.useState)(""),[h,x]=(0,r.useState)(!1),[f,p]=(0,r.useState)(!1),[b,N]=(0,r.useState)(!1);async function j(t){t.preventDefault(),x(!0),g(""),p(!1);let a=d.safeParse({name:c,email:e,password:s,terms:b});if(!a.success){x(!1),g(a.error.errors[0].message);return}let r=await (0,n.k)(e,s,c);x(!1),r.access_token?(p(!0),localStorage.setItem("token",r.access_token),window.location.href="/login"):g(r.error||"Registration failed")}return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-black",children:(0,a.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-xl p-8 flex flex-col gap-6",children:[(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,a.jsx)("h2",{className:"text-3xl font-extrabold text-center text-red-700",children:"Create Account"}),(0,a.jsxs)("form",{onSubmit:j,className:"flex flex-col gap-4",children:[(0,a.jsx)("input",{value:c,onChange:e=>u(e.target.value),placeholder:"Name",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"name"}),(0,a.jsx)("input",{value:e,onChange:e=>t(e.target.value),placeholder:"Email",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"email"}),(0,a.jsx)("input",{value:s,onChange:e=>o(e.target.value),type:"password",placeholder:"Password",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"new-password"}),(0,a.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("input",{type:"checkbox",checked:b,onChange:e=>N(e.target.checked),className:"accent-red-700"}),"I accept the"," ",(0,a.jsx)("a",{href:"#",className:"underline text-red-700",children:"terms and conditions"})]}),(0,a.jsx)("button",{type:"submit",className:"bg-black text-white font-semibold px-6 py-3 rounded-lg shadow hover:bg-green-700 transition disabled:opacity-60",disabled:h,children:h?"Registering...":"Register"}),m&&(0,a.jsx)("div",{className:"text-red-600 text-center text-sm mt-2",children:m}),f&&(0,a.jsx)("div",{className:"text-green-600 text-center text-sm mt-2",children:"Registration successful!"})]}),(0,a.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(i(),{href:"/login",className:"text-red-700 font-semibold hover:underline",children:"Login"})]})]})})}},9958:(e,t,s)=>{"use strict";s.d(t,{i:()=>r,k:()=>n});let a="http://localhost:3001";async function r(e,t){return(await fetch("".concat(a,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})})).json()}async function n(e,t,s){return(await fetch("".concat(a,"/auth/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:s})})).json()}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,153,441,684,358],()=>t(1319)),_N_E=e.O()}]);