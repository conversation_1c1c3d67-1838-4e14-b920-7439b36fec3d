(()=>{var e={};e.id=503,e.ids=[503],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2222:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o});var s=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o={children:["",{children:["user",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6123)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/user/dashboard/page",pathname:"/user/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:l,iconNode:c,...x},h)=>(0,s.createElement)("svg",{ref:h,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",i),...!l&&!d(x)&&{"aria-hidden":"true"},...x},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),x=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},d)=>(0,s.createElement)(c,{ref:d,iconNode:t,className:n(`lucide-${a(l(e))}`,`lucide-${e}`,r),...i}));return r.displayName=l(e),r}},2787:(e,t,r)=>{Promise.resolve().then(r.bind(r,9452))},2909:(e,t,r)=>{"use strict";r.d(t,{ZT:()=>n,oC:()=>l});var s=r(4999),a=r(9916);async function i(){try{let e=await (0,s.UL)(),t=e.get("jwt")?.value;if(!t)return{isAuthenticated:!1};let r=await fetch("https://backendapi-sixlight.onrender.com/auth/verify",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},cache:"no-store"});if(!r.ok)return{isAuthenticated:!1};let a=await r.json();return{isAuthenticated:!0,user:{id:a.id,email:a.email,role:a.role,name:a.name,profileImage:a.profileImage}}}catch(e){return console.error("Auth verification failed:",e),{isAuthenticated:!1}}}async function l(e="/login"){let t=await i();return t.isAuthenticated||(0,a.redirect)(e),t}async function n(e="/"){let t=await l("/login");return t.user?.role!=="ADMIN"&&(0,a.redirect)(e),t}},2941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3886:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\user\\\\dashboard\\\\UserDashboardClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\UserDashboardClient.tsx","default")},6123:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>l});var s=r(7413),a=r(2909),i=r(3886);let l="force-dynamic";async function n(){let e=await (0,a.oC)();return(0,s.jsx)(i.default,{user:e.user})}},6763:(e,t,r)=>{Promise.resolve().then(r.bind(r,3886))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9452:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(687),a=r(3210),i=r(474),l=r(5814),n=r.n(l),d=r(1891),o=r(5356);function c({user:e}){let[t,r]=(0,a.useState)(null),[l,c]=(0,a.useState)([]),[x,h]=(0,a.useState)(""),[u,m]=(0,a.useState)(0),[p,g]=(0,a.useState)(!1),[b,f]=(0,a.useState)(!0),v=()=>{m(JSON.parse(localStorage.getItem("cart")||"[]").length)},j=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PROCESSING":return"bg-blue-100 text-blue-800";case"COMPLETED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return b?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):x?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-red-600 text-center",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"⚠️"}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Error Loading Dashboard"}),(0,s.jsx)("p",{children:x})]})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{cartCount:u,onCartClick:()=>{g(!0)}}),(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-900 via-indigo-900 to-purple-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Welcome Back"})}),(0,s.jsxs)("p",{className:"text-xl text-gray-200 mb-4 max-w-2xl mx-auto",children:["Hello, ",e?.name||e?.email,"! Manage your orders and profile."]}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDD12 Secure user access"})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"✅ Server-side authentication active"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,s.jsx)(n(),{href:"/user/profile",className:"bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDC64"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"My Profile"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Update your personal information"})]})}),(0,s.jsx)(n(),{href:"/",className:"bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDECD️"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Shop Products"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Browse our amazing collection"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-6 border border-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Order Stats"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Total Orders: ",l.length]})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Recent Orders"}),0===l.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Start shopping to see your orders here!"}),(0,s.jsx)(n(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Start Shopping"})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[l.slice(0,5).map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[e.product.image&&(0,s.jsx)(i.default,{src:e.product.image,alt:e.product.name,width:60,height:60,className:"rounded-lg object-cover"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:e.product.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Order #",e.id," • Qty: ",e.quantity]}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-semibold text-gray-900",children:["K",e.totalPrice.toFixed(2)]}),(0,s.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${j(e.status)}`,children:e.status})]})]}),e.isCustomized&&(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-blue-600",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"})}),"Customized Product"]})})]},e.id)),l.length>5&&(0,s.jsx)("div",{className:"text-center pt-4",children:(0,s.jsxs)("p",{className:"text-gray-500",children:["Showing 5 of ",l.length," orders"]})})]})]})]})]}),(0,s.jsx)(o.A,{isOpen:p,onClose:()=>{g(!1),v()}})]})}r(5475)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[728,474,814,994,834],()=>r(2222));module.exports=s})();