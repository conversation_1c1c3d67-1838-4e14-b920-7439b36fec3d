(()=>{var e={};e.id=503,e.ids=[503],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2222:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),l=r.n(a),o=r(893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["user",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6123)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/user/dashboard/page",pathname:"/user/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(3210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:a="",children:l,iconNode:c,...x},m)=>(0,s.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:o("lucide",a),...!l&&!n(x)&&{"aria-hidden":"true"},...x},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),x=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},n)=>(0,s.createElement)(c,{ref:n,iconNode:t,className:o(`lucide-${i(l(e))}`,`lucide-${e}`,r),...a}));return r.displayName=l(e),r}},2941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4017:(e,t,r)=>{Promise.resolve().then(r.bind(r,6865))},6123:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\user\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx","default")},6865:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(687),i=r(3210),a=r(474),l=r(5814),o=r.n(l),n=r(1891),d=r(5356);function c(){let[e,t]=(0,i.useState)(null),[r,l]=(0,i.useState)([]),[c,x]=(0,i.useState)(""),[m,h]=(0,i.useState)(0),[u,p]=(0,i.useState)(!1),[b,g]=(0,i.useState)(!0),f=()=>{h(JSON.parse(localStorage.getItem("cart")||"[]").length)},j=()=>{p(!0)};return b?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:j}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading dashboard..."})]})})]}):c?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:j}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Error"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:c}),(0,s.jsxs)(o(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Home"]})]})})]}):e?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:j}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"w-24 h-24 mx-auto mb-6 relative",children:[(0,s.jsx)(a.default,{src:e.profileImage||"/usericon.png",alt:"Profile",width:96,height:96,className:"w-full h-full rounded-full object-cover border-4 border-white/20 shadow-2xl"}),(0,s.jsx)("div",{className:"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})]})}),(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:["Welcome back,",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:e.name||e.email.split("@")[0]})]}),(0,s.jsxs)("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto",children:[e.email," • ",e.role]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,s.jsx)(o(),{href:"/user/profile",className:"px-8 py-4 bg-white/10 backdrop-blur-lg text-white font-semibold rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300",children:"⚙️ Edit Profile"}),(0,s.jsx)(o(),{href:"/",className:"px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold rounded-2xl hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 shadow-lg",children:"\uD83D\uDECD️ Continue Shopping"})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-blue-700 bg-blue-100 px-3 py-1 rounded-full",children:"Total Orders"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-blue-700 mb-2",children:r.length}),(0,s.jsx)("div",{className:"text-sm text-blue-600",children:"Orders placed"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-green-700 bg-green-100 px-3 py-1 rounded-full",children:"Completed"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-green-700 mb-2",children:r.filter(e=>"COLLECTED"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-green-600",children:"Orders collected"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-yellow-700 bg-yellow-100 px-3 py-1 rounded-full",children:"Pending"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-yellow-700 mb-2",children:r.filter(e=>"PENDING"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-yellow-600",children:"Awaiting collection"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:"\uD83D\uDCCB Order History"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-100 px-4 py-2 rounded-xl",children:[r.length," ",1===r.length?"order":"orders"]})]}),0===r.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Start shopping to see your orders here."}),(0,s.jsx)(o(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:"\uD83D\uDECD️ Start Shopping"})]}):(0,s.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,s.jsxs)("div",{className:"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-white rounded-xl overflow-hidden border border-gray-200 shadow-sm",children:(0,s.jsx)(a.default,{src:e.product?.image||"/bottle-dummy.jpg",alt:e.product?.name||"Product",width:64,height:64,className:"w-full h-full object-contain"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-gray-900 text-lg",children:e.product?.name||"Product"}),(0,s.jsxs)("p",{className:"text-green-600 font-semibold",children:["K",e.product?.price??0]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("span",{className:`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${"COLLECTED"===e.status?"bg-green-100 text-green-700":"PENDING"===e.status?"bg-yellow-100 text-yellow-700":"bg-gray-100 text-gray-700"}`,children:"COLLECTED"===e.status?"✅ Collected":"PENDING"===e.status?"⏳ Pending":e.status}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:new Date(e.createdAt).toLocaleDateString()})]})]}),e.customization&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-xl border border-blue-200",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-blue-900 mb-2",children:"\uD83C\uDFA8 Customization Details"}),(0,s.jsxs)("div",{className:"space-y-1",children:[e.customization.color&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-blue-700",children:[(0,s.jsx)("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:e.customization.color}}),(0,s.jsxs)("span",{children:["Color: ",e.customization.color]})]}),e.customization.text&&(0,s.jsxs)("div",{className:"text-sm text-blue-700",children:["\uD83D\uDCDD Text: “",e.customization.text,"”"]})]})]})]},e.id))})]})]}),(0,s.jsx)(d.A,{isOpen:u,onClose:()=>{p(!1),f()}})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:j}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading user data..."})]})})]})}},7569:(e,t,r)=>{Promise.resolve().then(r.bind(r,6123))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,145,658,474,814,249],()=>r(2222));module.exports=s})();