"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[825],{228:(t,n,r)=>{r.d(n,{DY:()=>s,IU:()=>u,uv:()=>c});let o=t=>"object"==typeof t&&"function"==typeof t.then,i=[];function a(t,n,r=(t,n)=>t===n){if(t===n)return!0;if(!t||!n)return!1;let o=t.length;if(n.length!==o)return!1;for(let i=0;i<o;i++)if(!r(t[i],n[i]))return!1;return!0}function l(t,n=null,r=!1,s={}){for(let o of(null===n&&(n=[t]),i))if(a(n,o.keys,o.equal)){if(r)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return s.lifespan&&s.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,s.lifespan)),o.response;if(!r)throw o.promise}let c={keys:n,equal:s.equal,remove:()=>{let t=i.indexOf(c);-1!==t&&i.splice(t,1)},promise:(o(t)?t:t(...n)).then(t=>{c.response=t,s.lifespan&&s.lifespan>0&&(c.timeout=setTimeout(c.remove,s.lifespan))}).catch(t=>c.error=t)};if(i.push(c),!r)throw c.promise}let s=(t,n,r)=>l(t,n,!1,r),c=(t,n,r)=>void l(t,n,!0,r),u=t=>{if(void 0===t||0===t.length)i.splice(0,i.length);else{let n=i.find(n=>a(t,n.keys,n.equal));n&&n.remove()}}},544:(t,n,r)=>{let o,i,a,l,s;r.d(n,{A:()=>ei,B:()=>L,C:()=>ea,E:()=>R,F:()=>eu,a:()=>I,b:()=>S,c:()=>eS,d:()=>eO,e:()=>em,f:()=>eG,i:()=>A,u:()=>O});var c=r(3264),u=r(7431),f=r(2115),d=r.t(f,2),p=r(1933),v=r(5643);let h=t=>{let n,r=new Set,o=(t,o)=>{let i="function"==typeof t?t(n):t;if(!Object.is(i,n)){let t=n;n=(null!=o?o:"object"!=typeof i||null===i)?i:Object.assign({},n,i),r.forEach(r=>r(n,t))}},i=()=>n,a={setState:o,getState:i,getInitialState:()=>l,subscribe:t=>(r.add(t),()=>r.delete(t))},l=n=t(o,i,a);return a},m=t=>t?h(t):h,{useSyncExternalStoreWithSelector:b}=v,g=t=>t,y=(t,n)=>{let r=m(t),o=(t,o=n)=>(function(t,n=g,r){let o=b(t.subscribe,t.getState,t.getInitialState,n,r);return f.useDebugValue(o),o})(r,t,o);return Object.assign(o,r),o},w=(t,n)=>t?y(t,n):y;var E=r(5220),_=r.n(E),x=r(4342),j=r(228),M=r(5155),k=r(6354);function T(t){let n=t.root;for(;n.getState().previousRoot;)n=n.getState().previousRoot;return n}r(9509),d.act;let P=t=>t&&t.isOrthographicCamera,A=t=>t&&t.hasOwnProperty("current"),C=t=>null!=t&&("string"==typeof t||"number"==typeof t||t.isColor),S=((t,n)=>"undefined"!=typeof window&&((null==(t=window.document)?void 0:t.createElement)||(null==(n=window.navigator)?void 0:n.product)==="ReactNative"))()?f.useLayoutEffect:f.useEffect;function I(t){let n=f.useRef(t);return S(()=>void(n.current=t),[t]),n}function O(){let t=(0,k.u5)(),n=(0,k.y3)();return f.useMemo(()=>({children:r})=>{let o=(0,k.Nz)(t,!0,t=>t.type===f.StrictMode)?f.StrictMode:f.Fragment;return(0,M.jsx)(o,{children:(0,M.jsx)(n,{children:r})})},[t,n])}function L({set:t}){return S(()=>(t(new Promise(()=>null)),()=>t(!1)),[t]),null}let R=(t=>((t=class extends f.Component{constructor(...t){super(...t),this.state={error:!1}}componentDidCatch(t){this.props.set(t)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),t))();function D(t){var n;let r="undefined"!=typeof window?null!=(n=window.devicePixelRatio)?n:2:1;return Array.isArray(t)?Math.min(Math.max(t[0],r),t[1]):t}function N(t){var n;return null==(n=t.__r3f)?void 0:n.root.getState()}let z={obj:t=>t===Object(t)&&!z.arr(t)&&"function"!=typeof t,fun:t=>"function"==typeof t,str:t=>"string"==typeof t,num:t=>"number"==typeof t,boo:t=>"boolean"==typeof t,und:t=>void 0===t,nul:t=>null===t,arr:t=>Array.isArray(t),equ(t,n,{arrays:r="shallow",objects:o="reference",strict:i=!0}={}){let a;if(typeof t!=typeof n||!!t!=!!n)return!1;if(z.str(t)||z.num(t)||z.boo(t))return t===n;let l=z.obj(t);if(l&&"reference"===o)return t===n;let s=z.arr(t);if(s&&"reference"===r)return t===n;if((s||l)&&t===n)return!0;for(a in t)if(!(a in n))return!1;if(l&&"shallow"===r&&"shallow"===o){for(a in i?n:t)if(!z.equ(t[a],n[a],{strict:i,objects:"reference"}))return!1}else for(a in i?n:t)if(t[a]!==n[a])return!1;if(z.und(a)){if(s&&0===t.length&&0===n.length||l&&0===Object.keys(t).length&&0===Object.keys(n).length)return!0;if(t!==n)return!1}return!0}},F=["children","key","ref"];function U(t,n,r,o){let i=null==t?void 0:t.__r3f;return!i&&(i={root:n,type:r,parent:null,children:[],props:function(t){let n={};for(let r in t)F.includes(r)||(n[r]=t[r]);return n}(o),object:t,eventCount:0,handlers:{},isHidden:!1},t&&(t.__r3f=i)),i}function B(t,n){let r=t[n];if(!n.includes("-"))return{root:t,key:n,target:r};for(let i of(r=t,n.split("-"))){var o;n=i,t=r,r=null==(o=r)?void 0:o[n]}return{root:t,key:n,target:r}}let q=/-\d+$/;function Y(t,n){if(z.str(n.props.attach)){if(q.test(n.props.attach)){let r=n.props.attach.replace(q,""),{root:o,key:i}=B(t.object,r);Array.isArray(o[i])||(o[i]=[])}let{root:r,key:o}=B(t.object,n.props.attach);n.previousAttach=r[o],r[o]=n.object}else z.fun(n.props.attach)&&(n.previousAttach=n.props.attach(t.object,n.object))}function H(t,n){if(z.str(n.props.attach)){let{root:r,key:o}=B(t.object,n.props.attach),i=n.previousAttach;void 0===i?delete r[o]:r[o]=i}else null==n.previousAttach||n.previousAttach(t.object,n.object);delete n.previousAttach}let W=[...F,"args","dispose","attach","object","onUpdate","dispose"],G=new Map,X=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],V=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function $(t,n){var r,o;let i=t.__r3f,a=i&&T(i).getState(),l=null==i?void 0:i.eventCount;for(let r in n){let l=n[r];if(W.includes(r))continue;if(i&&V.test(r)){"function"==typeof l?i.handlers[r]=l:delete i.handlers[r],i.eventCount=Object.keys(i.handlers).length;continue}if(void 0===l)continue;let{root:s,key:u,target:f}=B(t,r);f instanceof c.zgK&&l instanceof c.zgK?f.mask=l.mask:f instanceof c.Q1f&&C(l)?f.set(l):null!==f&&"object"==typeof f&&"function"==typeof f.set&&"function"==typeof f.copy&&null!=l&&l.constructor&&f.constructor===l.constructor?f.copy(l):null!==f&&"object"==typeof f&&"function"==typeof f.set&&Array.isArray(l)?"function"==typeof f.fromArray?f.fromArray(l):f.set(...l):null!==f&&"object"==typeof f&&"function"==typeof f.set&&"number"==typeof l?"function"==typeof f.setScalar?f.setScalar(l):f.set(l):(s[u]=l,a&&!a.linear&&X.includes(u)&&null!=(o=s[u])&&o.isTexture&&s[u].format===c.GWd&&s[u].type===c.OUM&&(s[u].colorSpace=c.er$))}if(null!=i&&i.parent&&null!=a&&a.internal&&null!=(r=i.object)&&r.isObject3D&&l!==i.eventCount){let t=i.object,n=a.internal.interaction.indexOf(t);n>-1&&a.internal.interaction.splice(n,1),i.eventCount&&null!==t.raycast&&a.internal.interaction.push(t)}return i&&void 0===i.props.attach&&(i.object.isBufferGeometry?i.props.attach="geometry":i.object.isMaterial&&(i.props.attach="material")),i&&K(i),t}function K(t){var n;if(!t.parent)return;null==t.props.onUpdate||t.props.onUpdate(t.object);let r=null==(n=t.root)||null==n.getState?void 0:n.getState();r&&0===r.internal.frames&&r.invalidate()}function Z(t,n){t.manual||(P(t)?(t.left=-(n.width/2),t.right=n.width/2,t.top=n.height/2,t.bottom=-(n.height/2)):t.aspect=n.width/n.height,t.updateProjectionMatrix())}let Q=t=>null==t?void 0:t.isObject3D;function J(t){return(t.eventObject||t.object).uuid+"/"+t.index+t.instanceId}function ee(t,n,r,o){let i=r.get(n);i&&(r.delete(n),0===r.size&&(t.delete(o),i.target.releasePointerCapture(o)))}let et=t=>!!(null!=t&&t.render),en=f.createContext(null),er=(t,n)=>{let r=w((r,o)=>{let i,a=new c.Pq0,l=new c.Pq0,s=new c.Pq0;function u(t=o().camera,n=l,r=o().size){let{width:i,height:c,top:f,left:d}=r,p=i/c;n.isVector3?s.copy(n):s.set(...n);let v=t.getWorldPosition(a).distanceTo(s);if(P(t))return{width:i/t.zoom,height:c/t.zoom,top:f,left:d,factor:1,distance:v,aspect:p};{let n=2*Math.tan(t.fov*Math.PI/180/2)*v,r=i/c*n;return{width:r,height:n,top:f,left:d,factor:i/r,distance:v,aspect:p}}}let d=t=>r(n=>({performance:{...n.performance,current:t}})),p=new c.I9Y;return{set:r,get:o,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(n=1)=>t(o(),n),advance:(t,r)=>n(t,r,o()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new c.zD7,pointer:p,mouse:p,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let t=o();i&&clearTimeout(i),t.performance.current!==t.performance.min&&d(t.performance.min),i=setTimeout(()=>d(o().performance.max),t.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:u},setEvents:t=>r(n=>({...n,events:{...n.events,...t}})),setSize:(t,n,i=0,a=0)=>{let s=o().camera,c={width:t,height:n,top:i,left:a};r(t=>({size:c,viewport:{...t.viewport,...u(s,l,c)}}))},setDpr:t=>r(n=>{let r=D(t);return{viewport:{...n.viewport,dpr:r,initialDpr:n.viewport.initialDpr||r}}}),setFrameloop:(t="always")=>{let n=o().clock;n.stop(),n.elapsedTime=0,"never"!==t&&(n.start(),n.elapsedTime=0),r(()=>({frameloop:t}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:f.createRef(),active:!1,frames:0,priority:0,subscribe:(t,n,r)=>{let i=o().internal;return i.priority=i.priority+ +(n>0),i.subscribers.push({ref:t,priority:n,store:r}),i.subscribers=i.subscribers.sort((t,n)=>t.priority-n.priority),()=>{let r=o().internal;null!=r&&r.subscribers&&(r.priority=r.priority-(n>0),r.subscribers=r.subscribers.filter(n=>n.ref!==t))}}}}}),o=r.getState(),i=o.size,a=o.viewport.dpr,l=o.camera;return r.subscribe(()=>{let{camera:t,size:n,viewport:o,gl:s,set:c}=r.getState();if(n.width!==i.width||n.height!==i.height||o.dpr!==a){i=n,a=o.dpr,Z(t,n),o.dpr>0&&s.setPixelRatio(o.dpr);let r="undefined"!=typeof HTMLCanvasElement&&s.domElement instanceof HTMLCanvasElement;s.setSize(n.width,n.height,r)}t!==l&&(l=t,c(n=>({viewport:{...n.viewport,...n.viewport.getCurrentViewport(t)}})))}),r.subscribe(n=>t(n)),r};function eo(){let t=f.useContext(en);if(!t)throw Error("R3F: Hooks can only be used within the Canvas component!");return t}function ei(t=t=>t,n){return eo()(t,n)}function ea(t,n=0){let r=eo(),o=r.getState().internal.subscribe,i=I(t);return S(()=>o(i,n,r),[n,o,r]),null}let el=new WeakMap,es=t=>{var n;return"function"==typeof t&&(null==t||null==(n=t.prototype)?void 0:n.constructor)===t};function ec(t,n){return function(r,...o){let i;return es(r)?(i=el.get(r))||(i=new r,el.set(r,i)):i=r,t&&t(i),Promise.all(o.map(t=>new Promise((r,o)=>i.load(t,t=>{Q(null==t?void 0:t.scene)&&Object.assign(t,function(t){let n={nodes:{},materials:{},meshes:{}};return t&&t.traverse(t=>{t.name&&(n.nodes[t.name]=t),t.material&&!n.materials[t.material.name]&&(n.materials[t.material.name]=t.material),t.isMesh&&!n.meshes[t.name]&&(n.meshes[t.name]=t)}),n}(t.scene)),r(t)},n,n=>o(Error(`Could not load ${t}: ${null==n?void 0:n.message}`))))))}}function eu(t,n,r,o){let i=Array.isArray(n)?n:[n],a=(0,j.DY)(ec(r,o),[t,...i],{equal:z.equ});return Array.isArray(n)?a:a[0]}eu.preload=function(t,n,r){let o=Array.isArray(n)?n:[n];return(0,j.uv)(ec(r),[t,...o])},eu.clear=function(t,n){let r=Array.isArray(n)?n:[n];return(0,j.IU)([t,...r])};let ef={},ed=/^three(?=[A-Z])/,ep=t=>`${t[0].toUpperCase()}${t.slice(1)}`,ev=0,eh=t=>"function"==typeof t;function em(t){if(eh(t)){let n=`${ev++}`;return ef[n]=t,n}Object.assign(ef,t)}function eb(t,n){let r=ep(t),o=ef[r];if("primitive"!==t&&!o)throw Error(`R3F: ${r} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===t&&!n.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==n.args&&!Array.isArray(n.args))throw Error("R3F: The args prop must be an array!")}function eg(t){if(t.isHidden){var n;t.props.attach&&null!=(n=t.parent)&&n.object?Y(t.parent,t):Q(t.object)&&!1!==t.props.visible&&(t.object.visible=!0),t.isHidden=!1,K(t)}}function ey(t,n,r){let o=n.root.getState();if(t.parent||t.object===o.scene){if(!n.object){var i,a;let t=ef[ep(n.type)];n.object=null!=(i=n.props.object)?i:new t(...null!=(a=n.props.args)?a:[]),n.object.__r3f=n}if($(n.object,n.props),n.props.attach)Y(t,n);else if(Q(n.object)&&Q(t.object)){let o=t.object.children.indexOf(null==r?void 0:r.object);if(r&&-1!==o){let r=t.object.children.indexOf(n.object);-1!==r?(t.object.children.splice(r,1),t.object.children.splice(r<o?o-1:o,0,n.object)):(n.object.parent=t.object,t.object.children.splice(o,0,n.object),n.object.dispatchEvent({type:"added"}),t.object.dispatchEvent({type:"childadded",child:n.object}))}else t.object.add(n.object)}for(let t of n.children)ey(n,t);K(n)}}function ew(t,n){n&&(n.parent=t,t.children.push(n),ey(t,n))}function eE(t,n,r){if(!n||!r)return;n.parent=t;let o=t.children.indexOf(r);-1!==o?t.children.splice(o,0,n):t.children.push(n),ey(t,n,r)}function e_(t){if("function"==typeof t.dispose){let n=()=>{try{t.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?n():(0,x.unstable_scheduleCallback)(x.unstable_IdlePriority,n)}}function ex(t,n,r){if(!n)return;n.parent=null;let o=t.children.indexOf(n);-1!==o&&t.children.splice(o,1),n.props.attach?H(t,n):Q(n.object)&&Q(t.object)&&(t.object.remove(n.object),function(t,n){let{internal:r}=t.getState();r.interaction=r.interaction.filter(t=>t!==n),r.initialHits=r.initialHits.filter(t=>t!==n),r.hovered.forEach((t,o)=>{(t.eventObject===n||t.object===n)&&r.hovered.delete(o)}),r.capturedMap.forEach((t,o)=>{ee(r.capturedMap,n,t,o)})}(T(n),n.object));let i=null!==n.props.dispose&&!1!==r;for(let t=n.children.length-1;t>=0;t--){let r=n.children[t];ex(n,r,i)}n.children.length=0,delete n.object.__r3f,i&&"primitive"!==n.type&&"Scene"!==n.object.type&&e_(n.object),void 0===r&&K(n)}let ej=[],eM=()=>{},ek={},eT=0,eP=function(t){let n=_()(t);return n.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:f.version}),n}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(t,n,r){var o;return eb(t=ep(t)in ef?t:t.replace(ed,""),n),"primitive"===t&&null!=(o=n.object)&&o.__r3f&&delete n.object.__r3f,U(n.object,r,t,n)},removeChild:ex,appendChild:ew,appendInitialChild:ew,insertBefore:eE,appendChildToContainer(t,n){let r=t.getState().scene.__r3f;n&&r&&ew(r,n)},removeChildFromContainer(t,n){let r=t.getState().scene.__r3f;n&&r&&ex(r,n)},insertInContainerBefore(t,n,r){let o=t.getState().scene.__r3f;n&&r&&o&&eE(o,n,r)},getRootHostContext:()=>ek,getChildHostContext:()=>ek,commitUpdate(t,n,r,o,i){var a,l,s;eb(n,o);let c=!1;if("primitive"===t.type&&r.object!==o.object||(null==(a=o.args)?void 0:a.length)!==(null==(l=r.args)?void 0:l.length)?c=!0:null!=(s=o.args)&&s.some((t,n)=>{var o;return t!==(null==(o=r.args)?void 0:o[n])})&&(c=!0),c)ej.push([t,{...o},i]);else{let n=function(t,n){let r={};for(let o in n)if(!W.includes(o)&&!z.equ(n[o],t.props[o]))for(let t in r[o]=n[o],n)t.startsWith(`${o}-`)&&(r[t]=n[t]);for(let o in t.props){if(W.includes(o)||n.hasOwnProperty(o))continue;let{root:i,key:a}=B(t.object,o);if(i.constructor&&0===i.constructor.length){let t=function(t){let n=G.get(t.constructor);try{n||(n=new t.constructor,G.set(t.constructor,n))}catch(t){}return n}(i);z.und(t)||(r[a]=t[a])}else r[a]=0}return r}(t,o);Object.keys(n).length&&(Object.assign(t.props,n),$(t.object,n))}(null===i.sibling||(4&i.flags)==0)&&function(){for(let[t]of ej){let n=t.parent;if(n)for(let r of(t.props.attach?H(n,t):Q(t.object)&&Q(n.object)&&n.object.remove(t.object),t.children))r.props.attach?H(t,r):Q(r.object)&&Q(t.object)&&t.object.remove(r.object);t.isHidden&&eg(t),t.object.__r3f&&delete t.object.__r3f,"primitive"!==t.type&&e_(t.object)}for(let[o,i,a]of ej){o.props=i;let l=o.parent;if(l){let i=ef[ep(o.type)];o.object=null!=(t=o.props.object)?t:new i(...null!=(n=o.props.args)?n:[]),o.object.__r3f=o;var t,n,r=o.object;for(let t of[a,a.alternate])if(null!==t)if("function"==typeof t.ref){null==t.refCleanup||t.refCleanup();let n=t.ref(r);"function"==typeof n&&(t.refCleanup=n)}else t.ref&&(t.ref.current=r);for(let t of($(o.object,o.props),o.props.attach?Y(l,o):Q(o.object)&&Q(l.object)&&l.object.add(o.object),o.children))t.props.attach?Y(o,t):Q(t.object)&&Q(o.object)&&o.object.add(t.object);K(o)}}ej.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:t=>null==t?void 0:t.object,prepareForCommit:()=>null,preparePortalMount:t=>U(t.getState().scene,t,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(t){if(!t.isHidden){var n;t.props.attach&&null!=(n=t.parent)&&n.object?H(t.parent,t):Q(t.object)&&(t.object.visible=!1),t.isHidden=!0,K(t)}},unhideInstance:eg,createTextInstance:eM,hideTextInstance:eM,unhideTextInstance:eM,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:f.createContext(null),setCurrentUpdatePriority(t){eT=t},getCurrentUpdatePriority:()=>eT,resolveUpdatePriority(){var t;if(0!==eT)return eT;switch("undefined"!=typeof window&&(null==(t=window.event)?void 0:t.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return p.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return p.ContinuousEventPriority;default:return p.DefaultEventPriority}},resetFormInstance(){}}),eA=new Map,eC={objects:"shallow",strict:!1};function eS(t){let n,r,o=eA.get(t),i=null==o?void 0:o.fiber,a=null==o?void 0:o.store;o&&console.warn("R3F.createRoot should only be called once!");let l="function"==typeof reportError?reportError:console.error,s=a||er(eY,eH),f=i||eP.createContainer(s,p.ConcurrentRoot,null,!1,null,"",l,l,l,null);o||eA.set(t,{fiber:f,store:s});let d=!1,v=null;return{async configure(o={}){var i,a;let l;v=new Promise(t=>l=t);let{gl:f,size:p,scene:h,events:m,onCreated:b,shadows:g=!1,linear:y=!1,flat:w=!1,legacy:E=!1,orthographic:_=!1,frameloop:x="always",dpr:j=[1,2],performance:M,raycaster:k,camera:T,onPointerMissed:P}=o,A=s.getState(),C=A.gl;if(!A.gl){let n={canvas:t,powerPreference:"high-performance",antialias:!0,alpha:!0},r="function"==typeof f?await f(n):f;C=et(r)?r:new u.WebGLRenderer({...n,...f}),A.set({gl:C})}let S=A.raycaster;S||A.set({raycaster:S=new c.tBo});let{params:I,...O}=k||{};if(z.equ(O,S,eC)||$(S,{...O}),z.equ(I,S.params,eC)||$(S,{params:{...S.params,...I}}),!A.camera||A.camera===r&&!z.equ(r,T,eC)){r=T;let t=null==T?void 0:T.isCamera,n=t?T:_?new c.qUd(0,0,0,0,.1,1e3):new c.ubm(75,0,.1,1e3);!t&&(n.position.z=5,T&&($(n,T),!n.manual&&("aspect"in T||"left"in T||"right"in T||"bottom"in T||"top"in T)&&(n.manual=!0,n.updateProjectionMatrix())),A.camera||null!=T&&T.rotation||n.lookAt(0,0,0)),A.set({camera:n}),S.camera=n}if(!A.scene){let t;null!=h&&h.isScene?U(t=h,s,"",{}):(U(t=new c.Z58,s,"",{}),h&&$(t,h)),A.set({scene:t})}m&&!A.events.handlers&&A.set({events:m(s)});let L=function(t,n){if(!n&&"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement&&t.parentElement){let{width:n,height:r,top:o,left:i}=t.parentElement.getBoundingClientRect();return{width:n,height:r,top:o,left:i}}return!n&&"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?{width:t.width,height:t.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...n}}(t,p);if(z.equ(L,A.size,eC)||A.setSize(L.width,L.height,L.top,L.left),j&&A.viewport.dpr!==D(j)&&A.setDpr(j),A.frameloop!==x&&A.setFrameloop(x),A.onPointerMissed||A.set({onPointerMissed:P}),M&&!z.equ(M,A.performance,eC)&&A.set(t=>({performance:{...t.performance,...M}})),!A.xr){let t=(t,n)=>{let r=s.getState();"never"!==r.frameloop&&eH(t,!0,r,n)},n=()=>{let n=s.getState();n.gl.xr.enabled=n.gl.xr.isPresenting,n.gl.xr.setAnimationLoop(n.gl.xr.isPresenting?t:null),n.gl.xr.isPresenting||eY(n)},r={connect(){let t=s.getState().gl;t.xr.addEventListener("sessionstart",n),t.xr.addEventListener("sessionend",n)},disconnect(){let t=s.getState().gl;t.xr.removeEventListener("sessionstart",n),t.xr.removeEventListener("sessionend",n)}};"function"==typeof(null==(i=C.xr)?void 0:i.addEventListener)&&r.connect(),A.set({xr:r})}if(C.shadowMap){let t=C.shadowMap.enabled,n=C.shadowMap.type;if(C.shadowMap.enabled=!!g,z.boo(g))C.shadowMap.type=c.Wk7;else if(z.str(g)){let t={basic:c.bTm,percentage:c.QP0,soft:c.Wk7,variance:c.RyA};C.shadowMap.type=null!=(a=t[g])?a:c.Wk7}else z.obj(g)&&Object.assign(C.shadowMap,g);(t!==C.shadowMap.enabled||n!==C.shadowMap.type)&&(C.shadowMap.needsUpdate=!0)}return c.ppV.enabled=!E,d||(C.outputColorSpace=y?c.Zr2:c.er$,C.toneMapping=w?c.y_p:c.FV),A.legacy!==E&&A.set(()=>({legacy:E})),A.linear!==y&&A.set(()=>({linear:y})),A.flat!==w&&A.set(()=>({flat:w})),!f||z.fun(f)||et(f)||z.equ(f,C,eC)||$(C,f),n=b,d=!0,l(),this},render(r){return d||v||this.configure(),v.then(()=>{eP.updateContainer((0,M.jsx)(eI,{store:s,children:r,onCreated:n,rootElement:t}),f,null,()=>void 0)}),s},unmount(){eO(t)}}}function eI({store:t,children:n,onCreated:r,rootElement:o}){return S(()=>{let n=t.getState();n.set(t=>({internal:{...t.internal,active:!0}})),r&&r(n),t.getState().events.connected||null==n.events.connect||n.events.connect(o)},[]),(0,M.jsx)(en.Provider,{value:t,children:n})}function eO(t,n){let r=eA.get(t),o=null==r?void 0:r.fiber;if(o){let i=null==r?void 0:r.store.getState();i&&(i.internal.active=!1),eP.updateContainer(null,o,null,()=>{i&&setTimeout(()=>{try{null==i.events.disconnect||i.events.disconnect(),null==(r=i.gl)||null==(o=r.renderLists)||null==o.dispose||o.dispose(),null==(a=i.gl)||null==a.forceContextLoss||a.forceContextLoss(),null!=(l=i.gl)&&l.xr&&i.xr.disconnect();var r,o,a,l,s=i.scene;for(let t in"Scene"!==s.type&&(null==s.dispose||s.dispose()),s){let n=s[t];(null==n?void 0:n.type)!=="Scene"&&(null==n||null==n.dispose||n.dispose())}eA.delete(t),n&&n(t)}catch(t){}},500)})}}let eL=new Set,eR=new Set,eD=new Set;function eN(t,n){if(t.size)for(let{callback:r}of t.values())r(n)}function ez(t,n){switch(t){case"before":return eN(eL,n);case"after":return eN(eR,n);case"tail":return eN(eD,n)}}function eF(t,n,r){let a=n.clock.getDelta();"never"===n.frameloop&&"number"==typeof t&&(a=t-n.clock.elapsedTime,n.clock.oldTime=n.clock.elapsedTime,n.clock.elapsedTime=t),o=n.internal.subscribers;for(let t=0;t<o.length;t++)(i=o[t]).ref.current(i.store.getState(),a,r);return!n.internal.priority&&n.gl.render&&n.gl.render(n.scene,n.camera),n.internal.frames=Math.max(0,n.internal.frames-1),"always"===n.frameloop?1:n.internal.frames}let eU=!1,eB=!1;function eq(t){for(let r of(l=requestAnimationFrame(eq),eU=!0,a=0,ez("before",t),eB=!0,eA.values())){var n;(s=r.store.getState()).internal.active&&("always"===s.frameloop||s.internal.frames>0)&&!(null!=(n=s.gl.xr)&&n.isPresenting)&&(a+=eF(t,s))}if(eB=!1,ez("after",t),0===a)return ez("tail",t),eU=!1,cancelAnimationFrame(l)}function eY(t,n=1){var r;if(!t)return eA.forEach(t=>eY(t.store.getState(),n));(null==(r=t.gl.xr)||!r.isPresenting)&&t.internal.active&&"never"!==t.frameloop&&(n>1?t.internal.frames=Math.min(60,t.internal.frames+n):eB?t.internal.frames=2:t.internal.frames=1,eU||(eU=!0,requestAnimationFrame(eq)))}function eH(t,n=!0,r,o){if(n&&ez("before",t),r)eF(t,r,o);else for(let n of eA.values())eF(t,n.store.getState());n&&ez("after",t)}let eW={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eG(t){let{handlePointer:n}=function(t){function n(t){return t.filter(t=>["Move","Over","Enter","Out","Leave"].some(n=>{var r;return null==(r=t.__r3f)?void 0:r.handlers["onPointer"+n]}))}function r(n){let{internal:r}=t.getState();for(let t of r.hovered.values())if(!n.length||!n.find(n=>n.object===t.object&&n.index===t.index&&n.instanceId===t.instanceId)){let o=t.eventObject.__r3f;if(r.hovered.delete(J(t)),null!=o&&o.eventCount){let r=o.handlers,i={...t,intersections:n};null==r.onPointerOut||r.onPointerOut(i),null==r.onPointerLeave||r.onPointerLeave(i)}}}function o(t,n){for(let r=0;r<n.length;r++){let o=n[r].__r3f;null==o||null==o.handlers.onPointerMissed||o.handlers.onPointerMissed(t)}}return{handlePointer:function(i){switch(i){case"onPointerLeave":case"onPointerCancel":return()=>r([]);case"onLostPointerCapture":return n=>{let{internal:o}=t.getState();"pointerId"in n&&o.capturedMap.has(n.pointerId)&&requestAnimationFrame(()=>{o.capturedMap.has(n.pointerId)&&(o.capturedMap.delete(n.pointerId),r([]))})}}return function(a){let{onPointerMissed:l,internal:s}=t.getState();s.lastEvent.current=a;let u="onPointerMove"===i,f="onClick"===i||"onContextMenu"===i||"onDoubleClick"===i,d=function(n,r){let o=t.getState(),i=new Set,a=[],l=r?r(o.internal.interaction):o.internal.interaction;for(let t=0;t<l.length;t++){let n=N(l[t]);n&&(n.raycaster.camera=void 0)}o.previousRoot||null==o.events.compute||o.events.compute(n,o);let s=l.flatMap(function(t){let r=N(t);if(!r||!r.events.enabled||null===r.raycaster.camera)return[];if(void 0===r.raycaster.camera){var o;null==r.events.compute||r.events.compute(n,r,null==(o=r.previousRoot)?void 0:o.getState()),void 0===r.raycaster.camera&&(r.raycaster.camera=null)}return r.raycaster.camera?r.raycaster.intersectObject(t,!0):[]}).sort((t,n)=>{let r=N(t.object),o=N(n.object);return r&&o&&o.events.priority-r.events.priority||t.distance-n.distance}).filter(t=>{let n=J(t);return!i.has(n)&&(i.add(n),!0)});for(let t of(o.events.filter&&(s=o.events.filter(s,o)),s)){let n=t.object;for(;n;){var c;null!=(c=n.__r3f)&&c.eventCount&&a.push({...t,eventObject:n}),n=n.parent}}if("pointerId"in n&&o.internal.capturedMap.has(n.pointerId))for(let t of o.internal.capturedMap.get(n.pointerId).values())i.has(J(t.intersection))||a.push(t.intersection);return a}(a,u?n:void 0),p=f?function(n){let{internal:r}=t.getState(),o=n.offsetX-r.initialClick[0],i=n.offsetY-r.initialClick[1];return Math.round(Math.sqrt(o*o+i*i))}(a):0;"onPointerDown"===i&&(s.initialClick=[a.offsetX,a.offsetY],s.initialHits=d.map(t=>t.eventObject)),f&&!d.length&&p<=2&&(o(a,s.interaction),l&&l(a)),u&&r(d),!function(t,n,o,i){if(t.length){let a={stopped:!1};for(let l of t){let s=N(l.object);if(s||l.object.traverseAncestors(t=>{let n=N(t);if(n)return s=n,!1}),s){let{raycaster:u,pointer:f,camera:d,internal:p}=s,v=new c.Pq0(f.x,f.y,0).unproject(d),h=t=>{var n,r;return null!=(n=null==(r=p.capturedMap.get(t))?void 0:r.has(l.eventObject))&&n},m=t=>{let r={intersection:l,target:n.target};p.capturedMap.has(t)?p.capturedMap.get(t).set(l.eventObject,r):p.capturedMap.set(t,new Map([[l.eventObject,r]])),n.target.setPointerCapture(t)},b=t=>{let n=p.capturedMap.get(t);n&&ee(p.capturedMap,l.eventObject,n,t)},g={};for(let t in n){let r=n[t];"function"!=typeof r&&(g[t]=r)}let y={...l,...g,pointer:f,intersections:t,stopped:a.stopped,delta:o,unprojectedPoint:v,ray:u.ray,camera:d,stopPropagation(){let o="pointerId"in n&&p.capturedMap.get(n.pointerId);(!o||o.has(l.eventObject))&&(y.stopped=a.stopped=!0,p.hovered.size&&Array.from(p.hovered.values()).find(t=>t.eventObject===l.eventObject)&&r([...t.slice(0,t.indexOf(l)),l]))},target:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:b},currentTarget:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:b},nativeEvent:n};if(i(y),!0===a.stopped)break}}}}(d,a,p,function(t){let n=t.eventObject,r=n.__r3f;if(!(null!=r&&r.eventCount))return;let l=r.handlers;if(u){if(l.onPointerOver||l.onPointerEnter||l.onPointerOut||l.onPointerLeave){let n=J(t),r=s.hovered.get(n);r?r.stopped&&t.stopPropagation():(s.hovered.set(n,t),null==l.onPointerOver||l.onPointerOver(t),null==l.onPointerEnter||l.onPointerEnter(t))}null==l.onPointerMove||l.onPointerMove(t)}else{let r=l[i];r?(!f||s.initialHits.includes(n))&&(o(a,s.interaction.filter(t=>!s.initialHits.includes(t))),r(t)):f&&s.initialHits.includes(n)&&o(a,s.interaction.filter(t=>!s.initialHits.includes(t)))}})}}}}(t);return{priority:1,enabled:!0,compute(t,n,r){n.pointer.set(t.offsetX/n.size.width*2-1,-(2*(t.offsetY/n.size.height))+1),n.raycaster.setFromCamera(n.pointer,n.camera)},connected:void 0,handlers:Object.keys(eW).reduce((t,r)=>({...t,[r]:n(r)}),{}),update:()=>{var n;let{events:r,internal:o}=t.getState();null!=(n=o.lastEvent)&&n.current&&r.handlers&&r.handlers.onPointerMove(o.lastEvent.current)},connect:n=>{let{set:r,events:o}=t.getState();if(null==o.disconnect||o.disconnect(),r(t=>({events:{...t.events,connected:n}})),o.handlers)for(let t in o.handlers){let r=o.handlers[t],[i,a]=eW[t];n.addEventListener(i,r,{passive:a})}},disconnect:()=>{let{set:n,events:r}=t.getState();if(r.connected){if(r.handlers)for(let t in r.handlers){let n=r.handlers[t],[o]=eW[t];r.connected.removeEventListener(o,n)}n(t=>({events:{...t.events,connected:void 0}}))}}}}},620:(t,n)=>{function r(t,n){var r=t.length;for(t.push(n);0<r;){var o=r-1>>>1,i=t[o];if(0<a(i,n))t[o]=n,t[r]=i,r=o;else break}}function o(t){return 0===t.length?null:t[0]}function i(t){if(0===t.length)return null;var n=t[0],r=t.pop();if(r!==n){t[0]=r;for(var o=0,i=t.length,l=i>>>1;o<l;){var s=2*(o+1)-1,c=t[s],u=s+1,f=t[u];if(0>a(c,r))u<i&&0>a(f,c)?(t[o]=f,t[u]=r,o=u):(t[o]=c,t[s]=r,o=s);else if(u<i&&0>a(f,r))t[o]=f,t[u]=r,o=u;else break}}return n}function a(t,n){var r=t.sortIndex-n.sortIndex;return 0!==r?r:t.id-n.id}if(n.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l,s=performance;n.unstable_now=function(){return s.now()}}else{var c=Date,u=c.now();n.unstable_now=function(){return c.now()-u}}var f=[],d=[],p=1,v=null,h=3,m=!1,b=!1,g=!1,y="function"==typeof setTimeout?setTimeout:null,w="function"==typeof clearTimeout?clearTimeout:null,E="undefined"!=typeof setImmediate?setImmediate:null;function _(t){for(var n=o(d);null!==n;){if(null===n.callback)i(d);else if(n.startTime<=t)i(d),n.sortIndex=n.expirationTime,r(f,n);else break;n=o(d)}}function x(t){if(g=!1,_(t),!b)if(null!==o(f))b=!0,I();else{var n=o(d);null!==n&&O(x,n.startTime-t)}}var j=!1,M=-1,k=5,T=-1;function P(){return!(n.unstable_now()-T<k)}function A(){if(j){var t=n.unstable_now();T=t;var r=!0;try{e:{b=!1,g&&(g=!1,w(M),M=-1),m=!0;var a=h;try{t:{for(_(t),v=o(f);null!==v&&!(v.expirationTime>t&&P());){var s=v.callback;if("function"==typeof s){v.callback=null,h=v.priorityLevel;var c=s(v.expirationTime<=t);if(t=n.unstable_now(),"function"==typeof c){v.callback=c,_(t),r=!0;break t}v===o(f)&&i(f),_(t)}else i(f);v=o(f)}if(null!==v)r=!0;else{var u=o(d);null!==u&&O(x,u.startTime-t),r=!1}}break e}finally{v=null,h=a,m=!1}}}finally{r?l():j=!1}}}if("function"==typeof E)l=function(){E(A)};else if("undefined"!=typeof MessageChannel){var C=new MessageChannel,S=C.port2;C.port1.onmessage=A,l=function(){S.postMessage(null)}}else l=function(){y(A,0)};function I(){j||(j=!0,l())}function O(t,r){M=y(function(){t(n.unstable_now())},r)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(t){t.callback=null},n.unstable_continueExecution=function(){b||m||(b=!0,I())},n.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):k=0<t?Math.floor(1e3/t):5},n.unstable_getCurrentPriorityLevel=function(){return h},n.unstable_getFirstCallbackNode=function(){return o(f)},n.unstable_next=function(t){switch(h){case 1:case 2:case 3:var n=3;break;default:n=h}var r=h;h=n;try{return t()}finally{h=r}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(t,n){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var r=h;h=t;try{return n()}finally{h=r}},n.unstable_scheduleCallback=function(t,i,a){var l=n.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?l+a:l,t){case 1:var s=-1;break;case 2:s=250;break;case 5:s=0x3fffffff;break;case 4:s=1e4;break;default:s=5e3}return s=a+s,t={id:p++,callback:i,priorityLevel:t,startTime:a,expirationTime:s,sortIndex:-1},a>l?(t.sortIndex=a,r(d,t),null===o(f)&&t===o(d)&&(g?(w(M),M=-1):g=!0,O(x,a-l))):(t.sortIndex=s,r(f,t),b||m||(b=!0,I())),t},n.unstable_shouldYield=P,n.unstable_wrapCallback=function(t){var n=h;return function(){var r=h;h=n;try{return t.apply(this,arguments)}finally{h=r}}}},1933:(t,n,r)=>{t.exports=r(6500)},2436:(t,n,r)=>{var o=r(2115),i="function"==typeof Object.is?Object.is:function(t,n){return t===n&&(0!==t||1/t==1/n)||t!=t&&n!=n},a=o.useState,l=o.useEffect,s=o.useLayoutEffect,c=o.useDebugValue;function u(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!i(t,r)}catch(t){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,n){return n()}:function(t,n){var r=n(),o=a({inst:{value:r,getSnapshot:n}}),i=o[0].inst,f=o[1];return s(function(){i.value=r,i.getSnapshot=n,u(i)&&f({inst:i})},[t,r,n]),l(function(){return u(i)&&f({inst:i}),t(function(){u(i)&&f({inst:i})})},[t]),c(r),r};n.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:f},3064:(t,n,r)=>{r.d(n,{Do:()=>a,Fh:()=>v});var o=r(7431),i=r(3264);let a=/\bvoid\s+main\s*\(\s*\)\s*{/g;function l(t){return t.replace(/^[ \t]*#include +<([\w\d./]+)>/gm,function(t,n){let r=o.ShaderChunk[n];return r?l(r):t})}let s=[];for(let t=0;t<256;t++)s[t]=(t<16?"0":"")+t.toString(16);let c=Object.assign||function(){let t=arguments[0];for(let n=1,r=arguments.length;n<r;n++){let r=arguments[n];if(r)for(let n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u=Date.now(),f=new WeakMap,d=new Map,p=1e10;function v(t,n){let r=function(t){let n=JSON.stringify(t,m),r=g.get(n);return null==r&&g.set(n,r=++b),r}(n),o=f.get(t);if(o||f.set(t,o=Object.create(null)),o[r])return new o[r];let a=`_onBeforeCompile${r}`,y=function(o,i){t.onBeforeCompile.call(this,o,i);let s=this.customProgramCacheKey()+"|"+o.vertexShader+"|"+o.fragmentShader,f=d[s];if(!f){let t=function(t,{vertexShader:n,fragmentShader:r},o,i){let{vertexDefs:a,vertexMainIntro:s,vertexMainOutro:c,vertexTransform:u,fragmentDefs:f,fragmentMainIntro:d,fragmentMainOutro:p,fragmentColorTransform:v,customRewriter:m,timeUniform:b}=o;if(a=a||"",s=s||"",c=c||"",f=f||"",d=d||"",p=p||"",(u||m)&&(n=l(n)),(v||m)&&(r=l(r=r.replace(/^[ \t]*#include <((?:tonemapping|encodings|colorspace|fog|premultiplied_alpha|dithering)_fragment)>/gm,"\n//!BEGIN_POST_CHUNK $1\n$&\n//!END_POST_CHUNK\n"))),m){let t=m({vertexShader:n,fragmentShader:r});n=t.vertexShader,r=t.fragmentShader}if(v){let t=[];r=r.replace(/^\/\/!BEGIN_POST_CHUNK[^]+?^\/\/!END_POST_CHUNK/gm,n=>(t.push(n),"")),p=`${v}
${t.join("\n")}
${p}`}if(b){let t=`
uniform float ${b};
`;a=t+a,f=t+f}return u&&(n=`vec3 troika_position_${i};
vec3 troika_normal_${i};
vec2 troika_uv_${i};
${n}
`,a=`${a}
void troikaVertexTransform${i}(inout vec3 position, inout vec3 normal, inout vec2 uv) {
  ${u}
}
`,s=`
troika_position_${i} = vec3(position);
troika_normal_${i} = vec3(normal);
troika_uv_${i} = vec2(uv);
troikaVertexTransform${i}(troika_position_${i}, troika_normal_${i}, troika_uv_${i});
${s}
`,n=n.replace(/\b(position|normal|uv)\b/g,(t,n,r,o)=>/\battribute\s+vec[23]\s+$/.test(o.substr(0,r))?n:`troika_${n}_${i}`),t.map&&t.map.channel>0||(n=n.replace(/\bMAP_UV\b/g,`troika_uv_${i}`))),{vertexShader:n=h(n,i,a,s,c),fragmentShader:r=h(r,i,f,d,p)}}(this,o,n,r);f=d[s]=t}o.vertexShader=f.vertexShader,o.fragmentShader=f.fragmentShader,c(o.uniforms,this.uniforms),n.timeUniform&&(o.uniforms[n.timeUniform]={get value(){return Date.now()-u}}),this[a]&&this[a](o)},w=function(){return E(n.chained?t:t.clone())},E=function(o){let i=Object.create(o,_);return Object.defineProperty(i,"baseMaterial",{value:t}),Object.defineProperty(i,"id",{value:p++}),i.uuid=function(){let t=0xffffffff*Math.random()|0,n=0xffffffff*Math.random()|0,r=0xffffffff*Math.random()|0,o=0xffffffff*Math.random()|0;return(s[255&t]+s[t>>8&255]+s[t>>16&255]+s[t>>24&255]+"-"+s[255&n]+s[n>>8&255]+"-"+s[n>>16&15|64]+s[n>>24&255]+"-"+s[63&r|128]+s[r>>8&255]+"-"+s[r>>16&255]+s[r>>24&255]+s[255&o]+s[o>>8&255]+s[o>>16&255]+s[o>>24&255]).toUpperCase()}(),i.uniforms=c({},o.uniforms,n.uniforms),i.defines=c({},o.defines,n.defines),i.defines[`TROIKA_DERIVED_MATERIAL_${r}`]="",i.extensions=c({},o.extensions,n.extensions),i._listeners=void 0,i},_={constructor:{value:w},isDerivedMaterial:{value:!0},type:{get:()=>t.type,set:n=>{t.type=n}},isDerivedFrom:{writable:!0,configurable:!0,value:function(t){let n=this.baseMaterial;return t===n||n.isDerivedMaterial&&n.isDerivedFrom(t)||!1}},customProgramCacheKey:{writable:!0,configurable:!0,value:function(){return t.customProgramCacheKey()+"|"+r}},onBeforeCompile:{get:()=>y,set(t){this[a]=t}},copy:{writable:!0,configurable:!0,value:function(n){return t.copy.call(this,n),t.isShaderMaterial||t.isDerivedMaterial||(c(this.extensions,n.extensions),c(this.defines,n.defines),c(this.uniforms,i.LlO.clone(n.uniforms))),this}},clone:{writable:!0,configurable:!0,value:function(){return E(new t.constructor).copy(this)}},getDepthMaterial:{writable:!0,configurable:!0,value:function(){let r=this._depthMaterial;return r||((r=this._depthMaterial=v(t.isDerivedMaterial?t.getDepthMaterial():new i.CSG({depthPacking:i.N5j}),n)).defines.IS_DEPTH_MATERIAL="",r.uniforms=this.uniforms),r}},getDistanceMaterial:{writable:!0,configurable:!0,value:function(){let r=this._distanceMaterial;return r||((r=this._distanceMaterial=v(t.isDerivedMaterial?t.getDistanceMaterial():new i.aVO,n)).defines.IS_DISTANCE_MATERIAL="",r.uniforms=this.uniforms),r}},dispose:{writable:!0,configurable:!0,value(){let{_depthMaterial:n,_distanceMaterial:r}=this;n&&n.dispose(),r&&r.dispose(),t.dispose.call(this)}}};return o[r]=w,new w}function h(t,n,r,o,i){return(o||i||r)&&(t=t.replace(a,`
${r}
void troikaOrigMain${n}() {`)+`
void main() {
  ${o}
  troikaOrigMain${n}();
  ${i}
}`),t}function m(t,n){return"uniforms"===t?void 0:"function"==typeof n?n.toString():n}let b=0,g=new Map,y=`
uniform vec3 pointA;
uniform vec3 controlA;
uniform vec3 controlB;
uniform vec3 pointB;
uniform float radius;
varying float bezierT;

vec3 cubicBezier(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {
  float t2 = 1.0 - t;
  float b0 = t2 * t2 * t2;
  float b1 = 3.0 * t * t2 * t2;
  float b2 = 3.0 * t * t * t2;
  float b3 = t * t * t;
  return b0 * p1 + b1 * c1 + b2 * c2 + b3 * p2;
}

vec3 cubicBezierDerivative(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {
  float t2 = 1.0 - t;
  return -3.0 * p1 * t2 * t2 +
    c1 * (3.0 * t2 * t2 - 6.0 * t2 * t) +
    c2 * (6.0 * t2 * t - 3.0 * t * t) +
    3.0 * p2 * t * t;
}
`,w=`
float t = position.y;
bezierT = t;
vec3 bezierCenterPos = cubicBezier(pointA, controlA, controlB, pointB, t);
vec3 bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t));

// Make "sideways" always perpendicular to the camera ray; this ensures that any twists
// in the cylinder occur where you won't see them: 
vec3 viewDirection = normalMatrix * vec3(0.0, 0.0, 1.0);
if (bezierDir == viewDirection) {
  bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t == 1.0 ? t - 0.0001 : t + 0.0001));
}
vec3 sideways = normalize(cross(bezierDir, viewDirection));
vec3 upish = normalize(cross(sideways, bezierDir));

// Build a matrix for transforming this disc in the cylinder:
mat4 discTx;
discTx[0].xyz = sideways * radius;
discTx[1].xyz = bezierDir * radius;
discTx[2].xyz = upish * radius;
discTx[3].xyz = bezierCenterPos;
discTx[3][3] = 1.0;

// Apply transform, ignoring original y
position = (discTx * vec4(position.x, 0.0, position.z, 1.0)).xyz;
normal = normalize(mat3(discTx) * normal);
`,E=`
uniform vec3 dashing;
varying float bezierT;
`,_=`
if (dashing.x + dashing.y > 0.0) {
  float dashFrac = mod(bezierT - dashing.z, dashing.x + dashing.y);
  if (dashFrac > dashing.x) {
    discard;
  }
}
`,x=null,j=new i._4j({color:0xffffff,side:i.$EB});class M extends i.eaF{static getGeometry(){return x||(x=new i.Ho_(1,1,1,6,64).translate(0,.5,0))}constructor(){super(M.getGeometry(),j),this.pointA=new i.Pq0,this.controlA=new i.Pq0,this.controlB=new i.Pq0,this.pointB=new i.Pq0,this.radius=.01,this.dashArray=new i.I9Y,this.dashOffset=0,this.frustumCulled=!1}get material(){let t=this._derivedMaterial,n=this._baseMaterial||this._defaultMaterial||(this._defaultMaterial=j.clone());return t&&t.baseMaterial===n||(t=this._derivedMaterial=v(n,{chained:!0,uniforms:{pointA:{value:new i.Pq0},controlA:{value:new i.Pq0},controlB:{value:new i.Pq0},pointB:{value:new i.Pq0},radius:{value:.01},dashing:{value:new i.Pq0}},vertexDefs:y,vertexTransform:w,fragmentDefs:E,fragmentMainIntro:_}),n.addEventListener("dispose",function r(){n.removeEventListener("dispose",r),t.dispose()})),t}set material(t){this._baseMaterial=t}get customDepthMaterial(){return this.material.getDepthMaterial()}set customDepthMaterial(t){}get customDistanceMaterial(){return this.material.getDistanceMaterial()}set customDistanceMaterial(t){}onBeforeRender(){let{uniforms:t}=this.material,{pointA:n,controlA:r,controlB:o,pointB:i,radius:a,dashArray:l,dashOffset:s}=this;t.pointA.value.copy(n),t.controlA.value.copy(r),t.controlB.value.copy(o),t.pointB.value.copy(i),t.radius.value=a,t.dashing.value.set(l.x,l.y,s||0)}raycast(){}}},4253:(t,n,r)=>{r.d(n,{A:()=>o});function o(){return function(t){function n(t,n){for(var r,o,i,a,l,s=/([MLQCZ])([^MLQCZ]*)/g;r=s.exec(t);){var c=r[2].replace(/^\s*|\s*$/g,"").split(/[,\s]+/).map(function(t){return parseFloat(t)});switch(r[1]){case"M":a=o=c[0],l=i=c[1];break;case"L":(c[0]!==a||c[1]!==l)&&n("L",a,l,a=c[0],l=c[1]);break;case"Q":n("Q",a,l,a=c[2],l=c[3],c[0],c[1]);break;case"C":n("C",a,l,a=c[4],l=c[5],c[0],c[1],c[2],c[3]);break;case"Z":(a!==o||l!==i)&&n("L",a,l,o,i)}}}function r(t,r,o){void 0===o&&(o=16);var i={x:0,y:0};n(t,function(t,n,a,l,s,c,u,f,d){switch(t){case"L":r(n,a,l,s);break;case"Q":for(var p=n,v=a,h=1;h<o;h++)!function(t,n,r,o,i,a,l,s){var c=1-l;s.x=c*c*t+2*c*l*r+l*l*i,s.y=c*c*n+2*c*l*o+l*l*a}(n,a,c,u,l,s,h/(o-1),i),r(p,v,i.x,i.y),p=i.x,v=i.y;break;case"C":for(var m=n,b=a,g=1;g<o;g++)!function(t,n,r,o,i,a,l,s,c,u){var f=1-c;u.x=f*f*f*t+3*f*f*c*r+3*f*c*c*i+c*c*c*l,u.y=f*f*f*n+3*f*f*c*o+3*f*c*c*a+c*c*c*s}(n,a,c,u,f,d,l,s,g/(o-1),i),r(m,b,i.x,i.y),m=i.x,b=i.y}})}var o="precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}",i=new WeakMap,a={premultipliedAlpha:!1,preserveDrawingBuffer:!0,antialias:!1,depth:!1};function l(t,n){var r=t.getContext?t.getContext("webgl",a):t,o=i.get(r);if(!o){var l="undefined"!=typeof WebGL2RenderingContext&&r instanceof WebGL2RenderingContext,s={},c={},u={},f=-1,d=[];function p(t){var n=s[t];if(!n&&!(n=s[t]=r.getExtension(t)))throw Error(t+" not supported");return n}function v(t,n){var o=r.createShader(n);return r.shaderSource(o,t),r.compileShader(o),o}function h(){s={},c={},u={},f=-1,d.length=0}r.canvas.addEventListener("webglcontextlost",function(t){h(),t.preventDefault()},!1),i.set(r,o={gl:r,isWebGL2:l,getExtension:p,withProgram:function(t,n,o,i){if(!c[t]){var a={},s={},u=r.createProgram();r.attachShader(u,v(n,r.VERTEX_SHADER)),r.attachShader(u,v(o,r.FRAGMENT_SHADER)),r.linkProgram(u),c[t]={program:u,transaction:function(t){r.useProgram(u),t({setUniform:function(t,n){for(var o=[],i=arguments.length-2;i-- >0;)o[i]=arguments[i+2];var a=s[n]||(s[n]=r.getUniformLocation(u,n));r["uniform"+t].apply(r,[a].concat(o))},setAttribute:function(t,n,o,i,s){var c=a[t];c||(c=a[t]={buf:r.createBuffer(),loc:r.getAttribLocation(u,t),data:null}),r.bindBuffer(r.ARRAY_BUFFER,c.buf),r.vertexAttribPointer(c.loc,n,r.FLOAT,!1,0,0),r.enableVertexAttribArray(c.loc),l?r.vertexAttribDivisor(c.loc,i):p("ANGLE_instanced_arrays").vertexAttribDivisorANGLE(c.loc,i),s!==c.data&&(r.bufferData(r.ARRAY_BUFFER,s,o),c.data=s)}})}}}c[t].transaction(i)},withTexture:function(t,n){f++;try{r.activeTexture(r.TEXTURE0+f);var o=u[t];o||(o=u[t]=r.createTexture(),r.bindTexture(r.TEXTURE_2D,o),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.NEAREST),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,r.NEAREST)),r.bindTexture(r.TEXTURE_2D,o),n(o,f)}finally{f--}},withTextureFramebuffer:function(t,n,o){var i=r.createFramebuffer();d.push(i),r.bindFramebuffer(r.FRAMEBUFFER,i),r.activeTexture(r.TEXTURE0+n),r.bindTexture(r.TEXTURE_2D,t),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,t,0);try{o(i)}finally{r.deleteFramebuffer(i),r.bindFramebuffer(r.FRAMEBUFFER,d[--d.length-1]||null)}},handleContextLoss:h})}n(o)}function s(t,n,r,i,a,s,c,u){void 0===c&&(c=15),void 0===u&&(u=null),l(t,function(t){var l=t.gl,f=t.withProgram;(0,t.withTexture)("copy",function(t,d){l.texImage2D(l.TEXTURE_2D,0,l.RGBA,a,s,0,l.RGBA,l.UNSIGNED_BYTE,n),f("copy",o,"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}",function(t){var n=t.setUniform;(0,t.setAttribute)("aUV",2,l.STATIC_DRAW,0,new Float32Array([0,0,2,0,0,2])),n("1i","image",d),l.bindFramebuffer(l.FRAMEBUFFER,u||null),l.disable(l.BLEND),l.colorMask(8&c,4&c,2&c,1&c),l.viewport(r,i,a,s),l.scissor(r,i,a,s),l.drawArrays(l.TRIANGLES,0,3)})})})}var c=Object.freeze({__proto__:null,withWebGLContext:l,renderImageData:s,resizeWebGLCanvasWithoutClearing:function(t,n,r){var o=t.width,i=t.height;l(t,function(a){var l=a.gl,c=new Uint8Array(o*i*4);l.readPixels(0,0,o,i,l.RGBA,l.UNSIGNED_BYTE,c),t.width=n,t.height=r,s(l,c,0,0,o,i)})}});function u(t,n,o,i,a,l){void 0===l&&(l=1);var s=new Uint8Array(t*n),c=i[2]-i[0],u=i[3]-i[1],f=[];r(o,function(t,n,r,o){f.push({x1:t,y1:n,x2:r,y2:o,minX:Math.min(t,r),minY:Math.min(n,o),maxX:Math.max(t,r),maxY:Math.max(n,o)})}),f.sort(function(t,n){return t.maxX-n.maxX});for(var d=0;d<t;d++)for(var p=0;p<n;p++){var v=function(t,n){for(var r=1/0,o=1/0,i=f.length;i--;){var a=f[i];if(a.maxX+o<=t)break;if(t+o>a.minX&&n-o<a.maxY&&n+o>a.minY){var l=function(t,n,r,o,i,a){var l=i-r,s=a-o,c=l*l+s*s,u=c?Math.max(0,Math.min(1,((t-r)*l+(n-o)*s)/c)):0,f=t-(r+u*l),d=n-(o+u*s);return f*f+d*d}(t,n,a.x1,a.y1,a.x2,a.y2);l<r&&(o=Math.sqrt(r=l))}}return function(t,n){for(var r=0,o=f.length;o--;){var i=f[o];if(i.maxX<=t)break;i.y1>n!=i.y2>n&&t<(i.x2-i.x1)*(n-i.y1)/(i.y2-i.y1)+i.x1&&(r+=i.y1<i.y2?1:-1)}return 0!==r}(t,n)&&(o=-o),o}(i[0]+c*(d+.5)/t,i[1]+u*(p+.5)/n),h=Math.pow(1-Math.abs(v)/a,l)/2;v<0&&(h=1-h),h=Math.max(0,Math.min(255,Math.round(255*h))),s[p*t+d]=h}return s}function f(t,n,r,o,i,a,l,s,c,u){void 0===a&&(a=1),void 0===s&&(s=0),void 0===c&&(c=0),void 0===u&&(u=0),d(t,n,r,o,i,a,l,null,s,c,u)}function d(t,n,r,o,i,a,l,c,f,d,p){void 0===a&&(a=1),void 0===f&&(f=0),void 0===d&&(d=0),void 0===p&&(p=0);for(var v=u(t,n,r,o,i,a),h=new Uint8Array(4*v.length),m=0;m<v.length;m++)h[4*m+p]=v[m];s(l,h,f,d,t,n,1<<3-p,c)}var p=Object.freeze({__proto__:null,generate:u,generateIntoCanvas:f,generateIntoFramebuffer:d}),v=new Float32Array([0,0,2,0,0,2]),h=null,m=!1,b={},g=new WeakMap;function y(t){if(!m&&!x(t))throw Error("WebGL generation not supported")}function w(t,n,r,o,i,a,s){if(void 0===a&&(a=1),void 0===s&&(s=null),!s&&!(s=h)){var c="function"==typeof OffscreenCanvas?new OffscreenCanvas(1,1):"undefined"!=typeof document?document.createElement("canvas"):null;if(!c)throw Error("OffscreenCanvas or DOM canvas not supported");s=h=c.getContext("webgl",{depth:!1})}y(s);var u=new Uint8Array(t*n*4);l(s,function(l){var s=l.gl,c=l.withTexture,f=l.withTextureFramebuffer;c("readable",function(l,c){s.texImage2D(s.TEXTURE_2D,0,s.RGBA,t,n,0,s.RGBA,s.UNSIGNED_BYTE,null),f(l,c,function(l){_(t,n,r,o,i,a,s,l,0,0,0),s.readPixels(0,0,t,n,s.RGBA,s.UNSIGNED_BYTE,u)})})});for(var f=new Uint8Array(t*n),d=0,p=0;d<u.length;d+=4)f[p++]=u[d];return f}function E(t,n,r,o,i,a,l,s,c,u){void 0===a&&(a=1),void 0===s&&(s=0),void 0===c&&(c=0),void 0===u&&(u=0),_(t,n,r,o,i,a,l,null,s,c,u)}function _(t,n,i,a,s,c,u,f,d,p,h){void 0===c&&(c=1),void 0===d&&(d=0),void 0===p&&(p=0),void 0===h&&(h=0),y(u);var m=[];r(i,function(t,n,r,o){m.push(t,n,r,o)}),m=new Float32Array(m),l(u,function(r){var i=r.gl,l=r.isWebGL2,u=r.getExtension,b=r.withProgram,g=r.withTexture,y=r.withTextureFramebuffer,w=r.handleContextLoss;if(g("rawDistances",function(r,g){(t!==r._lastWidth||n!==r._lastHeight)&&i.texImage2D(i.TEXTURE_2D,0,i.RGBA,r._lastWidth=t,r._lastHeight=n,0,i.RGBA,i.UNSIGNED_BYTE,null),b("main","precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}","precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}",function(o){var f=o.setAttribute,d=o.setUniform,p=!l&&u("ANGLE_instanced_arrays"),h=!l&&u("EXT_blend_minmax");f("aUV",2,i.STATIC_DRAW,0,v),f("aLineSegment",4,i.DYNAMIC_DRAW,1,m),d.apply(void 0,["4f","uGlyphBounds"].concat(a)),d("1f","uMaxDistance",s),d("1f","uExponent",c),y(r,g,function(r){i.enable(i.BLEND),i.colorMask(!0,!0,!0,!0),i.viewport(0,0,t,n),i.scissor(0,0,t,n),i.blendFunc(i.ONE,i.ONE),i.blendEquationSeparate(i.FUNC_ADD,l?i.MAX:h.MAX_EXT),i.clear(i.COLOR_BUFFER_BIT),l?i.drawArraysInstanced(i.TRIANGLES,0,3,m.length/4):p.drawArraysInstancedANGLE(i.TRIANGLES,0,3,m.length/4)})}),b("post",o,"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}",function(r){r.setAttribute("aUV",2,i.STATIC_DRAW,0,v),r.setUniform("1i","tex",g),i.bindFramebuffer(i.FRAMEBUFFER,f),i.disable(i.BLEND),i.colorMask(0===h,1===h,2===h,3===h),i.viewport(d,p,t,n),i.scissor(d,p,t,n),i.drawArrays(i.TRIANGLES,0,3)})}),i.isContextLost())throw w(),Error("webgl context lost")})}function x(t){var n=t&&t!==h?t.canvas||t:b,r=g.get(n);if(void 0===r){m=!0;var o=null;try{var i=[97,106,97,61,99,137,118,80,80,118,137,99,61,97,106,97],a=w(4,4,"M8,8L16,8L24,24L16,24Z",[0,0,32,32],24,1,t);(r=a&&i.length===a.length&&a.every(function(t,n){return t===i[n]}))||(o="bad trial run results",console.info(i,a))}catch(t){r=!1,o=t.message}o&&console.warn("WebGL SDF generation not supported:",o),m=!1,g.set(n,r)}return r}var j=Object.freeze({__proto__:null,generate:w,generateIntoCanvas:E,generateIntoFramebuffer:_,isSupported:x});return t.forEachPathCommand=n,t.generate=function(t,n,r,o,i,a){void 0===i&&(i=Math.max(o[2]-o[0],o[3]-o[1])/2),void 0===a&&(a=1);try{return w.apply(j,arguments)}catch(t){return console.info("WebGL SDF generation failed, falling back to JS",t),u.apply(p,arguments)}},t.generateIntoCanvas=function(t,n,r,o,i,a,l,s,c,u){void 0===i&&(i=Math.max(o[2]-o[0],o[3]-o[1])/2),void 0===a&&(a=1),void 0===s&&(s=0),void 0===c&&(c=0),void 0===u&&(u=0);try{return E.apply(j,arguments)}catch(t){return console.info("WebGL SDF generation failed, falling back to JS",t),f.apply(p,arguments)}},t.javascript=p,t.pathToLineSegments=r,t.webgl=j,t.webglUtils=c,Object.defineProperty(t,"__esModule",{value:!0}),t}({})}},4342:(t,n,r)=>{t.exports=r(7319)},4688:(t,n,r)=>{r.d(n,{N:()=>w});var o=r(9630),i=r(544),a=r(2115),l=r(3264),s=Object.defineProperty,c=(t,n,r)=>n in t?s(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r,u=(t,n,r)=>(c(t,"symbol"!=typeof n?n+"":n,r),r);class f{constructor(){u(this,"_listeners")}addEventListener(t,n){void 0===this._listeners&&(this._listeners={});let r=this._listeners;void 0===r[t]&&(r[t]=[]),-1===r[t].indexOf(n)&&r[t].push(n)}hasEventListener(t,n){if(void 0===this._listeners)return!1;let r=this._listeners;return void 0!==r[t]&&-1!==r[t].indexOf(n)}removeEventListener(t,n){if(void 0===this._listeners)return;let r=this._listeners[t];if(void 0!==r){let t=r.indexOf(n);-1!==t&&r.splice(t,1)}}dispatchEvent(t){if(void 0===this._listeners)return;let n=this._listeners[t.type];if(void 0!==n){t.target=this;let r=n.slice(0);for(let n=0,o=r.length;n<o;n++)r[n].call(this,t);t.target=null}}}var d=Object.defineProperty,p=(t,n,r)=>n in t?d(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r,v=(t,n,r)=>(p(t,"symbol"!=typeof n?n+"":n,r),r);let h=new l.RlV,m=new l.Zcv,b=Math.cos(Math.PI/180*70),g=(t,n)=>(t%n+n)%n;class y extends f{constructor(t,n){super(),v(this,"object"),v(this,"domElement"),v(this,"enabled",!0),v(this,"target",new l.Pq0),v(this,"minDistance",0),v(this,"maxDistance",1/0),v(this,"minZoom",0),v(this,"maxZoom",1/0),v(this,"minPolarAngle",0),v(this,"maxPolarAngle",Math.PI),v(this,"minAzimuthAngle",-1/0),v(this,"maxAzimuthAngle",1/0),v(this,"enableDamping",!1),v(this,"dampingFactor",.05),v(this,"enableZoom",!0),v(this,"zoomSpeed",1),v(this,"enableRotate",!0),v(this,"rotateSpeed",1),v(this,"enablePan",!0),v(this,"panSpeed",1),v(this,"screenSpacePanning",!0),v(this,"keyPanSpeed",7),v(this,"zoomToCursor",!1),v(this,"autoRotate",!1),v(this,"autoRotateSpeed",2),v(this,"reverseOrbit",!1),v(this,"reverseHorizontalOrbit",!1),v(this,"reverseVerticalOrbit",!1),v(this,"keys",{LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"}),v(this,"mouseButtons",{LEFT:l.kBv.ROTATE,MIDDLE:l.kBv.DOLLY,RIGHT:l.kBv.PAN}),v(this,"touches",{ONE:l.wtR.ROTATE,TWO:l.wtR.DOLLY_PAN}),v(this,"target0"),v(this,"position0"),v(this,"zoom0"),v(this,"_domElementKeyEvents",null),v(this,"getPolarAngle"),v(this,"getAzimuthalAngle"),v(this,"setPolarAngle"),v(this,"setAzimuthalAngle"),v(this,"getDistance"),v(this,"getZoomScale"),v(this,"listenToKeyEvents"),v(this,"stopListenToKeyEvents"),v(this,"saveState"),v(this,"reset"),v(this,"update"),v(this,"connect"),v(this,"dispose"),v(this,"dollyIn"),v(this,"dollyOut"),v(this,"getScale"),v(this,"setScale"),this.object=t,this.domElement=n,this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=()=>f.phi,this.getAzimuthalAngle=()=>f.theta,this.setPolarAngle=t=>{let n=g(t,2*Math.PI),o=f.phi;o<0&&(o+=2*Math.PI),n<0&&(n+=2*Math.PI);let i=Math.abs(n-o);2*Math.PI-i<i&&(n<o?n+=2*Math.PI:o+=2*Math.PI),d.phi=n-o,r.update()},this.setAzimuthalAngle=t=>{let n=g(t,2*Math.PI),o=f.theta;o<0&&(o+=2*Math.PI),n<0&&(n+=2*Math.PI);let i=Math.abs(n-o);2*Math.PI-i<i&&(n<o?n+=2*Math.PI:o+=2*Math.PI),d.theta=n-o,r.update()},this.getDistance=()=>r.object.position.distanceTo(r.target),this.listenToKeyEvents=t=>{t.addEventListener("keydown",et),this._domElementKeyEvents=t},this.stopListenToKeyEvents=()=>{this._domElementKeyEvents.removeEventListener("keydown",et),this._domElementKeyEvents=null},this.saveState=()=>{r.target0.copy(r.target),r.position0.copy(r.object.position),r.zoom0=r.object.zoom},this.reset=()=>{r.target.copy(r.target0),r.object.position.copy(r.position0),r.object.zoom=r.zoom0,r.object.updateProjectionMatrix(),r.dispatchEvent(o),r.update(),c=s.NONE},this.update=(()=>{let n=new l.Pq0,i=new l.Pq0(0,1,0),a=new l.PTz().setFromUnitVectors(t.up,i),v=a.clone().invert(),g=new l.Pq0,w=new l.PTz,E=2*Math.PI;return function(){let _=r.object.position;a.setFromUnitVectors(t.up,i),v.copy(a).invert(),n.copy(_).sub(r.target),n.applyQuaternion(a),f.setFromVector3(n),r.autoRotate&&c===s.NONE&&R(2*Math.PI/60/60*r.autoRotateSpeed),r.enableDamping?(f.theta+=d.theta*r.dampingFactor,f.phi+=d.phi*r.dampingFactor):(f.theta+=d.theta,f.phi+=d.phi);let x=r.minAzimuthAngle,j=r.maxAzimuthAngle;isFinite(x)&&isFinite(j)&&(x<-Math.PI?x+=E:x>Math.PI&&(x-=E),j<-Math.PI?j+=E:j>Math.PI&&(j-=E),x<=j?f.theta=Math.max(x,Math.min(j,f.theta)):f.theta=f.theta>(x+j)/2?Math.max(x,f.theta):Math.min(j,f.theta)),f.phi=Math.max(r.minPolarAngle,Math.min(r.maxPolarAngle,f.phi)),f.makeSafe(),!0===r.enableDamping?r.target.addScaledVector(y,r.dampingFactor):r.target.add(y),r.zoomToCursor&&S||r.object.isOrthographicCamera?f.radius=q(f.radius):f.radius=q(f.radius*p),n.setFromSpherical(f),n.applyQuaternion(v),_.copy(r.target).add(n),r.object.matrixAutoUpdate||r.object.updateMatrix(),r.object.lookAt(r.target),!0===r.enableDamping?(d.theta*=1-r.dampingFactor,d.phi*=1-r.dampingFactor,y.multiplyScalar(1-r.dampingFactor)):(d.set(0,0,0),y.set(0,0,0));let M=!1;if(r.zoomToCursor&&S){let o=null;if(r.object instanceof l.ubm&&r.object.isPerspectiveCamera){let t=n.length();o=q(t*p);let i=t-o;r.object.position.addScaledVector(A,i),r.object.updateMatrixWorld()}else if(r.object.isOrthographicCamera){let t=new l.Pq0(C.x,C.y,0);t.unproject(r.object),r.object.zoom=Math.max(r.minZoom,Math.min(r.maxZoom,r.object.zoom/p)),r.object.updateProjectionMatrix(),M=!0;let i=new l.Pq0(C.x,C.y,0);i.unproject(r.object),r.object.position.sub(i).add(t),r.object.updateMatrixWorld(),o=n.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),r.zoomToCursor=!1;null!==o&&(r.screenSpacePanning?r.target.set(0,0,-1).transformDirection(r.object.matrix).multiplyScalar(o).add(r.object.position):(h.origin.copy(r.object.position),h.direction.set(0,0,-1).transformDirection(r.object.matrix),Math.abs(r.object.up.dot(h.direction))<b?t.lookAt(r.target):(m.setFromNormalAndCoplanarPoint(r.object.up,r.target),h.intersectPlane(m,r.target))))}else r.object instanceof l.qUd&&r.object.isOrthographicCamera&&(M=1!==p)&&(r.object.zoom=Math.max(r.minZoom,Math.min(r.maxZoom,r.object.zoom/p)),r.object.updateProjectionMatrix());return p=1,S=!1,!!(M||g.distanceToSquared(r.object.position)>u||8*(1-w.dot(r.object.quaternion))>u)&&(r.dispatchEvent(o),g.copy(r.object.position),w.copy(r.object.quaternion),M=!1,!0)}})(),this.connect=t=>{r.domElement=t,r.domElement.style.touchAction="none",r.domElement.addEventListener("contextmenu",en),r.domElement.addEventListener("pointerdown",Z),r.domElement.addEventListener("pointercancel",J),r.domElement.addEventListener("wheel",ee)},this.dispose=()=>{var t,n,o,i,a,l;r.domElement&&(r.domElement.style.touchAction="auto"),null==(t=r.domElement)||t.removeEventListener("contextmenu",en),null==(n=r.domElement)||n.removeEventListener("pointerdown",Z),null==(o=r.domElement)||o.removeEventListener("pointercancel",J),null==(i=r.domElement)||i.removeEventListener("wheel",ee),null==(a=r.domElement)||a.ownerDocument.removeEventListener("pointermove",Q),null==(l=r.domElement)||l.ownerDocument.removeEventListener("pointerup",J),null!==r._domElementKeyEvents&&r._domElementKeyEvents.removeEventListener("keydown",et)};let r=this,o={type:"change"},i={type:"start"},a={type:"end"},s={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},c=s.NONE,u=1e-6,f=new l.YHV,d=new l.YHV,p=1,y=new l.Pq0,w=new l.I9Y,E=new l.I9Y,_=new l.I9Y,x=new l.I9Y,j=new l.I9Y,M=new l.I9Y,k=new l.I9Y,T=new l.I9Y,P=new l.I9Y,A=new l.Pq0,C=new l.I9Y,S=!1,I=[],O={};function L(){return Math.pow(.95,r.zoomSpeed)}function R(t){r.reverseOrbit||r.reverseHorizontalOrbit?d.theta+=t:d.theta-=t}function D(t){r.reverseOrbit||r.reverseVerticalOrbit?d.phi+=t:d.phi-=t}let N=(()=>{let t=new l.Pq0;return function(n,r){t.setFromMatrixColumn(r,0),t.multiplyScalar(-n),y.add(t)}})(),z=(()=>{let t=new l.Pq0;return function(n,o){!0===r.screenSpacePanning?t.setFromMatrixColumn(o,1):(t.setFromMatrixColumn(o,0),t.crossVectors(r.object.up,t)),t.multiplyScalar(n),y.add(t)}})(),F=(()=>{let t=new l.Pq0;return function(n,o){let i=r.domElement;if(i&&r.object instanceof l.ubm&&r.object.isPerspectiveCamera){let a=r.object.position;t.copy(a).sub(r.target);let l=t.length();N(2*n*(l*=Math.tan(r.object.fov/2*Math.PI/180))/i.clientHeight,r.object.matrix),z(2*o*l/i.clientHeight,r.object.matrix)}else i&&r.object instanceof l.qUd&&r.object.isOrthographicCamera?(N(n*(r.object.right-r.object.left)/r.object.zoom/i.clientWidth,r.object.matrix),z(o*(r.object.top-r.object.bottom)/r.object.zoom/i.clientHeight,r.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),r.enablePan=!1)}})();function U(t){r.object instanceof l.ubm&&r.object.isPerspectiveCamera||r.object instanceof l.qUd&&r.object.isOrthographicCamera?p=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),r.enableZoom=!1)}function B(t){if(!r.zoomToCursor||!r.domElement)return;S=!0;let n=r.domElement.getBoundingClientRect(),o=t.clientX-n.left,i=t.clientY-n.top,a=n.width,l=n.height;C.x=o/a*2-1,C.y=-(i/l*2)+1,A.set(C.x,C.y,1).unproject(r.object).sub(r.object.position).normalize()}function q(t){return Math.max(r.minDistance,Math.min(r.maxDistance,t))}function Y(t){w.set(t.clientX,t.clientY)}function H(t){x.set(t.clientX,t.clientY)}function W(){if(1==I.length)w.set(I[0].pageX,I[0].pageY);else{let t=.5*(I[0].pageX+I[1].pageX),n=.5*(I[0].pageY+I[1].pageY);w.set(t,n)}}function G(){if(1==I.length)x.set(I[0].pageX,I[0].pageY);else{let t=.5*(I[0].pageX+I[1].pageX),n=.5*(I[0].pageY+I[1].pageY);x.set(t,n)}}function X(){let t=I[0].pageX-I[1].pageX,n=I[0].pageY-I[1].pageY,r=Math.sqrt(t*t+n*n);k.set(0,r)}function V(t){if(1==I.length)E.set(t.pageX,t.pageY);else{let n=eo(t),r=.5*(t.pageX+n.x),o=.5*(t.pageY+n.y);E.set(r,o)}_.subVectors(E,w).multiplyScalar(r.rotateSpeed);let n=r.domElement;n&&(R(2*Math.PI*_.x/n.clientHeight),D(2*Math.PI*_.y/n.clientHeight)),w.copy(E)}function $(t){if(1==I.length)j.set(t.pageX,t.pageY);else{let n=eo(t),r=.5*(t.pageX+n.x),o=.5*(t.pageY+n.y);j.set(r,o)}M.subVectors(j,x).multiplyScalar(r.panSpeed),F(M.x,M.y),x.copy(j)}function K(t){var n;let o=eo(t),i=t.pageX-o.x,a=t.pageY-o.y,l=Math.sqrt(i*i+a*a);T.set(0,l),P.set(0,Math.pow(T.y/k.y,r.zoomSpeed)),n=P.y,U(p/n),k.copy(T)}function Z(t){var n,o,a;!1!==r.enabled&&(0===I.length&&(null==(n=r.domElement)||n.ownerDocument.addEventListener("pointermove",Q),null==(o=r.domElement)||o.ownerDocument.addEventListener("pointerup",J)),a=t,I.push(a),"touch"===t.pointerType?function(t){switch(er(t),I.length){case 1:switch(r.touches.ONE){case l.wtR.ROTATE:if(!1===r.enableRotate)return;W(),c=s.TOUCH_ROTATE;break;case l.wtR.PAN:if(!1===r.enablePan)return;G(),c=s.TOUCH_PAN;break;default:c=s.NONE}break;case 2:switch(r.touches.TWO){case l.wtR.DOLLY_PAN:if(!1===r.enableZoom&&!1===r.enablePan)return;r.enableZoom&&X(),r.enablePan&&G(),c=s.TOUCH_DOLLY_PAN;break;case l.wtR.DOLLY_ROTATE:if(!1===r.enableZoom&&!1===r.enableRotate)return;r.enableZoom&&X(),r.enableRotate&&W(),c=s.TOUCH_DOLLY_ROTATE;break;default:c=s.NONE}break;default:c=s.NONE}c!==s.NONE&&r.dispatchEvent(i)}(t):function(t){let n;switch(t.button){case 0:n=r.mouseButtons.LEFT;break;case 1:n=r.mouseButtons.MIDDLE;break;case 2:n=r.mouseButtons.RIGHT;break;default:n=-1}switch(n){case l.kBv.DOLLY:if(!1===r.enableZoom)return;B(t),k.set(t.clientX,t.clientY),c=s.DOLLY;break;case l.kBv.ROTATE:if(t.ctrlKey||t.metaKey||t.shiftKey){if(!1===r.enablePan)return;H(t),c=s.PAN}else{if(!1===r.enableRotate)return;Y(t),c=s.ROTATE}break;case l.kBv.PAN:if(t.ctrlKey||t.metaKey||t.shiftKey){if(!1===r.enableRotate)return;Y(t),c=s.ROTATE}else{if(!1===r.enablePan)return;H(t),c=s.PAN}break;default:c=s.NONE}c!==s.NONE&&r.dispatchEvent(i)}(t))}function Q(t){!1!==r.enabled&&("touch"===t.pointerType?function(t){switch(er(t),c){case s.TOUCH_ROTATE:if(!1===r.enableRotate)return;V(t),r.update();break;case s.TOUCH_PAN:if(!1===r.enablePan)return;$(t),r.update();break;case s.TOUCH_DOLLY_PAN:if(!1===r.enableZoom&&!1===r.enablePan)return;r.enableZoom&&K(t),r.enablePan&&$(t),r.update();break;case s.TOUCH_DOLLY_ROTATE:if(!1===r.enableZoom&&!1===r.enableRotate)return;r.enableZoom&&K(t),r.enableRotate&&V(t),r.update();break;default:c=s.NONE}}(t):function(t){if(!1!==r.enabled)switch(c){case s.ROTATE:if(!1===r.enableRotate)return;E.set(t.clientX,t.clientY),_.subVectors(E,w).multiplyScalar(r.rotateSpeed);let n=r.domElement;n&&(R(2*Math.PI*_.x/n.clientHeight),D(2*Math.PI*_.y/n.clientHeight)),w.copy(E),r.update();break;case s.DOLLY:var o,i;if(!1===r.enableZoom)return;(T.set(t.clientX,t.clientY),P.subVectors(T,k),P.y>0)?(o=L(),U(p/o)):P.y<0&&(i=L(),U(p*i)),k.copy(T),r.update();break;case s.PAN:if(!1===r.enablePan)return;j.set(t.clientX,t.clientY),M.subVectors(j,x).multiplyScalar(r.panSpeed),F(M.x,M.y),x.copy(j),r.update()}}(t))}function J(t){var n,o,i;(function(t){delete O[t.pointerId];for(let n=0;n<I.length;n++)if(I[n].pointerId==t.pointerId)return void I.splice(n,1)})(t),0===I.length&&(null==(n=r.domElement)||n.releasePointerCapture(t.pointerId),null==(o=r.domElement)||o.ownerDocument.removeEventListener("pointermove",Q),null==(i=r.domElement)||i.ownerDocument.removeEventListener("pointerup",J)),r.dispatchEvent(a),c=s.NONE}function ee(t){if(!1!==r.enabled&&!1!==r.enableZoom&&(c===s.NONE||c===s.ROTATE)){var n,o;t.preventDefault(),r.dispatchEvent(i),(B(t),t.deltaY<0)?(n=L(),U(p*n)):t.deltaY>0&&(o=L(),U(p/o)),r.update(),r.dispatchEvent(a)}}function et(t){if(!1!==r.enabled&&!1!==r.enablePan){let n=!1;switch(t.code){case r.keys.UP:F(0,r.keyPanSpeed),n=!0;break;case r.keys.BOTTOM:F(0,-r.keyPanSpeed),n=!0;break;case r.keys.LEFT:F(r.keyPanSpeed,0),n=!0;break;case r.keys.RIGHT:F(-r.keyPanSpeed,0),n=!0}n&&(t.preventDefault(),r.update())}}function en(t){!1!==r.enabled&&t.preventDefault()}function er(t){let n=O[t.pointerId];void 0===n&&(n=new l.I9Y,O[t.pointerId]=n),n.set(t.pageX,t.pageY)}function eo(t){return O[(t.pointerId===I[0].pointerId?I[1]:I[0]).pointerId]}this.dollyIn=(t=L())=>{U(p*t),r.update()},this.dollyOut=(t=L())=>{U(p/t),r.update()},this.getScale=()=>p,this.setScale=t=>{U(t),r.update()},this.getZoomScale=()=>L(),void 0!==n&&this.connect(n),this.update()}}let w=a.forwardRef(({makeDefault:t,camera:n,regress:r,domElement:l,enableDamping:s=!0,keyEvents:c=!1,onChange:u,onStart:f,onEnd:d,...p},v)=>{let h=(0,i.A)(t=>t.invalidate),m=(0,i.A)(t=>t.camera),b=(0,i.A)(t=>t.gl),g=(0,i.A)(t=>t.events),w=(0,i.A)(t=>t.setEvents),E=(0,i.A)(t=>t.set),_=(0,i.A)(t=>t.get),x=(0,i.A)(t=>t.performance),j=n||m,M=l||g.connected||b.domElement,k=a.useMemo(()=>new y(j),[j]);return(0,i.C)(()=>{k.enabled&&k.update()},-1),a.useEffect(()=>(c&&k.connect(!0===c?M:c),k.connect(M),()=>void k.dispose()),[c,M,r,k,h]),a.useEffect(()=>{let t=t=>{h(),r&&x.regress(),u&&u(t)},n=t=>{f&&f(t)},o=t=>{d&&d(t)};return k.addEventListener("change",t),k.addEventListener("start",n),k.addEventListener("end",o),()=>{k.removeEventListener("start",n),k.removeEventListener("end",o),k.removeEventListener("change",t)}},[u,f,d,k,h,w]),a.useEffect(()=>{if(t){let t=_().controls;return E({controls:k}),()=>E({controls:t})}},[t,k]),a.createElement("primitive",(0,o.A)({ref:v,object:k,enableDamping:s},p))})},5202:(t,n,r)=>{function o(){var t=Object.create(null);function n(t,n){var r=void 0;self.troikaDefine=function(t){return r=t};var o=URL.createObjectURL(new Blob(["/** "+t.replace(/\*/g,"")+" **/\n\ntroikaDefine(\n"+n+"\n)"],{type:"application/javascript"}));try{importScripts(o)}catch(t){console.error(t)}return URL.revokeObjectURL(o),delete self.troikaDefine,r}self.addEventListener("message",function(r){var o=r.data,i=o.messageId,a=o.action,l=o.data;try{"registerModule"===a&&function r(o,i){var a=o.id,l=o.name,s=o.dependencies;void 0===s&&(s=[]);var c=o.init;void 0===c&&(c=function(){});var u=o.getTransferables;if(void 0===u&&(u=null),!t[a])try{s=s.map(function(n){return n&&n.isWorkerModule&&(r(n,function(t){if(t instanceof Error)throw t}),n=t[n.id].value),n}),c=n("<"+l+">.init",c),u&&(u=n("<"+l+">.getTransferables",u));var f=null;"function"==typeof c?f=c.apply(void 0,s):console.error("worker module init function failed to rehydrate"),t[a]={id:a,value:f,getTransferables:u},i(f)}catch(t){t&&t.noLog||console.error(t),i(t)}}(l,function(t){t instanceof Error?postMessage({messageId:i,success:!1,error:t.message}):postMessage({messageId:i,success:!0,result:{isCallable:"function"==typeof t}})}),"callModule"===a&&function(n,r){var o,i=n.id,a=n.args;t[i]&&"function"==typeof t[i].value||r(Error("Worker module "+i+": not found or its 'init' did not return a function"));try{var l=(o=t[i]).value.apply(o,a);l&&"function"==typeof l.then?l.then(s,function(t){return r(t instanceof Error?t:Error(""+t))}):s(l)}catch(t){r(t)}function s(n){try{var o=t[i].getTransferables&&t[i].getTransferables(n);o&&Array.isArray(o)&&o.length||(o=void 0),r(n,o)}catch(t){console.error(t),r(t)}}}(l,function(t,n){t instanceof Error?postMessage({messageId:i,success:!1,error:t.message}):postMessage({messageId:i,success:!0,result:t},n||void 0)})}catch(t){postMessage({messageId:i,success:!1,error:t.stack})}})}r.d(n,{Qw:()=>d,kl:()=>function t(n){if((!n||"function"!=typeof n.init)&&!s)throw Error("requires `options.init` function");var r,o=n.dependencies,l=n.init,c=n.getTransferables,f=n.workerId,d=((r=function(){for(var t=[],n=arguments.length;n--;)t[n]=arguments[n];return r._getInitResult().then(function(n){if("function"==typeof n)return n.apply(void 0,t);throw Error("Worker module function was called but `init` did not return a callable function")})})._getInitResult=function(){var t=n.dependencies,o=n.init,i=Promise.all(t=Array.isArray(t)?t.map(function(t){return t&&(t=t.onMainThread||t)._getInitResult&&(t=t._getInitResult()),t}):[]).then(function(t){return o.apply(null,t)});return r._getInitResult=function(){return i},i},r);null==f&&(f="#default");var h="workerModule"+ ++a,m=n.name||h,b=null;function g(){for(var t=[],n=arguments.length;n--;)t[n]=arguments[n];if(!i())return d.apply(void 0,t);if(!b){b=v(f,"registerModule",g.workerModuleData);var r=function(){b=null,u[f].delete(r)};(u[f]||(u[f]=new Set)).add(r)}return b.then(function(n){if(n.isCallable)return v(f,"callModule",{id:h,args:t});throw Error("Worker module function was called but `init` did not return a callable function")})}return o=o&&o.map(function(n){return"function"!=typeof n||n.workerModuleData||(s=!0,n=t({workerId:f,name:"<"+m+"> function dependency: "+n.name,init:"function(){return (\n"+p(n)+"\n)}"}),s=!1),n&&n.workerModuleData&&(n=n.workerModuleData),n}),g.workerModuleData={isWorkerModule:!0,id:h,name:m,dependencies:o,init:p(l),getTransferables:c&&p(c)},g.onMainThread=d,g}}),r(9509);var i=function(){var t=!1;if("undefined"!=typeof window&&void 0!==window.document)try{new Worker(URL.createObjectURL(new Blob([""],{type:"application/javascript"}))).terminate(),t=!0}catch(t){console.log("Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: ["+t.message+"]")}return i=function(){return t},t},a=0,l=0,s=!1,c=Object.create(null),u=Object.create(null),f=Object.create(null);function d(t){u[t]&&u[t].forEach(function(t){t()}),c[t]&&(c[t].terminate(),delete c[t])}function p(t){var n=t.toString();return!/^function/.test(n)&&/^\w+\s*\(/.test(n)&&(n="function "+n),n}function v(t,n,r){return new Promise(function(i,a){var s=++l;f[s]=function(t){t.success?i(t.result):a(Error("Error in worker "+n+" call: "+t.error))},(function(t){var n=c[t];if(!n){var r=p(o);(n=c[t]=new Worker(URL.createObjectURL(new Blob(["/** Worker Module Bootstrap: "+t.replace(/\*/g,"")+" **/\n\n;("+r+")()"],{type:"application/javascript"})))).onmessage=function(t){var n=t.data,r=n.messageId,o=f[r];if(!o)throw Error("WorkerModule response with empty or unknown messageId");delete f[r],o(n)}}return n})(t).postMessage({messageId:s,action:n,data:r})})}},5220:(t,n,r)=>{t.exports=r(1724)},5454:(t,n,r)=>{r.d(n,{A:()=>o});let o=function(){return function(t){var n,r,o,i,a={R:"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73",EN:"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9",ES:"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2",ET:"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj",AN:"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u",CS:"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b",B:"a,3,f+2,2v,690",S:"9,2,k",WS:"c,k,4f4,1vk+a,u,1j,335",ON:"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i",BN:"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1",NSM:"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n",AL:"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d",LRO:"6ct",RLO:"6cu",LRE:"6cq",RLE:"6cr",PDF:"6cs",LRI:"6ee",RLI:"6ef",FSI:"6eg",PDI:"6eh"},l={},s={};l.L=1,s[1]="L",Object.keys(a).forEach(function(t,n){l[t]=1<<n+1,s[l[t]]=t}),Object.freeze(l);var c=l.LRI|l.RLI|l.FSI,u=l.L|l.R|l.AL,f=l.B|l.S|l.WS|l.ON|l.FSI|l.LRI|l.RLI|l.PDI,d=l.BN|l.RLE|l.LRE|l.RLO|l.LRO|l.PDF,p=l.S|l.WS|l.B|c|l.PDI|d,v=null;function h(t){if(!v){v=new Map;var n=function(t){if(a.hasOwnProperty(t)){var n=0;a[t].split(",").forEach(function(r){var o=r.split("+"),i=o[0],a=o[1];i=parseInt(i,36),a=a?parseInt(a,36):0,v.set(n+=i,l[t]);for(var s=0;s<a;s++)v.set(++n,l[t])})}};for(var r in a)n(r)}return v.get(t.codePointAt(0))||l.L}var m={pairs:"14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1",canonical:"6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye"};function b(t,n){var r,o=0,i=new Map,a=n&&new Map;return t.split(",").forEach(function t(l){if(-1!==l.indexOf("+"))for(var s=+l;s--;)t(r);else{r=l;var c=l.split(">"),u=c[0],f=c[1];u=String.fromCodePoint(o+=parseInt(u,36)),f=String.fromCodePoint(o+=parseInt(f,36)),i.set(u,f),n&&a.set(f,u)}}),{map:i,reverseMap:a}}function g(){if(!n){var t=b(m.pairs,!0),i=t.map,a=t.reverseMap;n=i,r=a,o=b(m.canonical,!1).map}}function y(t){return g(),n.get(t)||null}function w(t){return g(),r.get(t)||null}function E(t){return g(),o.get(t)||null}var _=l.L,x=l.R,j=l.EN,M=l.ES,k=l.ET,T=l.AN,P=l.CS,A=l.B,C=l.S,S=l.ON,I=l.BN,O=l.NSM,L=l.AL,R=l.LRO,D=l.RLO,N=l.LRE,z=l.RLE,F=l.PDF,U=l.LRI,B=l.RLI,q=l.FSI,Y=l.PDI;function H(t){if(!i){var n=b("14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1",!0),r=n.map;n.reverseMap.forEach(function(t,n){r.set(n,t)}),i=r}return i.get(t)||null}function W(t,n,r,o){var i=t.length;r=Math.max(0,null==r?0:+r),o=Math.min(i-1,null==o?i-1:+o);var a=[];return n.paragraphs.forEach(function(i){var l=Math.max(r,i.start),s=Math.min(o,i.end);if(l<s){for(var c=n.levels.slice(l,s+1),u=s;u>=l&&h(t[u])&p;u--)c[u]=i.level;for(var f=i.level,d=1/0,v=0;v<c.length;v++){var m=c[v];m>f&&(f=m),m<d&&(d=1|m)}for(var b=f;b>=d;b--)for(var g=0;g<c.length;g++)if(c[g]>=b){for(var y=g;g+1<c.length&&c[g+1]>=b;)g++;g>y&&a.push([y+l,g+l])}}}),a}function G(t,n,r,o){for(var i=W(t,n,r,o),a=[],l=0;l<t.length;l++)a[l]=l;return i.forEach(function(t){for(var n=t[0],r=t[1],o=a.slice(n,r+1),i=o.length;i--;)a[r-i]=o[i]}),a}return t.closingToOpeningBracket=w,t.getBidiCharType=h,t.getBidiCharTypeName=function(t){return s[h(t)]},t.getCanonicalBracket=E,t.getEmbeddingLevels=function(t,n){for(var r=new Uint32Array(t.length),o=0;o<t.length;o++)r[o]=h(t[o]);var i=new Map;function a(t,n){var o=r[t];r[t]=n,i.set(o,i.get(o)-1),o&f&&i.set(f,i.get(f)-1),i.set(n,(i.get(n)||0)+1),n&f&&i.set(f,(i.get(f)||0)+1)}for(var l=new Uint8Array(t.length),s=new Map,v=[],m=null,b=0;b<t.length;b++)m||v.push(m={start:b,end:t.length-1,level:"rtl"===n?1:"ltr"===n?0:tS(b,!1)}),r[b]&A&&(m.end=b,m=null);for(var g=z|N|D|R|c|Y|F|A,H=function(t){return t+(1&t?1:2)},W=function(t){return t+(1&t?2:1)},G=0;G<v.length;G++){var X=[{_level:(m=v[G]).level,_override:0,_isolate:0}],V=void 0,$=0,K=0,Z=0;i.clear();for(var Q=m.start;Q<=m.end;Q++){var J=r[Q];if(V=X[X.length-1],i.set(J,(i.get(J)||0)+1),J&f&&i.set(f,(i.get(f)||0)+1),J&g)if(J&(z|N)){l[Q]=V._level;var ee=(J===z?W:H)(V._level);!(ee<=125)||$||K?!$&&K++:X.push({_level:ee,_override:0,_isolate:0})}else if(J&(D|R)){l[Q]=V._level;var et=(J===D?W:H)(V._level);!(et<=125)||$||K?!$&&K++:X.push({_level:et,_override:J&D?x:_,_isolate:0})}else if(J&c){J&q&&(J=1===tS(Q+1,!0)?B:U),l[Q]=V._level,V._override&&a(Q,V._override);var en=(J===B?W:H)(V._level);en<=125&&0===$&&0===K?(Z++,X.push({_level:en,_override:0,_isolate:1,_isolInitIndex:Q})):$++}else if(J&Y){if($>0)$--;else if(Z>0){for(K=0;!X[X.length-1]._isolate;)X.pop();var er=X[X.length-1]._isolInitIndex;null!=er&&(s.set(er,Q),s.set(Q,er)),X.pop(),Z--}V=X[X.length-1],l[Q]=V._level,V._override&&a(Q,V._override)}else J&F?(0===$&&(K>0?K--:!V._isolate&&X.length>1&&(X.pop(),V=X[X.length-1])),l[Q]=V._level):J&A&&(l[Q]=m.level);else l[Q]=V._level,V._override&&J!==I&&a(Q,V._override)}for(var eo=[],ei=null,ea=m.start;ea<=m.end;ea++){var el=r[ea];if(!(el&d)){var es=l[ea],ec=el&c,eu=el===Y;ei&&es===ei._level?(ei._end=ea,ei._endsWithIsolInit=ec):eo.push(ei={_start:ea,_end:ea,_level:es,_startsWithPDI:eu,_endsWithIsolInit:ec})}}for(var ef=[],ed=0;ed<eo.length;ed++){var ep=eo[ed];if(!ep._startsWithPDI||ep._startsWithPDI&&!s.has(ep._start)){for(var ev=[ei=ep],eh=void 0;ei&&ei._endsWithIsolInit&&null!=(eh=s.get(ei._end));)for(var em=ed+1;em<eo.length;em++)if(eo[em]._start===eh){ev.push(ei=eo[em]);break}for(var eb=[],eg=0;eg<ev.length;eg++)for(var ey=ev[eg],ew=ey._start;ew<=ey._end;ew++)eb.push(ew);for(var eE=l[eb[0]],e_=m.level,ex=eb[0]-1;ex>=0;ex--)if(!(r[ex]&d)){e_=l[ex];break}var ej=eb[eb.length-1],eM=l[ej],ek=m.level;if(!(r[ej]&c)){for(var eT=ej+1;eT<=m.end;eT++)if(!(r[eT]&d)){ek=l[eT];break}}ef.push({_seqIndices:eb,_sosType:Math.max(e_,eE)%2?x:_,_eosType:Math.max(ek,eM)%2?x:_})}}for(var eP=0;eP<ef.length;eP++){var eA=ef[eP],eC=eA._seqIndices,eS=eA._sosType,eI=eA._eosType,eO=1&l[eC[0]]?x:_;if(i.get(O))for(var eL=0;eL<eC.length;eL++){var eR=eC[eL];if(r[eR]&O){for(var eD=eS,eN=eL-1;eN>=0;eN--)if(!(r[eC[eN]]&d)){eD=r[eC[eN]];break}a(eR,eD&(c|Y)?S:eD)}}if(i.get(j))for(var ez=0;ez<eC.length;ez++){var eF=eC[ez];if(r[eF]&j)for(var eU=ez-1;eU>=-1;eU--){var eB=-1===eU?eS:r[eC[eU]];if(eB&u){eB===L&&a(eF,T);break}}}if(i.get(L))for(var eq=0;eq<eC.length;eq++){var eY=eC[eq];r[eY]&L&&a(eY,x)}if(i.get(M)||i.get(P))for(var eH=1;eH<eC.length-1;eH++){var eW=eC[eH];if(r[eW]&(M|P)){for(var eG=0,eX=0,eV=eH-1;eV>=0&&(eG=r[eC[eV]])&d;eV--);for(var e$=eH+1;e$<eC.length&&(eX=r[eC[e$]])&d;e$++);eG===eX&&(r[eW]===M?eG===j:eG&(j|T))&&a(eW,eG)}}if(i.get(j)){for(var eK=0;eK<eC.length;eK++)if(r[eC[eK]]&j){for(var eZ=eK-1;eZ>=0&&r[eC[eZ]]&(k|d);eZ--)a(eC[eZ],j);for(eK++;eK<eC.length&&r[eC[eK]]&(k|d|j);eK++)r[eC[eK]]!==j&&a(eC[eK],j)}}if(i.get(k)||i.get(M)||i.get(P))for(var eQ=0;eQ<eC.length;eQ++){var eJ=eC[eQ];if(r[eJ]&(k|M|P)){a(eJ,S);for(var e1=eQ-1;e1>=0&&r[eC[e1]]&d;e1--)a(eC[e1],S);for(var e0=eQ+1;e0<eC.length&&r[eC[e0]]&d;e0++)a(eC[e0],S)}}if(i.get(j))for(var e2=0,e3=eS;e2<eC.length;e2++){var e5=eC[e2],e4=r[e5];e4&j?e3===_&&a(e5,_):e4&u&&(e3=e4)}if(i.get(f)){for(var e6=x|j|T,e8=e6|_,e7=[],e9=[],te=0;te<eC.length;te++)if(r[eC[te]]&f){var tt=t[eC[te]],tn=void 0;if(null!==y(tt))if(e9.length<63)e9.push({char:tt,seqIndex:te});else break;else if(null!==(tn=w(tt)))for(var tr=e9.length-1;tr>=0;tr--){var to=e9[tr].char;if(to===tn||to===w(E(tt))||y(E(to))===tt){e7.push([e9[tr].seqIndex,te]),e9.length=tr;break}}}e7.sort(function(t,n){return t[0]-n[0]});for(var ti=0;ti<e7.length;ti++){for(var ta=e7[ti],tl=ta[0],ts=ta[1],tc=!1,tu=0,tf=tl+1;tf<ts;tf++){var td=eC[tf];if(r[td]&e8){tc=!0;var tp=r[td]&e6?x:_;if(tp===eO){tu=tp;break}}}if(tc&&!tu){tu=eS;for(var tv=tl-1;tv>=0;tv--){var th=eC[tv];if(r[th]&e8){var tm=r[th]&e6?x:_;tu=tm!==eO?tm:eO;break}}}if(tu){if(r[eC[tl]]=r[eC[ts]]=tu,tu!==eO){for(var tb=tl+1;tb<eC.length;tb++)if(!(r[eC[tb]]&d)){h(t[eC[tb]])&O&&(r[eC[tb]]=tu);break}}if(tu!==eO){for(var tg=ts+1;tg<eC.length;tg++)if(!(r[eC[tg]]&d)){h(t[eC[tg]])&O&&(r[eC[tg]]=tu);break}}}}for(var ty=0;ty<eC.length;ty++)if(r[eC[ty]]&f){for(var tw=ty,tE=ty,t_=eS,tx=ty-1;tx>=0;tx--)if(r[eC[tx]]&d)tw=tx;else{t_=r[eC[tx]]&e6?x:_;break}for(var tj=eI,tM=ty+1;tM<eC.length;tM++)if(r[eC[tM]]&(f|d))tE=tM;else{tj=r[eC[tM]]&e6?x:_;break}for(var tk=tw;tk<=tE;tk++)r[eC[tk]]=t_===tj?t_:eO;ty=tE}}}for(var tT=m.start;tT<=m.end;tT++){var tP=l[tT],tA=r[tT];if(1&tP?tA&(_|j|T)&&l[tT]++:tA&x?l[tT]++:tA&(T|j)&&(l[tT]+=2),tA&d&&(l[tT]=0===tT?m.level:l[tT-1]),tT===m.end||h(t[tT])&(C|A))for(var tC=tT;tC>=0&&h(t[tC])&p;tC--)l[tC]=m.level}}return{levels:l,paragraphs:v};function tS(n,o){for(var i=n;i<t.length;i++){var a=r[i];if(a&(x|L))return 1;if(a&(A|_)||o&&a===Y)break;if(a&c){var l=function(n){for(var o=1,i=n+1;i<t.length;i++){var a=r[i];if(a&A)break;if(a&Y){if(0==--o)return i}else a&c&&o++}return -1}(i);i=-1===l?t.length:l}}return 0}},t.getMirroredCharacter=H,t.getMirroredCharactersMap=function(t,n,r,o){var i=t.length;r=Math.max(0,null==r?0:+r),o=Math.min(i-1,null==o?i-1:+o);for(var a=new Map,l=r;l<=o;l++)if(1&n[l]){var s=H(t[l]);null!==s&&a.set(l,s)}return a},t.getReorderSegments=W,t.getReorderedIndices=G,t.getReorderedString=function(t,n,r,o){var i=G(t,n,r,o),a=[].concat(t);return i.forEach(function(r,o){a[o]=(1&n.levels[r]?H(t[r]):null)||t[r]}),a.join("")},t.openingToClosingBracket=y,Object.defineProperty(t,"__esModule",{value:!0}),t}({})}},5643:(t,n,r)=>{t.exports=r(6115)},6115:(t,n,r)=>{var o=r(2115),i=r(9033),a="function"==typeof Object.is?Object.is:function(t,n){return t===n&&(0!==t||1/t==1/n)||t!=t&&n!=n},l=i.useSyncExternalStore,s=o.useRef,c=o.useEffect,u=o.useMemo,f=o.useDebugValue;n.useSyncExternalStoreWithSelector=function(t,n,r,o,i){var d=s(null);if(null===d.current){var p={hasValue:!1,value:null};d.current=p}else p=d.current;var v=l(t,(d=u(function(){function t(t){if(!c){if(c=!0,l=t,t=o(t),void 0!==i&&p.hasValue){var n=p.value;if(i(n,t))return s=n}return s=t}if(n=s,a(l,t))return n;var r=o(t);return void 0!==i&&i(n,r)?(l=t,n):(l=t,s=r)}var l,s,c=!1,u=void 0===r?null:r;return[function(){return t(n())},null===u?void 0:function(){return t(u())}]},[n,r,o,i]))[0],d[1]);return c(function(){p.hasValue=!0,p.value=v},[v]),f(v),v}},6354:(t,n,r)=>{r.d(n,{Af:()=>s,Nz:()=>i,u5:()=>c,y3:()=>d});var o=r(2115);function i(t,n,r){if(!t)return;if(!0===r(t))return t;let o=n?t.return:t.child;for(;o;){let t=i(o,n,r);if(t)return t;o=n?null:o.sibling}}function a(t){try{return Object.defineProperties(t,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(n){return t}}(()=>{var t,n;return"undefined"!=typeof window&&((null==(t=window.document)?void 0:t.createElement)||(null==(n=window.navigator)?void 0:n.product)==="ReactNative")})()?o.useLayoutEffect:o.useEffect;let l=a(o.createContext(null));class s extends o.Component{render(){return o.createElement(l.Provider,{value:this._reactInternals},this.props.children)}}function c(){let t=o.useContext(l);if(null===t)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let n=o.useId();return o.useMemo(()=>{for(let r of[t,null==t?void 0:t.alternate]){if(!r)continue;let t=i(r,!1,t=>{let r=t.memoizedState;for(;r;){if(r.memoizedState===n)return!0;r=r.next}});if(t)return t}},[t,n])}let u=Symbol.for("react.context"),f=t=>null!==t&&"object"==typeof t&&"$$typeof"in t&&t.$$typeof===u;function d(){let t=function(){let t=c(),[n]=o.useState(()=>new Map);n.clear();let r=t;for(;r;){let t=r.type;f(t)&&t!==l&&!n.has(t)&&n.set(t,o.use(a(t))),r=r.return}return n}();return o.useMemo(()=>Array.from(t.keys()).reduce((n,r)=>i=>o.createElement(n,null,o.createElement(r.Provider,{...i,value:t.get(r)})),t=>o.createElement(s,{...t})),[t])}},6500:(t,n)=>{n.ConcurrentRoot=1,n.ContinuousEventPriority=8,n.DefaultEventPriority=32,n.DiscreteEventPriority=2},6670:(t,n,r)=>{r.d(n,{jI:()=>G});var o=r(2115);function i(){return(i=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t}).apply(this,arguments)}function a(t,n){if(null==t)return{};var r,o,i={},a=Object.keys(t);for(o=0;o<a.length;o++)n.indexOf(r=a[o])>=0||(i[r]=t[r]);return i}function l(t){var n=(0,o.useRef)(t),r=(0,o.useRef)(function(t){n.current&&n.current(t)});return n.current=t,r.current}var s=function(t,n,r){return void 0===n&&(n=0),void 0===r&&(r=1),t>r?r:t<n?n:t},c=function(t){return"touches"in t},u=function(t){return t&&t.ownerDocument.defaultView||self},f=function(t,n,r){var o=t.getBoundingClientRect(),i=c(n)?function(t,n){for(var r=0;r<t.length;r++)if(t[r].identifier===n)return t[r];return t[0]}(n.touches,r):n;return{left:s((i.pageX-(o.left+u(t).pageXOffset))/o.width),top:s((i.pageY-(o.top+u(t).pageYOffset))/o.height)}},d=function(t){c(t)||t.preventDefault()},p=o.memo(function(t){var n=t.onMove,r=t.onKey,s=a(t,["onMove","onKey"]),p=(0,o.useRef)(null),v=l(n),h=l(r),m=(0,o.useRef)(null),b=(0,o.useRef)(!1),g=(0,o.useMemo)(function(){var t=function(t){d(t),(c(t)?t.touches.length>0:t.buttons>0)&&p.current?v(f(p.current,t,m.current)):r(!1)},n=function(){return r(!1)};function r(r){var o=b.current,i=u(p.current),a=r?i.addEventListener:i.removeEventListener;a(o?"touchmove":"mousemove",t),a(o?"touchend":"mouseup",n)}return[function(t){var n=t.nativeEvent,o=p.current;if(o&&(d(n),(!b.current||c(n))&&o)){if(c(n)){b.current=!0;var i=n.changedTouches||[];i.length&&(m.current=i[0].identifier)}o.focus(),v(f(o,n,m.current)),r(!0)}},function(t){var n=t.which||t.keyCode;n<37||n>40||(t.preventDefault(),h({left:39===n?.05:37===n?-.05:0,top:40===n?.05:38===n?-.05:0}))},r]},[h,v]),y=g[0],w=g[1],E=g[2];return(0,o.useEffect)(function(){return E},[E]),o.createElement("div",i({},s,{onTouchStart:y,onMouseDown:y,className:"react-colorful__interactive",ref:p,onKeyDown:w,tabIndex:0,role:"slider"}))}),v=function(t){return t.filter(Boolean).join(" ")},h=function(t){var n=t.color,r=t.left,i=t.top,a=v(["react-colorful__pointer",t.className]);return o.createElement("div",{className:a,style:{top:100*(void 0===i?.5:i)+"%",left:100*r+"%"}},o.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:n}}))},m=function(t,n,r){return void 0===n&&(n=0),void 0===r&&(r=Math.pow(10,n)),Math.round(r*t)/r},b={grad:.9,turn:360,rad:360/(2*Math.PI)},g=function(t){return I(y(t))},y=function(t){return"#"===t[0]&&(t=t.substring(1)),t.length<6?{r:parseInt(t[0]+t[0],16),g:parseInt(t[1]+t[1],16),b:parseInt(t[2]+t[2],16),a:4===t.length?m(parseInt(t[3]+t[3],16)/255,2):1}:{r:parseInt(t.substring(0,2),16),g:parseInt(t.substring(2,4),16),b:parseInt(t.substring(4,6),16),a:8===t.length?m(parseInt(t.substring(6,8),16)/255,2):1}},w=function(t,n){return void 0===n&&(n="deg"),Number(t)*(b[n]||1)},E=function(t){var n=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(t);return n?_({h:w(n[1],n[2]),s:Number(n[3]),l:Number(n[4]),a:void 0===n[5]?1:Number(n[5])/(n[6]?100:1)}):{h:0,s:0,v:0,a:1}},_=function(t){var n=t.s,r=t.l;return{h:t.h,s:(n*=(r<50?r:100-r)/100)>0?2*n/(r+n)*100:0,v:r+n,a:t.a}},x=function(t){return S(T(t))},j=function(t){var n=t.s,r=t.v,o=t.a,i=(200-n)*r/100;return{h:m(t.h),s:m(i>0&&i<200?n*r/100/(i<=100?i:200-i)*100:0),l:m(i/2),a:m(o,2)}},M=function(t){var n=j(t);return"hsl("+n.h+", "+n.s+"%, "+n.l+"%)"},k=function(t){var n=j(t);return"hsla("+n.h+", "+n.s+"%, "+n.l+"%, "+n.a+")"},T=function(t){var n=t.h,r=t.s,o=t.v,i=t.a;n=n/360*6,r/=100,o/=100;var a=Math.floor(n),l=o*(1-r),s=o*(1-(n-a)*r),c=o*(1-(1-n+a)*r),u=a%6;return{r:m(255*[o,s,l,l,c,o][u]),g:m(255*[c,o,o,s,l,l][u]),b:m(255*[l,l,c,o,o,s][u]),a:m(i,2)}},P=function(t){var n=/hsva?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(t);return n?O({h:w(n[1],n[2]),s:Number(n[3]),v:Number(n[4]),a:void 0===n[5]?1:Number(n[5])/(n[6]?100:1)}):{h:0,s:0,v:0,a:1}},A=function(t){var n=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(t);return n?I({r:Number(n[1])/(n[2]?100/255:1),g:Number(n[3])/(n[4]?100/255:1),b:Number(n[5])/(n[6]?100/255:1),a:void 0===n[7]?1:Number(n[7])/(n[8]?100:1)}):{h:0,s:0,v:0,a:1}},C=function(t){var n=t.toString(16);return n.length<2?"0"+n:n},S=function(t){var n=t.r,r=t.g,o=t.b,i=t.a,a=i<1?C(m(255*i)):"";return"#"+C(n)+C(r)+C(o)+a},I=function(t){var n=t.r,r=t.g,o=t.b,i=t.a,a=Math.max(n,r,o),l=a-Math.min(n,r,o),s=l?a===n?(r-o)/l:a===r?2+(o-n)/l:4+(n-r)/l:0;return{h:m(60*(s<0?s+6:s)),s:m(a?l/a*100:0),v:m(a/255*100),a:i}},O=function(t){return{h:m(t.h),s:m(t.s),v:m(t.v),a:m(t.a,2)}},L=o.memo(function(t){var n=t.hue,r=t.onChange,i=v(["react-colorful__hue",t.className]);return o.createElement("div",{className:i},o.createElement(p,{onMove:function(t){r({h:360*t.left})},onKey:function(t){r({h:s(n+360*t.left,0,360)})},"aria-label":"Hue","aria-valuenow":m(n),"aria-valuemax":"360","aria-valuemin":"0"},o.createElement(h,{className:"react-colorful__hue-pointer",left:n/360,color:M({h:n,s:100,v:100,a:1})})))}),R=o.memo(function(t){var n=t.hsva,r=t.onChange,i={backgroundColor:M({h:n.h,s:100,v:100,a:1})};return o.createElement("div",{className:"react-colorful__saturation",style:i},o.createElement(p,{onMove:function(t){r({s:100*t.left,v:100-100*t.top})},onKey:function(t){r({s:s(n.s+100*t.left,0,100),v:s(n.v-100*t.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+m(n.s)+"%, Brightness "+m(n.v)+"%"},o.createElement(h,{className:"react-colorful__saturation-pointer",top:1-n.v/100,left:n.s/100,color:M(n)})))}),D=function(t,n){if(t===n)return!0;for(var r in t)if(t[r]!==n[r])return!1;return!0},N=function(t,n){return t.replace(/\s/g,"")===n.replace(/\s/g,"")},z=function(t,n){return t.toLowerCase()===n.toLowerCase()||D(y(t),y(n))};function F(t,n,r){var i=l(r),a=(0,o.useState)(function(){return t.toHsva(n)}),s=a[0],c=a[1],u=(0,o.useRef)({color:n,hsva:s});return(0,o.useEffect)(function(){if(!t.equal(n,u.current.color)){var r=t.toHsva(n);u.current={hsva:r,color:n},c(r)}},[n,t]),(0,o.useEffect)(function(){var n;D(s,u.current.hsva)||t.equal(n=t.fromHsva(s),u.current.color)||(u.current={hsva:s,color:n},i(n))},[s,t,i]),[s,(0,o.useCallback)(function(t){c(function(n){return Object.assign({},n,t)})},[])]}var U,B="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,q=new Map,Y=function(t){B(function(){var n=t.current?t.current.ownerDocument:document;if(void 0!==n&&!q.has(n)){var o=n.createElement("style");o.innerHTML='.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}',q.set(n,o);var i=U||r.nc;i&&o.setAttribute("nonce",i),n.head.appendChild(o)}},[])},H=function(t){var n=t.className,r=t.colorModel,l=t.color,s=void 0===l?r.defaultColor:l,c=t.onChange,u=a(t,["className","colorModel","color","onChange"]),f=(0,o.useRef)(null);Y(f);var d=F(r,s,c),p=d[0],h=d[1],m=v(["react-colorful",n]);return o.createElement("div",i({},u,{ref:f,className:m}),o.createElement(R,{hsva:p,onChange:h}),o.createElement(L,{hue:p.h,onChange:h,className:"react-colorful__last-control"}))},W={defaultColor:"000",toHsva:g,fromHsva:function(t){return x({h:t.h,s:t.s,v:t.v,a:1})},equal:z},G=function(t){return o.createElement(H,i({},t,{colorModel:W}))},X=function(t){var n=t.className,r=t.hsva,o=t.onChange,i={backgroundImage:"linear-gradient(90deg, "+k(Object.assign({},r,{a:0}))+", "+k(Object.assign({},r,{a:1}))+")"},a=v(["react-colorful__alpha",n]),l=m(100*r.a);return e.createElement("div",{className:a},e.createElement("div",{className:"react-colorful__alpha-gradient",style:i}),e.createElement(p,{onMove:function(t){o({a:t.left})},onKey:function(t){o({a:s(r.a+t.left)})},"aria-label":"Alpha","aria-valuetext":l+"%","aria-valuenow":l,"aria-valuemin":"0","aria-valuemax":"100"},e.createElement(h,{className:"react-colorful__alpha-pointer",left:r.a,color:k(r)})))}},7319:(t,n)=>{function r(t,n){var r=t.length;for(t.push(n);0<r;){var o=r-1>>>1,i=t[o];if(0<a(i,n))t[o]=n,t[r]=i,r=o;else break}}function o(t){return 0===t.length?null:t[0]}function i(t){if(0===t.length)return null;var n=t[0],r=t.pop();if(r!==n){t[0]=r;for(var o=0,i=t.length,l=i>>>1;o<l;){var s=2*(o+1)-1,c=t[s],u=s+1,f=t[u];if(0>a(c,r))u<i&&0>a(f,c)?(t[o]=f,t[u]=r,o=u):(t[o]=c,t[s]=r,o=s);else if(u<i&&0>a(f,r))t[o]=f,t[u]=r,o=u;else break}}return n}function a(t,n){var r=t.sortIndex-n.sortIndex;return 0!==r?r:t.id-n.id}if(n.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l,s=performance;n.unstable_now=function(){return s.now()}}else{var c=Date,u=c.now();n.unstable_now=function(){return c.now()-u}}var f=[],d=[],p=1,v=null,h=3,m=!1,b=!1,g=!1,y="function"==typeof setTimeout?setTimeout:null,w="function"==typeof clearTimeout?clearTimeout:null,E="undefined"!=typeof setImmediate?setImmediate:null;function _(t){for(var n=o(d);null!==n;){if(null===n.callback)i(d);else if(n.startTime<=t)i(d),n.sortIndex=n.expirationTime,r(f,n);else break;n=o(d)}}function x(t){if(g=!1,_(t),!b)if(null!==o(f))b=!0,I();else{var n=o(d);null!==n&&O(x,n.startTime-t)}}var j=!1,M=-1,k=5,T=-1;function P(){return!(n.unstable_now()-T<k)}function A(){if(j){var t=n.unstable_now();T=t;var r=!0;try{e:{b=!1,g&&(g=!1,w(M),M=-1),m=!0;var a=h;try{t:{for(_(t),v=o(f);null!==v&&!(v.expirationTime>t&&P());){var s=v.callback;if("function"==typeof s){v.callback=null,h=v.priorityLevel;var c=s(v.expirationTime<=t);if(t=n.unstable_now(),"function"==typeof c){v.callback=c,_(t),r=!0;break t}v===o(f)&&i(f),_(t)}else i(f);v=o(f)}if(null!==v)r=!0;else{var u=o(d);null!==u&&O(x,u.startTime-t),r=!1}}break e}finally{v=null,h=a,m=!1}}}finally{r?l():j=!1}}}if("function"==typeof E)l=function(){E(A)};else if("undefined"!=typeof MessageChannel){var C=new MessageChannel,S=C.port2;C.port1.onmessage=A,l=function(){S.postMessage(null)}}else l=function(){y(A,0)};function I(){j||(j=!0,l())}function O(t,r){M=y(function(){t(n.unstable_now())},r)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(t){t.callback=null},n.unstable_continueExecution=function(){b||m||(b=!0,I())},n.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):k=0<t?Math.floor(1e3/t):5},n.unstable_getCurrentPriorityLevel=function(){return h},n.unstable_getFirstCallbackNode=function(){return o(f)},n.unstable_next=function(t){switch(h){case 1:case 2:case 3:var n=3;break;default:n=h}var r=h;h=n;try{return t()}finally{h=r}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(t,n){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var r=h;h=t;try{return n()}finally{h=r}},n.unstable_scheduleCallback=function(t,i,a){var l=n.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?l+a:l,t){case 1:var s=-1;break;case 2:s=250;break;case 5:s=0x3fffffff;break;case 4:s=1e4;break;default:s=5e3}return s=a+s,t={id:p++,callback:i,priorityLevel:t,startTime:a,expirationTime:s,sortIndex:-1},a>l?(t.sortIndex=a,r(d,t),null===o(f)&&t===o(d)&&(g?(w(M),M=-1):g=!0,O(x,a-l))):(t.sortIndex=s,r(f,t),b||m||(b=!0,I())),t},n.unstable_shouldYield=P,n.unstable_wrapCallback=function(t){var n=h;return function(){var r=h;h=n;try{return t.apply(this,arguments)}finally{h=r}}}},7558:(t,n,r)=>{r.d(n,{Hl:()=>p});var o=r(544),i=r(2115),a=r(7431);function l(t,n){let r;return(...o)=>{window.clearTimeout(r),r=window.setTimeout(()=>t(...o),n)}}let s=["x","y","top","bottom","left","right","width","height"],c=(t,n)=>s.every(r=>t[r]===n[r]);var u=r(6354),f=r(5155);function d({ref:t,children:n,fallback:r,resize:s,style:u,gl:d,events:p=o.f,eventSource:v,eventPrefix:h,shadows:m,linear:b,flat:g,legacy:y,orthographic:w,frameloop:E,dpr:_,performance:x,raycaster:j,camera:M,scene:k,onPointerMissed:T,onCreated:P,...A}){i.useMemo(()=>(0,o.e)(a),[]);let C=(0,o.u)(),[S,I]=function({debounce:t,scroll:n,polyfill:r,offsetSize:o}={debounce:0,scroll:!1,offsetSize:!1}){var a,s,u;let f=r||("undefined"==typeof window?class{}:window.ResizeObserver);if(!f)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[d,p]=(0,i.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),v=(0,i.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:d,orientationHandler:null}),h=t?"number"==typeof t?t:t.scroll:null,m=t?"number"==typeof t?t:t.resize:null,b=(0,i.useRef)(!1);(0,i.useEffect)(()=>(b.current=!0,()=>void(b.current=!1)));let[g,y,w]=(0,i.useMemo)(()=>{let t=()=>{if(!v.current.element)return;let{left:t,top:n,width:r,height:i,bottom:a,right:l,x:s,y:u}=v.current.element.getBoundingClientRect(),f={left:t,top:n,width:r,height:i,bottom:a,right:l,x:s,y:u};v.current.element instanceof HTMLElement&&o&&(f.height=v.current.element.offsetHeight,f.width=v.current.element.offsetWidth),Object.freeze(f),b.current&&!c(v.current.lastBounds,f)&&p(v.current.lastBounds=f)};return[t,m?l(t,m):t,h?l(t,h):t]},[p,o,h,m]);function E(){v.current.scrollContainers&&(v.current.scrollContainers.forEach(t=>t.removeEventListener("scroll",w,!0)),v.current.scrollContainers=null),v.current.resizeObserver&&(v.current.resizeObserver.disconnect(),v.current.resizeObserver=null),v.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",v.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",v.current.orientationHandler))}function _(){v.current.element&&(v.current.resizeObserver=new f(w),v.current.resizeObserver.observe(v.current.element),n&&v.current.scrollContainers&&v.current.scrollContainers.forEach(t=>t.addEventListener("scroll",w,{capture:!0,passive:!0})),v.current.orientationHandler=()=>{w()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",v.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",v.current.orientationHandler))}return a=w,s=!!n,(0,i.useEffect)(()=>{if(s)return window.addEventListener("scroll",a,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",a,!0)},[a,s]),u=y,(0,i.useEffect)(()=>(window.addEventListener("resize",u),()=>void window.removeEventListener("resize",u)),[u]),(0,i.useEffect)(()=>{E(),_()},[n,w,y]),(0,i.useEffect)(()=>E,[]),[t=>{t&&t!==v.current.element&&(E(),v.current.element=t,v.current.scrollContainers=function t(n){let r=[];if(!n||n===document.body)return r;let{overflow:o,overflowX:i,overflowY:a}=window.getComputedStyle(n);return[o,i,a].some(t=>"auto"===t||"scroll"===t)&&r.push(n),[...r,...t(n.parentElement)]}(t),_())},d,g]}({scroll:!0,debounce:{scroll:50,resize:0},...s}),O=i.useRef(null),L=i.useRef(null);i.useImperativeHandle(t,()=>O.current);let R=(0,o.a)(T),[D,N]=i.useState(!1),[z,F]=i.useState(!1);if(D)throw D;if(z)throw z;let U=i.useRef(null);(0,o.b)(()=>{let t=O.current;I.width>0&&I.height>0&&t&&(U.current||(U.current=(0,o.c)(t)),async function(){await U.current.configure({gl:d,scene:k,events:p,shadows:m,linear:b,flat:g,legacy:y,orthographic:w,frameloop:E,dpr:_,performance:x,raycaster:j,camera:M,size:I,onPointerMissed:(...t)=>null==R.current?void 0:R.current(...t),onCreated:t=>{null==t.events.connect||t.events.connect(v?(0,o.i)(v)?v.current:v:L.current),h&&t.setEvents({compute:(t,n)=>{let r=t[h+"X"],o=t[h+"Y"];n.pointer.set(r/n.size.width*2-1,-(2*(o/n.size.height))+1),n.raycaster.setFromCamera(n.pointer,n.camera)}}),null==P||P(t)}}),U.current.render((0,f.jsx)(C,{children:(0,f.jsx)(o.E,{set:F,children:(0,f.jsx)(i.Suspense,{fallback:(0,f.jsx)(o.B,{set:N}),children:null!=n?n:null})})}))}())}),i.useEffect(()=>{let t=O.current;if(t)return()=>(0,o.d)(t)},[]);let B=v?"none":"auto";return(0,f.jsx)("div",{ref:L,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:B,...u},...A,children:(0,f.jsx)("div",{ref:S,style:{width:"100%",height:"100%"},children:(0,f.jsx)("canvas",{ref:O,style:{display:"block"},children:r})})})}function p(t){return(0,f.jsx)(u.Af,{children:(0,f.jsx)(d,{...t})})}r(1933),r(5220),r(4342)},8247:(t,n,r)=>{t.exports=r(620)},8842:(t,n,r)=>{r.d(n,{E:()=>c});var o=r(9630),i=r(2115),a=r(1966),l=r(544),s=r(228);let c=i.forwardRef(({sdfGlyphSize:t=64,anchorX:n="center",anchorY:r="middle",font:c,fontSize:u=1,children:f,characters:d,onSync:p,...v},h)=>{let m=(0,l.A)(({invalidate:t})=>t),[b]=i.useState(()=>new a.EY),[g,y]=i.useMemo(()=>{let t=[],n="";return i.Children.forEach(f,r=>{"string"==typeof r||"number"==typeof r?n+=r:t.push(r)}),[t,n]},[f]);return(0,s.DY)(()=>new Promise(t=>(0,a.PY)({font:c,characters:d},t)),["troika-text",c,d]),i.useLayoutEffect(()=>void b.sync(()=>{m(),p&&p(b)})),i.useEffect(()=>()=>b.dispose(),[b]),i.createElement("primitive",(0,o.A)({object:b,ref:h,font:c,text:y,anchorX:n,anchorY:r,fontSize:u,sdfGlyphSize:t},v),g)})},9033:(t,n,r)=>{t.exports=r(2436)},9630:(t,n,r)=>{r.d(n,{A:()=>o});function o(){return(o=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var o in r)({}).hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t}).apply(null,arguments)}}}]);