"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{2283:(e,s,l)=>{l.r(s),l.d(s,{default:()=>i});var a=l(5155),t=l(2115),r=l(9946);let c=(0,r.A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]]),o=(0,r.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),i=e=>{let{productImage:s,onCustomizationChange:l,initialCustomization:r}=e,[i,n]=(0,t.useState)(""),[d,x]=(0,t.useState)("#000000"),[u,m]=(0,t.useState)(20),[h,p]=(0,t.useState)("Arial"),b=()=>{l({canvasData:JSON.stringify({text:i,textColor:d,fontSize:u,fontFamily:h,type:"basic"}),preview:i?"data:text/plain;base64,".concat(btoa("".concat(i," (").concat(d,")"))):void 0})};return t.useEffect(()=>{i.trim()&&b()},[i,d,u,h]),t.useEffect(()=>{b()},[i,d,u,h]),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"\uD83C\uDFA8 Product Customizer"}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:"Customize your product with text and colors"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 text-xs",children:[(0,a.jsx)("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded-full",children:"✓ Auto-save enabled"}),(0,a.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-1 rounded-full",children:"✓ Real-time preview"}),(0,a.jsx)("span",{className:"bg-purple-100 text-purple-700 px-2 py-1 rounded-full",children:"✓ Ready to use"})]})]}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"text-blue-600 mt-1",children:"\uD83D\uDCA1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-800 mb-1",children:"Quick Start Guide"}),(0,a.jsxs)("p",{className:"text-sm text-blue-700 mb-2",children:["1. Enter your custom text below",(0,a.jsx)("br",{}),"2. Choose your preferred color and font",(0,a.jsx)("br",{}),"3. Your design will auto-save as you type",(0,a.jsx)("br",{}),'4. Click "Add to Cart" when you\'re happy with your design']})]})]})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"relative bg-gray-100 rounded-xl overflow-hidden",children:[(0,a.jsx)("img",{src:s,alt:"Product",className:"w-full h-64 object-contain",onError:e=>{e.target.src="/bottle-dummy.jpg"}}),i&&(0,a.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none",style:{color:d,fontSize:"".concat(u,"px"),fontFamily:h,textShadow:"1px 1px 2px rgba(0,0,0,0.5)"},children:i})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:[(0,a.jsx)(c,{className:"inline mr-2",size:16}),"Custom Text"]}),(0,a.jsx)("input",{type:"text",value:i,onChange:e=>n(e.target.value),placeholder:"Enter your custom text",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200",maxLength:30}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[i.length,"/30 characters"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:[(0,a.jsx)(o,{className:"inline mr-2",size:16}),"Text Color"]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("input",{type:"color",value:d,onChange:e=>x(e.target.value),className:"w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("div",{className:"text-sm font-mono text-gray-600 bg-gray-100 px-3 py-2 rounded-lg",children:d.toUpperCase()})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:["Font Size: ",u,"px"]}),(0,a.jsx)("input",{type:"range",min:"12",max:"48",value:u,onChange:e=>m(Number(e.target.value)),className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-700",children:"Font Family"}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100",children:[(0,a.jsx)("option",{value:"Arial",children:"Arial"}),(0,a.jsx)("option",{value:"Times New Roman",children:"Times New Roman"}),(0,a.jsx)("option",{value:"Helvetica",children:"Helvetica"}),(0,a.jsx)("option",{value:"Georgia",children:"Georgia"}),(0,a.jsx)("option",{value:"Verdana",children:"Verdana"}),(0,a.jsx)("option",{value:"Comic Sans MS",children:"Comic Sans MS"}),(0,a.jsx)("option",{value:"Impact",children:"Impact"})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-xl",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Customization Preview"}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Text:"})," ",i||"No text added"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Color:"})," ",d]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Font:"})," ",h," (",u,"px)"]})]})]})]}),(0,a.jsx)("div",{className:"mt-6 text-sm text-gray-500 text-center",children:"\uD83D\uDCA1 Tip: Your customization will be saved when you add the product to cart"})]})}}}]);