"use client";
import { useState } from "react";
import { login } from "@/api/auth";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";
import { Eye, EyeOff, AlertCircle } from "lucide-react";

// Zod validation schema for login
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [formData, setFormData] = useState<LoginFormData>({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<Partial<LoginFormData>>({});
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");
    setErrors({});

    // Validate form data with Zod
    const validation = loginSchema.safeParse(formData);
    if (!validation.success) {
      setLoading(false);
      const fieldErrors: Partial<LoginFormData> = {};
      validation.error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as keyof LoginFormData] = err.message;
        }
      });
      setErrors(fieldErrors);
      return;
    }

    try {
      const result = await login(formData.email, formData.password);
      setLoading(false);

      if (result.access_token) {
        localStorage.setItem("token", result.access_token);

        // Store user information including role
        if (result.user) {
          localStorage.setItem("user", JSON.stringify(result.user));
        }

        // Check if there's a redirect parameter in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const redirectPath = urlParams.get("redirect");

        // Check if user has admin role before redirecting to admin pages
        const isAdmin = result.user?.role === "ADMIN";
        if (redirectPath && redirectPath.startsWith("/admin/") && !isAdmin) {
          setError("You don't have permission to access the admin area");
          return;
        }

        // Redirect to the specified path or default to user dashboard
        window.location.href = redirectPath || "/user/dashboard";
      } else {
        setError(result.error || "Login failed");
      }
    } catch {
      setLoading(false);
      setError("Network error. Please try again.");
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100">
        <div className="flex flex-col items-center">
          <Image
            src="/6 Light Logo.png"
            alt="6 Light Logo"
            width={64}
            height={64}
            className="mb-2"
          />
        </div>
        <h2 className="text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Sign In
        </h2>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          {/* Email Input */}
          <div className="space-y-1">
            <input
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              placeholder="Email"
              type="email"
              className={`w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition ${
                errors.email
                  ? "border-red-500 focus:ring-red-300"
                  : "border-gray-300 focus:ring-blue-300"
              }`}
              autoComplete="email"
              aria-invalid={!!errors.email}
              aria-describedby={errors.email ? "email-error" : undefined}
            />
            {errors.email && (
              <div
                id="email-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.email}
              </div>
            )}
          </div>

          {/* Password Input */}
          <div className="space-y-1">
            <div className="relative">
              <input
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                className={`w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition ${
                  errors.password
                    ? "border-red-500 focus:ring-red-300"
                    : "border-gray-300 focus:ring-blue-300"
                }`}
                autoComplete="current-password"
                aria-invalid={!!errors.password}
                aria-describedby={
                  errors.password ? "password-error" : undefined
                }
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            {errors.password && (
              <div
                id="password-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.password}
              </div>
            )}
          </div>
          <button
            type="submit"
            className={`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${
              loading
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            }`}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Signing in...</span>
              </div>
            ) : (
              "Sign In"
            )}
          </button>

          {/* Global Error Display */}
          {error && (
            <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200">
              <AlertCircle size={16} />
              {error}
            </div>
          )}
        </form>
        <div className="text-center text-sm text-gray-600">
          Don&apos;t have an account?{" "}
          <Link
            href="/register"
            className="text-red-700 font-semibold hover:underline"
          >
            Register
          </Link>
        </div>
      </div>
    </div>
  );
}
