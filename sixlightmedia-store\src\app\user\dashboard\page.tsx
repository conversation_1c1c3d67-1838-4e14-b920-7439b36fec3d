import { requireAuth } from "@/lib/auth";
import UserDashboardClient from "./UserDashboardClient";

type User = {
  name?: string;
  email: string;
  profileImage?: string;
};

type Order = {
  id: number;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  customerAddress: string;
  customColor?: string;
  customText?: string;
  isCustomized: boolean;
  customizationData?: string;
  customizationPreview?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: string;
  createdAt: string;
  product: {
    id: number;
    name: string;
    price: number;
    image?: string;
  };
  customization?: {
    color?: string;
    text?: string;
  };
};

export default async function UserDashboard() {
  // Server-side authentication check - redirects if not authenticated
  const auth = await requireAuth();

  return <UserDashboardClient user={auth.user} />;
}
