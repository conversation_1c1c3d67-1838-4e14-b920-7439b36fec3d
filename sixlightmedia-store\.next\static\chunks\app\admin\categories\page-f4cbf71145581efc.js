(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[631],{1277:(e,t,r)=>{Promise.resolve().then(r.bind(r,7117))},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},7117:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(5155),s=r(2115),i=r(6874),o=r.n(i),n=r(4615),l=r(3389),d=r(3843);function c(){let[e,t]=(0,s.useState)([]),[r,i]=(0,s.useState)(""),[c,h]=(0,s.useState)(""),[x,m]=(0,s.useState)(0),[u,g]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!0),f=()=>{m(JSON.parse(localStorage.getItem("cart")||"[]").length)},v=()=>{g(!0)};return((0,s.useEffect)(()=>{f(),fetch((0,d.e9)(d.i3.ENDPOINTS.CATEGORIES)).then(e=>e.json()).then(e=>{t(e),b(!1)}).catch(()=>{t([]),b(!1)})},[]),p)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(n.A,{cartCount:x,onCartClick:v}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading categories..."})]})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(n.A,{cartCount:x,onCartClick:v}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,a.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Category"}),(0,a.jsx)("span",{className:"text-white",children:" Management"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto",children:"Organize your products with custom categories for better navigation and user experience"}),(0,a.jsxs)(o(),{href:"/admin/dashboard",className:"inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-lg text-white font-semibold rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Dashboard"]})]})})]}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"➕ Add New Category"}),(0,a.jsxs)("form",{onSubmit:function(e){e.preventDefault(),h(""),r.trim()&&fetch((0,d.e9)(d.i3.ENDPOINTS.CATEGORIES),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r.trim()})}).then(e=>{if(!e.ok)throw Error();return e.json()}).then(e=>{t(t=>[...t,e]),i("")}).catch(()=>h("Failed to add category"))},className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("input",{type:"text",value:r,onChange:e=>i(e.target.value),placeholder:"Enter category name...",className:"flex-1 px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",required:!0}),(0,a.jsx)("button",{type:"submit",className:"px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"Add Category"})]}),c&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-2xl",children:c})]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:["\uD83C\uDFF7️ Categories (",e.length,")"]})}),0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83C\uDFF7️"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No categories yet"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Add your first category to start organizing your products."})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>(0,a.jsx)("div",{className:"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold",children:e.name.charAt(0).toUpperCase()}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",e.id]})]})]}),(0,a.jsx)("button",{onClick:()=>{var r;return r=e.id,void fetch((0,d.e9)("".concat(d.i3.ENDPOINTS.CATEGORIES,"/").concat(r)),{method:"DELETE"}).then(e=>{if(!e.ok)throw Error();t(e=>e.filter(e=>e.id!==r))}).catch(()=>h("Failed to delete category"))},className:"p-2 text-red-500 hover:bg-red-50 rounded-xl transition-all duration-200 hover:scale-110",title:"Delete category",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},e.id))})]})]}),(0,a.jsx)(l.A,{isOpen:u,onClose:()=>{g(!1),f()}})]})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:h,iconNode:x,...m}=e;return(0,a.createElement)("svg",{ref:t,...d,width:s,height:s,stroke:r,strokeWidth:o?24*Number(i)/Number(s):i,className:n("lucide",c),...!h&&!l(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let r=(0,a.forwardRef)((r,i)=>{let{className:l,...d}=r;return(0,a.createElement)(c,{ref:i,iconNode:t,className:n("lucide-".concat(s(o(e))),"lucide-".concat(e),l),...d})});return r.displayName=o(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,211,441,684,358],()=>t(1277)),_N_E=e.O()}]);