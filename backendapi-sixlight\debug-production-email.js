// Production Email Debug Script
// Run this in production to test email configuration

console.log('🔍 Production Email Configuration Debug');
console.log('=====================================');

console.log('📧 Environment Variables:');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
console.log(`SMTP_HOST: ${process.env.SMTP_HOST}`);
console.log(`SMTP_PORT: ${process.env.SMTP_PORT}`);
console.log(`SMTP_USER: ${process.env.SMTP_USER}`);
console.log(`SMTP_PASS: ${process.env.SMTP_PASS ? '***SET***' : 'NOT SET'}`);
console.log(`EMAIL_VERIFICATION_EXPIRES_HOURS: ${process.env.EMAIL_VERIFICATION_EXPIRES_HOURS}`);
console.log(`PASSWORD_RESET_EXPIRES_HOURS: ${process.env.PASSWORD_RESET_EXPIRES_HOURS}`);

console.log('\n🔍 Checking for missing variables:');
const requiredVars = [
  'NODE_ENV',
  'FRONTEND_URL', 
  'SMTP_HOST',
  'SMTP_PORT',
  'SMTP_USER',
  'SMTP_PASS',
  'EMAIL_VERIFICATION_EXPIRES_HOURS',
  'PASSWORD_RESET_EXPIRES_HOURS'
];

const missing = requiredVars.filter(varName => !process.env[varName]);
if (missing.length > 0) {
  console.log('❌ Missing environment variables:');
  missing.forEach(varName => console.log(`   - ${varName}`));
} else {
  console.log('✅ All required environment variables are set');
}

console.log('\n🔍 Configuration Issues:');
if (process.env.FRONTEND_URL && process.env.FRONTEND_URL.includes('localhost')) {
  console.log('❌ FRONTEND_URL still points to localhost - this will not work in production!');
  console.log(`   Current: ${process.env.FRONTEND_URL}`);
  console.log('   Should be: https://your-production-domain.com');
}

if (process.env.NODE_ENV !== 'production') {
  console.log('⚠️  NODE_ENV is not set to "production"');
  console.log(`   Current: ${process.env.NODE_ENV}`);
}

console.log('\n📧 Email verification URL that will be generated:');
const sampleToken = 'sample-token-123';
const verificationUrl = `${process.env.FRONTEND_URL || 'FRONTEND_URL_NOT_SET'}/verify-email?token=${sampleToken}`;
console.log(`   ${verificationUrl}`);

if (verificationUrl.includes('localhost')) {
  console.log('❌ This URL will not work for users - they cannot access localhost!');
} else {
  console.log('✅ This URL looks correct for production');
}

console.log('\n🔧 Next Steps:');
console.log('1. Set FRONTEND_URL to your actual production domain');
console.log('2. Set NODE_ENV to "production"');
console.log('3. Ensure all SMTP variables are correctly set');
console.log('4. Test email sending after fixing these issues');
