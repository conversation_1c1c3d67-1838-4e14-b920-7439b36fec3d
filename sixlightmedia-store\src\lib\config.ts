/**
 * Configuration utilities for the Six Light Media Store
 * Handles environment-specific settings and API endpoints
 */

// Environment detection
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';

// API Configuration
export const API_CONFIG = {
  // Backend API URL with environment-specific defaults
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 
    (isProduction 
      ? 'https://backendapi-sixlight.onrender.com' 
      : 'http://localhost:3001'
    ),
  
  // API endpoints
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/auth/login',
      REGISTER: '/auth/register',
      REFRESH: '/auth/refresh',
    },
    PRODUCTS: '/product',
    CATEGORIES: '/categories',
    ORDERS: '/orders',
    ADMIN: {
      DASHBOARD: '/admin/dashboard',
      ORDERS: '/admin/orders',
      USERS: '/admin/users',
      PRODUCTS: '/admin/products',
      CATEGORIES: '/admin/categories',
    },
    USER: {
      DASHBOARD: '/user/dashboard',
      PROFILE: '/user/profile',
    },
  },
  
  // Request configuration
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
  
  // Timeout settings
  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev
};

// Site Configuration
export const SITE_CONFIG = {
  URL: process.env.NEXT_PUBLIC_SITE_URL || 
    (isProduction 
      ? 'https://yourdomain.com' 
      : 'http://localhost:3000'
    ),
  NAME: 'Six Light Media Store',
  DESCRIPTION: 'Premium custom products and personalized gifts',
  LOGO: '/6 Light Logo.png',
};

// ImageKit Configuration
export const IMAGEKIT_CONFIG = {
  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || '',
  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || '',
  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || '',
};

// Analytics Configuration
export const ANALYTICS_CONFIG = {
  GA_ID: process.env.NEXT_PUBLIC_GA_ID || '',
  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,
};

// Utility functions
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = API_CONFIG.BASE_URL.replace(/\/$/, ''); // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

export const getAuthHeaders = (): Record<string, string> => {
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
  return {
    ...API_CONFIG.DEFAULT_HEADERS,
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Environment validation
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!API_CONFIG.BASE_URL) {
    errors.push('NEXT_PUBLIC_API_URL is not configured');
  }
  
  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {
    errors.push('NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured');
  }
  
  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {
    errors.push('NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured');
  }
  
  if (isProduction && !SITE_CONFIG.URL.includes('yourdomain.com')) {
    // Only warn in production if still using placeholder
    if (SITE_CONFIG.URL.includes('yourdomain.com')) {
      errors.push('NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Debug information (development only)
export const getDebugInfo = () => {
  if (!isDevelopment) return null;
  
  return {
    environment: process.env.NODE_ENV,
    apiUrl: API_CONFIG.BASE_URL,
    siteUrl: SITE_CONFIG.URL,
    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,
    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,
  };
};
