{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACK;AAAtB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,uCAAgC,aAAa,OAAO,CAAC;IACvD,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport default function Header({\r\n  cartCount = 0,\r\n  onCartClick,\r\n}: {\r\n  cartCount?: number;\r\n  onCartClick?: () => void;\r\n}) {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n  const [userRole, setUserRole] = useState<string | null>(null);\r\n  const [profileImage, setProfileImage] = useState<string | null>(null);\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function handleProfileImageUpdated() {\r\n      const token = localStorage.getItem(\"token\");\r\n      if (token) {\r\n        try {\r\n          // Fetch latest user data from API instead of relying on JWT token\r\n          const response = await fetch(\r\n            getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD),\r\n            {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n          );\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data && data.user) {\r\n              setProfileImage(data.user.profileImage || null);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Failed to fetch updated profile image:\", error);\r\n          // Fallback to JWT token method\r\n          try {\r\n            const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n            setProfileImage(payload.profileImage || null);\r\n          } catch {\r\n            setProfileImage(null);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n      const token = localStorage.getItem(\"token\");\r\n      setIsLoggedIn(!!token);\r\n      if (token) {\r\n        // Decode JWT to get role (simple base64 decode, not secure for prod, but fine for client display)\r\n        try {\r\n          const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n          setUserRole(payload.role || null);\r\n          setProfileImage(payload.profileImage || null);\r\n        } catch {\r\n          setUserRole(null);\r\n          setProfileImage(null);\r\n        }\r\n      } else {\r\n        setUserRole(null);\r\n        setProfileImage(null);\r\n      }\r\n      // Listen for profile image update event\r\n      window.addEventListener(\"profileImageUpdated\", handleProfileImageUpdated);\r\n    }\r\n    // Cleanup event listener\r\n    return () => {\r\n      window.removeEventListener(\r\n        \"profileImageUpdated\",\r\n        handleProfileImageUpdated\r\n      );\r\n    };\r\n  }, []);\r\n\r\n  // Prevent background scroll and horizontal scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n      document.documentElement.style.overflowX = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close menus on Escape key\r\n  useEffect(() => {\r\n    function handleKeyDown(e: KeyboardEvent) {\r\n      if (e.key === \"Escape\") {\r\n        setShowDropdown(false);\r\n        setMobileMenuOpen(false);\r\n      }\r\n    }\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, []);\r\n\r\n  // Ensure dropdown closes when mobile menu opens\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) setShowDropdown(false);\r\n  }, [mobileMenuOpen]);\r\n\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"token\");\r\n    setIsLoggedIn(false);\r\n    window.location.href = \"/login\";\r\n  }\r\n\r\n  function handleDropdownToggle() {\r\n    setShowDropdown((prev) => !prev);\r\n  }\r\n\r\n  function handleDropdownClose() {\r\n    setShowDropdown(false);\r\n  }\r\n\r\n  return (\r\n    <header className=\"w-full bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-30\">\r\n      <nav className=\"max-w-7xl mx-auto flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 md:py-4\">\r\n        {/* Brand Logo */}\r\n        <Link href=\"/\" className=\"flex items-center gap-2 min-w-0 group\">\r\n          <div className=\"relative\">\r\n            <Image\r\n              src=\"/6 Light Logo.png\"\r\n              alt=\"Six Light Media Logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"h-8 w-auto sm:h-10 md:h-12 transition-transform duration-300 group-hover:scale-110\"\r\n              priority\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl\"></div>\r\n          </div>\r\n          <span className=\"truncate text-base xs:text-lg sm:text-2xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-indigo-500 group-hover:via-purple-500 group-hover:to-pink-500 transition-all duration-300\">\r\n            Store\r\n          </span>\r\n        </Link>\r\n        {/* Desktop Nav */}\r\n        <div className=\"hidden sm:flex gap-2 md:gap-4 lg:gap-6 items-center min-w-0\">\r\n          <Link\r\n            href=\"/\"\r\n            className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n          >\r\n            🏠 Home\r\n          </Link>\r\n          {isLoggedIn && (\r\n            <Link\r\n              href=\"/user/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n            >\r\n              📊 Dashboard\r\n            </Link>\r\n          )}\r\n          {isLoggedIn && userRole === \"ADMIN\" && (\r\n            <Link\r\n              href=\"/admin/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n            >\r\n              ⚡ Admin\r\n            </Link>\r\n          )}\r\n          {!isLoggedIn ? (\r\n            <>\r\n              <Link\r\n                href=\"/login\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                🔐 Login\r\n              </Link>\r\n              <Link\r\n                href=\"/register\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                ✨ Register\r\n              </Link>\r\n            </>\r\n          ) : (\r\n            <div className=\"relative min-w-0\">\r\n              <button\r\n                className=\"flex items-center gap-2 focus:outline-none min-w-0 px-2 py-1 rounded-2xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                onClick={handleDropdownToggle}\r\n                aria-label=\"Open user menu\"\r\n              >\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                    className=\"rounded-full border-2 border-gradient-to-r from-indigo-200 to-purple-200 object-cover bg-white h-8 w-8 md:h-9 md:w-9 shadow-md\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <span className=\"hidden md:inline font-semibold text-gray-700 truncate max-w-[80px]\">\r\n                  Account\r\n                </span>\r\n                <svg\r\n                  className={`w-4 h-4 ml-1 text-gray-600 transition-transform duration-300 ${\r\n                    showDropdown ? \"rotate-180\" : \"\"\r\n                  }`}\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M19 9l-7 7-7-7\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n              {showDropdown && (\r\n                <div\r\n                  className=\"absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-lg border border-gray-200 rounded-2xl shadow-2xl z-50 overflow-hidden\"\r\n                  onMouseLeave={handleDropdownClose}\r\n                >\r\n                  <div className=\"p-2\">\r\n                    <Link\r\n                      href=\"/user/dashboard\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">📊</span>\r\n                      Dashboard\r\n                    </Link>\r\n                    <Link\r\n                      href=\"/user/profile\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">⚙️</span>\r\n                      Profile Settings\r\n                    </Link>\r\n                    <div className=\"border-t border-gray-100 my-2\"></div>\r\n                    <button\r\n                      onClick={() => {\r\n                        handleLogout();\r\n                        handleDropdownClose();\r\n                      }}\r\n                      className=\"flex items-center gap-3 w-full text-left px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                    >\r\n                      <span className=\"text-lg\">🚪</span>\r\n                      Logout\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {/* Responsive Mobile Menu Button */}\r\n        <button\r\n          type=\"button\"\r\n          className=\"flex items-center justify-center p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 sm:hidden hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300\"\r\n          aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\r\n          onClick={() => setMobileMenuOpen((open) => !open)}\r\n        >\r\n          {mobileMenuOpen ? (\r\n            <X\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={!mobileMenuOpen}\r\n            />\r\n          ) : (\r\n            <Menu\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={mobileMenuOpen}\r\n            />\r\n          )}\r\n        </button>\r\n        {/* Cart Button */}\r\n        <button\r\n          className=\"relative px-3 py-1 md:px-4 md:py-2 rounded-2xl font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg hover:from-red-600 hover:to-pink-700 hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 ml-1 md:ml-2 text-sm md:text-base transform hover:-translate-y-0.5\"\r\n          onClick={onCartClick}\r\n          aria-label=\"Open cart\"\r\n        >\r\n          <span className=\"hidden sm:inline\">🛒 Cart</span>\r\n          <span className=\"sm:hidden\">🛒</span>\r\n          <span className=\"sm:ml-1\">({cartCount})</span>\r\n          {cartCount > 0 && (\r\n            <span className=\"absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs rounded-full px-1.5 py-0.5 animate-bounce font-bold shadow-lg\">\r\n              {cartCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </nav>\r\n      {/* Mobile Nav Drawer Overlay & Drawer: Only render when open */}\r\n      {mobileMenuOpen && (\r\n        <>\r\n          {/* Overlay */}\r\n          <div\r\n            className=\"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300 block\"\r\n            style={{ left: 0, right: 0 }}\r\n            onClick={() => setMobileMenuOpen(false)}\r\n            aria-hidden={!mobileMenuOpen}\r\n          />\r\n          {/* Drawer */}\r\n          <div\r\n            className=\"fixed top-0 right-0 z-50 w-full max-w-xs h-screen bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs min-w-0 sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden translate-x-0 border-l border-gray-200\"\r\n            role=\"dialog\"\r\n            aria-modal=\"true\"\r\n            tabIndex={-1}\r\n            style={{ right: 0, left: \"auto\" }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Tab\") {\r\n                // Basic focus trap: keep focus inside drawer\r\n                const focusable = Array.from(\r\n                  (e.currentTarget as HTMLElement).querySelectorAll(\r\n                    'a, button, input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n                  )\r\n                ) as HTMLElement[];\r\n                if (focusable.length === 0) return;\r\n                const first = focusable[0];\r\n                const last = focusable[focusable.length - 1];\r\n                if (!e.shiftKey && document.activeElement === last) {\r\n                  e.preventDefault();\r\n                  first.focus();\r\n                } else if (e.shiftKey && document.activeElement === first) {\r\n                  e.preventDefault();\r\n                  last.focus();\r\n                }\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center justify-between px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n              <span className=\"font-black text-lg bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\r\n                📱 Menu\r\n              </span>\r\n              <button\r\n                className=\"p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 hover:bg-white/50 transition-all duration-300\"\r\n                aria-label=\"Close menu\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n                tabIndex={mobileMenuOpen ? 0 : -1}\r\n              >\r\n                <X className=\"w-6 h-6 text-gray-700\" />\r\n              </button>\r\n            </div>\r\n            <nav className=\"flex flex-col gap-2 px-4 py-6 flex-1 overflow-y-auto max-h-[calc(100vh-80px)]\">\r\n              <Link\r\n                href=\"/\"\r\n                className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n              >\r\n                <span className=\"text-lg\">🏠</span>\r\n                Home\r\n              </Link>\r\n              {isLoggedIn && (\r\n                <Link\r\n                  href=\"/user/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">📊</span>\r\n                  Dashboard\r\n                </Link>\r\n              )}\r\n              {isLoggedIn && userRole === \"ADMIN\" && (\r\n                <Link\r\n                  href=\"/admin/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">⚡</span>\r\n                  Admin Panel\r\n                </Link>\r\n              )}\r\n              {!isLoggedIn ? (\r\n                <>\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">🔐</span>\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">✨</span>\r\n                    Register\r\n                  </Link>\r\n                </>\r\n              ) : (\r\n                <div className=\"flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200\">\r\n                  <Link\r\n                    href=\"/user/profile\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">⚙️</span>\r\n                    Profile Settings\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleLogout();\r\n                      setMobileMenuOpen(false);\r\n                    }}\r\n                    className=\"flex items-center gap-3 w-full text-left px-4 py-3 rounded-2xl font-semibold text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  >\r\n                    <span className=\"text-lg\">🚪</span>\r\n                    Logout\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </nav>\r\n            {isLoggedIn && (\r\n              <div className=\"flex items-center gap-3 px-4 py-4 border-t border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={40}\r\n                    height={40}\r\n                    className=\"rounded-full border-2 border-indigo-200 object-cover bg-white shadow-md w-10 h-10\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"font-bold text-gray-900 truncate text-sm\">\r\n                    Account\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-600 truncate\">Logged in</p>\r\n                  <span className=\"inline-block mt-1 px-2 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full\">\r\n                    {userRole}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAQe,SAAS,OAAO,EAC7B,YAAY,CAAC,EACb,WAAW,EAIZ;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,eAAe;gBACb,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,IAAI;wBACF,kEAAkE;wBAClE,MAAM,WAAW,MAAM,MACrB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAC7C;4BACE,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBAGF,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,IAAI,QAAQ,KAAK,IAAI,EAAE;gCACrB,gBAAgB,KAAK,IAAI,CAAC,YAAY,IAAI;4BAC5C;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0CAA0C;wBACxD,+BAA+B;wBAC/B,IAAI;4BACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;4BACnD,gBAAgB,QAAQ,YAAY,IAAI;wBAC1C,EAAE,OAAM;4BACN,gBAAgB;wBAClB;oBACF;gBACF;YACF;YACA,wCAAmC;gBACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,cAAc,CAAC,CAAC;gBAChB,IAAI,OAAO;oBACT,kGAAkG;oBAClG,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;wBACnD,YAAY,QAAQ,IAAI,IAAI;wBAC5B,gBAAgB,QAAQ,YAAY,IAAI;oBAC1C,EAAE,OAAM;wBACN,YAAY;wBACZ,gBAAgB;oBAClB;gBACF,OAAO;oBACL,YAAY;oBACZ,gBAAgB;gBAClB;gBACA,wCAAwC;gBACxC,OAAO,gBAAgB,CAAC,uBAAuB;YACjD;YACA,yBAAyB;YACzB;oCAAO;oBACL,OAAO,mBAAmB,CACxB,uBACA;gBAEJ;;QACF;2BAAG,EAAE;IAEL,2EAA2E;IAC3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;YAC7C,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;YAC7C;YACA;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;oBAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;gBAC7C;;QACF;2BAAG;QAAC;KAAe;IAEnB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,cAAc,CAAgB;gBACrC,IAAI,EAAE,GAAG,KAAK,UAAU;oBACtB,gBAAgB;oBAChB,kBAAkB;gBACpB;YACF;YACA,OAAO,gBAAgB,CAAC,WAAW;YACnC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;2BAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB,gBAAgB;QACtC;2BAAG;QAAC;KAAe;IAEnB,SAAS;QACP,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,SAAS;QACP,gBAAgB,CAAC,OAAS,CAAC;IAC7B;IAEA,SAAS;QACP,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,QAAQ;;;;;;kDAEV,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;gCAAK,WAAU;0CAA2Q;;;;;;;;;;;;kCAK7R,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,4BACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,cAAc,aAAa,yBAC1B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,CAAC,2BACA;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;6DAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;;0DAEX,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,gBAAgB;wDACrB,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,6LAAC;gDAAK,WAAU;0DAAqE;;;;;;0DAGrF,6LAAC;gDACC,WAAW,CAAC,6DAA6D,EACvE,eAAe,eAAe,IAC9B;gDACF,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;oCAIP,8BACC,6LAAC;wCACC,WAAU;wCACV,cAAc;kDAEd,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,6LAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,6LAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDACC,SAAS;wDACP;wDACA;oDACF;oDACA,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUjD,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,cAAY,iBAAiB,eAAe;wBAC5C,SAAS,IAAM,kBAAkB,CAAC,OAAS,CAAC;kCAE3C,+BACC,6LAAC,+LAAA,CAAA,IAAC;4BACA,WAAU;4BACV,eAAa,CAAC;;;;;iDAGhB,6LAAC,qMAAA,CAAA,OAAI;4BACH,WAAU;4BACV,eAAa;;;;;;;;;;;kCAKnB,6LAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAW;;0CAEX,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,6LAAC;gCAAK,WAAU;0CAAY;;;;;;0CAC5B,6LAAC;gCAAK,WAAU;;oCAAU;oCAAE;oCAAU;;;;;;;4BACrC,YAAY,mBACX,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAMR,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,MAAM;4BAAG,OAAO;wBAAE;wBAC3B,SAAS,IAAM,kBAAkB;wBACjC,eAAa,CAAC;;;;;;kCAGhB,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,cAAW;wBACX,UAAU,CAAC;wBACX,OAAO;4BAAE,OAAO;4BAAG,MAAM;wBAAO;wBAChC,WAAW,CAAC;4BACV,IAAI,EAAE,GAAG,KAAK,OAAO;gCACnB,6CAA6C;gCAC7C,MAAM,YAAY,MAAM,IAAI,CAC1B,AAAC,EAAE,aAAa,CAAiB,gBAAgB,CAC/C;gCAGJ,IAAI,UAAU,MAAM,KAAK,GAAG;gCAC5B,MAAM,QAAQ,SAAS,CAAC,EAAE;gCAC1B,MAAM,OAAO,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;gCAC5C,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,MAAM;oCAClD,EAAE,cAAc;oCAChB,MAAM,KAAK;gCACb,OAAO,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,OAAO;oCACzD,EAAE,cAAc;oCAChB,KAAK,KAAK;gCACZ;4BACF;wBACF;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAkG;;;;;;kDAGlH,6LAAC;wCACC,WAAU;wCACV,cAAW;wCACX,SAAS,IAAM,kBAAkB;wCACjC,UAAU,iBAAiB,IAAI,CAAC;kDAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAGpC,4BACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAItC,cAAc,aAAa,yBAC1B,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAQ;;;;;;;oCAIrC,CAAC,2BACA;;0DACE,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;qEAKtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,6LAAC;gDACC,SAAS;oDACP;oDACA,kBAAkB;gDACpB;gDACA,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;4BAM1C,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,gBAAgB;gDACrB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,6LAAC;gDAAE,WAAU;0DAAiC;;;;;;0DAC9C,6LAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;GAtbwB;KAAA", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/OrderForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  customizationData?: {\n    canvasData?: string;\n    preview?: string;\n  };\n  price: number;\n  quantity?: number;\n};\n\ntype OrderFormProps = {\n  cartItems: CartItem[];\n  onOrderSuccess: () => void;\n  onCancel: () => void;\n};\n\ntype OrderFormData = {\n  customerName: string;\n  customerPhone: string;\n  customerAddress: string;\n};\n\nexport default function OrderForm({\n  cartItems,\n  onOrderSuccess,\n  onCancel,\n}: OrderFormProps) {\n  const [formData, setFormData] = useState<OrderFormData>({\n    customerName: \"\",\n    customerPhone: \"\",\n    customerAddress: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  useEffect(() => {\n    // Get user info to pre-fill name\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD), {\n        headers: { Authorization: `Bearer ${token}` },\n      })\n        .then((res) => res.json())\n        .then((data) => {\n          if (data && data.user) {\n            setFormData((prev) => ({\n              ...prev,\n              customerName: data.user.name || \"\",\n            }));\n          }\n        })\n        .catch(() => {\n          // Ignore error, user can still fill form manually\n        });\n    }\n  }, []);\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setError(\"Please log in to place an order\");\n      setLoading(false);\n      return;\n    }\n\n    try {\n      // Create orders for each cart item\n      const orderPromises = cartItems.map((item) =>\n        fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.ORDERS), {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({\n            productId: item.productId,\n            customerName: formData.customerName,\n            customerPhone: formData.customerPhone,\n            customerAddress: formData.customerAddress,\n            customColor: item.color,\n            customText: item.text,\n            isCustomized: item.customized,\n            customizationData: item.customizationData?.canvasData,\n            customizationPreview: item.customizationData?.preview,\n            quantity: item.quantity || 1,\n          }),\n        })\n      );\n\n      const responses = await Promise.all(orderPromises);\n\n      // Check if all orders were successful\n      const allSuccessful = responses.every((res) => res.ok);\n\n      if (allSuccessful) {\n        // Clear cart\n        localStorage.removeItem(\"cart\");\n        onOrderSuccess();\n      } else {\n        setError(\"Some orders failed to process. Please try again.\");\n      }\n    } catch {\n      setError(\"Failed to place order. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">\n              Complete Your Order\n            </h2>\n            <button\n              onClick={onCancel}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close\"\n            >\n              ×\n            </button>\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h3 className=\"font-semibold mb-3\">Order Summary</h3>\n            {cartItems.map((item, index) => (\n              <div\n                key={index}\n                className=\"flex gap-4 py-3 border-b border-gray-200 last:border-b-0\"\n              >\n                {/* Customization Preview */}\n                {item.customized && item.customizationData?.preview && (\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-16 h-16 border border-gray-300 rounded-lg overflow-hidden bg-gray-50\">\n                      <img\n                        src={item.customizationData.preview}\n                        alt=\"Customization preview\"\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex-1\">\n                  <div className=\"font-medium\">{item.name}</div>\n                  {item.customized && (\n                    <div className=\"text-sm text-gray-600 mt-1\">\n                      {item.color && (\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <span>Color:</span>\n                          <div\n                            className=\"w-3 h-3 rounded border border-gray-300\"\n                            style={{ backgroundColor: item.color }}\n                          ></div>\n                          <span>{item.color}</span>\n                        </div>\n                      )}\n                      {item.text && (\n                        <div className=\"mb-1\">\n                          <span>Text: &ldquo;{item.text}&rdquo;</span>\n                        </div>\n                      )}\n                      {item.customizationData?.canvasData && (\n                        <div className=\"inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                          <svg\n                            className=\"w-3 h-3\"\n                            fill=\"currentColor\"\n                            viewBox=\"0 0 20 20\"\n                          >\n                            <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z\" />\n                          </svg>\n                          Advanced Design\n                        </div>\n                      )}\n                    </div>\n                  )}\n                  <div className=\"text-sm text-gray-500 mt-1\">\n                    Qty: {item.quantity || 1}\n                  </div>\n                </div>\n\n                <div className=\"text-right\">\n                  <div className=\"font-semibold\">\n                    K{(item.price * (item.quantity || 1)).toFixed(2)}\n                  </div>\n                </div>\n              </div>\n            ))}\n            <div className=\"flex justify-between items-center pt-3 mt-3 border-t border-gray-300\">\n              <div className=\"font-bold text-lg\">Total:</div>\n              <div className=\"font-bold text-lg text-[#1a237e]\">\n                K{calculateTotal().toFixed(2)}\n              </div>\n            </div>\n          </div>\n\n          {/* Order Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label\n                htmlFor=\"customerName\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Full Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"customerName\"\n                name=\"customerName\"\n                value={formData.customerName}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerPhone\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Phone Number *\n              </label>\n              <input\n                type=\"tel\"\n                id=\"customerPhone\"\n                name=\"customerPhone\"\n                value={formData.customerPhone}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"e.g. +260 971 781 907\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerAddress\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Delivery/Pickup Address *\n              </label>\n              <textarea\n                id=\"customerAddress\"\n                name=\"customerAddress\"\n                value={formData.customerAddress}\n                onChange={handleInputChange}\n                required\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full address or preferred pickup location\"\n              />\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm bg-red-50 p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"flex gap-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={onCancel}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold transition ${\n                  loading\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"hover:bg-[#2a3490]\"\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center gap-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                    <span>Placing Order...</span>\n                  </div>\n                ) : (\n                  `Place Order - K${calculateTotal().toFixed(2)}`\n                )}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-4 text-xs text-gray-500 text-center\">\n            By placing this order, you agree to our terms and conditions. You\n            will be contacted for payment and delivery arrangements.\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgCe,SAAS,UAAU,EAChC,SAAS,EACT,cAAc,EACd,QAAQ,EACO;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,cAAc;QACd,eAAe;QACf,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,iCAAiC;YACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG;oBACpD,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAC9C,GACG,IAAI;2CAAC,CAAC,MAAQ,IAAI,IAAI;0CACtB,IAAI;2CAAC,CAAC;wBACL,IAAI,QAAQ,KAAK,IAAI,EAAE;4BACrB;uDAAY,CAAC,OAAS,CAAC;wCACrB,GAAG,IAAI;wCACP,cAAc,KAAK,IAAI,CAAC,IAAI,IAAI;oCAClC,CAAC;;wBACH;oBACF;0CACC,KAAK;2CAAC;oBACL,kDAAkD;oBACpD;;YACJ;QACF;8BAAG,EAAE;IAEL,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,mCAAmC;YACnC,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAC,OACnC,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;oBAClC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,WAAW,KAAK,SAAS;wBACzB,cAAc,SAAS,YAAY;wBACnC,eAAe,SAAS,aAAa;wBACrC,iBAAiB,SAAS,eAAe;wBACzC,aAAa,KAAK,KAAK;wBACvB,YAAY,KAAK,IAAI;wBACrB,cAAc,KAAK,UAAU;wBAC7B,mBAAmB,KAAK,iBAAiB,EAAE;wBAC3C,sBAAsB,KAAK,iBAAiB,EAAE;wBAC9C,UAAU,KAAK,QAAQ,IAAI;oBAC7B;gBACF;YAGF,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC;YAEpC,sCAAsC;YACtC,MAAM,gBAAgB,UAAU,KAAK,CAAC,CAAC,MAAQ,IAAI,EAAE;YAErD,IAAI,eAAe;gBACjB,aAAa;gBACb,aAAa,UAAU,CAAC;gBACxB;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;4BAClC,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;oCAEC,WAAU;;wCAGT,KAAK,UAAU,IAAI,KAAK,iBAAiB,EAAE,yBAC1C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK,KAAK,iBAAiB,CAAC,OAAO;oDACnC,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;sDAMlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe,KAAK,IAAI;;;;;;gDACtC,KAAK,UAAU,kBACd,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,kBACT,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,KAAK,KAAK;oEAAC;;;;;;8EAEvC,6LAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;;wDAGpB,KAAK,IAAI,kBACR,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;;oEAAK;oEAAc,KAAK,IAAI;oEAAC;;;;;;;;;;;;wDAGjC,KAAK,iBAAiB,EAAE,4BACvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;8EAER,cAAA,6LAAC;wEAAK,GAAE;;;;;;;;;;;gEACJ;;;;;;;;;;;;;8DAMd,6LAAC;oDAAI,WAAU;;wDAA6B;wDACpC,KAAK,QAAQ,IAAI;;;;;;;;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDAAgB;oDAC3B,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;mCAxD7C;;;;;0CA6DT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;4CAAmC;4CAC9C,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kCAMjC,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;;kDACC,6LAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,QAAQ;wCACR,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAIf,uBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,6EAA6E,EACvF,UACI,kCACA,sBACJ;kDAED,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;mDAGR,CAAC,eAAe,EAAE,iBAAiB,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;kCAMvD,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;;;;;AAQlE;GA3SwB;KAAA", "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Cart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport OrderForm from \"./OrderForm\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  customizationData?: {\n    canvasData?: string;\n    preview?: string;\n  };\n  price: number;\n  quantity?: number;\n};\n\ntype CartProps = {\n  isOpen: boolean;\n  onClose: () => void;\n};\n\nexport default function Cart({ isOpen, onClose }: CartProps) {\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n  const [showOrderForm, setShowOrderForm] = useState(false);\n  const [orderSuccess, setOrderSuccess] = useState(false);\n\n  useEffect(() => {\n    if (isOpen) {\n      loadCartItems();\n    }\n  }, [isOpen]);\n\n  const loadCartItems = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\n    setCartItems(cart);\n  };\n\n  const removeItem = (itemId: number) => {\n    const updatedCart = cartItems.filter((item) => item.id !== itemId);\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const updateQuantity = (itemId: number, newQuantity: number) => {\n    if (newQuantity < 1) {\n      removeItem(itemId);\n      return;\n    }\n\n    const updatedCart = cartItems.map((item) =>\n      item.id === itemId ? { ...item, quantity: newQuantity } : item\n    );\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleCheckout = () => {\n    setShowOrderForm(true);\n  };\n\n  const handleOrderSuccess = () => {\n    setShowOrderForm(false);\n    setOrderSuccess(true);\n    setCartItems([]);\n\n    // Close success message after 3 seconds\n    setTimeout(() => {\n      setOrderSuccess(false);\n      onClose();\n    }, 3000);\n  };\n\n  if (!isOpen) return null;\n\n  if (showOrderForm) {\n    return (\n      <OrderForm\n        cartItems={cartItems}\n        onOrderSuccess={handleOrderSuccess}\n        onCancel={() => setShowOrderForm(false)}\n      />\n    );\n  }\n\n  if (orderSuccess) {\n    return (\n      <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-xl shadow-lg p-8 text-center max-w-md\">\n          <div className=\"text-green-600 text-6xl mb-4\">✓</div>\n          <h2 className=\"text-2xl font-bold text-[#1a237e] mb-2\">\n            Order Placed Successfully!\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Thank you for your order. We will contact you soon for payment and\n            delivery arrangements.\n          </p>\n          <div className=\"animate-pulse text-sm text-gray-500\">\n            Closing automatically...\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">Shopping Cart</h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close cart\"\n            >\n              ×\n            </button>\n          </div>\n\n          {cartItems.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">🛒</div>\n              <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n                Your cart is empty\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                Add some products to get started!\n              </p>\n              <button\n                onClick={onClose}\n                className=\"px-6 py-2 bg-[#1a237e] text-white rounded-md hover:bg-[#2a3490] transition\"\n              >\n                Continue Shopping\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Cart Items */}\n              <div className=\"space-y-4 mb-6\">\n                {cartItems.map((item) => (\n                  <div\n                    key={item.id}\n                    className=\"flex items-start gap-4 p-4 border border-gray-200 rounded-lg\"\n                  >\n                    {/* Customization Preview */}\n                    {item.customized && item.customizationData?.preview && (\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-20 h-20 border border-gray-300 rounded-lg overflow-hidden bg-gray-50\">\n                          <img\n                            src={item.customizationData.preview}\n                            alt=\"Customization preview\"\n                            className=\"w-full h-full object-cover\"\n                          />\n                        </div>\n                        <div className=\"text-xs text-center text-gray-500 mt-1\">\n                          Custom Design\n                        </div>\n                      </div>\n                    )}\n\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-lg\">{item.name}</h3>\n                      {item.customized && (\n                        <div className=\"text-sm text-gray-600 mt-1\">\n                          {item.color && (\n                            <div className=\"flex items-center gap-2 mb-1\">\n                              <span>Color:</span>\n                              <div\n                                className=\"w-4 h-4 rounded border border-gray-300\"\n                                style={{ backgroundColor: item.color }}\n                              ></div>\n                              <span className=\"font-mono text-xs\">\n                                {item.color}\n                              </span>\n                            </div>\n                          )}\n                          {item.text && (\n                            <div className=\"mb-1\">\n                              <span>Text: &ldquo;</span>\n                              <span className=\"font-medium\">{item.text}</span>\n                              <span>&rdquo;</span>\n                            </div>\n                          )}\n                          {item.customizationData?.canvasData && (\n                            <div className=\"inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                              <svg\n                                className=\"w-3 h-3\"\n                                fill=\"currentColor\"\n                                viewBox=\"0 0 20 20\"\n                              >\n                                <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z\" />\n                              </svg>\n                              Advanced Design\n                            </div>\n                          )}\n                        </div>\n                      )}\n                      <div className=\"text-lg font-semibold text-[#1a237e] mt-2\">\n                        K{item.price.toFixed(2)} each\n                      </div>\n                    </div>\n\n                    {/* Quantity Controls */}\n                    <div className=\"flex items-center gap-2\">\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) - 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        -\n                      </button>\n                      <span className=\"w-8 text-center font-semibold\">\n                        {item.quantity || 1}\n                      </span>\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) + 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        +\n                      </button>\n                    </div>\n\n                    {/* Remove Button */}\n                    <button\n                      onClick={() => removeItem(item.id)}\n                      className=\"text-red-500 hover:text-red-700 p-2\"\n                      aria-label=\"Remove item\"\n                    >\n                      🗑️\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              {/* Cart Total */}\n              <div className=\"border-t border-gray-200 pt-4 mb-6\">\n                <div className=\"flex justify-between items-center text-xl font-bold\">\n                  <span>Total:</span>\n                  <span className=\"text-[#1a237e]\">\n                    K{calculateTotal().toFixed(2)}\n                  </span>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={onClose}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n                >\n                  Continue Shopping\n                </button>\n                <button\n                  onClick={handleCheckout}\n                  className=\"flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold hover:bg-[#2a3490] transition\"\n                >\n                  Proceed to Checkout\n                </button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAyBe,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO,EAAa;;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;yBAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QAC3D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,cAAc,GAAG;YACnB,WAAW;YACX;QACF;QAEA,MAAM,cAAc,UAAU,GAAG,CAAC,CAAC,OACjC,KAAK,EAAE,KAAK,SAAS;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY,IAAI;QAE5D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,gBAAgB;QAChB,aAAa,EAAE;QAEf,wCAAwC;QACxC,WAAW;YACT,gBAAgB;YAChB;QACF,GAAG;IACL;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,IAAI,eAAe;QACjB,qBACE,6LAAC,kIAAA,CAAA,UAAS;YACR,WAAW;YACX,gBAAgB;YAChB,UAAU,IAAM,iBAAiB;;;;;;IAGvC;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;kCAC9C,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAM7D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;oBAKF,UAAU,MAAM,KAAK,kBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;6CAKH;;0CAEE,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wCAEC,WAAU;;4CAGT,KAAK,UAAU,IAAI,KAAK,iBAAiB,EAAE,yBAC1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK,KAAK,iBAAiB,CAAC,OAAO;4DACnC,KAAI;4DACJ,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;kEAAyC;;;;;;;;;;;;0DAM5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyB,KAAK,IAAI;;;;;;oDAC/C,KAAK,UAAU,kBACd,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,kBACT,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,KAAK,KAAK;wEAAC;;;;;;kFAEvC,6LAAC;wEAAK,WAAU;kFACb,KAAK,KAAK;;;;;;;;;;;;4DAIhB,KAAK,IAAI,kBACR,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,6LAAC;kFAAK;;;;;;;;;;;;4DAGT,KAAK,iBAAiB,EAAE,4BACvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,WAAU;wEACV,MAAK;wEACL,SAAQ;kFAER,cAAA,6LAAC;4EAAK,GAAE;;;;;;;;;;;oEACJ;;;;;;;;;;;;;kEAMd,6LAAC;wDAAI,WAAU;;4DAA4C;4DACvD,KAAK,KAAK,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAK5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ,IAAI;;;;;;kEAEpB,6LAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;;;;;;;0DAMH,6LAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;gDACV,cAAW;0DACZ;;;;;;;uCAzFI,KAAK,EAAE;;;;;;;;;;0CAiGlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;;gDAAiB;gDAC7B,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA9PwB;KAAA", "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport React, { useEffect } from \"react\";\nimport Link from \"next/link\";\nimport gsap from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport Header from \"@/components/Header\";\nimport Cart from \"@/components/Cart\";\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\n\n// Register GSAP plugins\nif (typeof window !== \"undefined\") {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\ntype Product = {\n  id: number;\n  name: string;\n  image: string;\n  description: string;\n  customizable: boolean;\n  slug: string;\n  category: { id: number; name: string };\n  modelUrl?: string;\n  price?: number;\n};\n\n// Fetch products from backend\nasync function fetchProducts() {\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.PRODUCTS), {\n    cache: \"no-store\",\n  });\n  if (!res.ok) throw new Error(\"Failed to fetch products\");\n  return res.json();\n}\n\n// Fetch categories from backend\nasync function fetchCategories() {\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.CATEGORIES), {\n    cache: \"no-store\",\n  });\n  if (!res.ok) throw new Error(\"Failed to fetch categories\");\n  return res.json();\n}\n\nconst shops = [\n  {\n    name: \"East Park Mall Branch\",\n    address: \"East Park Mall, Lusaka, Zambia\",\n    phone: \"+260 971 781 907\",\n    hours: \"Mon-Fri: 08:00-18:00, Sat: 09:00-13:00, Sun: Closed\",\n    map: \"https://maps.app.goo.gl/DayGVmNoSzL2Y9EA7\",\n    image: \"/shops.jpg\",\n  },\n  {\n    name: \"Pinnacle Mall Branch\",\n    address: \"Pinnacle Mall, Lusaka, Zambia\",\n    phone: \"+260 974 594 572\",\n    hours: \"Mon-Fri: 08:00-18:00, Sat-Sun: 09:00-13:00\",\n    map: \"https://maps.app.goo.gl/Xk7Q1tBhXfBGLnzL8\",\n    image: \"/shops.jpg\",\n  },\n];\n\nexport default function Home() {\n  const [products, setProducts] = React.useState<Product[]>([]);\n  const [categories, setCategories] = React.useState<\n    { id: number; name: string }[]\n  >([]);\n  const [search, setSearch] = React.useState(\"\");\n  const [selectedCategory, setSelectedCategory] = React.useState(\"All\");\n  const [currentPage, setCurrentPage] = React.useState(1);\n  const [cartCount, setCartCount] = React.useState(0);\n  const [isCartOpen, setIsCartOpen] = React.useState(false);\n  const PRODUCTS_PER_PAGE = 4;\n  const productGridRef = React.useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    fetchProducts()\n      .then(setProducts)\n      .catch(() => setProducts([]));\n\n    fetchCategories()\n      .then(setCategories)\n      .catch(() => setCategories([]));\n\n    // Load cart count\n    updateCartCount();\n  }, []);\n\n  const updateCartCount = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\n    setCartCount(cart.length);\n  };\n\n  const handleCartClick = () => {\n    setIsCartOpen(true);\n  };\n\n  const handleCartClose = () => {\n    setIsCartOpen(false);\n    updateCartCount(); // Update count when cart closes\n  };\n\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [search, selectedCategory]);\n\n  React.useEffect(() => {\n    if (productGridRef.current) {\n      gsap.fromTo(\n        productGridRef.current.children,\n        { opacity: 0, y: 40 },\n        {\n          opacity: 1,\n          y: 0,\n          stagger: 0.12,\n          duration: 0.7,\n          ease: \"power3.out\",\n        }\n      );\n    }\n  }, [products.length]);\n\n  // GSAP Animations on mount\n  React.useEffect(() => {\n    // Hero animations\n    const tl = gsap.timeline();\n\n    tl.fromTo(\n      \".hero-logo\",\n      { opacity: 0, y: 50, scale: 0.8 },\n      { opacity: 1, y: 0, scale: 1, duration: 1, ease: \"power3.out\" }\n    )\n      .fromTo(\n        \".hero-title\",\n        { opacity: 0, y: 30 },\n        { opacity: 1, y: 0, duration: 0.8, ease: \"power2.out\" },\n        \"-=0.5\"\n      )\n      .fromTo(\n        \".hero-subtitle\",\n        { opacity: 0, y: 20 },\n        { opacity: 1, y: 0, duration: 0.6, ease: \"power2.out\" },\n        \"-=0.3\"\n      )\n      .fromTo(\n        \".hero-buttons\",\n        { opacity: 0, y: 20 },\n        { opacity: 1, y: 0, duration: 0.6, ease: \"power2.out\" },\n        \"-=0.2\"\n      )\n      .fromTo(\n        \".hero-stats\",\n        { opacity: 0, y: 20 },\n        { opacity: 1, y: 0, duration: 0.6, ease: \"power2.out\" },\n        \"-=0.1\"\n      );\n\n    // Scroll-triggered animations\n    gsap.registerPlugin(ScrollTrigger);\n\n    // Features section animation\n    gsap.fromTo(\n      \".feature-card\",\n      {\n        opacity: 0,\n        y: 50,\n        scale: 0.9,\n      },\n      {\n        opacity: 1,\n        y: 0,\n        scale: 1,\n        duration: 0.6,\n        stagger: 0.1,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: \".features-section\",\n          start: \"top 80%\",\n          end: \"bottom 20%\",\n          toggleActions: \"play none none reverse\",\n        },\n      }\n    );\n\n    // Locations section animation\n    gsap.fromTo(\n      \".location-card\",\n      {\n        opacity: 0,\n        x: -50,\n        rotationY: -15,\n      },\n      {\n        opacity: 1,\n        x: 0,\n        rotationY: 0,\n        duration: 0.8,\n        stagger: 0.2,\n        ease: \"power2.out\",\n        scrollTrigger: {\n          trigger: \".locations-section\",\n          start: \"top 80%\",\n          end: \"bottom 20%\",\n          toggleActions: \"play none none reverse\",\n        },\n      }\n    );\n  }, []);\n\n  const filteredProducts = products.filter((p) => {\n    const matchesCategory =\n      selectedCategory === \"All\" || p.category?.name === selectedCategory;\n    const matchesSearch =\n      p.name.toLowerCase().includes(search.toLowerCase()) ||\n      p.description.toLowerCase().includes(search.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n  const totalPages = Math.ceil(filteredProducts.length / PRODUCTS_PER_PAGE);\n  const paginatedProducts = filteredProducts.slice(\n    (currentPage - 1) * PRODUCTS_PER_PAGE,\n    currentPage * PRODUCTS_PER_PAGE\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 text-gray-900 font-sans overflow-x-hidden\">\n      <Header cartCount={cartCount} onCartClick={handleCartClick} />\n\n      {/* Hero Section */}\n      <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800\"></div>\n        <div className=\"absolute inset-0 bg-black/20\"></div>\n\n        {/* Animated Background Shapes */}\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-gradient-to-r from-yellow-400 to-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute -bottom-8 left-20 w-72 h-72 bg-gradient-to-r from-pink-400 to-red-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"></div>\n\n        {/* Hero Content */}\n        <div className=\"relative z-10 text-center px-4 max-w-6xl mx-auto\">\n          <div className=\"mb-8\">\n            <Image\n              src=\"/6 Light Logo.png\"\n              alt=\"Six Light Media Logo\"\n              width={300}\n              height={120}\n              priority\n              className=\"mx-auto mb-8 drop-shadow-2xl\"\n            />\n          </div>\n\n          <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-black mb-6 text-white leading-tight\">\n            <span className=\"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent\">\n              Six Light\n            </span>\n            <br />\n            <span className=\"text-white\">Media Store</span>\n          </h1>\n\n          <p className=\"text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed\">\n            Transform your ideas into reality with our premium custom printing,\n            engraving, and personalization services.\n            <span className=\"text-yellow-400 font-semibold\">\n              {\" \"}\n              Professional quality, lightning-fast delivery.\n            </span>\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <button\n              onClick={() =>\n                document\n                  .getElementById(\"products\")\n                  ?.scrollIntoView({ behavior: \"smooth\" })\n              }\n              className=\"group relative px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold text-lg rounded-full shadow-2xl hover:shadow-yellow-500/25 transform hover:scale-105 transition-all duration-300\"\n            >\n              <span className=\"relative z-10\">Explore Products</span>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-yellow-300 to-orange-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </button>\n\n            <button\n              onClick={() =>\n                document\n                  .getElementById(\"locations\")\n                  ?.scrollIntoView({ behavior: \"smooth\" })\n              }\n              className=\"px-8 py-4 border-2 border-white text-white font-bold text-lg rounded-full hover:bg-white hover:text-gray-900 transition-all duration-300\"\n            >\n              Find Our Stores\n            </button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-black text-yellow-400 mb-2\">\n                1000+\n              </div>\n              <div className=\"text-gray-300 text-sm md:text-base\">\n                Happy Customers\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-black text-pink-400 mb-2\">\n                24h\n              </div>\n              <div className=\"text-gray-300 text-sm md:text-base\">\n                Fast Delivery\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-black text-purple-400 mb-2\">\n                100%\n              </div>\n              <div className=\"text-gray-300 text-sm md:text-base\">\n                Quality Guaranteed\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-black text-blue-400 mb-2\">\n                5★\n              </div>\n              <div className=\"text-gray-300 text-sm md:text-base\">\n                Customer Rating\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n      </section>\n\n      {/* Products Section */}\n      <section id=\"products\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-black mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\n              Featured Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Discover our premium collection of customizable products, crafted\n              with precision and designed to make your brand stand out.\n            </p>\n          </div>\n\n          {/* Search and Category Filter */}\n          <div className=\"flex flex-col lg:flex-row gap-6 mb-12 items-center justify-center\">\n            <div className=\"relative w-full max-w-md\">\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={search}\n                onChange={(e) => setSearch(e.target.value)}\n                className=\"w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-300 text-lg\"\n              />\n              <div className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n                <svg\n                  className=\"w-6 h-6\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  />\n                </svg>\n              </div>\n            </div>\n\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full max-w-xs px-6 py-4 border-2 border-gray-200 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-300 text-lg bg-white\"\n            >\n              <option value=\"All\">All Categories</option>\n              {categories.map((cat) => (\n                <option key={cat.id} value={cat.name}>\n                  {cat.name}\n                </option>\n              ))}\n            </select>\n          </div>\n          <div\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\"\n            ref={productGridRef}\n          >\n            {paginatedProducts.map((product) => (\n              <Link\n                key={product.id}\n                href={`/product/${product.slug}`}\n                className=\"group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2\"\n                prefetch={false}\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n\n                <div className=\"relative p-6\">\n                  <div className=\"aspect-square mb-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden\">\n                    {product.image ? (\n                      <Image\n                        src={product.image || \"/bottle-dummy.jpg\"}\n                        alt={product.name}\n                        width={200}\n                        height={200}\n                        className=\"w-full h-full object-contain group-hover:scale-110 transition-transform duration-500\"\n                      />\n                    ) : (\n                      <Image\n                        src=\"/bottle-dummy.jpg\"\n                        alt=\"No image available\"\n                        width={200}\n                        height={200}\n                        className=\"w-full h-full object-contain opacity-60 group-hover:scale-110 transition-transform duration-500\"\n                      />\n                    )}\n                  </div>\n\n                  <h3 className=\"text-xl font-bold mb-3 text-gray-900 group-hover:text-indigo-600 transition-colors duration-300 line-clamp-2\">\n                    {product.name}\n                  </h3>\n\n                  <p className=\"text-gray-600 mb-6 line-clamp-3 leading-relaxed\">\n                    {product.description}\n                  </p>\n\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-full text-sm group-hover:from-indigo-600 group-hover:to-purple-700 transition-all duration-300 shadow-lg\">\n                      View Details\n                      <svg\n                        className=\"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M9 5l7 7-7 7\"\n                        />\n                      </svg>\n                    </span>\n\n                    {product.customizable && (\n                      <span className=\"px-3 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded-full\">\n                        Customizable\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n          {/* Pagination Controls */}\n          {totalPages > 1 && (\n            <div className=\"flex justify-center items-center gap-6 mt-16\">\n              <button\n                className=\"px-8 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg\"\n                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}\n                disabled={currentPage === 1}\n              >\n                Previous\n              </button>\n              <span className=\"px-6 py-3 bg-gray-100 rounded-2xl font-semibold text-gray-700\">\n                Page {currentPage} of {totalPages}\n              </span>\n              <button\n                className=\"px-8 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg\"\n                onClick={() =>\n                  setCurrentPage((p) => Math.min(totalPages, p + 1))\n                }\n                disabled={currentPage === totalPages}\n              >\n                Next\n              </button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"features-section py-20 bg-gradient-to-br from-gray-50 to-indigo-50\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-black mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\n              Why Choose Six Light Media?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Experience the difference with our premium services and unmatched\n              quality\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <div className=\"feature-card group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                <svg\n                  className=\"w-8 h-8 text-white\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold mb-4 text-gray-900\">\n                Premium Quality\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                High-quality printing, engraving, and custom products that\n                exceed expectations\n              </p>\n            </div>\n\n            <div className=\"feature-card group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                <svg\n                  className=\"w-8 h-8 text-white\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold mb-4 text-gray-900\">\n                Full Customization\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                Personalize T-Shirts, Bottles, and more with your own text,\n                colors, and designs\n              </p>\n            </div>\n\n            <div className=\"feature-card group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                <svg\n                  className=\"w-8 h-8 text-white\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M13 10V3L4 14h7v7l9-11h-7z\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold mb-4 text-gray-900\">\n                Lightning Fast\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                Quick turnaround times with professional service that delivers\n                on time\n              </p>\n            </div>\n\n            <div className=\"feature-card group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                <svg\n                  className=\"w-8 h-8 text-white\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold mb-4 text-gray-900\">\n                Trusted Partner\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                Trusted by leading brands across Zambia for consistent quality\n                and reliability\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Locations Section */}\n      <section id=\"locations\" className=\"locations-section py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-black mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\n              Visit Our Stores\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Find us at convenient locations across Lusaka for all your custom\n              printing needs\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {shops.map((shop) => (\n              <div\n                key={shop.name}\n                className=\"location-card group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2\"\n              >\n                <div className=\"aspect-video overflow-hidden\">\n                  <Image\n                    src={shop.image}\n                    alt={shop.name}\n                    width={600}\n                    height={300}\n                    className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                  />\n                </div>\n\n                <div className=\"p-8\">\n                  <h3 className=\"text-2xl font-bold mb-4 text-gray-900 group-hover:text-indigo-600 transition-colors duration-300\">\n                    {shop.name}\n                  </h3>\n\n                  <div className=\"space-y-3 mb-6\">\n                    <div className=\"flex items-start gap-3\">\n                      <svg\n                        className=\"w-5 h-5 text-indigo-500 mt-1 flex-shrink-0\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                        />\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                        />\n                      </svg>\n                      <p className=\"text-gray-600\">{shop.address}</p>\n                    </div>\n\n                    <div className=\"flex items-center gap-3\">\n                      <svg\n                        className=\"w-5 h-5 text-indigo-500 flex-shrink-0\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                        />\n                      </svg>\n                      <p className=\"text-gray-600\">{shop.phone}</p>\n                    </div>\n\n                    <div className=\"flex items-start gap-3\">\n                      <svg\n                        className=\"w-5 h-5 text-indigo-500 mt-1 flex-shrink-0\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        />\n                      </svg>\n                      <p className=\"text-gray-600\">{shop.hours}</p>\n                    </div>\n                  </div>\n\n                  <a\n                    href={shop.map}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg group-hover:shadow-indigo-500/25\"\n                  >\n                    <svg\n                      className=\"w-5 h-5 mr-2\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                      />\n                    </svg>\n                    View on Google Maps\n                  </a>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gradient-to-br from-gray-900 via-indigo-900 to-purple-900 text-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"text-center\">\n            <Image\n              src=\"/6 Light Logo.png\"\n              alt=\"Six Light Media Logo\"\n              width={200}\n              height={80}\n              className=\"mx-auto mb-8 opacity-90\"\n            />\n\n            <h3 className=\"text-2xl font-bold mb-4\">Six Light Media Store</h3>\n            <p className=\"text-gray-300 mb-8 max-w-2xl mx-auto\">\n              Your trusted partner for premium custom printing, engraving, and\n              personalization services in Zambia.\n            </p>\n\n            <div className=\"flex justify-center space-x-6 mb-8\">\n              <a\n                href=\"#\"\n                className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n              >\n                <svg\n                  className=\"w-6 h-6\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"#\"\n                className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n              >\n                <svg\n                  className=\"w-6 h-6\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\" />\n                </svg>\n              </a>\n              <a\n                href=\"#\"\n                className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n              >\n                <svg\n                  className=\"w-6 h-6\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\" />\n                </svg>\n              </a>\n            </div>\n\n            <div className=\"border-t border-gray-700 pt-8\">\n              <p className=\"text-gray-400\">\n                &copy; {new Date().getFullYear()} Six Light Media. All rights\n                reserved. | Crafted by{\" \"}\n                <Link\n                  href=\"https://www.techadotech.com/\"\n                  target=\"_blank\"\n                  className=\"text-white hover:underline\"\n                >\n                  Techado Tech Limited\n                </Link>\n              </p>\n            </div>\n          </div>\n        </div>\n      </footer>\n\n      {/* Cart Modal */}\n      <Cart isOpen={isCartOpen} onClose={handleCartClose} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,wBAAwB;AACxB,wCAAmC;IACjC,gJAAA,CAAA,UAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAcA,8BAA8B;AAC9B,eAAe;IACb,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,GAAG;QAChE,OAAO;IACT;IACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;IAC7B,OAAO,IAAI,IAAI;AACjB;AAEA,gCAAgC;AAChC,eAAe;IACb,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,UAAU,GAAG;QAClE,OAAO;IACT;IACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;IAC7B,OAAO,IAAI,IAAI;AACjB;AAEA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,KAAK;QACL,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,KAAK;QACL,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAY,EAAE;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAEhD,EAAE;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,oBAAoB;IAC1B,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,gBACG,IAAI,CAAC,aACL,KAAK;kCAAC,IAAM,YAAY,EAAE;;YAE7B,kBACG,IAAI,CAAC,eACL,KAAK;kCAAC,IAAM,cAAc,EAAE;;YAE/B,kBAAkB;YAClB;QACF;yBAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa,KAAK,MAAM;IAC1B;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;QACd,mBAAmB,gCAAgC;IACrD;IAEA,6JAAA,CAAA,UAAK,CAAC,SAAS;0BAAC;YACd,eAAe;QACjB;yBAAG;QAAC;QAAQ;KAAiB;IAE7B,6JAAA,CAAA,UAAK,CAAC,SAAS;0BAAC;YACd,IAAI,eAAe,OAAO,EAAE;gBAC1B,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,eAAe,OAAO,CAAC,QAAQ,EAC/B;oBAAE,SAAS;oBAAG,GAAG;gBAAG,GACpB;oBACE,SAAS;oBACT,GAAG;oBACH,SAAS;oBACT,UAAU;oBACV,MAAM;gBACR;YAEJ;QACF;yBAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,2BAA2B;IAC3B,6JAAA,CAAA,UAAK,CAAC,SAAS;0BAAC;YACd,kBAAkB;YAClB,MAAM,KAAK,gJAAA,CAAA,UAAI,CAAC,QAAQ;YAExB,GAAG,MAAM,CACP,cACA;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI,GAChC;gBAAE,SAAS;gBAAG,GAAG;gBAAG,OAAO;gBAAG,UAAU;gBAAG,MAAM;YAAa,GAE7D,MAAM,CACL,eACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAK,MAAM;YAAa,GACtD,SAED,MAAM,CACL,kBACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAK,MAAM;YAAa,GACtD,SAED,MAAM,CACL,iBACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAK,MAAM;YAAa,GACtD,SAED,MAAM,CACL,eACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAK,MAAM;YAAa,GACtD;YAGJ,8BAA8B;YAC9B,gJAAA,CAAA,UAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;YAEjC,6BAA6B;YAC7B,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,iBACA;gBACE,SAAS;gBACT,GAAG;gBACH,OAAO;YACT,GACA;gBACE,SAAS;gBACT,GAAG;gBACH,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,8BAA8B;YAC9B,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,kBACA;gBACE,SAAS;gBACT,GAAG,CAAC;gBACJ,WAAW,CAAC;YACd,GACA;gBACE,SAAS;gBACT,GAAG;gBACH,WAAW;gBACX,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;QAEJ;yBAAG,EAAE;IAEL,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,kBACJ,qBAAqB,SAAS,EAAE,QAAQ,EAAE,SAAS;QACrD,MAAM,gBACJ,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW,OAChD,EAAE,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW;QACzD,OAAO,mBAAmB;IAC5B;IACA,MAAM,aAAa,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;IACvD,MAAM,oBAAoB,iBAAiB,KAAK,CAC9C,CAAC,cAAc,CAAC,IAAI,mBACpB,cAAc;IAGhB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;gBAAC,WAAW;gBAAW,aAAa;;;;;;0BAG3C,6LAAC;gBAAQ,WAAU;;kCAEjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAId,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAA4F;;;;;;kDAG5G,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAa;;;;;;;;;;;;0CAG/B,6LAAC;gCAAE,WAAU;;oCAA2E;kDAGtF,6LAAC;wCAAK,WAAU;;4CACb;4CAAI;;;;;;;;;;;;;0CAKT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IACP,SACG,cAAc,CAAC,aACd,eAAe;gDAAE,UAAU;4CAAS;wCAE1C,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,6LAAC;wCACC,SAAS,IACP,SACG,cAAc,CAAC,cACd,eAAe;gDAAE,UAAU;4CAAS;wCAE1C,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAuD;;;;;;0DAGtE,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAItD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqD;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAItD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAuD;;;;;;0DAGtE,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAItD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqD;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoH;;;;;;8CAGlI,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAOzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAMV,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACnD,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;wCACnB,WAAW,GAAG,CAAC,CAAC,oBACf,6LAAC;gDAAoB,OAAO,IAAI,IAAI;0DACjC,IAAI,IAAI;+CADE,IAAI,EAAE;;;;;;;;;;;;;;;;;sCAMzB,6LAAC;4BACC,WAAU;4BACV,KAAK;sCAEJ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;oCAChC,WAAU;oCACV,UAAU;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,KAAK,iBACZ,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,QAAQ,KAAK,IAAI;wDACtB,KAAK,QAAQ,IAAI;wDACjB,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;6EAGZ,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;;;;;;8DAKhB,6LAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;8DAGf,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAA8N;8EAE5O,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,QAAO;oEACP,SAAQ;8EAER,cAAA,6LAAC;wEACC,eAAc;wEACd,gBAAe;wEACf,aAAa;wEACb,GAAE;;;;;;;;;;;;;;;;;wDAKP,QAAQ,YAAY,kBACnB,6LAAC;4DAAK,WAAU;sEAA6E;;;;;;;;;;;;;;;;;;;mCAvD9F,QAAQ,EAAE;;;;;;;;;;wBAiEpB,aAAa,mBACZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,eAAe,CAAC,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI;oCACrD,UAAU,gBAAgB;8CAC3B;;;;;;8CAGD,6LAAC;oCAAK,WAAU;;wCAAgE;wCACxE;wCAAY;wCAAK;;;;;;;8CAEzB,6LAAC;oCACC,WAAU;oCACV,SAAS,IACP,eAAe,CAAC,IAAM,KAAK,GAAG,CAAC,YAAY,IAAI;oCAEjD,UAAU,gBAAgB;8CAC3B;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoH;;;;;;8CAGlI,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;sDAIR,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrD,6LAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoH;;;;;;8CAGlI,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,IAAI;gDACd,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAGZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,QAAO;oEACP,SAAQ;;sFAER,6LAAC;4EACC,eAAc;4EACd,gBAAe;4EACf,aAAa;4EACb,GAAE;;;;;;sFAEJ,6LAAC;4EACC,eAAc;4EACd,gBAAe;4EACf,aAAa;4EACb,GAAE;;;;;;;;;;;;8EAGN,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,OAAO;;;;;;;;;;;;sEAG5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,QAAO;oEACP,SAAQ;8EAER,cAAA,6LAAC;wEACC,eAAc;wEACd,gBAAe;wEACf,aAAa;wEACb,GAAE;;;;;;;;;;;8EAGN,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,KAAK;;;;;;;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,QAAO;oEACP,SAAQ;8EAER,cAAA,6LAAC;wEACC,eAAc;wEACd,gBAAe;wEACf,aAAa;wEACb,GAAE;;;;;;;;;;;8EAGN,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,KAAK;;;;;;;;;;;;;;;;;;8DAI5C,6LAAC;oDACC,MAAM,KAAK,GAAG;oDACd,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,6LAAC;4DACC,WAAU;4DACV,MAAK;4DACL,QAAO;4DACP,SAAQ;sEAER,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,aAAa;gEACb,GAAE;;;;;;;;;;;wDAEA;;;;;;;;;;;;;;mCA/FL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BA0GxB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAGZ,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6LAAC;wCACC,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6LAAC;wCACC,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAAgB;wCACnB,IAAI,OAAO,WAAW;wCAAG;wCACV;sDACvB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,QAAO;4CACP,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC,6HAAA,CAAA,UAAI;gBAAC,QAAQ;gBAAY,SAAS;;;;;;;;;;;;AAGzC;GAtuBwB;KAAA", "debugId": null}}]}