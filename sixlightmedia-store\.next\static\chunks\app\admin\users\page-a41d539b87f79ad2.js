(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{657:(e,t,a)=>{Promise.resolve().then(a.bind(a,2899))},2899:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(5155),l=a(2115);function r(){let[e,t]=(0,l.useState)([]),[a,r]=(0,l.useState)(!0),[n,o]=(0,l.useState)(""),[i,c]=(0,l.useState)(null);async function d(e,a){c(e);let s=localStorage.getItem("token"),l=await fetch("".concat("http://localhost:3001","/admin/users/").concat(e,"/role"),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({role:a})});c(null),l.ok?t(t=>t.map(t=>t.id===e?{...t,role:a}:t)):alert("Failed to update user role")}async function h(e){if(!confirm("Are you sure you want to delete this user?"))return;let a=localStorage.getItem("token");(await fetch("".concat("http://localhost:3001","/admin/users/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(a)}})).ok?t(t=>t.filter(t=>t.id!==e)):alert("Failed to delete user")}return((0,l.useEffect)(()=>{let e=localStorage.getItem("token");fetch("".concat("http://localhost:3001","/admin/users"),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{Array.isArray(e)?t(e):Array.isArray(e.users)?t(e.users):t([])}).catch(()=>o("Failed to load users")).finally(()=>r(!1))},[]),a)?(0,s.jsx)("div",{className:"text-center mt-16",children:"Loading..."}):n?(0,s.jsx)("div",{className:"text-red-600 text-center mt-16",children:n}):(0,s.jsxs)("div",{className:"max-w-2xl mx-auto mt-10 bg-white rounded-2xl shadow p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center text-[#1a237e]",children:"Manage Users"}),(0,s.jsx)("ul",{className:"divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("li",{className:"py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-[#171717]",children:e.name}),(0,s.jsx)("span",{className:"block text-gray-500 text-sm",children:e.email})]}),(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsxs)("select",{className:"border rounded px-2 py-1 text-sm",value:e.role||"USER",onChange:t=>d(e.id,t.target.value),disabled:i===e.id,children:[(0,s.jsx)("option",{value:"USER",children:"User"}),(0,s.jsx)("option",{value:"ADMIN",children:"Admin"})]}),(0,s.jsx)("button",{className:"px-3 py-1 rounded bg-red-600 text-white text-xs font-semibold hover:bg-red-700 transition",onClick:()=>h(e.id),disabled:i===e.id,children:"Delete"})]})]},e.id))})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(657)),_N_E=e.O()}]);