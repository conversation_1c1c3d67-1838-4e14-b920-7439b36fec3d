"use client";

import React, { useEffect, useRef, useState } from "react";
import { fabric } from "fabric";
import {
  Type,
  Image as ImageIcon,
  Palette,
  RotateCw,
  Move,
  Trash2,
  Download,
  Upload,
  Undo,
  Redo,
  Save,
} from "lucide-react";

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

interface ProductCustomizerProps {
  productImage: string;
  onCustomizationChange: (customization: CustomizationData) => void;
  initialCustomization?: CustomizationData | null;
}

const ProductCustomizer: React.FC<ProductCustomizerProps> = ({
  productImage,
  onCustomizationChange,
  initialCustomization,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [selectedTool, setSelectedTool] = useState<string>("select");
  const [textColor, setTextColor] = useState("#000000");
  const [fontSize, setFontSize] = useState(20);
  const [fontFamily, setFontFamily] = useState("Arial");
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!canvasRef.current) return;

    const fabricCanvas = new fabric.Canvas(canvasRef.current, {
      width: 500,
      height: 500,
      backgroundColor: "#ffffff",
    });

    // Load product image as background
    if (productImage) {
      fabric.Image.fromURL(
        productImage,
        (img) => {
          if (img) {
            img.scaleToWidth(500);
            img.scaleToHeight(500);
            img.set({
              selectable: false,
              evented: false,
              opacity: 0.8,
            });
            fabricCanvas.setBackgroundImage(
              img,
              fabricCanvas.renderAll.bind(fabricCanvas)
            );
          }
        },
        { crossOrigin: "anonymous" }
      );
    }

    // Canvas event listeners
    fabricCanvas.on("object:added", saveState);
    fabricCanvas.on("object:removed", saveState);
    fabricCanvas.on("object:modified", saveState);

    setCanvas(fabricCanvas);
    saveState();

    return () => {
      fabricCanvas.dispose();
    };
  }, [productImage]);

  // Save canvas state for undo/redo
  const saveState = () => {
    if (!canvas) return;

    const state = JSON.stringify(canvas.toJSON());
    setHistory((prev) => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(state);
      return newHistory.slice(-20); // Keep last 20 states
    });
    setHistoryIndex((prev) => prev + 1);

    // Notify parent of changes
    onCustomizationChange({
      canvasData: state,
      preview: canvas.toDataURL(),
    });
  };

  // Undo functionality
  const undo = () => {
    if (historyIndex > 0 && canvas) {
      setHistoryIndex((prev) => prev - 1);
      canvas.loadFromJSON(history[historyIndex - 1], () => {
        canvas.renderAll();
      });
    }
  };

  // Redo functionality
  const redo = () => {
    if (historyIndex < history.length - 1 && canvas) {
      setHistoryIndex((prev) => prev + 1);
      canvas.loadFromJSON(history[historyIndex + 1], () => {
        canvas.renderAll();
      });
    }
  };

  // Add text to canvas
  const addText = () => {
    if (!canvas) return;

    const text = new fabric.IText("Click to edit", {
      left: 100,
      top: 100,
      fontFamily: fontFamily,
      fontSize: fontSize,
      fill: textColor,
    });

    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.renderAll();
  };

  // Add shape to canvas
  const addShape = (shapeType: string) => {
    if (!canvas) return;

    let shape;
    switch (shapeType) {
      case "rectangle":
        shape = new fabric.Rect({
          left: 100,
          top: 100,
          width: 100,
          height: 100,
          fill: textColor,
        });
        break;
      case "circle":
        shape = new fabric.Circle({
          left: 100,
          top: 100,
          radius: 50,
          fill: textColor,
        });
        break;
      case "triangle":
        shape = new fabric.Triangle({
          left: 100,
          top: 100,
          width: 100,
          height: 100,
          fill: textColor,
        });
        break;
    }

    if (shape) {
      canvas.add(shape);
      canvas.setActiveObject(shape);
      canvas.renderAll();
    }
  };

  // Upload and add image
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !canvas) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const imgUrl = e.target?.result as string;
      fabric.Image.fromURL(imgUrl, (img) => {
        if (img) {
          img.scaleToWidth(150);
          img.set({
            left: 100,
            top: 100,
          });
          canvas.add(img);
          canvas.setActiveObject(img);
          canvas.renderAll();
        }
      });
    };
    reader.readAsDataURL(file);
  };

  // Delete selected object
  const deleteSelected = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      canvas.remove(activeObject);
      canvas.renderAll();
    }
  };

  // Clear canvas
  const clearCanvas = () => {
    if (!canvas) return;

    canvas.clear();
    // Re-add background image
    if (productImage) {
      fabric.Image.fromURL(
        productImage,
        (img) => {
          if (img) {
            img.scaleToWidth(500);
            img.scaleToHeight(500);
            img.set({
              selectable: false,
              evented: false,
              opacity: 0.8,
            });
            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas));
          }
        },
        { crossOrigin: "anonymous" }
      );
    }
  };

  // Export canvas as image
  const exportCanvas = () => {
    if (!canvas) return;

    const dataURL = canvas.toDataURL({
      format: "png",
      quality: 1,
      multiplier: 2,
    });

    const link = document.createElement("a");
    link.download = "custom-product.png";
    link.href = dataURL;
    link.click();
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
      <div className="mb-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          🎨 Advanced Product Customizer
        </h3>
        <p className="text-gray-600">
          Design your product with text, shapes, and images
        </p>
      </div>

      {/* Toolbar */}
      <div className="mb-6 p-4 bg-gray-50 rounded-xl">
        <div className="flex flex-wrap gap-2 mb-4">
          {/* Tool Buttons */}
          <button
            onClick={addText}
            className="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Type size={16} />
            Add Text
          </button>

          <button
            onClick={() => addShape("rectangle")}
            className="flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            Rectangle
          </button>

          <button
            onClick={() => addShape("circle")}
            className="flex items-center gap-2 px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
          >
            Circle
          </button>

          <button
            onClick={() => addShape("triangle")}
            className="flex items-center gap-2 px-3 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
          >
            Triangle
          </button>

          <label className="flex items-center gap-2 px-3 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors cursor-pointer">
            <ImageIcon size={16} />
            Upload Image
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
          </label>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Color:</label>
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
            />
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">
              Font Size:
            </label>
            <input
              type="range"
              min="10"
              max="100"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-20"
            />
            <span className="text-sm text-gray-600">{fontSize}px</span>
          </div>

          <select
            value={fontFamily}
            onChange={(e) => setFontFamily(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded text-sm"
          >
            <option value="Arial">Arial</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Georgia">Georgia</option>
            <option value="Verdana">Verdana</option>
          </select>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={undo}
            disabled={historyIndex <= 0}
            className="flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Undo size={14} />
            Undo
          </button>

          <button
            onClick={redo}
            disabled={historyIndex >= history.length - 1}
            className="flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Redo size={14} />
            Redo
          </button>

          <button
            onClick={deleteSelected}
            className="flex items-center gap-1 px-2 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
          >
            <Trash2 size={14} />
            Delete
          </button>

          <button
            onClick={clearCanvas}
            className="flex items-center gap-1 px-2 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
          >
            Clear All
          </button>

          <button
            onClick={exportCanvas}
            className="flex items-center gap-1 px-2 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            <Download size={14} />
            Export
          </button>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex justify-center">
        <div className="border-2 border-gray-300 rounded-lg overflow-hidden">
          <canvas ref={canvasRef} />
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500 text-center">
        💡 Tip: Click and drag to move objects, double-click text to edit
      </div>
    </div>
  );
};

export default ProductCustomizer;
