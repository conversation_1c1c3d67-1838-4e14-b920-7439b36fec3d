"use client";

import * as React from "react";
import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import dynamic from "next/dynamic";
import {
  Type,
  Image as ImageIcon,
  Palette,
  RotateCw,
  Move,
  Trash2,
  Download,
  Upload,
  Undo,
  Redo,
  Save,
  Square,
  Circle,
  Triangle,
} from "lucide-react";

// Dynamic import for fabric to avoid SSR issues
let fabric: any = null;
if (typeof window !== "undefined") {
  fabric = require("fabric").fabric;
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

// Fabric.js object interface
interface FabricObject {
  selectable?: boolean;
  evented?: boolean;
  id?: string;
  type?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  fontSize?: number;
  fontFamily?: string;
  textAlign?: string;
  fontWeight?: string;
  fontStyle?: string;
  textDecoration?: string;
  angle?: number;
  left?: number;
  top?: number;
  width?: number;
  height?: number;
  scaleX?: number;
  scaleY?: number;
  set?: (props: any) => void;
  scale?: (value: number) => void;
  center?: () => void;
  rotate?: (angle: number) => void;
}

interface ProductCustomizerProps {
  productImage: string;
  onCustomizationChange: (customization: CustomizationData) => void;
  initialCustomization?: CustomizationData | null;
  productId?: string | number;
  // Legacy props for backward compatibility
  initialData?: CustomizationData;
  onSave?: (data: CustomizationData) => void;
  onCanvasReady?: (canvas: any) => void;
  productImageUrl?: string;
  productName?: string;
}

export default function ProductCustomizer({
  // New interface props
  productImage,
  onCustomizationChange,
  initialCustomization,
  productId,
  // Legacy props for backward compatibility
  initialData,
  onSave,
  onCanvasReady,
  productImageUrl,
  productName = "Custom Product",
}: ProductCustomizerProps) {
  // Normalize props to handle both interfaces
  const normalizedProductImage = productImage || productImageUrl || "";
  const normalizedInitialData = initialCustomization || initialData;
  const normalizedOnSave = onCustomizationChange || onSave;

  // Debounced auto-save function
  const debouncedAutoSave = useMemo(
    () =>
      debounce((data: CustomizationData) => {
        if (normalizedOnSave) {
          normalizedOnSave(data);
        }
      }, 500),
    [normalizedOnSave]
  );
  // Canvas and editor state
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [canvas, setCanvas] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<
    "text" | "shapes" | "images" | "colors"
  >("text");
  const [activeObject, setActiveObject] = useState<any>(null);
  const [canvasHistory, setCanvasHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [fabricLoaded, setFabricLoaded] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
  const [showUnsavedWarning, setShowUnsavedWarning] = useState<boolean>(false);
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [showSaveSuccess, setShowSaveSuccess] = useState<boolean>(false);

  // Text properties
  const [textColor, setTextColor] = useState<string>("#000000");
  const [fontSize, setFontSize] = useState<number>(24);
  const [fontFamily, setFontFamily] = useState<string>("Arial");
  const [textAlign, setTextAlign] = useState<string>("left");
  const [fontWeight, setFontWeight] = useState<string>("normal");
  const [fontStyle, setFontStyle] = useState<string>("normal");
  const [textDecoration, setTextDecoration] = useState<string>("none");

  // Available fonts
  const fonts = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Courier New",
    "Verdana",
    "Georgia",
    "Palatino",
    "Garamond",
    "Bookman",
    "Comic Sans MS",
    "Trebuchet MS",
    "Impact",
  ];

  // Available colors
  const colors = [
    "#000000", // Black
    "#FFFFFF", // White
    "#FF0000", // Red
    "#00FF00", // Green
    "#0000FF", // Blue
    "#FFFF00", // Yellow
    "#FF00FF", // Magenta
    "#00FFFF", // Cyan
    "#FFA500", // Orange
    "#800080", // Purple
    "#008000", // Dark Green
    "#800000", // Maroon
    "#008080", // Teal
    "#000080", // Navy
    "#FFC0CB", // Pink
    "#A52A2A", // Brown
    "#808080", // Gray
    "#C0C0C0", // Silver
  ];

  // Sample images for quick insertion
  const sampleImages = [
    "/sample-images/logo1.png",
    "/sample-images/logo2.png",
    "/sample-images/shape1.svg",
    "/sample-images/shape2.svg",
    "/sample-images/pattern1.png",
    "/sample-images/pattern2.png",
  ];

  // Initialize Fabric canvas
  useEffect(() => {
    if (!canvasRef.current || typeof window === "undefined") return;

    // Load Fabric.js dynamically
    const loadFabric = async () => {
      if (!fabric) {
        const fabricModule = await import("fabric");
        fabric = fabricModule.fabric;
      }

      // Create canvas
      const fabricCanvas = new fabric.Canvas(canvasRef.current, {
        width: 500,
        height: 500,
        backgroundColor: "#FFFFFF",
        preserveObjectStacking: true,
      });

      // Set up event listeners
      fabricCanvas.on("object:modified", handleCanvasChange);
      fabricCanvas.on("object:added", handleCanvasChange);
      fabricCanvas.on("object:removed", handleCanvasChange);
      fabricCanvas.on("selection:created", handleSelectionChange);
      fabricCanvas.on("selection:updated", handleSelectionChange);
      fabricCanvas.on("selection:cleared", () => setActiveObject(null));

      // Load initial data if provided
      if (normalizedInitialData?.canvasData) {
        fabricCanvas.loadFromJSON(normalizedInitialData.canvasData, () => {
          fabricCanvas.renderAll();
          saveToHistory(fabricCanvas);
        });
      } else if (normalizedProductImage) {
        // Load product image as background
        fabric.Image.fromURL(
          normalizedProductImage,
          (img: any) => {
            // Scale image to fit canvas while maintaining aspect ratio
            const scale = Math.min(
              fabricCanvas.width / img.width,
              fabricCanvas.height / img.height
            );
            img.scale(scale * 0.8);

            // Center the image
            img.set({
              left: fabricCanvas.width / 2,
              top: fabricCanvas.height / 2,
              originX: "center",
              originY: "center",
              selectable: false, // Make background non-selectable
              evented: false, // Disable events on background
            });

            fabricCanvas.add(img);
            fabricCanvas.sendToBack(img);
            fabricCanvas.renderAll();
            saveToHistory(fabricCanvas);
          },
          { crossOrigin: "anonymous" }
        );
      }

      setCanvas(fabricCanvas);
      setFabricLoaded(true);

      // Notify parent component that canvas is ready
      if (onCanvasReady) {
        onCanvasReady(fabricCanvas);
      }

      // Save initial state to history
      saveToHistory(fabricCanvas);
    };

    loadFabric();

    // Cleanup function
    return () => {
      if (canvas) {
        canvas.dispose();
      }
    };
  }, []);

  // Check if canvas is empty (only has background image)
  const isCanvasEmpty = (): boolean => {
    if (!canvas) return true;
    const objects = canvas.getObjects();
    // Consider canvas empty if it only has the background image (non-selectable)
    return (
      objects.filter((obj: FabricObject) => obj.selectable !== false).length ===
      0
    );
  };

  // Handle canvas changes
  const handleCanvasChange = () => {
    if (!canvas) return;
    saveToHistory(canvas);
    setHasUnsavedChanges(true);

    // Auto-save to parent component (debounced)
    if (debouncedAutoSave) {
      // Temporarily disable selection borders for clean preview
      const activeObj = canvas.getActiveObject();
      canvas.discardActiveObject();
      canvas.renderAll();

      // Get data URL for preview
      const previewUrl = canvas.toDataURL({
        format: "png",
        quality: 0.8,
      });

      // Get JSON data
      const canvasData = JSON.stringify(canvas.toJSON());

      // Re-enable selection
      if (activeObj) {
        canvas.setActiveObject(activeObj);
        canvas.renderAll();
      }

      // Call debounced callback with updated data
      debouncedAutoSave({
        canvasData,
        preview: previewUrl,
      });
    }
  };

  // Handle selection changes
  const handleSelectionChange = (e: { selected: FabricObject[] }) => {
    const selectedObject = e.selected[0];
    setActiveObject(selectedObject);

    // Update text properties if text is selected
    if (selectedObject && selectedObject.type === "text") {
      setTextColor(selectedObject.fill || "#000000");
      setFontSize(selectedObject.fontSize || 24);
      setFontFamily(selectedObject.fontFamily || "Arial");
      setTextAlign(selectedObject.textAlign || "left");
      setFontWeight(selectedObject.fontWeight || "normal");
      setFontStyle(selectedObject.fontStyle || "normal");
      setTextDecoration(selectedObject.textDecoration || "none");
    }
  };

  // Save canvas state to history
  const saveToHistory = (fabricCanvas: any) => {
    if (!fabricCanvas) return;

    const json = JSON.stringify(fabricCanvas.toJSON());

    // If we're not at the end of the history, truncate
    if (historyIndex < canvasHistory.length - 1) {
      setCanvasHistory((prev) => prev.slice(0, historyIndex + 1));
    }

    setCanvasHistory((prev) => [...prev, json]);
    setHistoryIndex((prev) => prev + 1);
  };

  // Undo action
  const handleUndo = useCallback(() => {
    if (historyIndex <= 0 || !canvas) return;

    const newIndex = historyIndex - 1;
    setHistoryIndex(newIndex);

    canvas.loadFromJSON(canvasHistory[newIndex], () => {
      canvas.renderAll();
      setHasUnsavedChanges(true);
    });
  }, [historyIndex, canvas, canvasHistory]);

  // Redo action
  const handleRedo = useCallback(() => {
    if (historyIndex >= canvasHistory.length - 1 || !canvas) return;

    const newIndex = historyIndex + 1;
    setHistoryIndex(newIndex);

    canvas.loadFromJSON(canvasHistory[newIndex], () => {
      canvas.renderAll();
      setHasUnsavedChanges(true);
    });
  }, [historyIndex, canvasHistory, canvas]);

  // Add text to canvas
  const addText = (text = "Double click to edit") => {
    if (!canvas || !fabric) return;

    const textObject = new fabric.IText(text, {
      left: canvas.width / 2,
      top: canvas.height / 2,
      originX: "center",
      originY: "center",
      fontFamily: fontFamily,
      fontSize: fontSize,
      fill: textColor,
      textAlign: textAlign,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      textDecoration: textDecoration,
    });

    canvas.add(textObject);
    canvas.setActiveObject(textObject);
    canvas.renderAll();
  };

  // Add image to canvas
  const addImage = (url: string) => {
    if (!canvas || !fabric) return;

    fabric.Image.fromURL(
      url,
      (img: any) => {
        // Scale image to fit canvas while maintaining aspect ratio
        const scale = Math.min(
          canvas.width / 3 / img.width,
          canvas.height / 3 / img.height
        );
        img.scale(scale);

        // Center the image
        img.set({
          left: canvas.width / 2,
          top: canvas.height / 2,
          originX: "center",
          originY: "center",
        });

        canvas.add(img);
        canvas.setActiveObject(img);
        canvas.renderAll();
      },
      { crossOrigin: "anonymous" }
    );
  };

  // Add rectangle to canvas
  const addRectangle = () => {
    if (!canvas || !fabric) return;

    const rect = new fabric.Rect({
      left: canvas.width / 2,
      top: canvas.height / 2,
      originX: "center",
      originY: "center",
      width: 100,
      height: 60,
      fill: "#3b82f6",
      stroke: "#1e40af",
      strokeWidth: 2,
    });

    canvas.add(rect);
    canvas.setActiveObject(rect);
    canvas.renderAll();
  };

  // Add circle to canvas
  const addCircle = () => {
    if (!canvas || !fabric) return;

    const circle = new fabric.Circle({
      left: canvas.width / 2,
      top: canvas.height / 2,
      originX: "center",
      originY: "center",
      radius: 50,
      fill: "#10b981",
      stroke: "#047857",
      strokeWidth: 2,
    });

    canvas.add(circle);
    canvas.setActiveObject(circle);
    canvas.renderAll();
  };

  // Add triangle to canvas
  const addTriangle = () => {
    if (!canvas || !fabric) return;

    const triangle = new fabric.Triangle({
      left: canvas.width / 2,
      top: canvas.height / 2,
      originX: "center",
      originY: "center",
      width: 100,
      height: 100,
      fill: "#f59e0b",
      stroke: "#d97706",
      strokeWidth: 2,
    });

    canvas.add(triangle);
    canvas.setActiveObject(triangle);
    canvas.renderAll();
  };

  // Upload image
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0]) return;

    const file = e.target.files[0];
    const reader = new FileReader();

    reader.onload = (event) => {
      if (!event.target?.result) return;
      addImage(event.target.result as string);
    };

    reader.readAsDataURL(file);
    e.target.value = ""; // Reset input
  };

  // Delete selected object
  const deleteSelected = useCallback(() => {
    if (!canvas || !canvas.getActiveObject()) return;

    canvas.remove(canvas.getActiveObject());
    canvas.renderAll();
    setActiveObject(null);
  }, [canvas]);

  // Update text properties
  const updateTextProperty = (
    property: string,
    value: string | number | boolean
  ) => {
    if (!canvas || !activeObject || activeObject.type !== "text") return;

    activeObject.set({ [property]: value });
    canvas.renderAll();

    // Update local state
    switch (property) {
      case "fill":
        setTextColor(value as string);
        break;
      case "fontSize":
        setFontSize(value as number);
        break;
      case "fontFamily":
        setFontFamily(value as string);
        break;
      case "textAlign":
        setTextAlign(value as string);
        break;
      case "fontWeight":
        setFontWeight(value as string);
        break;
      case "fontStyle":
        setFontStyle(value as string);
        break;
      case "textDecoration":
        setTextDecoration(value as string);
        break;
    }
  };

  // Export canvas as image
  const exportImage = () => {
    if (!canvas) return;

    setIsExporting(true);

    // Temporarily disable selection borders
    const activeObj = canvas.getActiveObject();
    canvas.discardActiveObject();
    canvas.renderAll();

    // Get data URL
    const dataUrl = canvas.toDataURL({
      format: "png",
      quality: 1,
      multiplier: 2, // Higher resolution
    });

    // Re-enable selection
    if (activeObj) {
      canvas.setActiveObject(activeObj);
      canvas.renderAll();
    }

    // Create download link
    const link = document.createElement("a");
    link.download = `${productName
      .replace(/\s+/g, "-")
      .toLowerCase()}-design.png`;
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setIsExporting(false);
  };

  // Save design
  const saveDesign = useCallback(() => {
    if (!canvas || !normalizedOnSave) return;

    // Temporarily disable selection borders
    const activeObj = canvas.getActiveObject();
    canvas.discardActiveObject();
    canvas.renderAll();

    // Get data URL for preview
    const previewUrl = canvas.toDataURL({
      format: "png",
      quality: 0.8,
    });

    // Get JSON data
    const canvasData = JSON.stringify(canvas.toJSON());

    // Re-enable selection
    if (activeObj) {
      canvas.setActiveObject(activeObj);
      canvas.renderAll();
    }

    // Call onSave callback
    normalizedOnSave({
      canvasData,
      preview: previewUrl,
    });

    setHasUnsavedChanges(false);
  }, [canvas, normalizedOnSave]);

  // Bring object forward
  const bringForward = () => {
    if (!canvas || !activeObject) return;
    canvas.bringForward(activeObject);
    canvas.renderAll();
  };

  // Send object backward
  const sendBackward = () => {
    if (!canvas || !activeObject) return;
    canvas.sendBackward(activeObject);
    canvas.renderAll();
  };

  // Rotate object
  const rotateObject = (angle: number = 90) => {
    if (!canvas || !activeObject) return;
    activeObject.rotate((activeObject.angle || 0) + angle);
    canvas.renderAll();
  };

  // Center object
  const centerObject = () => {
    if (!canvas || !activeObject) return;
    activeObject.center();
    canvas.renderAll();
  };

  // Set active object by ID
  const setActiveObjectById = (id: string) => {
    if (!canvas) return;

    const objects = canvas.getObjects();
    const obj = objects.find((o: FabricObject) => o.id === id);
    if (obj) {
      canvas.setActiveObject(obj);
      canvas.renderAll();
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when canvas is focused or no input is focused
      const activeElement = document.activeElement;
      const isInputFocused =
        activeElement?.tagName === "INPUT" ||
        activeElement?.tagName === "TEXTAREA" ||
        (activeElement as HTMLElement)?.contentEditable === "true";

      if (isInputFocused) return;

      // Prevent default for our shortcuts
      if (
        (e.ctrlKey || e.metaKey) &&
        ["z", "y", "s"].includes(e.key.toLowerCase())
      ) {
        e.preventDefault();
      }

      if (e.key === "Delete" || e.key === "Backspace") {
        e.preventDefault();
      }

      // Handle shortcuts
      if (
        (e.ctrlKey || e.metaKey) &&
        e.key.toLowerCase() === "z" &&
        !e.shiftKey
      ) {
        handleUndo();
      } else if (
        (e.ctrlKey || e.metaKey) &&
        (e.key.toLowerCase() === "y" ||
          (e.key.toLowerCase() === "z" && e.shiftKey))
      ) {
        handleRedo();
      } else if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === "s") {
        saveDesign();
      } else if (e.key === "Delete" || e.key === "Backspace") {
        deleteSelected();
      } else if (e.key === "Escape") {
        if (canvas) {
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    canvas,
    historyIndex,
    canvasHistory,
    handleUndo,
    handleRedo,
    saveDesign,
    deleteSelected,
  ]);

  // Show loading state while Fabric.js is loading
  if (!fabricLoaded) {
    return (
      <div
        className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 space-y-6"
        data-has-unsaved-changes={hasUnsavedChanges ? "true" : "false"}
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 font-medium">
              Loading Professional Designer...
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Preparing your advanced customization tools
            </p>
            <div className="mt-4 bg-blue-50 rounded-lg p-3 text-sm text-blue-700">
              💡 <strong>Tip:</strong> You&apos;ll be able to add text, shapes,
              images, and more!
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <React.Fragment>
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              🎨 Professional Designer
            </h3>
            {hasUnsavedChanges && (
              <div className="flex items-center gap-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-full text-sm">
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                Auto-saving...
              </div>
            )}
          </div>
          <p className="text-gray-600 mb-3">
            Create stunning designs with our professional-grade editor. Add
            text, shapes, images, and customize colors.
          </p>
          <div className="flex flex-wrap gap-2 text-xs">
            <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full">
              ✓ Real-time preview
            </span>
            <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
              ✓ Undo/Redo
            </span>
            <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
              ✓ High-quality export
            </span>
            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full">
              ✓ Auto-save
            </span>
            <span className="bg-indigo-100 text-indigo-700 px-2 py-1 rounded-full">
              ✓ Keyboard shortcuts
            </span>
          </div>

          {/* Keyboard shortcuts info */}
          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
            <div className="text-xs text-gray-600">
              <strong>Keyboard Shortcuts:</strong>
              <span className="ml-2">Ctrl+Z (Undo)</span>
              <span className="ml-2">Ctrl+Y (Redo)</span>
              <span className="ml-2">Ctrl+S (Save)</span>
              <span className="ml-2">Delete (Remove)</span>
              <span className="ml-2">Esc (Deselect)</span>
            </div>
          </div>
        </div>

        {/* Main editor layout with sidebar */}
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Left sidebar with tabs */}
          <div className="w-full lg:w-64 bg-gray-50 rounded-xl overflow-hidden border border-gray-200 flex flex-col order-2 lg:order-1">
            {/* Sidebar tabs */}
            <div className="flex border-b border-gray-200">
              <button
                className={`flex-1 py-3 text-sm font-medium ${
                  activeTab === "text"
                    ? "bg-white text-blue-600"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
                onClick={() => setActiveTab("text")}
              >
                Text
              </button>
              <button
                className={`flex-1 py-3 text-sm font-medium ${
                  activeTab === "shapes"
                    ? "bg-white text-blue-600"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
                onClick={() => setActiveTab("shapes")}
              >
                Shapes
              </button>
              <button
                className={`flex-1 py-3 text-sm font-medium ${
                  activeTab === "images"
                    ? "bg-white text-blue-600"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
                onClick={() => setActiveTab("images")}
              >
                Images
              </button>
              <button
                className={`flex-1 py-3 text-sm font-medium ${
                  activeTab === "colors"
                    ? "bg-white text-blue-600"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
                onClick={() => setActiveTab("colors")}
              >
                Colors
              </button>
            </div>

            {/* Tab content */}
            <div className="flex-1 overflow-y-auto p-4">
              {/* Text tab */}
              {activeTab === "text" && (
                <div className="space-y-4">
                  <button
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                    onClick={() => addText()}
                  >
                    <Type size={16} /> Add Text
                  </button>

                  {activeObject && activeObject.type === "text" && (
                    <div className="space-y-4 pt-4 border-t border-gray-200">
                      <h4 className="font-medium text-gray-900">
                        Text Properties
                      </h4>

                      {/* Font family */}
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">
                          Font
                        </label>
                        <select
                          className="w-full border border-gray-300 rounded-lg p-2 text-sm"
                          value={fontFamily}
                          onChange={(e) =>
                            updateTextProperty("fontFamily", e.target.value)
                          }
                        >
                          {fonts.map((font) => (
                            <option key={font} value={font}>
                              {font}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Font size */}
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">
                          Size
                        </label>
                        <input
                          type="range"
                          min="8"
                          max="80"
                          value={fontSize}
                          onChange={(e) =>
                            updateTextProperty(
                              "fontSize",
                              parseInt(e.target.value)
                            )
                          }
                          className="w-full"
                        />
                        <div className="text-xs text-gray-500 text-right">
                          {fontSize}px
                        </div>
                      </div>

                      {/* Text color */}
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">
                          Color
                        </label>
                        <div className="grid grid-cols-6 gap-2">
                          {colors.slice(0, 12).map((color) => (
                            <button
                              key={color}
                              className={`w-full aspect-square rounded-full border ${
                                textColor === color
                                  ? "border-blue-500 ring-2 ring-blue-300"
                                  : "border-gray-300"
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => updateTextProperty("fill", color)}
                              aria-label={`Color ${color}`}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Text alignment */}
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">
                          Alignment
                        </label>
                        <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                          <button
                            className={`flex-1 py-1 ${
                              textAlign === "left"
                                ? "bg-blue-100 text-blue-600"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              updateTextProperty("textAlign", "left")
                            }
                          >
                            Left
                          </button>
                          <button
                            className={`flex-1 py-1 ${
                              textAlign === "center"
                                ? "bg-blue-100 text-blue-600"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              updateTextProperty("textAlign", "center")
                            }
                          >
                            Center
                          </button>
                          <button
                            className={`flex-1 py-1 ${
                              textAlign === "right"
                                ? "bg-blue-100 text-blue-600"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              updateTextProperty("textAlign", "right")
                            }
                          >
                            Right
                          </button>
                        </div>
                      </div>

                      {/* Text style */}
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">
                          Style
                        </label>
                        <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                          <button
                            className={`flex-1 py-1 font-bold ${
                              fontWeight === "bold"
                                ? "bg-blue-100 text-blue-600"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              updateTextProperty(
                                "fontWeight",
                                fontWeight === "bold" ? "normal" : "bold"
                              )
                            }
                          >
                            B
                          </button>
                          <button
                            className={`flex-1 py-1 italic ${
                              fontStyle === "italic"
                                ? "bg-blue-100 text-blue-600"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              updateTextProperty(
                                "fontStyle",
                                fontStyle === "italic" ? "normal" : "italic"
                              )
                            }
                          >
                            I
                          </button>
                          <button
                            className={`flex-1 py-1 underline ${
                              textDecoration === "underline"
                                ? "bg-blue-100 text-blue-600"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              updateTextProperty(
                                "textDecoration",
                                textDecoration === "underline"
                                  ? ""
                                  : "underline"
                              )
                            }
                          >
                            U
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Shapes tab */}
              {activeTab === "shapes" && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    <button
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                      onClick={addRectangle}
                    >
                      <Square size={16} /> Rectangle
                    </button>
                    <button
                      className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                      onClick={addCircle}
                    >
                      <Circle size={16} /> Circle
                    </button>
                    <button
                      className="w-full bg-yellow-600 text-white py-3 px-4 rounded-lg hover:bg-yellow-700 transition-colors flex items-center justify-center gap-2"
                      onClick={addTriangle}
                    >
                      <Triangle size={16} /> Triangle
                    </button>
                  </div>

                  {activeObject &&
                    activeObject.type !== "text" &&
                    activeObject.type !== "image" && (
                      <div className="space-y-4 pt-4 border-t border-gray-200">
                        <h4 className="font-medium text-gray-900">
                          Shape Properties
                        </h4>

                        {/* Shape color */}
                        <div>
                          <label className="block text-sm text-gray-600 mb-1">
                            Fill Color
                          </label>
                          <div className="grid grid-cols-6 gap-2">
                            {colors.slice(0, 12).map((color) => (
                              <button
                                key={color}
                                className={`w-full aspect-square rounded-full border ${
                                  activeObject?.fill === color
                                    ? "border-blue-500 ring-2 ring-blue-300"
                                    : "border-gray-300"
                                }`}
                                style={{ backgroundColor: color }}
                                onClick={() => {
                                  if (activeObject) {
                                    activeObject.set({ fill: color });
                                    canvas.renderAll();
                                  }
                                }}
                                aria-label={`Color ${color}`}
                              />
                            ))}
                          </div>
                        </div>

                        {/* Stroke color */}
                        <div>
                          <label className="block text-sm text-gray-600 mb-1">
                            Border Color
                          </label>
                          <div className="grid grid-cols-6 gap-2">
                            {colors.slice(0, 12).map((color) => (
                              <button
                                key={color}
                                className={`w-full aspect-square rounded-full border ${
                                  activeObject?.stroke === color
                                    ? "border-blue-500 ring-2 ring-blue-300"
                                    : "border-gray-300"
                                }`}
                                style={{ backgroundColor: color }}
                                onClick={() => {
                                  if (activeObject) {
                                    activeObject.set({ stroke: color });
                                    canvas.renderAll();
                                  }
                                }}
                                aria-label={`Border color ${color}`}
                              />
                            ))}
                          </div>
                        </div>

                        {/* Stroke width */}
                        <div>
                          <label className="block text-sm text-gray-600 mb-1">
                            Border Width
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="10"
                            value={activeObject?.strokeWidth || 0}
                            onChange={(e) => {
                              if (activeObject) {
                                activeObject.set({
                                  strokeWidth: parseInt(e.target.value),
                                });
                                canvas.renderAll();
                              }
                            }}
                            className="w-full"
                          />
                          <div className="text-xs text-gray-500 text-right">
                            {activeObject?.strokeWidth || 0}px
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              )}

              {/* Images tab */}
              {activeTab === "images" && (
                <div className="space-y-4">
                  <button
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload size={16} /> Upload Image
                  </button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                  />

                  <h4 className="font-medium text-gray-900 pt-4 border-t border-gray-200">
                    Sample Images
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {sampleImages.map((img, index) => (
                      <button
                        key={index}
                        className="border border-gray-200 rounded-lg p-2 hover:border-blue-500 transition-colors"
                        onClick={() => addImage(img)}
                      >
                        <img
                          src={img}
                          alt={`Sample ${index + 1}`}
                          className="w-full aspect-square object-contain"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Colors tab */}
              {activeTab === "colors" && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">
                    Background Color
                  </h4>
                  <div className="grid grid-cols-4 gap-2">
                    {colors.map((color) => (
                      <button
                        key={color}
                        className={`w-full aspect-square rounded-lg border ${
                          canvas?.backgroundColor === color
                            ? "border-blue-500 ring-2 ring-blue-300"
                            : "border-gray-300"
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => {
                          if (canvas) {
                            canvas.setBackgroundColor(color, () =>
                              canvas.renderAll()
                            );
                            handleCanvasChange();
                          }
                        }}
                        aria-label={`Background color ${color}`}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Main canvas area */}
          <div className="flex-1 order-1 lg:order-2">
            <div className="relative">
              {/* Mobile-friendly canvas info */}
              <div className="lg:hidden mb-3 p-3 bg-blue-50 rounded-lg text-sm text-blue-700">
                💡 <strong>Tip:</strong> Use the tools below to customize your
                design. Tap objects to select and edit them.
              </div>
              {/* Canvas container */}
              <div className="border border-gray-300 rounded-lg overflow-hidden bg-white shadow-inner relative">
                <canvas ref={canvasRef} />

                {/* Empty canvas guide */}
                {canvas &&
                  canvas
                    .getObjects()
                    .filter((obj: FabricObject) => obj.selectable !== false)
                    .length === 0 && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <div className="text-center bg-white bg-opacity-90 p-6 rounded-xl shadow-lg max-w-sm">
                        <div className="text-4xl mb-3">🎨</div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Start Creating!
                        </h4>
                        <p className="text-sm text-gray-600 mb-4">
                          Use the tools on the left to add text, shapes, or
                          images to your design.
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                          <button
                            className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700 transition-colors pointer-events-auto"
                            onClick={() => addText("Your Text Here")}
                          >
                            + Add Text
                          </button>
                          <button
                            className="bg-green-600 text-white px-3 py-1 rounded-full text-xs hover:bg-green-700 transition-colors pointer-events-auto"
                            onClick={addRectangle}
                          >
                            + Add Shape
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
              </div>

              {/* Toolbar */}
              <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-1 flex flex-col gap-1">
                <button
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  onClick={() => rotateObject()}
                  title="Rotate 90°"
                  disabled={!activeObject}
                >
                  <RotateCw size={16} />
                </button>
                <button
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  onClick={centerObject}
                  title="Center Object"
                  disabled={!activeObject}
                >
                  <Move size={16} />
                </button>
                <button
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  onClick={bringForward}
                  title="Bring Forward"
                  disabled={!activeObject}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="7" y="7" width="10" height="10" rx="1" />
                    <rect x="4" y="4" width="10" height="10" rx="1" />
                  </svg>
                </button>
                <button
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  onClick={sendBackward}
                  title="Send Backward"
                  disabled={!activeObject}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="7" y="7" width="10" height="10" rx="1" />
                    <rect x="10" y="10" width="10" height="10" rx="1" />
                  </svg>
                </button>
                <button
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-red-500"
                  onClick={deleteSelected}
                  title="Delete Object"
                  disabled={!activeObject}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            {/* Bottom toolbar */}
            <div className="mt-4 flex justify-between items-center">
              <div className="flex gap-2">
                <button
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm"
                  onClick={handleUndo}
                  disabled={historyIndex <= 0}
                >
                  <Undo size={16} /> Undo
                </button>
                <button
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm"
                  onClick={handleRedo}
                  disabled={historyIndex >= canvasHistory.length - 1}
                >
                  <Redo size={16} /> Redo
                </button>
              </div>

              <div className="flex gap-2">
                <button
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm"
                  onClick={exportImage}
                  disabled={isExporting}
                >
                  <Download size={16} />
                  {isExporting ? "Exporting..." : "Export"}
                </button>
                {onSave && (
                  <button
                    className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-1 text-sm"
                    onClick={saveDesign}
                  >
                    <Save size={16} /> Save Design
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Warning about unsaved changes */}
      {showUnsavedWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-bold mb-4">Unsaved Changes</h3>
            <p className="mb-6">
              You have unsaved changes to your design. Are you sure you want to
              leave without saving?
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                onClick={() => setShowUnsavedWarning(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                onClick={() => {
                  setShowUnsavedWarning(false);
                  // Handle discard action
                }}
              >
                Discard Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
}
