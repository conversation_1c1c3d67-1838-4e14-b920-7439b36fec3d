import { requireAuth } from "@/lib/auth";
import UserProfileClient from "./UserProfileClient";

type User = {
  id: number;
  name?: string;
  email: string;
  role: string;
  profileImage?: string;
};

export default async function ProfileManagement() {
  // Server-side authentication check - redirects if not authenticated
  const auth = await requireAuth();

  return <UserProfileClient user={auth.user} />;
}
