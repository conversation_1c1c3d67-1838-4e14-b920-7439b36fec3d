"use client";
import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import Header from "@/components/Header";
import Cart from "@/components/Cart";
import { getApiUrl, API_CONFIG } from "@/lib/config";

type User = {
  id: string | number;
  name?: string;
  email: string;
  role: string;
  profileImage?: string;
};

export default function ProfileManagement() {
  const [user, setUser] = useState<User | null>(null);
  const [editName, setEditName] = useState("");
  const [profileImage, setProfileImage] = useState<string>("");
  const [imageUploading, setImageUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [profileSaving, setProfileSaving] = useState(false);
  const [passwordChanging, setPasswordChanging] = useState(false);
  const [passwords, setPasswords] = useState({
    oldPassword: "",
    newPassword: "",
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [cartCount, setCartCount] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  const updateCartCount = () => {
    const cart = JSON.parse(localStorage.getItem("cart") || "[]");
    setCartCount(cart.length);
  };

  const handleCartClick = () => {
    setIsCartOpen(true);
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
    updateCartCount(); // Update count when cart closes
  };

  useEffect(() => {
    // Load cart count
    updateCartCount();
  }, []);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      setError("Not authenticated");
      setLoading(false);
      return;
    }
    fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD), {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => {
        if (data && data.user) {
          setUser(data.user);
          setEditName(data.user.name || "");
          setProfileImage(data.user.profileImage || "");
        } else setError(data.error || "Failed to load user info");
        setLoading(false);
      })
      .catch(() => {
        setError("Failed to load user info");
        setLoading(false);
      });
  }, []);

  async function handleProfileUpdate(e: React.FormEvent) {
    e.preventDefault();
    const token = localStorage.getItem("token");
    if (!token || !user) return;

    setProfileSaving(true);
    setError("");
    setSuccess("");

    try {
      const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.PROFILE), {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ name: editName, profileImage }),
      });

      if (res.ok) {
        setUser((u: User | null) =>
          u ? { ...u, name: editName, profileImage } : u
        );
        setSuccess("Profile updated successfully");
        // Dispatch event to update header profile image
        window.dispatchEvent(new Event("profileImageUpdated"));
      } else {
        setError("Failed to update profile");
      }
    } catch {
      setError("Failed to update profile");
    } finally {
      setProfileSaving(false);
    }
  }

  async function handleImageUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;

    setImageUploading(true);
    setUploadProgress(0);
    setError("");
    setSuccess("");

    try {
      // Simulate progress for auth request
      setUploadProgress(10);

      // Get ImageKit auth params
      const authRes = await fetch("/api/imagekit-auth");
      const auth = await authRes.json();

      setUploadProgress(20);

      const formData = new FormData();
      formData.append("file", file);
      formData.append("fileName", file.name);
      formData.append(
        "publicKey",
        process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY!
      );
      formData.append("signature", auth.signature);
      formData.append("expire", auth.expire);
      formData.append("token", auth.token);
      formData.append("folder", "/profile-images");

      setUploadProgress(30);

      // Upload to ImageKit with XMLHttpRequest for progress tracking
      const uploadPromise = new Promise<{ url: string }>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const percentComplete =
              Math.round((event.loaded / event.total) * 70) + 30; // 30-100%
            setUploadProgress(percentComplete);
          }
        });

        xhr.addEventListener("load", () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch {
              reject(new Error("Invalid response format"));
            }
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        });

        xhr.addEventListener("error", () => {
          reject(new Error("Network error during upload"));
        });

        xhr.open("POST", "https://upload.imagekit.io/api/v1/files/upload");
        xhr.send(formData);
      });

      const uploadData = await uploadPromise;

      if (uploadData.url) {
        setProfileImage(uploadData.url);
        setUploadProgress(100);
        setSuccess("Profile image uploaded successfully!");
      } else {
        setError("Image upload failed: No URL returned");
      }
    } catch (error) {
      console.error("ImageKit error:", error);
      setError(
        "Image upload failed: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    } finally {
      setImageUploading(false);
      // Reset progress after a short delay
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }

  async function handlePasswordChange(e: React.FormEvent) {
    e.preventDefault();
    const token = localStorage.getItem("token");
    if (!token) return;

    setPasswordChanging(true);
    setError("");
    setSuccess("");

    try {
      const res = await fetch(
        getApiUrl(API_CONFIG.ENDPOINTS.USER.CHANGE_PASSWORD),
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(passwords),
        }
      );

      if (res.ok) {
        setSuccess("Password changed successfully");
        setPasswords({ oldPassword: "", newPassword: "" });
      } else {
        setError("Failed to change password");
      }
    } catch {
      setError("Failed to change password");
    } finally {
      setPasswordChanging(false);
    }
  }

  async function handleDeleteAccount() {
    const token = localStorage.getItem("token");
    if (!token) return;
    if (
      !confirm(
        "Are you sure you want to delete your account? This cannot be undone."
      )
    )
      return;
    const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DELETE), {
      method: "DELETE",
      headers: { Authorization: `Bearer ${token}` },
    });
    if (res.ok) {
      alert("Account deleted");
      localStorage.removeItem("token");
      window.location.href = "/";
    } else {
      setError("Failed to delete account");
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <Header cartCount={cartCount} onCartClick={handleCartClick} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-xl text-gray-600 font-medium">
              Loading profile...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <Header cartCount={cartCount} onCartClick={handleCartClick} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg
                className="w-12 h-12 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Access Error
            </h1>
            <p className="text-gray-600 mb-8">{error}</p>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <Header cartCount={cartCount} onCartClick={handleCartClick} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-xl text-gray-600 font-medium">
              Loading user data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header cartCount={cartCount} onCartClick={handleCartClick} />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
              <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                Profile
              </span>
              <span className="text-white"> Settings</span>
            </h1>
            <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
              Manage your account settings and personalize your experience
            </p>

            <Link
              href="/user/dashboard"
              className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-lg text-white font-semibold rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-12">
        {/* Success/Error Messages */}
        {success && (
          <div className="mb-8 p-4 bg-green-50 border border-green-200 text-green-700 rounded-2xl flex items-center">
            <svg
              className="w-5 h-5 mr-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            {success}
          </div>
        )}

        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 text-red-700 rounded-2xl flex items-center">
            <svg
              className="w-5 h-5 mr-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01"
              />
            </svg>
            {error}
          </div>
        )}

        {/* Profile Picture Section */}
        <div className="bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            📸 Profile Picture
          </h2>

          <div className="flex flex-col items-center">
            <div className="relative mb-6">
              <div className="relative">
                <Image
                  src={profileImage || "/usericon.png"}
                  alt="Profile"
                  width={120}
                  height={120}
                  className={`w-30 h-30 rounded-full object-cover border-4 border-gray-200 shadow-lg transition-opacity ${
                    imageUploading ? "opacity-50" : "opacity-100"
                  }`}
                />
                {imageUploading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white/90 rounded-full p-3">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    </div>
                  </div>
                )}
              </div>
              <button
                className={`absolute bottom-2 right-2 w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 ${
                  imageUploading
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:scale-110"
                }`}
                onClick={() => !imageUploading && fileInputRef.current?.click()}
                title={
                  imageUploading ? "Uploading..." : "Change profile picture"
                }
                disabled={imageUploading}
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </button>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={fileInputRef}
                onChange={handleImageUpload}
                disabled={imageUploading}
              />
            </div>

            {/* Upload Progress Bar */}
            {imageUploading && uploadProgress > 0 && (
              <div className="w-full max-w-md mb-6">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Uploading image...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                {user.name || user.email}
              </h3>
              <p className="text-gray-600">{user.email}</p>
              <span className="inline-block mt-2 px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full">
                {user.role}
              </span>
            </div>
          </div>
        </div>

        {/* Edit Profile Form */}
        <div className="bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            ✏️ Edit Profile
          </h2>

          <form onSubmit={handleProfileUpdate} className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Display Name
              </label>
              <input
                type="text"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                className="w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg"
                placeholder="Enter your display name"
                maxLength={40}
                required
              />
            </div>

            <button
              type="submit"
              className={`w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg ${
                profileSaving || imageUploading
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:shadow-xl transform hover:-translate-y-1"
              }`}
              disabled={profileSaving || imageUploading}
            >
              {profileSaving ? (
                <div className="flex items-center justify-center gap-3">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Saving Profile...</span>
                </div>
              ) : (
                "💾 Save Profile"
              )}
            </button>
          </form>
        </div>

        {/* Change Password Form */}
        <div className="bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            🔒 Change Password
          </h2>

          <form onSubmit={handlePasswordChange} className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Current Password
              </label>
              <input
                type="password"
                placeholder="Enter your current password"
                className="w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg"
                value={passwords.oldPassword}
                onChange={(e) =>
                  setPasswords((p) => ({ ...p, oldPassword: e.target.value }))
                }
                required
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                New Password
              </label>
              <input
                type="password"
                placeholder="Enter your new password"
                className="w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg"
                value={passwords.newPassword}
                onChange={(e) =>
                  setPasswords((p) => ({ ...p, newPassword: e.target.value }))
                }
                required
              />
            </div>

            <button
              type="submit"
              className={`w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg ${
                passwordChanging
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:shadow-xl transform hover:-translate-y-1"
              }`}
              disabled={passwordChanging}
            >
              {passwordChanging ? (
                <div className="flex items-center justify-center gap-3">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Changing Password...</span>
                </div>
              ) : (
                "🔐 Change Password"
              )}
            </button>
          </form>
        </div>

        {/* Danger Zone */}
        <div className="bg-white rounded-3xl shadow-xl p-8 border border-red-200">
          <h2 className="text-2xl font-bold text-red-900 mb-6 flex items-center">
            ⚠️ Danger Zone
          </h2>

          <div className="bg-red-50 border border-red-200 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              Delete Account
            </h3>
            <p className="text-red-700 mb-4">
              Once you delete your account, there is no going back. Please be
              certain.
            </p>

            <button
              onClick={handleDeleteAccount}
              className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold rounded-2xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              🗑️ Delete Account
            </button>
          </div>
        </div>
      </div>

      {/* Cart Modal */}
      <Cart isOpen={isCartOpen} onClose={handleCartClose} />
    </div>
  );
}
