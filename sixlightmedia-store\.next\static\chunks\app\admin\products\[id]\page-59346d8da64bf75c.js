(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[727],{582:(e,t,a)=>{Promise.resolve().then(a.bind(a,6189))},5695:(e,t,a)=>{"use strict";var l=a(8999);a.o(l,"useParams")&&a.d(t,{useParams:function(){return l.useParams}}),a.o(l,"useRouter")&&a.d(t,{useRouter:function(){return l.useRouter}})},6189:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var l=a(5155),s=a(2115),r=a(5695),n=a(6766),o=a(5089);function i(){var e;let t=(0,r.useRouter)(),a=(0,r.useParams)(),i=null==a?void 0:a.id,[c,d]=(0,s.useState)(null),[u,m]=(0,s.useState)(!0),[h,p]=(0,s.useState)(""),[g,x]=(0,s.useState)(!1),[b,f]=(0,s.useState)([]),[v,j]=(0,s.useState)(!0),[N,y]=(0,s.useState)("");async function w(e){e.preventDefault(),x(!0);let a=localStorage.getItem("token"),l={...c};delete l.id;let s=await fetch("".concat("http://localhost:3001","/admin/products/").concat(i),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(l)});x(!1),s.ok?t.push("/admin/dashboard"):p("Failed to update product")}return((0,s.useEffect)(()=>{j(!0),fetch("".concat("http://localhost:3001","/categories")).then(e=>{if(!e.ok)throw Error("Failed to fetch categories");return e.json()}).then(e=>{f(e),y("")}).catch(()=>{f([]),y("Failed to load categories")}).finally(()=>j(!1))},[]),(0,s.useEffect)(()=>{if(!i)return;let e=localStorage.getItem("token");fetch("".concat("http://localhost:3001","/admin/products/").concat(i),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{var t,a,l,s,r,n,o,i,c,u,m;d({id:e.id,name:null!=(a=e.name)?a:"",price:null!=(l=e.price)?l:0,image:null!=(s=e.image)?s:"",slug:null!=(r=e.slug)?r:"",description:null!=(n=e.description)?n:"",customizable:null!=(o=e.customizable)&&o,categoryId:null!=(c=null!=(i=e.categoryId)?i:null==(t=e.category)?void 0:t.id)?c:void 0,modelUrl:null!=(u=e.modelUrl)?u:"",category:null!=(m=e.category)?m:void 0})}).catch(()=>p("Failed to load product")).finally(()=>m(!1))},[i]),u)?(0,l.jsx)("div",{className:"text-center mt-16",children:"Loading..."}):h?(0,l.jsx)("div",{className:"text-red-600 text-center mt-16",children:h}):c?(0,l.jsxs)("div",{className:"max-w-lg mx-auto mt-10 bg-white rounded-2xl shadow p-8",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center text-[#1a237e]",children:"Edit Product"}),(0,l.jsxs)("form",{onSubmit:w,className:"flex flex-col gap-4",children:[(0,l.jsxs)("label",{className:"font-semibold",children:["Name",(0,l.jsx)("input",{className:"w-full border rounded px-3 py-2 mt-1",value:c.name||"",onChange:e=>d({...c,name:e.target.value}),required:!0})]}),(0,l.jsxs)("label",{className:"font-semibold",children:["Price",(0,l.jsxs)("div",{className:"flex items-center mt-1",children:[(0,l.jsx)("span",{className:"px-2 py-2 bg-gray-100 border border-r-0 rounded-l",children:"K"}),(0,l.jsx)("input",{type:"number",min:"0",step:"0.01",className:"w-full border rounded-r px-3 py-2 focus:outline-none",value:void 0!==c.price?c.price:0,onChange:e=>d({...c,price:Number(e.target.value)}),required:!0})]})]}),(0,l.jsxs)("div",{className:"text-lg font-semibold text-green-700 mt-2",children:["Price: ",(0,l.jsxs)("span",{className:"font-bold",children:["K",null!=(e=c.price)?e:0]})]}),(0,l.jsxs)("label",{className:"font-semibold",children:["Product Image",(0,l.jsxs)(o.IKContext,{publicKey:"public_kFR0vTVL7DIDI8YW9eF6/luGjB4=".replace(/"/g,""),urlEndpoint:"https://ik.imagekit.io/fwbvmq9re".replace(/"/g,""),authenticator:async()=>(await fetch("/api/imagekit-auth")).json(),children:[(0,l.jsx)(o.IKUpload,{fileName:c.slug?"".concat(c.slug,".jpg"):"product.jpg",onSuccess:e=>d({...c,image:e.url}),onError:()=>p("Image upload failed"),className:"border rounded px-3 py-2 w-full"}),c.image&&(0,l.jsx)(n.default,{src:c.image,alt:"Preview",width:96,height:96,className:"mt-2 h-24 w-auto rounded shadow"})]})]}),(0,l.jsxs)("label",{className:"font-semibold",children:["Category",v?(0,l.jsx)("div",{className:"text-gray-500 text-sm mt-1",children:"Loading categories..."}):N?(0,l.jsx)("div",{className:"text-red-600 text-sm mt-1",children:N}):null,(0,l.jsxs)("select",{className:"w-full border rounded px-3 py-2 mt-1",value:c.categoryId||"",onChange:e=>d({...c,categoryId:Number(e.target.value)}),required:!0,disabled:v||!!N,children:[(0,l.jsx)("option",{value:"",disabled:!0,children:"Select category"}),b.map(e=>(0,l.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,l.jsx)("a",{href:"/admin/categories",className:"text-blue-600 text-xs underline mt-1 inline-block hover:text-blue-800",target:"_blank",rel:"noopener noreferrer",children:"Manage categories"})]}),(0,l.jsx)("button",{type:"submit",className:"bg-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow hover:bg-blue-700 transition mt-4",disabled:g,children:g?"Saving...":"Save Changes"})]})]}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,89,441,684,358],()=>t(582)),_N_E=e.O()}]);