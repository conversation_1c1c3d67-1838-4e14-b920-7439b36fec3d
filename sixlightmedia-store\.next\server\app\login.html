<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/50d61da5ea5ba4b5.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-609cfb1662a59ce4.js"/><script src="/_next/static/chunks/4bd1b696-b9e56d0422fc4d24.js" async=""></script><script src="/_next/static/chunks/684-b7fdb8bd4e0876ce.js" async=""></script><script src="/_next/static/chunks/main-app-153c1e2df70ea4ca.js" async=""></script><script src="/_next/static/chunks/766-a26fec496fc2c915.js" async=""></script><script src="/_next/static/chunks/874-82947e16c34490df.js" async=""></script><script src="/_next/static/chunks/619-037269d330a5068d.js" async=""></script><script src="/_next/static/chunks/app/login/page-79479444224bbabf.js" async=""></script><meta name="theme-color" content="#1a237e"/><meta name="msapplication-TileColor" content="#1a237e"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="Six Light Media Store"/><link rel="icon" href="/favicon.ico" sizes="any"/><link rel="icon" href="/icon.svg" type="image/svg+xml"/><link rel="apple-touch-icon" href="/apple-touch-icon.png"/><link rel="manifest" href="/manifest.json"/><link rel="preconnect" href="https://fonts.googleapis.com"/><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous"/><link rel="dns-prefetch" href="//fonts.googleapis.com"/><link rel="dns-prefetch" href="//fonts.gstatic.com"/><script type="application/ld+json">{"@context":"https://schema.org","@type":"Organization","name":"Six Light Media Store","description":"Premium custom products and personalized gifts","url":"https://yourdomain.com","logo":"https://yourdomain.com/6 Light Logo.png","contactPoint":{"@type":"ContactPoint","contactType":"customer service","availableLanguage":"English"},"sameAs":["https://facebook.com/sixlightmedia","https://twitter.com/sixlightmedia","https://instagram.com/sixlightmedia"]}</script><title>Six Light Media Store - Premium Custom Products &amp; Personalized Gifts</title><meta name="description" content="Discover premium custom products and personalized gifts at Six Light Media Store. From engraved bottles to custom t-shirts, we create unique items tailored just for you. Fast delivery, premium quality, 100% satisfaction guaranteed."/><meta name="author" content="Six Light Media"/><meta name="keywords" content="custom products,personalized gifts,engraved bottles,custom t-shirts,Six Light Media,premium quality,custom printing,personalized items,unique gifts,custom design,Zambia,e-commerce"/><meta name="creator" content="Six Light Media"/><meta name="publisher" content="Six Light Media"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="category" content="e-commerce"/><link rel="canonical" href="https://yourdomain.com"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="Six Light Media Store - Premium Custom Products &amp; Personalized Gifts"/><meta property="og:description" content="Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you."/><meta property="og:url" content="https://yourdomain.com"/><meta property="og:site_name" content="Six Light Media Store"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="https://yourdomain.com/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Six Light Media Store - Premium Custom Products"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@sixlightmedia"/><meta name="twitter:title" content="Six Light Media Store - Premium Custom Products &amp; Personalized Gifts"/><meta name="twitter:description" content="Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you."/><meta name="twitter:image" content="https://yourdomain.com/og-image.jpg"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4"><div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100"><div class="flex flex-col items-center"><img alt="6 Light Logo" loading="lazy" width="64" height="64" decoding="async" data-nimg="1" class="mb-2" style="color:transparent" srcSet="/_next/image?url=%2F6%20Light%20Logo.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F6%20Light%20Logo.png&amp;w=128&amp;q=75 2x" src="/_next/image?url=%2F6%20Light%20Logo.png&amp;w=128&amp;q=75"/></div><h2 class="text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Sign In</h2><form class="flex flex-col gap-4"><div class="space-y-1"><input placeholder="Email" type="email" class="w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition border-gray-300 focus:ring-blue-300" autoComplete="email" aria-invalid="false" value=""/></div><div class="space-y-1"><div class="relative"><input type="password" placeholder="Password" class="w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition border-gray-300 focus:ring-blue-300" autoComplete="current-password" aria-invalid="false" value=""/><button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700" aria-label="Show password"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye" aria-hidden="true"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></button></div></div><button type="submit" class="w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">Sign In</button></form><div class="text-center text-sm text-gray-600 space-y-2"><div><a class="text-blue-600 hover:text-blue-800 font-medium" href="/forgot-password">Forgot your password?</a></div><div>Don&#x27;t have an account?<!-- --> <a class="text-red-700 font-semibold hover:underline" href="/register">Register</a></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-609cfb1662a59ce4.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[9690,[\"766\",\"static/chunks/766-a26fec496fc2c915.js\",\"874\",\"static/chunks/874-82947e16c34490df.js\",\"619\",\"static/chunks/619-037269d330a5068d.js\",\"520\",\"static/chunks/app/login/page-79479444224bbabf.js\"],\"default\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/50d61da5ea5ba4b5.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"97rbG1mQvDyjlw8EaP1eS\",\"p\":\"\",\"c\":[\"\",\"login\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"login\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/50d61da5ea5ba4b5.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=5\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#1a237e\"}],[\"$\",\"meta\",null,{\"name\":\"msapplication-TileColor\",\"content\":\"#1a237e\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-title\",\"content\":\"Six Light Media Store\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"sizes\":\"any\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/icon.svg\",\"type\":\"image/svg+xml\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"href\":\"/apple-touch-icon.png\"}],[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/manifest.json\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.googleapis.com\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.gstatic.com\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"//fonts.googleapis.com\"}],[\"$\",\"link\",null,{\"rel\":\"dns-prefetch\",\"href\":\"//fonts.gstatic.com\"}],[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"Organization\\\",\\\"name\\\":\\\"Six Light Media Store\\\",\\\"description\\\":\\\"Premium custom products and personalized gifts\\\",\\\"url\\\":\\\"https://yourdomain.com\\\",\\\"logo\\\":\\\"https://yourdomain.com/6 Light Logo.png\\\",\\\"contactPoint\\\":{\\\"@type\\\":\\\"ContactPoint\\\",\\\"contactType\\\":\\\"customer service\\\",\\\"availableLanguage\\\":\\\"English\\\"},\\\"sameAs\\\":[\\\"https://facebook.com/sixlightmedia\\\",\\\"https://twitter.com/sixlightmedia\\\",\\\"https://instagram.com/sixlightmedia\\\"]}\"}}]]}],[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],\"$undefined\"]}]]}]]}],{\"children\":[\"login\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"n_oaigDEl4wtPfn0lLOoA\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Six Light Media Store - Premium Custom Products \u0026 Personalized Gifts\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Discover premium custom products and personalized gifts at Six Light Media Store. From engraved bottles to custom t-shirts, we create unique items tailored just for you. Fast delivery, premium quality, 100% satisfaction guaranteed.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Six Light Media\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"custom products,personalized gifts,engraved bottles,custom t-shirts,Six Light Media,premium quality,custom printing,personalized items,unique gifts,custom design,Zambia,e-commerce\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Six Light Media\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"Six Light Media\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"name\":\"category\",\"content\":\"e-commerce\"}],[\"$\",\"link\",\"9\",{\"rel\":\"canonical\",\"href\":\"https://yourdomain.com\"}],[\"$\",\"meta\",\"10\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"11\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:title\",\"content\":\"Six Light Media Store - Premium Custom Products \u0026 Personalized Gifts\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:description\",\"content\":\"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:url\",\"content\":\"https://yourdomain.com\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:site_name\",\"content\":\"Six Light Media Store\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image\",\"content\":\"https://yourdomain.com/og-image.jpg\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"20\",{\"property\":\"og:image:alt\",\"content\":\"Six Light Media Store - Premium Custom Products\"}],[\"$\",\"meta\",\"21\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:creator\",\"content\":\"@sixlightmedia\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:title\",\"content\":\"Six Light Media Store - Premium Custom Products \u0026 Personalized Gifts\"}],[\"$\",\"meta\",\"25\",{\"name\":\"twitter:description\",\"content\":\"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.\"}],[\"$\",\"meta\",\"26\",{\"name\":\"twitter:image\",\"content\":\"https://yourdomain.com/og-image.jpg\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>