"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[342],{1351:(t,e,r)=>{let n;function i(t){return t+.5|0}r.d(e,{$:()=>eu,A:()=>tW,B:()=>tI,C:()=>ef,D:()=>t_,E:()=>eM,F:()=>X,G:()=>eJ,H:()=>tc,I:()=>eX,J:()=>e1,K:()=>e0,L:()=>tD,M:()=>eH,N:()=>tm,O:()=>B,P:()=>to,Q:()=>H,R:()=>eO,S:()=>tC,T:()=>ta,U:()=>tM,V:()=>en,W:()=>tR,X:()=>eo,Y:()=>ec,Z:()=>eg,_:()=>tB,a:()=>e_,a0:()=>ek,a1:()=>tH,a2:()=>tX,a3:()=>t3,a4:()=>V,a5:()=>tt,a6:()=>t9,a7:()=>tr,a8:()=>function t(e,r,n,i){return new Proxy({_cacheable:!1,_proxy:e,_context:r,_subProxy:n,_stack:new Set,_descriptors:eS(e,i),setContext:r=>t(e,r,n,i),override:o=>t(e.override(o),r,n,i)},{deleteProperty:(t,r)=>(delete t[r],delete e[r],!0),get:(e,r,n)=>ej(e,r,()=>(function(e,r,n){let{_proxy:i,_context:o,_subProxy:a,_descriptors:l}=e,s=i[r];return tr(s)&&l.isScriptable(r)&&(s=function(t,e,r,n){let{_proxy:i,_context:o,_subProxy:a,_stack:l}=r;if(l.has(t))throw Error("Recursion detected: "+Array.from(l).join("->")+"->"+t);l.add(t);let s=e(o,a||n);return l.delete(t),eR(t,s)&&(s=eI(i._scopes,i,t,s)),s}(r,s,e,n)),F(s)&&s.length&&(s=function(e,r,n,i){let{_proxy:o,_context:a,_subProxy:l,_descriptors:s}=n;if(void 0!==a.index&&i(e))return r[a.index%r.length];if(E(r[0])){let n=r,i=o._scopes.filter(t=>t!==n);for(let f of(r=[],n)){let n=eI(i,o,e,f);r.push(t(n,a,l&&l[e],s))}}return r}(r,s,e,l.isIndexable)),eR(r,s)&&(s=t(s,o,a&&a[r],l)),s})(e,r,n)),getOwnPropertyDescriptor:(t,r)=>t._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,r)=>Reflect.has(e,r),ownKeys:()=>Reflect.ownKeys(e),set:(t,r,n)=>(e[r]=n,delete t[r],!0)})},a9:()=>eT,aA:()=>e4,aB:()=>e6,aC:()=>tZ,aD:()=>e3,aE:()=>es,aF:()=>tO,aG:()=>I,aH:()=>tv,aI:()=>tb,aJ:()=>tx,aK:()=>tp,aL:()=>tk,aM:()=>t6,aN:()=>td,aO:()=>ei,aP:()=>tN,aQ:()=>tA,aa:()=>eS,ab:()=>U,ac:()=>W,ad:()=>t$,ae:()=>eG,af:()=>ea,ag:()=>tn,ah:()=>ra,ai:()=>Z,aj:()=>ti,ak:()=>tj,al:()=>ex,am:()=>e$,an:()=>rr,ao:()=>re,ap:()=>e2,aq:()=>e8,ar:()=>e5,as:()=>eh,at:()=>ed,au:()=>el,av:()=>ep,aw:()=>ev,ax:()=>ew,ay:()=>rt,az:()=>tT,b:()=>F,c:()=>tG,d:()=>er,e:()=>tK,f:()=>G,g:()=>Y,h:()=>te,i:()=>E,j:()=>eP,k:()=>N,l:()=>tE,m:()=>D,n:()=>$,o:()=>t8,p:()=>tS,q:()=>tq,r:()=>tL,s:()=>tg,t:()=>tw,u:()=>tY,v:()=>L,w:()=>tz,x:()=>ty,y:()=>eE,z:()=>eU});let o=(t,e,r)=>Math.max(Math.min(t,r),e);function a(t){return o(i(2.55*t),0,255)}function l(t){return o(i(255*t),0,255)}function s(t){return o(i(t/2.55)/100,0,1)}function f(t){return o(i(100*t),0,100)}let c={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},u=[..."0123456789ABCDEF"],h=t=>u[15&t],d=t=>u[(240&t)>>4]+u[15&t],g=t=>(240&t)>>4==(15&t),p=t=>g(t.r)&&g(t.g)&&g(t.b)&&g(t.a),b=(t,e)=>t<255?e(t):"",m=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function y(t,e,r){let n=e*Math.min(r,1-r),i=(e,i=(e+t/30)%12)=>r-n*Math.max(Math.min(i-3,9-i,1),-1);return[i(0),i(8),i(4)]}function x(t,e,r){let n=(n,i=(n+t/60)%6)=>r-r*e*Math.max(Math.min(i,4-i,1),0);return[n(5),n(3),n(1)]}function v(t,e,r){let n,i=y(t,1,.5);for(e+r>1&&(n=1/(e+r),e*=n,r*=n),n=0;n<3;n++)i[n]*=1-e-r,i[n]+=e;return i}function w(t){let e,r,n,i=t.r/255,o=t.g/255,a=t.b/255,l=Math.max(i,o,a),s=Math.min(i,o,a),f=(l+s)/2;l!==s&&(n=l-s,r=f>.5?n/(2-l-s):n/(l+s),e=60*(e=i===l?(o-a)/n+6*(o<a):o===l?(a-i)/n+2:(i-o)/n+4)+.5);return[0|e,r||0,f]}function M(t,e,r,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,r,n)).map(l)}function k(t){return(t%360+360)%360}let _={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},O={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},P=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,T=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,S=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function C(t,e,r){if(t){let n=w(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*r,0===e?360:1)),t.r=(n=M(y,n,void 0,void 0))[0],t.g=n[1],t.b=n[2]}}function R(t,e){return t?Object.assign(e||{},t):t}function j(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=l(t[3]))):(e=R(t,{r:0,g:0,b:0,a:1})).a=l(e.a),e}class A{constructor(t){let e;if(t instanceof A)return t;let r=typeof t;"object"===r?e=j(t):"string"===r&&(e=function(t){var e,r=t.length;return"#"===t[0]&&(4===r||5===r?e={r:255&17*c[t[1]],g:255&17*c[t[2]],b:255&17*c[t[3]],a:5===r?17*c[t[4]]:255}:(7===r||9===r)&&(e={r:c[t[1]]<<4|c[t[2]],g:c[t[3]]<<4|c[t[4]],b:c[t[5]]<<4|c[t[6]],a:9===r?c[t[7]]<<4|c[t[8]]:255})),e}(t)||function(t){n||((n=function(){let t,e,r,n,i,o={},a=Object.keys(O),l=Object.keys(_);for(t=0;t<a.length;t++){for(e=0,n=i=a[t];e<l.length;e++)r=l[e],i=i.replace(r,_[r]);r=parseInt(O[n],16),o[i]=[r>>16&255,r>>8&255,255&r]}return o}()).transparent=[0,0,0,0]);let e=n[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,r,n,i=P.exec(t),l=255;if(i){if(i[7]!==e){let t=+i[7];l=i[8]?a(t):o(255*t,0,255)}return e=+i[1],r=+i[3],n=+i[5],e=255&(i[2]?a(e):o(e,0,255)),{r:e,g:r=255&(i[4]?a(r):o(r,0,255)),b:n=255&(i[6]?a(n):o(n,0,255)),a:l}}}(t):function(t){let e,r=m.exec(t),n=255;if(!r)return;r[5]!==e&&(n=r[6]?a(+r[5]):l(+r[5]));let i=k(+r[2]),o=r[3]/100,s=r[4]/100;return{r:(e="hwb"===r[1]?M(v,i,o,s):"hsv"===r[1]?M(x,i,o,s):M(y,i,o,s))[0],g:e[1],b:e[2],a:n}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=R(this._rgb);return t&&(t.a=s(t.a)),t}set rgb(t){this._rgb=j(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${s(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=p(t=this._rgb)?h:d,t?"#"+e(t.r)+e(t.g)+e(t.b)+b(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=w(t),r=e[0],n=f(e[1]),i=f(e[2]);return t.a<255?`hsla(${r}, ${n}%, ${i}%, ${s(t.a)})`:`hsl(${r}, ${n}%, ${i}%)`}(this._rgb):void 0}mix(t,e){if(t){let r,n=this.rgb,i=t.rgb,o=e===r?.5:e,a=2*o-1,l=n.a-i.a,s=((a*l==-1?a:(a+l)/(1+a*l))+1)/2;r=1-s,n.r=255&s*n.r+r*i.r+.5,n.g=255&s*n.g+r*i.g+.5,n.b=255&s*n.b+r*i.b+.5,n.a=o*n.a+(1-o)*i.a,this.rgb=n}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,r){let n=S(s(t.r)),i=S(s(t.g)),o=S(s(t.b));return{r:l(T(n+r*(S(s(e.r))-n))),g:l(T(i+r*(S(s(e.g))-i))),b:l(T(o+r*(S(s(e.b))-o))),a:t.a+r*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new A(this.rgb)}alpha(t){return this._rgb.a=l(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=i(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return C(this._rgb,2,t),this}darken(t){return C(this._rgb,2,-t),this}saturate(t){return C(this._rgb,1,t),this}desaturate(t){return C(this._rgb,1,-t),this}rotate(t){var e,r;return e=this._rgb,(r=w(e))[0]=k(r[0]+t),e.r=(r=M(y,r,void 0,void 0))[0],e.g=r[1],e.b=r[2],this}}function I(){}let W=(()=>{let t=0;return()=>t++})();function N(t){return null==t}function F(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function E(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function Y(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function B(t,e){return Y(t)?t:e}function L(t,e){return void 0===t?e:t}let D=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,$=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function H(t,e,r){if(t&&"function"==typeof t.call)return t.apply(r,e)}function X(t,e,r,n){let i,o,a;if(F(t))if(o=t.length,n)for(i=o-1;i>=0;i--)e.call(r,t[i],i);else for(i=0;i<o;i++)e.call(r,t[i],i);else if(E(t))for(i=0,o=(a=Object.keys(t)).length;i<o;i++)e.call(r,t[a[i]],a[i])}function Z(t,e){let r,n,i,o;if(!t||!e||t.length!==e.length)return!1;for(r=0,n=t.length;r<n;++r)if(i=t[r],o=e[r],i.datasetIndex!==o.datasetIndex||i.index!==o.index)return!1;return!0}function q(t){if(F(t))return t.map(q);if(E(t)){let e=Object.create(null),r=Object.keys(t),n=r.length,i=0;for(;i<n;++i)e[r[i]]=q(t[r[i]]);return e}return t}function z(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function Q(t,e,r,n){if(!z(t))return;let i=e[t],o=r[t];E(i)&&E(o)?V(i,o,n):e[t]=q(o)}function V(t,e,r){let n,i=F(e)?e:[e],o=i.length;if(!E(t))return t;let a=(r=r||{}).merger||Q;for(let e=0;e<o;++e){if(!E(n=i[e]))continue;let o=Object.keys(n);for(let e=0,i=o.length;e<i;++e)a(o[e],t,n,r)}return t}function U(t,e){return V(t,e,{merger:K})}function K(t,e,r){if(!z(t))return;let n=e[t],i=r[t];E(n)&&E(i)?U(n,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=q(i))}let J={"":t=>t,x:t=>t.x,y:t=>t.y};function G(t,e){return(J[e]||(J[e]=function(t){let e=function(t){let e=t.split("."),r=[],n="";for(let t of e)(n+=t).endsWith("\\")?n=n.slice(0,-1)+".":(r.push(n),n="");return r}(t);return t=>{for(let r of e){if(""===r)break;t=t&&t[r]}return t}}(e)))(t)}function tt(t){return t.charAt(0).toUpperCase()+t.slice(1)}let te=t=>void 0!==t,tr=t=>"function"==typeof t,tn=(t,e)=>{if(t.size!==e.size)return!1;for(let r of t)if(!e.has(r))return!1;return!0};function ti(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let to=Math.PI,ta=2*to,tl=ta+to,ts=Number.POSITIVE_INFINITY,tf=to/180,tc=to/2,tu=to/4,th=2*to/3,td=Math.log10,tg=Math.sign;function tp(t,e,r){return Math.abs(t-e)<r}function tb(t){let e=Math.round(t),r=Math.pow(10,Math.floor(td(t=tp(t,e,t/1e3)?e:t))),n=t/r;return(n<=1?1:n<=2?2:n<=5?5:10)*r}function tm(t){let e,r=[],n=Math.sqrt(t);for(e=1;e<n;e++)t%e==0&&(r.push(e),r.push(t/e));return n===(0|n)&&r.push(n),r.sort((t,e)=>t-e).pop(),r}function ty(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tx(t,e){let r=Math.round(t);return r-e<=t&&r+e>=t}function tv(t,e,r){let n,i,o;for(n=0,i=t.length;n<i;n++)isNaN(o=t[n][r])||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function tw(t){return to/180*t}function tM(t){return 180/to*t}function tk(t){if(!Y(t))return;let e=1,r=0;for(;Math.round(t*e)/e!==t;)e*=10,r++;return r}function t_(t,e){let r=e.x-t.x,n=e.y-t.y,i=Math.sqrt(r*r+n*n),o=Math.atan2(n,r);return o<-.5*to&&(o+=ta),{angle:o,distance:i}}function tO(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tP(t,e){return(t-e+tl)%ta-to}function tT(t){return(t%ta+ta)%ta}function tS(t,e,r,n){let i=tT(t),o=tT(e),a=tT(r),l=tT(o-i),s=tT(a-i),f=tT(i-o),c=tT(i-a);return i===o||i===a||n&&o===a||l>s&&f<c}function tC(t,e,r){return Math.max(e,Math.min(r,t))}function tR(t){return tC(t,-32768,32767)}function tj(t,e,r,n=1e-6){return t>=Math.min(e,r)-n&&t<=Math.max(e,r)+n}function tA(t,e,r){let n;r=r||(r=>t[r]<e);let i=t.length-1,o=0;for(;i-o>1;)r(n=o+i>>1)?o=n:i=n;return{lo:o,hi:i}}let tI=(t,e,r,n)=>tA(t,r,n?n=>{let i=t[n][e];return i<r||i===r&&t[n+1][e]===r}:n=>t[n][e]<r),tW=(t,e,r)=>tA(t,r,n=>t[n][e]>=r);function tN(t,e,r){let n=0,i=t.length;for(;n<i&&t[n]<e;)n++;for(;i>n&&t[i-1]>r;)i--;return n>0||i<t.length?t.slice(n,i):t}let tF=["push","pop","shift","splice","unshift"];function tE(t,e){if(t._chartjs)return void t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tF.forEach(e=>{let r="_onData"+tt(e),n=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let i=n.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[r]&&t[r](...e)}),i}})})}function tY(t,e){let r=t._chartjs;if(!r)return;let n=r.listeners,i=n.indexOf(e);-1!==i&&n.splice(i,1),n.length>0||(tF.forEach(e=>{delete t[e]}),delete t._chartjs)}function tB(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tL="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tD(t,e){let r=[],n=!1;return function(...i){r=i,n||(n=!0,tL.call(window,()=>{n=!1,t.apply(e,r)}))}}function t$(t,e){let r;return function(...n){return e?(clearTimeout(r),r=setTimeout(t,e,n)):t.apply(this,n),e}}let tH=t=>"start"===t?"left":"end"===t?"right":"center",tX=(t,e,r)=>"start"===t?e:"end"===t?r:(e+r)/2,tZ=(t,e,r,n)=>t===(n?"left":"right")?r:"center"===t?(e+r)/2:e;function tq(t,e,r){let n=e.length,i=0,o=n;if(t._sorted){let{iScale:a,vScale:l,_parsed:s}=t,f=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=a.axis,{min:u,max:h,minDefined:d,maxDefined:g}=a.getUserBounds();if(d){if(i=Math.min(tI(s,c,u).lo,r?n:tI(e,c,a.getPixelForValue(u)).lo),f){let t=s.slice(0,i+1).reverse().findIndex(t=>!N(t[l.axis]));i-=Math.max(0,t)}i=tC(i,0,n-1)}if(g){let t=Math.max(tI(s,a.axis,h,!0).hi+1,r?0:tI(e,c,a.getPixelForValue(h),!0).hi+1);if(f){let e=s.slice(t-1).findIndex(t=>!N(t[l.axis]));t+=Math.max(0,e)}o=tC(t,i,n)-i}else o=n-i}return{start:i,count:o}}function tz(t){let{xScale:e,yScale:r,_scaleRanges:n}=t,i={xmin:e.min,xmax:e.max,ymin:r.min,ymax:r.max};if(!n)return t._scaleRanges=i,!0;let o=n.xmin!==e.min||n.xmax!==e.max||n.ymin!==r.min||n.ymax!==r.max;return Object.assign(n,i),o}let tQ=t=>0===t||1===t,tV=(t,e,r)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*ta/r)),tU=(t,e,r)=>Math.pow(2,-10*t)*Math.sin((t-e)*ta/r)+1,tK={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*tc)+1,easeOutSine:t=>Math.sin(t*tc),easeInOutSine:t=>-.5*(Math.cos(to*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tQ(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tQ(t)?t:tV(t,.075,.3),easeOutElastic:t=>tQ(t)?t:tU(t,.075,.3),easeInOutElastic:t=>tQ(t)?t:t<.5?.5*tV(2*t,.1125,.45):.5+.5*tU(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tK.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tK.easeInBounce(2*t):.5*tK.easeOutBounce(2*t-1)+.5};function tJ(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tG(t){return tJ(t)?t:new A(t)}function t0(t){return tJ(t)?t:new A(t).saturate(.5).darken(.1).hexString()}let t1=["x","y","borderWidth","radius","tension"],t5=["color","borderColor","backgroundColor"],t2=new Map;function t8(t,e,r){return(function(t,e){let r=t+JSON.stringify(e=e||{}),n=t2.get(r);return n||(n=new Intl.NumberFormat(t,e),t2.set(r,n)),n})(e,r).format(t)}let t4={values:t=>F(t)?t:""+t,numeric(t,e,r){let n;if(0===t)return"0";let i=this.chart.options.locale,o=t;if(r.length>1){var a,l;let e,i=Math.max(Math.abs(r[0].value),Math.abs(r[r.length-1].value));(i<1e-4||i>1e15)&&(n="scientific"),a=t,Math.abs(e=(l=r).length>3?l[2].value-l[1].value:l[1].value-l[0].value)>=1&&a!==Math.floor(a)&&(e=a-Math.floor(a)),o=e}let s=td(Math.abs(o)),f=isNaN(s)?1:Math.max(Math.min(-1*Math.floor(s),20),0),c={notation:n,minimumFractionDigits:f,maximumFractionDigits:f};return Object.assign(c,this.options.ticks.format),t8(t,i,c)},logarithmic(t,e,r){return 0===t?"0":[1,2,3,5,10,15].includes(r[e].significand||t/Math.pow(10,Math.floor(td(t))))||e>.8*r.length?t4.numeric.call(this,t,e,r):""}};var t6={formatters:t4};let t3=Object.create(null),t9=Object.create(null);function t7(t,e){if(!e)return t;let r=e.split(".");for(let e=0,n=r.length;e<n;++e){let n=r[e];t=t[n]||(t[n]=Object.create(null))}return t}function et(t,e,r){return"string"==typeof e?V(t7(t,e),r):V(t7(t,""),e)}class ee{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>t0(e.backgroundColor),this.hoverBorderColor=(t,e)=>t0(e.borderColor),this.hoverColor=(t,e)=>t0(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return et(this,t,e)}get(t){return t7(this,t)}describe(t,e){return et(t9,t,e)}override(t,e){return et(t3,t,e)}route(t,e,r,n){let i=t7(this,t),o=t7(this,r),a="_"+e;Object.defineProperties(i,{[a]:{value:i[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[a],e=o[n];return E(t)?Object.assign({},e,t):L(t,e)},set(t){this[a]=t}}})}apply(t){t.forEach(t=>t(this))}}var er=new ee({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t5},numbers:{type:"number",properties:t1}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t6.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function en(t,e,r,n,i){let o=e[i];return o||(o=e[i]=t.measureText(i).width,r.push(i)),o>n&&(n=o),n}function ei(t,e,r,n){let i,o,a,l,s,f=(n=n||{}).data=n.data||{},c=n.garbageCollect=n.garbageCollect||[];n.font!==e&&(f=n.data={},c=n.garbageCollect=[],n.font=e),t.save(),t.font=e;let u=0,h=r.length;for(i=0;i<h;i++)if(null==(l=r[i])||F(l)){if(F(l))for(o=0,a=l.length;o<a;o++)null==(s=l[o])||F(s)||(u=en(t,f,c,u,s))}else u=en(t,f,c,u,l);t.restore();let d=c.length/2;if(d>r.length){for(i=0;i<d;i++)delete f[c[i]];c.splice(0,d)}return u}function eo(t,e,r){let n=t.currentDevicePixelRatio,i=0!==r?Math.max(r/2,.5):0;return Math.round((e-i)*n)/n+i}function ea(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function el(t,e,r,n){es(t,e,r,n,null)}function es(t,e,r,n,i){let o,a,l,s,f,c,u,h,d=e.pointStyle,g=e.rotation,p=e.radius,b=(g||0)*tf;if(d&&"object"==typeof d&&("[object HTMLImageElement]"===(o=d.toString())||"[object HTMLCanvasElement]"===o)){t.save(),t.translate(r,n),t.rotate(b),t.drawImage(d,-d.width/2,-d.height/2,d.width,d.height),t.restore();return}if(!isNaN(p)&&!(p<=0)){switch(t.beginPath(),d){default:i?t.ellipse(r,n,i/2,p,0,0,ta):t.arc(r,n,p,0,ta),t.closePath();break;case"triangle":c=i?i/2:p,t.moveTo(r+Math.sin(b)*c,n-Math.cos(b)*p),b+=th,t.lineTo(r+Math.sin(b)*c,n-Math.cos(b)*p),b+=th,t.lineTo(r+Math.sin(b)*c,n-Math.cos(b)*p),t.closePath();break;case"rectRounded":f=.516*p,a=Math.cos(b+tu)*(s=p-f),u=Math.cos(b+tu)*(i?i/2-f:s),l=Math.sin(b+tu)*s,h=Math.sin(b+tu)*(i?i/2-f:s),t.arc(r-u,n-l,f,b-to,b-tc),t.arc(r+h,n-a,f,b-tc,b),t.arc(r+u,n+l,f,b,b+tc),t.arc(r-h,n+a,f,b+tc,b+to),t.closePath();break;case"rect":if(!g){s=Math.SQRT1_2*p,c=i?i/2:s,t.rect(r-c,n-s,2*c,2*s);break}b+=tu;case"rectRot":u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(r-u,n-l),t.lineTo(r+h,n-a),t.lineTo(r+u,n+l),t.lineTo(r-h,n+a),t.closePath();break;case"crossRot":b+=tu;case"cross":u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(r-u,n-l),t.lineTo(r+u,n+l),t.moveTo(r+h,n-a),t.lineTo(r-h,n+a);break;case"star":u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(r-u,n-l),t.lineTo(r+u,n+l),t.moveTo(r+h,n-a),t.lineTo(r-h,n+a),b+=tu,u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(r-u,n-l),t.lineTo(r+u,n+l),t.moveTo(r+h,n-a),t.lineTo(r-h,n+a);break;case"line":a=i?i/2:Math.cos(b)*p,l=Math.sin(b)*p,t.moveTo(r-a,n-l),t.lineTo(r+a,n+l);break;case"dash":t.moveTo(r,n),t.lineTo(r+Math.cos(b)*(i?i/2:p),n+Math.sin(b)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function ef(t,e,r){return r=r||.5,!e||t&&t.x>e.left-r&&t.x<e.right+r&&t.y>e.top-r&&t.y<e.bottom+r}function ec(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function eu(t){t.restore()}function eh(t,e,r,n,i){if(!e)return t.lineTo(r.x,r.y);if("middle"===i){let n=(e.x+r.x)/2;t.lineTo(n,e.y),t.lineTo(n,r.y)}else"after"===i!=!!n?t.lineTo(e.x,r.y):t.lineTo(r.x,e.y);t.lineTo(r.x,r.y)}function ed(t,e,r,n){if(!e)return t.lineTo(r.x,r.y);t.bezierCurveTo(n?e.cp1x:e.cp2x,n?e.cp1y:e.cp2y,n?r.cp2x:r.cp1x,n?r.cp2y:r.cp1y,r.x,r.y)}function eg(t,e,r,n,i,o={}){let a,l,s=F(e)?e:[e],f=o.strokeWidth>0&&""!==o.strokeColor;for(t.save(),t.font=i.string,o.translation&&t.translate(o.translation[0],o.translation[1]),N(o.rotation)||t.rotate(o.rotation),o.color&&(t.fillStyle=o.color),o.textAlign&&(t.textAlign=o.textAlign),o.textBaseline&&(t.textBaseline=o.textBaseline),a=0;a<s.length;++a)l=s[a],o.backdrop&&function(t,e){let r=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=r}(t,o.backdrop),f&&(o.strokeColor&&(t.strokeStyle=o.strokeColor),N(o.strokeWidth)||(t.lineWidth=o.strokeWidth),t.strokeText(l,r,n,o.maxWidth)),t.fillText(l,r,n,o.maxWidth),function(t,e,r,n,i){if(i.strikethrough||i.underline){let o=t.measureText(n),a=e-o.actualBoundingBoxLeft,l=e+o.actualBoundingBoxRight,s=r-o.actualBoundingBoxAscent,f=r+o.actualBoundingBoxDescent,c=i.strikethrough?(s+f)/2:f;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(a,c),t.lineTo(l,c),t.stroke()}}(t,r,n,l,o),n+=Number(i.lineHeight);t.restore()}function ep(t,e){let{x:r,y:n,w:i,h:o,radius:a}=e;t.arc(r+a.topLeft,n+a.topLeft,a.topLeft,1.5*to,to,!0),t.lineTo(r,n+o-a.bottomLeft),t.arc(r+a.bottomLeft,n+o-a.bottomLeft,a.bottomLeft,to,tc,!0),t.lineTo(r+i-a.bottomRight,n+o),t.arc(r+i-a.bottomRight,n+o-a.bottomRight,a.bottomRight,tc,0,!0),t.lineTo(r+i,n+a.topRight),t.arc(r+i-a.topRight,n+a.topRight,a.topRight,0,-tc,!0),t.lineTo(r+a.topLeft,n)}let eb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,em=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,ey=t=>+t||0;function ex(t,e){let r={},n=E(e),i=n?Object.keys(e):e,o=E(t)?n?r=>L(t[r],t[e[r]]):e=>t[e]:()=>t;for(let t of i)r[t]=ey(o(t));return r}function ev(t){return ex(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ew(t){return ex(t,["topLeft","topRight","bottomLeft","bottomRight"])}function eM(t){let e=ev(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ek(t,e){t=t||{},e=e||er.font;let r=L(t.size,e.size);"string"==typeof r&&(r=parseInt(r,10));let n=L(t.style,e.style);n&&!(""+n).match(em)&&(console.warn('Invalid font style specified: "'+n+'"'),n=void 0);let i={family:L(t.family,e.family),lineHeight:function(t,e){let r=(""+t).match(eb);if(!r||"normal"===r[1])return 1.2*e;switch(t=+r[2],r[3]){case"px":return t;case"%":t/=100}return e*t}(L(t.lineHeight,e.lineHeight),r),size:r,style:n,weight:L(t.weight,e.weight),string:""};return i.string=!i||N(i.size)||N(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family,i}function e_(t,e,r,n){let i,o,a,l=!0;for(i=0,o=t.length;i<o;++i)if(void 0!==(a=t[i])&&(void 0!==e&&"function"==typeof a&&(a=a(e),l=!1),void 0!==r&&F(a)&&(a=a[r%a.length],l=!1),void 0!==a))return n&&!l&&(n.cacheable=!1),a}function eO(t,e,r){let{min:n,max:i}=t,o=$(e,(i-n)/2),a=(t,e)=>r&&0===t?0:t+e;return{min:a(n,-Math.abs(o)),max:a(i,o)}}function eP(t,e){return Object.assign(Object.create(t),e)}function eT(t,e=[""],r,n,i=()=>t[0]){let o=r||t;return void 0===n&&(n=eN("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:o,_fallback:n,_getTarget:i,override:r=>eT([r,...t],e,o,n)},{deleteProperty:(e,r)=>(delete e[r],delete e._keys,delete t[0][r],!0),get:(r,n)=>ej(r,n,()=>(function(t,e,r,n){let i;for(let o of e)if(void 0!==(i=eN(eC(o,t),r)))return eR(t,i)?eI(r,n,t,i):i})(n,e,t,r)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eF(t).includes(e),ownKeys:t=>eF(t),set(t,e,r){let n=t._storage||(t._storage=i());return t[e]=n[e]=r,delete t._keys,!0}})}function eS(t,e={scriptable:!0,indexable:!0}){let{_scriptable:r=e.scriptable,_indexable:n=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:r,indexable:n,isScriptable:tr(r)?r:()=>r,isIndexable:tr(n)?n:()=>n}}let eC=(t,e)=>t?t+tt(e):e,eR=(t,e)=>E(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function ej(t,e,r){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let n=r();return t[e]=n,n}let eA=(t,e)=>!0===t?e:"string"==typeof t?G(e,t):void 0;function eI(t,e,r,n){var i;let o=e._rootScopes,a=(i=e._fallback,tr(i)?i(r,n):i),l=[...t,...o],s=new Set;s.add(n);let f=eW(s,l,r,a||r,n);return null!==f&&(void 0===a||a===r||null!==(f=eW(s,l,a,f,n)))&&eT(Array.from(s),[""],o,a,()=>(function(t,e,r){let n=t._getTarget();e in n||(n[e]={});let i=n[e];return F(i)&&E(r)?r:i||{}})(e,r,n))}function eW(t,e,r,n,i){for(;r;)r=function(t,e,r,n,i){for(let a of e){let e=eA(r,a);if(e){var o;t.add(e);let a=(o=e._fallback,tr(o)?o(r,i):o);if(void 0!==a&&a!==r&&a!==n)return a}else if(!1===e&&void 0!==n&&r!==n)return null}return!1}(t,e,r,n,i);return r}function eN(t,e){for(let r of e){if(!r)continue;let e=r[t];if(void 0!==e)return e}}function eF(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let r of t)for(let t of Object.keys(r).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eE(t,e,r,n){let i,o,a,{iScale:l}=t,{key:s="r"}=this._parsing,f=Array(n);for(i=0;i<n;++i)a=e[o=i+r],f[i]={r:l.parse(G(a,s),o)};return f}let eY=Number.EPSILON||1e-14,eB=(t,e)=>e<t.length&&!t[e].skip&&t[e],eL=t=>"x"===t?"y":"x";function eD(t,e,r){return Math.max(Math.min(t,r),e)}function e$(t,e,r,n,i){let o,a,l,s;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let r,n,i,o=eL(e),a=t.length,l=Array(a).fill(0),s=Array(a),f=eB(t,0);for(r=0;r<a;++r)if(n=i,i=f,f=eB(t,r+1),i){if(f){let t=f[e]-i[e];l[r]=0!==t?(f[o]-i[o])/t:0}s[r]=n?f?tg(l[r-1])!==tg(l[r])?0:(l[r-1]+l[r])/2:l[r-1]:l[r]}!function(t,e,r){let n,i,o,a,l,s=t.length,f=eB(t,0);for(let c=0;c<s-1;++c)if(l=f,f=eB(t,c+1),l&&f){if(tp(e[c],0,eY)){r[c]=r[c+1]=0;continue}(a=Math.pow(n=r[c]/e[c],2)+Math.pow(i=r[c+1]/e[c],2))<=9||(o=3/Math.sqrt(a),r[c]=n*o*e[c],r[c+1]=i*o*e[c])}}(t,l,s),function(t,e,r="x"){let n,i,o,a=eL(r),l=t.length,s=eB(t,0);for(let f=0;f<l;++f){if(i=o,o=s,s=eB(t,f+1),!o)continue;let l=o[r],c=o[a];i&&(n=(l-i[r])/3,o[`cp1${r}`]=l-n,o[`cp1${a}`]=c-n*e[f]),s&&(n=(s[r]-l)/3,o[`cp2${r}`]=l+n,o[`cp2${a}`]=c+n*e[f])}}(t,s,e)}(t,i);else{let r=n?t[t.length-1]:t[0];for(o=0,a=t.length;o<a;++o)s=function(t,e,r,n){let i=t.skip?e:t,o=r.skip?e:r,a=tO(e,i),l=tO(o,e),s=a/(a+l),f=l/(a+l);s=isNaN(s)?0:s,f=isNaN(f)?0:f;let c=n*s,u=n*f;return{previous:{x:e.x-c*(o.x-i.x),y:e.y-c*(o.y-i.y)},next:{x:e.x+u*(o.x-i.x),y:e.y+u*(o.y-i.y)}}}(r,l=t[o],t[Math.min(o+1,a-!n)%a],e.tension),l.cp1x=s.previous.x,l.cp1y=s.previous.y,l.cp2x=s.next.x,l.cp2y=s.next.y,r=l}e.capBezierPoints&&function(t,e){let r,n,i,o,a,l=ef(t[0],e);for(r=0,n=t.length;r<n;++r)a=o,o=l,l=r<n-1&&ef(t[r+1],e),o&&(i=t[r],a&&(i.cp1x=eD(i.cp1x,e.left,e.right),i.cp1y=eD(i.cp1y,e.top,e.bottom)),l&&(i.cp2x=eD(i.cp2x,e.left,e.right),i.cp2y=eD(i.cp2y,e.top,e.bottom)))}(t,r)}function eH(){return"undefined"!=typeof window&&"undefined"!=typeof document}function eX(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eZ(t,e,r){let n;return"string"==typeof t?(n=parseInt(t,10),-1!==t.indexOf("%")&&(n=n/100*e.parentNode[r])):n=t,n}let eq=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),ez=["top","right","bottom","left"];function eQ(t,e,r){let n={};r=r?"-"+r:"";for(let i=0;i<4;i++){let o=ez[i];n[o]=parseFloat(t[e+"-"+o+r])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}let eV=(t,e,r)=>(t>0||e>0)&&(!r||!r.shadowRoot);function eU(t,e){if("native"in t)return t;let{canvas:r,currentDevicePixelRatio:n}=e,i=eq(r),o="border-box"===i.boxSizing,a=eQ(i,"padding"),l=eQ(i,"border","width"),{x:s,y:f,box:c}=function(t,e){let r,n,i=t.touches,o=i&&i.length?i[0]:t,{offsetX:a,offsetY:l}=o,s=!1;if(eV(a,l,t.target))r=a,n=l;else{let t=e.getBoundingClientRect();r=o.clientX-t.left,n=o.clientY-t.top,s=!0}return{x:r,y:n,box:s}}(t,r),u=a.left+(c&&l.left),h=a.top+(c&&l.top),{width:d,height:g}=e;return o&&(d-=a.width+l.width,g-=a.height+l.height),{x:Math.round((s-u)/d*r.width/n),y:Math.round((f-h)/g*r.height/n)}}let eK=t=>Math.round(10*t)/10;function eJ(t,e,r,n){let i=eq(t),o=eQ(i,"margin"),a=eZ(i.maxWidth,t,"clientWidth")||ts,l=eZ(i.maxHeight,t,"clientHeight")||ts,s=function(t,e,r){let n,i;if(void 0===e||void 0===r){let o=t&&eX(t);if(o){let t=o.getBoundingClientRect(),a=eq(o),l=eQ(a,"border","width"),s=eQ(a,"padding");e=t.width-s.width-l.width,r=t.height-s.height-l.height,n=eZ(a.maxWidth,o,"clientWidth"),i=eZ(a.maxHeight,o,"clientHeight")}else e=t.clientWidth,r=t.clientHeight}return{width:e,height:r,maxWidth:n||ts,maxHeight:i||ts}}(t,e,r),{width:f,height:c}=s;if("content-box"===i.boxSizing){let t=eQ(i,"border","width"),e=eQ(i,"padding");f-=e.width+t.width,c-=e.height+t.height}return f=Math.max(0,f-o.width),c=Math.max(0,n?f/n:c-o.height),f=eK(Math.min(f,a,s.maxWidth)),c=eK(Math.min(c,l,s.maxHeight)),f&&!c&&(c=eK(f/2)),(void 0!==e||void 0!==r)&&n&&s.height&&c>s.height&&(f=eK(Math.floor((c=s.height)*n))),{width:f,height:c}}function eG(t,e,r){let n=e||1,i=Math.floor(t.height*n),o=Math.floor(t.width*n);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let a=t.canvas;return a.style&&(r||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==n||a.height!==i||a.width!==o)&&(t.currentDevicePixelRatio=n,a.height=i,a.width=o,t.ctx.setTransform(n,0,0,n,0,0),!0)}let e0=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eH()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function e1(t,e){let r=eq(t).getPropertyValue(e),n=r&&r.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function e5(t,e,r,n){return{x:t.x+r*(e.x-t.x),y:t.y+r*(e.y-t.y)}}function e2(t,e,r,n){return{x:t.x+r*(e.x-t.x),y:"middle"===n?r<.5?t.y:e.y:"after"===n?r<1?t.y:e.y:r>0?e.y:t.y}}function e8(t,e,r,n){let i={x:t.cp2x,y:t.cp2y},o={x:e.cp1x,y:e.cp1y},a=e5(t,i,r),l=e5(i,o,r),s=e5(o,e,r),f=e5(a,l,r),c=e5(l,s,r);return e5(f,c,r)}function e4(t,e,r){var n;return t?(n=r,{x:t=>e+e+n-t,setWidth(t){n=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e6(t,e){let r,n;("ltr"===e||"rtl"===e)&&(n=[(r=t.canvas.style).getPropertyValue("direction"),r.getPropertyPriority("direction")],r.setProperty("direction",e,"important"),t.prevTextDirection=n)}function e3(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e9(t){return"angle"===t?{between:tS,compare:tP,normalize:tT}:{between:tj,compare:(t,e)=>t-e,normalize:t=>t}}function e7({start:t,end:e,count:r,loop:n,style:i}){return{start:t%r,end:e%r,loop:n&&(e-t+1)%r==0,style:i}}function rt(t,e,r){let n,i,o;if(!r)return[t];let{property:a,start:l,end:s}=r,f=e.length,{compare:c,between:u,normalize:h}=e9(a),{start:d,end:g,loop:p,style:b}=function(t,e,r){let n,{property:i,start:o,end:a}=r,{between:l,normalize:s}=e9(i),f=e.length,{start:c,end:u,loop:h}=t;if(h){for(c+=f,u+=f,n=0;n<f&&l(s(e[c%f][i]),o,a);++n)c--,u--;c%=f,u%=f}return u<c&&(u+=f),{start:c,end:u,loop:h,style:t.style}}(t,e,r),m=[],y=!1,x=null,v=()=>u(l,o,n)&&0!==c(l,o),w=()=>0===c(s,n)||u(s,o,n),M=()=>y||v(),k=()=>!y||w();for(let t=d,r=d;t<=g;++t)(i=e[t%f]).skip||(n=h(i[a]))!==o&&(y=u(n,l,s),null===x&&M()&&(x=0===c(n,l)?t:r),null!==x&&k()&&(m.push(e7({start:x,end:t,loop:p,count:f,style:b})),x=null),r=t,o=n);return null!==x&&m.push(e7({start:x,end:g,loop:p,count:f,style:b})),m}function re(t,e){let r=[],n=t.segments;for(let i=0;i<n.length;i++){let o=rt(n[i],t.points,e);o.length&&r.push(...o)}return r}function rr(t,e){let r=t.points,n=t.options.spanGaps,i=r.length;if(!i)return[];let o=!!t._loop,{start:a,end:l}=function(t,e,r,n){let i=0,o=e-1;if(r&&!n)for(;i<e&&!t[i].skip;)i++;for(;i<e&&t[i].skip;)i++;for(i%=e,r&&(o+=i);o>i&&t[o%e].skip;)o--;return{start:i,end:o%=e}}(r,i,o,n);if(!0===n)return rn(t,[{start:a,end:l,loop:o}],r,e);let s=l<a?l+i:l,f=!!t._fullLoop&&0===a&&l===i-1;return rn(t,function(t,e,r,n){let i,o=t.length,a=[],l=e,s=t[e];for(i=e+1;i<=r;++i){let r=t[i%o];r.skip||r.stop?s.skip||(n=!1,a.push({start:e%o,end:(i-1)%o,loop:n}),e=l=r.stop?i:null):(l=i,s.skip&&(e=i)),s=r}return null!==l&&a.push({start:e%o,end:l%o,loop:n}),a}(r,a,s,f),r,e)}function rn(t,e,r,n){return n&&n.setContext&&r?function(t,e,r,n){let i=t._chart.getContext(),o=ri(t.options),{_datasetIndex:a,options:{spanGaps:l}}=t,s=r.length,f=[],c=o,u=e[0].start,h=u;function d(t,e,n,i){let o=l?-1:1;if(t!==e){for(t+=s;r[t%s].skip;)t-=o;for(;r[e%s].skip;)e+=o;t%s!=e%s&&(f.push({start:t%s,end:e%s,loop:n,style:i}),c=i,u=e%s)}}for(let t of e){let e,o=r[(u=l?u:t.start)%s];for(h=u+1;h<=t.end;h++){let l=r[h%s];(function(t,e){if(!e)return!1;let r=[],n=function(t,e){return tJ(e)?(r.includes(e)||r.push(e),r.indexOf(e)):e};return JSON.stringify(t,n)!==JSON.stringify(e,n)})(e=ri(n.setContext(eP(i,{type:"segment",p0:o,p1:l,p0DataIndex:(h-1)%s,p1DataIndex:h%s,datasetIndex:a}))),c)&&d(u,h-1,t.loop,c),o=l,c=e}u<h-1&&d(u,h-1,t.loop,c)}return f}(t,e,r,n):e}function ri(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function ro(t,e,r){return t.options.clip?t[r]:e[r]}function ra(t,e){let r=e._clip;if(r.disabled)return!1;let n=function(t,e){let{xScale:r,yScale:n}=t;return r&&n?{left:ro(r,e,"left"),right:ro(r,e,"right"),top:ro(n,e,"top"),bottom:ro(n,e,"bottom")}:e}(e,t.chartArea);return{left:!1===r.left?0:n.left-(!0===r.left?0:r.left),right:!1===r.right?t.width:n.right+(!0===r.right?0:r.right),top:!1===r.top?0:n.top-(!0===r.top?0:r.top),bottom:!1===r.bottom?t.height:n.bottom+(!0===r.bottom?0:r.bottom)}}},4416:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5695:(t,e,r)=>{var n=r(8999);r.o(n,"useParams")&&r.d(e,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(e,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(e,{useSearchParams:function(){return n.useSearchParams}})},9946:(t,e,r)=>{r.d(e,{A:()=>u});var n=r(2115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase()),a=t=>{let e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)},l=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()},s=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:c="",children:u,iconNode:h,...d}=t;return(0,n.createElement)("svg",{ref:e,...f,width:i,height:i,stroke:r,strokeWidth:a?24*Number(o)/Number(i):o,className:l("lucide",c),...!u&&!s(d)&&{"aria-hidden":"true"},...d},[...h.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(u)?u:[u]])}),u=(t,e)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:s,...f}=r;return(0,n.createElement)(c,{ref:o,iconNode:e,className:l("lucide-".concat(i(a(t))),"lucide-".concat(t),s),...f})});return r.displayName=a(t),r}}}]);