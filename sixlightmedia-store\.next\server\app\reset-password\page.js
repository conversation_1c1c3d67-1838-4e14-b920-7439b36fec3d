(()=>{var e={};e.id=700,e.ids=[700],e.modules={322:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(687),a=r(3210),o=r(6189),i=r(3282),l=r(5814),n=r.n(l),d=r(474),c=r(9275),u=r(3613),m=r(5336),p=r(4021),x=r(2597),h=r(3861);let g=c.z.object({password:c.z.string().min(6,"Password must be at least 6 characters"),confirmPassword:c.z.string().min(6,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function f(){let[e,s]=(0,a.useState)({password:"",confirmPassword:""}),[r,l]=(0,a.useState)({}),[c,f]=(0,a.useState)(!1),[w,b]=(0,a.useState)(!1),[v,y]=(0,a.useState)(""),[j,N]=(0,a.useState)(!1),[P,k]=(0,a.useState)(!1),[C,A]=(0,a.useState)(null);(0,o.useSearchParams)();let L=(0,o.useRouter)(),R=(e,t)=>{s(s=>({...s,[e]:t})),r[e]&&l(s=>({...s,[e]:void 0}))},S=async s=>{if(s.preventDefault(),!C)return void y("Invalid reset token. Please request a new password reset.");f(!0),l({}),y("");let r=g.safeParse(e);if(!r.success){f(!1);let e={};r.error.errors.forEach(s=>{s.path[0]&&(e[s.path[0]]=s.message)}),l(e);return}try{let s=await (0,i.xw)(C,e.password);f(!1),b(!0),y(s.message||"Password reset successfully!"),setTimeout(()=>{L.push("/login?message=Password reset successfully! You can now log in with your new password.")},3e3)}catch(e){f(!1),e.message?.includes("expired")?y("Reset link has expired. Please request a new password reset."):e.message?.includes("Invalid")?y("Invalid reset link. Please request a new password reset."):y("Failed to reset password. Please try again.")}};return C||w?w?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)(d.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,t.jsx)(m.A,{className:"h-16 w-16 text-green-500 mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-green-700",children:"Password Reset!"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-600",children:v}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to login page..."})]}),(0,t.jsx)(n(),{href:"/login",className:"inline-block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors mt-6",children:"Continue to Login"})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)(d.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reset Password"}),(0,t.jsx)("p",{className:"text-gray-600 text-center mt-2",children:"Enter your new password below."})]}),(0,t.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("input",{id:"password",type:j?"text":"password",value:e.password,onChange:e=>R("password",e.target.value),placeholder:"Enter new password",className:`w-full pl-10 pr-12 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ${r.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,autoComplete:"new-password","aria-invalid":!!r.password,"aria-describedby":r.password?"password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>N(!j),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:j?(0,t.jsx)(x.A,{size:20}):(0,t.jsx)(h.A,{size:20})})]}),r.password&&(0,t.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(u.A,{size:16}),r.password]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("input",{id:"confirmPassword",type:P?"text":"password",value:e.confirmPassword,onChange:e=>R("confirmPassword",e.target.value),placeholder:"Confirm new password",className:`w-full pl-10 pr-12 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ${r.confirmPassword?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,autoComplete:"new-password","aria-invalid":!!r.confirmPassword,"aria-describedby":r.confirmPassword?"confirm-password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>k(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:P?(0,t.jsx)(x.A,{size:20}):(0,t.jsx)(h.A,{size:20})})]}),r.confirmPassword&&(0,t.jsxs)("div",{id:"confirm-password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(u.A,{size:16}),r.confirmPassword]})]}),(0,t.jsx)("button",{type:"submit",className:`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${c?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"}`,disabled:c,children:c?(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,t.jsx)("span",{children:"Resetting Password..."})]}):"Reset Password"}),v&&!w&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,t.jsx)(u.A,{size:16}),v]})]}),(0,t.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Remember your password?"," ",(0,t.jsx)(n(),{href:"/login",className:"text-blue-600 hover:text-blue-800 font-medium",children:"Sign in"})]})})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-orange-50 to-yellow-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)(d.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,t.jsx)(u.A,{className:"h-16 w-16 text-red-500 mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-700",children:"Invalid Link"})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:v}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n(),{href:"/forgot-password",className:"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors",children:"Request New Reset Link"}),(0,t.jsx)(n(),{href:"/login",className:"inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors",children:"Back to Login"})]})]})})}function w(){return(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})})}),children:(0,t.jsx)(f,{})})}},598:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(5239),a=r(8088),o=r(8170),i=r.n(o),l=r(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(s,n);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5316)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\reset-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1335:(e,s,r)=>{Promise.resolve().then(r.bind(r,322))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4021:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5316:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\reset-password\\page.tsx","default")},5336:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6189:(e,s,r)=>{"use strict";var t=r(5773);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},8207:(e,s,r)=>{Promise.resolve().then(r.bind(r,5316))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[728,474,814,743,24],()=>r(598));module.exports=t})();