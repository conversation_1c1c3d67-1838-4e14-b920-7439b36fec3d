"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[156],{9156:(e,t,r)=>{r.r(t),r.d(t,{default:()=>h});var o=r(5155),s=r(7558),i=r(8842),n=r(4688),l=r(5321),a=r(2115),c=r(6670);function h(e){let{color:t="white",text:r="",onColorChange:l,modelUrl:h}=e,[m,u]=(0,a.useState)(!1);return(0,o.jsxs)("div",{style:{width:"100%"},children:[(0,o.jsxs)(s.Hl,{style:{height:320,width:"100%",background:"#f9f9f9"},camera:{position:[0,0,5],fov:50},children:[(0,o.jsx)("ambientLight",{intensity:.7}),(0,o.jsx)("directionalLight",{position:[5,5,5],intensity:.7}),h&&""!==h.trim()?(0,o.jsx)(f,{color:t,modelUrl:h}):(0,o.jsxs)("mesh",{children:[(0,o.jsx)("boxGeometry",{args:[1,2,1]}),(0,o.jsx)("meshStandardMaterial",{color:t})]}),(0,o.jsx)(d,{children:"string"==typeof r&&""!==r.trim()&&r.length<=24?(0,o.jsx)(i.E,{position:[0,0,.75],fontSize:.25,color:"#222",anchorX:"center",anchorY:"middle",maxWidth:1.2,children:r}):null}),(0,o.jsx)(n.N,{enablePan:!0,enableZoom:!0,enableRotate:!0})]}),(0,o.jsxs)("div",{className:"flex flex-col items-center mt-2",children:[(0,o.jsx)("button",{className:"bg-[#ffd600] text-[#171717] font-semibold px-4 py-1 rounded-full shadow hover:bg-[#ffe066] transition mb-2",onClick:()=>u(e=>!e),children:m?"Close Color Picker":"Pick Custom Color"}),m&&(0,o.jsx)(c.jI,{color:t,onChange:l,style:{width:180,height:120}})]})]})}class d extends a.Component{static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(){}render(){return this.state.hasError?null:this.props.children}constructor(e){super(e),this.state={hasError:!1}}}function f(e){let{color:t,modelUrl:r}=e,{scene:s,materials:i}=(0,l.p)(r);return a.useEffect(()=>{if(i&&i.BottleMaterial){let e=i.BottleMaterial;e.color&&e.color.set(t)}},[t,i]),(0,o.jsx)("primitive",{object:s})}}}]);