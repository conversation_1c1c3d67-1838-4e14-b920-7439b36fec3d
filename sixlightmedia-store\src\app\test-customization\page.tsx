import { requireAuth } from "@/lib/auth";
import TestCustomizationClient from "./TestCustomizationClient";

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

export default async function TestCustomizationPage() {
  // Server-side authentication check - redirects if not authenticated
  const auth = await requireAuth();

  return <TestCustomizationClient user={auth.user} />;
}
