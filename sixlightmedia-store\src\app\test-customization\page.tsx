"use client";

import React, { useState } from "react";
import dynamic from "next/dynamic";

// Dynamic import of the SmartProductCustomizer
const SmartProductCustomizer = dynamic(
  () => import("@/components/SmartProductCustomizer"),
  {
    ssr: false,
    loading: () => (
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Customizer...</p>
          </div>
        </div>
      </div>
    ),
  }
);

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

export default function TestCustomizationPage() {
  const [customizationData, setCustomizationData] = useState<CustomizationData | null>(null);
  const [showData, setShowData] = useState(false);

  const handleCustomizationChange = (data: CustomizationData) => {
    setCustomizationData(data);
    console.log("Customization data updated:", data);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🎨 Product Customization Test
            </h1>
            <p className="text-lg text-gray-600">
              Testing the advanced product customization system with Fabric.js
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Customizer Section */}
            <div className="space-y-6">
              <div className="bg-white rounded-3xl shadow-xl p-6 border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  🛠️ Customizer
                </h2>
                <SmartProductCustomizer
                  productImage="/bottle-dummy.jpg"
                  onCustomizationChange={handleCustomizationChange}
                  initialCustomization={customizationData}
                  productId="test-product"
                />
              </div>
            </div>

            {/* Data Display Section */}
            <div className="space-y-6">
              <div className="bg-white rounded-3xl shadow-xl p-6 border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  📊 Customization Data
                </h2>
                
                <div className="space-y-4">
                  <button
                    onClick={() => setShowData(!showData)}
                    className="w-full py-3 px-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
                  >
                    {showData ? "Hide" : "Show"} Raw Data
                  </button>

                  {customizationData?.preview && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Preview:</h3>
                      <img
                        src={customizationData.preview}
                        alt="Customization Preview"
                        className="w-full max-w-md mx-auto border rounded-lg shadow-sm"
                      />
                    </div>
                  )}

                  {showData && customizationData && (
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold text-gray-700 mb-2">Canvas Data:</h3>
                        <pre className="bg-gray-100 p-3 rounded-lg text-xs overflow-auto max-h-40">
                          {customizationData.canvasData ? 
                            JSON.stringify(JSON.parse(customizationData.canvasData), null, 2) : 
                            "No canvas data"
                          }
                        </pre>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-gray-700 mb-2">Preview Data:</h3>
                        <div className="bg-gray-100 p-3 rounded-lg text-xs break-all max-h-20 overflow-auto">
                          {customizationData.preview ? 
                            `${customizationData.preview.substring(0, 100)}...` : 
                            "No preview data"
                          }
                        </div>
                      </div>
                    </div>
                  )}

                  {!customizationData && (
                    <div className="text-center py-8 text-gray-500">
                      <p>No customization data yet.</p>
                      <p className="text-sm mt-2">Start customizing to see data here!</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Status Section */}
              <div className="bg-white rounded-3xl shadow-xl p-6 border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  ✅ Status Check
                </h2>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">Fabric.js Available:</span>
                    <span className="text-green-600 font-semibold">
                      {typeof window !== "undefined" && window.fabric ? "✅ Yes" : "❌ No"}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">Customization Active:</span>
                    <span className={`font-semibold ${customizationData ? "text-green-600" : "text-gray-500"}`}>
                      {customizationData ? "✅ Yes" : "⏳ Waiting"}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">Canvas Data:</span>
                    <span className={`font-semibold ${customizationData?.canvasData ? "text-green-600" : "text-gray-500"}`}>
                      {customizationData?.canvasData ? "✅ Available" : "❌ None"}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">Preview Image:</span>
                    <span className={`font-semibold ${customizationData?.preview ? "text-green-600" : "text-gray-500"}`}>
                      {customizationData?.preview ? "✅ Generated" : "❌ None"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-8 bg-white rounded-3xl shadow-xl p-6 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              📋 Test Instructions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-700 mb-3">Basic Tests:</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Check if the customizer loads properly</li>
                  <li>• Verify Fabric.js is detected and working</li>
                  <li>• Test adding text to the canvas</li>
                  <li>• Test changing text properties (color, size, font)</li>
                  <li>• Test adding shapes (rectangle, circle, triangle)</li>
                  <li>• Test image upload functionality</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-gray-700 mb-3">Advanced Tests:</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Test undo/redo functionality</li>
                  <li>• Test object manipulation (move, resize, rotate)</li>
                  <li>• Test delete functionality</li>
                  <li>• Test export/download feature</li>
                  <li>• Verify customization data is captured</li>
                  <li>• Check preview image generation</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
