import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ProductModule } from './product/product.module';
import { AdminController } from './admin.controller';
import { UserController } from './user.controller';
import { AuthModule } from './auth/auth.module';
import { PrismaService } from './prisma.service';
import { CategoryController } from './category/category.controller';
import { CategoryService } from './category/category.service';

@Module({
  imports: [ProductModule, AuthModule],
  controllers: [
    AppController,
    AdminController,
    UserController,
    CategoryController,
  ],
  providers: [AppService, PrismaService, CategoryService],
})
export class AppModule {}
