(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{5905:(e,t,s)=>{Promise.resolve().then(s.bind(s,9690))},9690:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var n=s(5155),a=s(2115),r=s(9958),o=s(6874),i=s.n(o),l=s(6766);function c(){let[e,t]=(0,a.useState)(""),[s,o]=(0,a.useState)(""),[c,d]=(0,a.useState)(""),[u,h]=(0,a.useState)(!1);async function g(t){t.preventDefault(),h(!0),d("");let n=await (0,r.i)(e,s);h(!1),n.access_token?(localStorage.setItem("token",n.access_token),window.location.href="/user/dashboard"):d(n.error||"Login failed")}return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-black",children:(0,n.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-xl p-8 flex flex-col gap-6",children:[(0,n.jsx)("div",{className:"flex flex-col items-center",children:(0,n.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,n.jsx)("h2",{className:"text-3xl font-extrabold text-center text-red-700",children:"Sign In"}),(0,n.jsxs)("form",{onSubmit:g,className:"flex flex-col gap-4",children:[(0,n.jsx)("input",{value:e,onChange:e=>t(e.target.value),placeholder:"Email",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"email"}),(0,n.jsx)("input",{value:s,onChange:e=>o(e.target.value),type:"password",placeholder:"Password",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"current-password"}),(0,n.jsx)("button",{type:"submit",className:"bg-black text-white font-semibold px-6 py-3 rounded-lg shadow hover:bg-green-700 transition disabled:opacity-60",disabled:u,children:u?"Logging in...":"Login"}),c&&(0,n.jsx)("div",{className:"text-red-600 text-center text-sm mt-2",children:c})]}),(0,n.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,n.jsx)(i(),{href:"/register",className:"text-red-700 font-semibold hover:underline",children:"Register"})]})]})})}},9958:(e,t,s)=>{"use strict";s.d(t,{i:()=>a,k:()=>r});let n="http://localhost:3001";async function a(e,t){return(await fetch("".concat(n,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})})).json()}async function r(e,t,s){return(await fetch("".concat(n,"/auth/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:s})})).json()}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,441,684,358],()=>t(5905)),_N_E=e.O()}]);