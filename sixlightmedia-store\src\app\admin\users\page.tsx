"use client";
import { useEffect, useState } from "react";
import { getApiUrl, API_CONFIG } from "@/lib/config";

interface User {
  id: string | number;
  name: string;
  email: string;
  role?: string;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [updating, setUpdating] = useState<string | number | null>(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    fetch(getApiUrl(API_CONFIG.ENDPOINTS.ADMIN.USERS), {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => {
        // If the response is not an array, try to extract the array
        if (Array.isArray(data)) {
          setUsers(data);
        } else if (Array.isArray(data.users)) {
          setUsers(data.users);
        } else {
          setUsers([]);
        }
      })
      .catch(() => setError("Failed to load users"))
      .finally(() => setLoading(false));
  }, []);

  async function handleRoleChange(userId: string | number, newRole: string) {
    setUpdating(userId);
    const token = localStorage.getItem("token");
    const res = await fetch(
      getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.USERS}/${userId}/role`),
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ role: newRole }),
      }
    );
    setUpdating(null);
    if (res.ok) {
      setUsers((prev) =>
        prev.map((u) => (u.id === userId ? { ...u, role: newRole } : u))
      );
    } else {
      alert("Failed to update user role");
    }
  }

  async function handleDeleteUser(userId: string | number) {
    if (!confirm("Are you sure you want to delete this user?")) return;
    const token = localStorage.getItem("token");
    const res = await fetch(
      getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.USERS}/${userId}`),
      {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    if (res.ok) {
      setUsers((prev) => prev.filter((u) => u.id !== userId));
    } else {
      alert("Failed to delete user");
    }
  }

  if (loading) return <div className="text-center mt-16">Loading...</div>;
  if (error)
    return <div className="text-red-600 text-center mt-16">{error}</div>;

  return (
    <div className="max-w-2xl mx-auto mt-10 bg-white rounded-2xl shadow p-8">
      <h2 className="text-2xl font-bold mb-6 text-center text-[#1a237e]">
        Manage Users
      </h2>
      <ul className="divide-y divide-gray-200">
        {users.map((user) => (
          <li
            key={user.id}
            className="py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"
          >
            <div>
              <span className="font-medium text-[#171717]">{user.name}</span>
              <span className="block text-gray-500 text-sm">{user.email}</span>
            </div>
            <div className="flex gap-2 items-center">
              <select
                className="border rounded px-2 py-1 text-sm"
                value={user.role || "USER"}
                onChange={(e) => handleRoleChange(user.id, e.target.value)}
                disabled={updating === user.id}
              >
                <option value="USER">User</option>
                <option value="ADMIN">Admin</option>
              </select>
              <button
                className="px-3 py-1 rounded bg-red-600 text-white text-xs font-semibold hover:bg-red-700 transition"
                onClick={() => handleDeleteUser(user.id)}
                disabled={updating === user.id}
              >
                Delete
              </button>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
