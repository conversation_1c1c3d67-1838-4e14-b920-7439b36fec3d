(()=>{var e={};e.id=733,e.ids=[733],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1423:(e,t,r)=>{Promise.resolve().then(r.bind(r,2031))},1671:(e,t,r)=>{Promise.resolve().then(r.bind(r,6953))},1755:()=>{},2031:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3624:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),n=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2031)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(7413),i=r(2376),a=r.n(i),n=r(8726),o=r.n(n);r(1135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4899:()=>{},6953:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(687),i=r(3210);function a(){let[e,t]=(0,i.useState)([]),[r,a]=(0,i.useState)(!0),[n,o]=(0,i.useState)(""),[l,d]=(0,i.useState)(null);async function p(e,r){d(e);let s=localStorage.getItem("token"),i=await fetch(`http://localhost:3001/admin/users/${e}/role`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({role:r})});d(null),i.ok?t(t=>t.map(t=>t.id===e?{...t,role:r}:t)):alert("Failed to update user role")}async function c(e){if(!confirm("Are you sure you want to delete this user?"))return;let r=localStorage.getItem("token");(await fetch(`http://localhost:3001/admin/users/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`}})).ok?t(t=>t.filter(t=>t.id!==e)):alert("Failed to delete user")}return r?(0,s.jsx)("div",{className:"text-center mt-16",children:"Loading..."}):n?(0,s.jsx)("div",{className:"text-red-600 text-center mt-16",children:n}):(0,s.jsxs)("div",{className:"max-w-2xl mx-auto mt-10 bg-white rounded-2xl shadow p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center text-[#1a237e]",children:"Manage Users"}),(0,s.jsx)("ul",{className:"divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("li",{className:"py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-[#171717]",children:e.name}),(0,s.jsx)("span",{className:"block text-gray-500 text-sm",children:e.email})]}),(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsxs)("select",{className:"border rounded px-2 py-1 text-sm",value:e.role||"USER",onChange:t=>p(e.id,t.target.value),disabled:l===e.id,children:[(0,s.jsx)("option",{value:"USER",children:"User"}),(0,s.jsx)("option",{value:"ADMIN",children:"Admin"})]}),(0,s.jsx)("button",{className:"px-3 py-1 rounded bg-red-600 text-white text-xs font-semibold hover:bg-red-700 transition",onClick:()=>c(e.id),disabled:l===e.id,children:"Delete"})]})]},e.id))})]})}},7219:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7467:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,145,658],()=>r(3624));module.exports=s})();