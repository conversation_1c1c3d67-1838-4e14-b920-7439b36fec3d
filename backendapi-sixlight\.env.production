# PRODUCTION Environment Variables
# Copy these values to your production hosting platform

# Environment
NODE_ENV="production"

# Server
PORT=3001

# Database URLs (your existing production database)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
DIRECT_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# SECURITY: Strong JWT Secret
JWT_SECRET="tVfn+VlANCzmunn+0AvG3WKJ+frOoPBUfLD8XsLS+bA="

# Email Configuration (REQUIRED)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="dxsl tqan xfzc vynn"

# Security Settings
EMAIL_VERIFICATION_EXPIRES_HOURS=24
PASSWORD_RESET_EXPIRES_HOURS=1

# CRITICAL: Update these URLs for production
# Your actual production domains
FRONTEND_URL="https://sixlightmediastorebeta.netlify.app"
BACKEND_URL="https://backendapi-sixlight.onrender.com"

# Email Service Configuration
EMAIL_FROM_NAME="Six Light Media Store"
EMAIL_FROM_ADDRESS="<EMAIL>"

# CORS Configuration (must match your frontend domain)
CORS_ORIGIN="https://sixlightmediastorebeta.netlify.app"

# Rate Limiting (production security)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration (production)
SESSION_SECRET="your-production-session-secret-here"
COOKIE_SECURE="true"

# Additional Production Settings
TRUST_PROXY="true"
HELMET_ENABLED="true"

# Logging Level
LOG_LEVEL="info"

# IMPORTANT INSTRUCTIONS:
# 1. Replace "your-frontend-domain.vercel.app" with your actual frontend URL
# 2. Replace "backendapi-sixlight.onrender.com" with your actual backend URL
# 3. Generate a new SESSION_SECRET for production
# 4. Set these in your hosting platform's environment variables
# 5. DO NOT commit this file to git with real values
