"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma.service");
const email_service_1 = require("../email/email.service");
const bcrypt = require("bcryptjs");
const crypto = require("crypto");
const client_1 = require("@prisma/client");
let AuthService = class AuthService {
    prisma;
    jwtService;
    emailService;
    constructor(prisma, jwtService, emailService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.emailService = emailService;
    }
    async validateUser(email, pass) {
        const user = await this.prisma.user.findUnique({ where: { email } });
        if (user && (await bcrypt.compare(pass, user.password))) {
            if (!user.emailVerified) {
                throw new Error('Please verify your email address before logging in');
            }
            const { password, ...result } = user;
            return result;
        }
        return null;
    }
    async login(user) {
        const payload = { email: user.email, sub: user.id, role: user.role };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user.id,
                email: user.email,
                role: user.role,
                name: user.name,
            },
        };
    }
    async register(data) {
        const existingUser = await this.prisma.user.findUnique({
            where: { email: data.email },
        });
        if (existingUser) {
            throw new Error('User with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(data.password, 10);
        const emailVerificationToken = crypto.randomBytes(32).toString('hex');
        const emailVerificationExpires = new Date();
        emailVerificationExpires.setHours(emailVerificationExpires.getHours() +
            parseInt(process.env.EMAIL_VERIFICATION_EXPIRES_HOURS || '24'));
        const userRole = client_1.$Enums.Role.USER;
        const user = await this.prisma.user.create({
            data: {
                email: data.email,
                password: hashedPassword,
                name: data.name,
                role: userRole,
                emailVerified: false,
                emailVerificationToken,
                emailVerificationExpires,
            },
        });
        try {
            console.log(`🚀 Sending verification email to: ${user.email}`);
            await this.emailService.sendEmailVerification(user.email, emailVerificationToken, user.name || undefined);
            console.log(`✅ Verification email sent successfully to: ${user.email}`);
        }
        catch (error) {
            console.error('❌ Failed to send verification email:', error);
            console.error('📧 Email details:', {
                email: user.email,
                token: emailVerificationToken,
                name: user.name,
            });
        }
        return {
            message: 'Registration successful! Please check your email to verify your account.',
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                emailVerified: user.emailVerified,
            },
        };
    }
    async verifyEmail(token) {
        const user = await this.prisma.user.findUnique({
            where: { emailVerificationToken: token },
        });
        if (!user) {
            throw new Error('Invalid verification token');
        }
        if (user.emailVerificationExpires &&
            user.emailVerificationExpires < new Date()) {
            throw new Error('Verification token has expired');
        }
        if (user.emailVerified) {
            throw new Error('Email is already verified');
        }
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                emailVerified: true,
                emailVerificationToken: null,
                emailVerificationExpires: null,
            },
        });
        return {
            message: 'Email verified successfully! You can now log in.',
        };
    }
    async requestPasswordReset(email) {
        const user = await this.prisma.user.findUnique({
            where: { email },
        });
        if (!user) {
            return {
                message: 'If an account with that email exists, a password reset link has been sent.',
            };
        }
        const passwordResetToken = crypto.randomBytes(32).toString('hex');
        const passwordResetExpires = new Date();
        passwordResetExpires.setHours(passwordResetExpires.getHours() +
            parseInt(process.env.PASSWORD_RESET_EXPIRES_HOURS || '1'));
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                passwordResetToken,
                passwordResetExpires,
            },
        });
        try {
            await this.emailService.sendPasswordReset(user.email, passwordResetToken, user.name || undefined);
        }
        catch (error) {
            console.error('Failed to send password reset email:', error);
            throw new Error('Failed to send password reset email');
        }
        return {
            message: 'If an account with that email exists, a password reset link has been sent.',
        };
    }
    async resetPassword(token, newPassword) {
        const user = await this.prisma.user.findUnique({
            where: { passwordResetToken: token },
        });
        if (!user) {
            throw new Error('Invalid reset token');
        }
        if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
            throw new Error('Reset token has expired');
        }
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedPassword,
                passwordResetToken: null,
                passwordResetExpires: null,
            },
        });
        return {
            message: 'Password reset successfully! You can now log in with your new password.',
        };
    }
    async resendVerificationEmail(email) {
        const user = await this.prisma.user.findUnique({
            where: { email },
        });
        if (!user) {
            throw new Error('User not found');
        }
        if (user.emailVerified) {
            throw new Error('Email is already verified');
        }
        const emailVerificationToken = crypto.randomBytes(32).toString('hex');
        const emailVerificationExpires = new Date();
        emailVerificationExpires.setHours(emailVerificationExpires.getHours() +
            parseInt(process.env.EMAIL_VERIFICATION_EXPIRES_HOURS || '24'));
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                emailVerificationToken,
                emailVerificationExpires,
            },
        });
        await this.emailService.sendEmailVerification(user.email, emailVerificationToken, user.name || undefined);
        return {
            message: 'Verification email sent! Please check your inbox.',
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        email_service_1.EmailService])
], AuthService);
//# sourceMappingURL=auth.service.js.map