(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[798],{1988:(e,t,s)=>{Promise.resolve().then(s.bind(s,9720))},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},9720:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(5155),a=s(2115),l=s(5695),i=s(4615),c=s(3389),d=s(3843);function n(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[o,x]=(0,a.useState)(""),[m,u]=(0,a.useState)(0),[h,p]=(0,a.useState)(!1),g=(0,l.useRouter)(),y=()=>{u(JSON.parse(localStorage.getItem("cart")||"[]").length)};(0,a.useEffect)(()=>{y()},[]),(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(!e)return void g.push("/login");try{let t=JSON.parse(atob(e.split(".")[1]));if("ADMIN"!==t.role)return void g.push("/")}catch(e){g.push("/login");return}N()},[g]);let N=async()=>{let e=localStorage.getItem("token");try{let s=await fetch((0,d.e9)(d.i3.ENDPOINTS.ADMIN.ORDERS),{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){let e=await s.json();t(e)}else x("Failed to fetch orders")}catch(e){x("Failed to fetch orders")}finally{n(!1)}},j=async(s,r)=>{let a=localStorage.getItem("token");try{(await fetch((0,d.e9)("".concat(d.i3.ENDPOINTS.ADMIN.ORDERS,"/").concat(s,"/status")),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify({status:r})})).ok?t(e.map(e=>e.id===s?{...e,status:r}:e)):x("Failed to update order status")}catch(e){x("Failed to update order status")}},f=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PROCESSING":return"bg-blue-100 text-blue-800";case"COMPLETED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return s?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.A,{cartCount:m,onCartClick:()=>{p(!0)}}),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-[#1a237e]",children:"Order Management"}),(0,r.jsx)("button",{onClick:()=>g.push("/admin/dashboard"),className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition",children:"Back to Dashboard"})]}),o&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:o}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("h2",{className:"text-xl font-semibold",children:["All Orders (",e.length,")"]})}),0===e.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Orders will appear here when customers place them."})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order Details"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer Info"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customization"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:["Order #",e.id]}),(0,r.jsx)("div",{className:"text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsxs)("div",{className:"text-gray-500",children:["Qty: ",e.quantity]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.customerName}),(0,r.jsx)("div",{className:"text-gray-500",children:e.customerPhone}),(0,r.jsx)("div",{className:"text-gray-500",children:e.customerEmail}),(0,r.jsx)("div",{className:"text-gray-500 text-xs mt-1 max-w-xs",children:e.customerAddress})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.product.name}),(0,r.jsxs)("div",{className:"text-gray-500",children:["K",e.unitPrice.toFixed(2)," each"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm",children:e.isCustomized?(0,r.jsxs)(r.Fragment,{children:[e.customColor&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:e.customColor}}),(0,r.jsx)("span",{className:"text-xs font-mono",children:e.customColor})]}),e.customText&&(0,r.jsxs)("div",{className:"text-xs text-gray-600",children:["Text: “",e.customText,"”"]})]}):(0,r.jsx)("span",{className:"text-gray-400 text-xs",children:"No customization"})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["K",e.totalPrice.toFixed(2)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(f(e.status)),children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:(0,r.jsxs)("select",{value:e.status,onChange:t=>j(e.id,t.target.value),className:"border border-gray-300 rounded px-2 py-1 text-xs",children:[(0,r.jsx)("option",{value:"PENDING",children:"Pending"}),(0,r.jsx)("option",{value:"PROCESSING",children:"Processing"}),(0,r.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,r.jsx)("option",{value:"CANCELLED",children:"Cancelled"})]})})]},e.id))})]})})]})]})}),(0,r.jsx)(c.A,{isOpen:h,onClose:()=>{p(!1),y()}})]})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:x,iconNode:m,...u}=e;return(0,r.createElement)("svg",{ref:t,...n,width:a,height:a,stroke:s,strokeWidth:i?24*Number(l)/Number(a):l,className:c("lucide",o),...!x&&!d(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:d,...n}=s;return(0,r.createElement)(o,{ref:l,iconNode:t,className:c("lucide-".concat(a(i(e))),"lucide-".concat(e),d),...n})});return s.displayName=i(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,211,441,684,358],()=>t(1988)),_N_E=e.O()}]);