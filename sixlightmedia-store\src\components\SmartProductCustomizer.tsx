"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

interface ProductCustomizerProps {
  productImage: string;
  onCustomizationChange: (customization: CustomizationData) => void;
  initialCustomization?: CustomizationData | null;
  productId?: string | number; // Add productId for localStorage persistence
}

// Dynamically import the fallback customizer
const ProductCustomizerFallback = dynamic(
  () => import("@/components/ProductCustomizerFallback"),
  {
    ssr: false,
    loading: () => (
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Customizer...</p>
          </div>
        </div>
      </div>
    ),
  }
);

const SmartProductCustomizer: React.FC<ProductCustomizerProps> = (props) => {
  const [fabricAvailable, setFabricAvailable] = useState<boolean | null>(null);
  const [AdvancedCustomizer, setAdvancedCustomizer] =
    useState<React.ComponentType<ProductCustomizerProps> | null>(null);

  useEffect(() => {
    // Check if Fabric.js is available
    const checkFabricAvailability = async () => {
      try {
        // Try to dynamically import fabric
        await import("fabric");

        // If successful, try to import the advanced customizer
        const { default: ProductCustomizer } = await import(
          "@/components/ProductCustomizer"
        );
        setAdvancedCustomizer(() => ProductCustomizer);
        setFabricAvailable(true);
      } catch (error) {
        // Fabric.js or advanced customizer not available
        setFabricAvailable(false);
      }
    };

    checkFabricAvailability();
  }, []);

  // Show loading state while checking
  if (fabricAvailable === null) {
    return (
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking customizer capabilities...</p>
          </div>
        </div>
      </div>
    );
  }

  // Use advanced customizer if available, otherwise use fallback
  if (fabricAvailable && AdvancedCustomizer) {
    return <AdvancedCustomizer {...props} />;
  }

  return <ProductCustomizerFallback {...props} />;
};

export default SmartProductCustomizer;
