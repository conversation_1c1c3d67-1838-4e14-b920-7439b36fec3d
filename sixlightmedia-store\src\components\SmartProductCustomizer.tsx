"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

interface ProductCustomizerProps {
  productImage: string;
  onCustomizationChange: (customization: CustomizationData) => void;
  initialCustomization?: CustomizationData | null;
  productId?: string | number; // Add productId for localStorage persistence
}

// Dynamically import the fallback customizer
const ProductCustomizerFallback = dynamic(
  () => import("@/components/ProductCustomizerFallback"),
  {
    ssr: false,
    loading: () => (
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Customizer...</p>
          </div>
        </div>
      </div>
    ),
  }
);

const SmartProductCustomizer: React.FC<ProductCustomizerProps> = (props) => {
  const [fabricAvailable, setFabricAvailable] = useState<boolean | null>(null);
  const [AdvancedCustomizer, setAdvancedCustomizer] =
    useState<React.ComponentType<ProductCustomizerProps> | null>(null);

  useEffect(() => {
    // Check if Fabric.js is available
    const checkFabricAvailability = async () => {
      try {
        console.log(
          "SmartProductCustomizer: Checking Fabric.js availability..."
        );

        // Try to dynamically import fabric
        const fabricModule = await import("fabric");
        console.log(
          "SmartProductCustomizer: Fabric.js imported successfully:",
          !!fabricModule.fabric
        );

        // Verify fabric is actually usable
        if (!fabricModule.fabric) {
          throw new Error("Fabric.js imported but fabric object is null");
        }

        // If successful, try to import the advanced customizer
        console.log("SmartProductCustomizer: Importing ProductCustomizer...");
        const { default: ProductCustomizer } = await import(
          "@/components/ProductCustomizer"
        );
        console.log(
          "SmartProductCustomizer: ProductCustomizer imported successfully:",
          !!ProductCustomizer
        );

        if (!ProductCustomizer) {
          throw new Error("ProductCustomizer imported but is null");
        }

        setAdvancedCustomizer(() => ProductCustomizer);
        setFabricAvailable(true);
        console.log("SmartProductCustomizer: Advanced customizer ready!");
      } catch (error) {
        // Fabric.js or advanced customizer not available
        console.error(
          "SmartProductCustomizer: Error loading advanced customizer:",
          error
        );
        setFabricAvailable(false);
      }
    };

    // Add timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.error(
        "SmartProductCustomizer: Timeout - falling back to basic customizer"
      );
      setFabricAvailable(false);
    }, 3000); // Reduced to 3 seconds for faster fallback

    checkFabricAvailability().finally(() => {
      clearTimeout(timeout);
    });
  }, []);

  // Show loading state while checking
  if (fabricAvailable === null) {
    return (
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking customizer capabilities...</p>
            <p className="text-sm text-gray-500 mt-2">
              Loading advanced design tools...
            </p>
            <p className="text-xs text-gray-400 mt-2">
              If this takes too long, we'll automatically switch to basic mode
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Use advanced customizer if available, otherwise use fallback
  if (fabricAvailable && AdvancedCustomizer) {
    return <AdvancedCustomizer {...props} />;
  }

  return <ProductCustomizerFallback {...props} />;
};

export default SmartProductCustomizer;
