-- 1. Create Category table (if not already created by <PERSON>rism<PERSON>)
CREATE TABLE IF NOT EXISTS "Category" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT UNIQUE NOT NULL
);

-- 2. Insert unique categories from Product.category into Category
INSERT INTO "Category" ("name")
SELECT DISTINCT "category" FROM "Product"
WHERE "category" IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM "Category" WHERE "Category"."name" = "Product"."category"
  );

-- 3. Add nullable categoryId column to Product (if not already present)
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "categoryId" INTEGER;

-- 4. Set categoryId for each product based on its old category value
UPDATE "Product" p
SET "categoryId" = c."id"
FROM "Category" c
WHERE p."category" = c."name";

-- 5. Drop old category column from Product
ALTER TABLE "Product" DROP COLUMN IF EXISTS "category";

-- 6. (Optional) Set categoryId to NOT NULL if all products have a category
-- ALTER TABLE "Product" ALTER COLUMN "categoryId" SET NOT NULL;

-- 7. Add foreign key constraint
ALTER TABLE "Product"
  ADD CONSTRAINT "Product_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE SET NULL;
