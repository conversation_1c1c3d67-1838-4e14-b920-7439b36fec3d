"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[88],{9088:(e,t,r)=>{r.d(t,{u:()=>t1});var n,i,o,a,s,l,c,u,f,p,d,h,g,v=function(){return n||"undefined"!=typeof window&&(n=window.gsap)&&n.registerPlugin&&n},m=1,y=[],x=[],b=[],_=Date.now,w=function(e,t){return t},k=function(){var e=f.core,t=e.bridge||{},r=e._scrollers,n=e._proxies;r.push.apply(r,x),n.push.apply(n,b),x=r,b=n,w=function(e,r){return t[e](r)}},C=function(e,t){return~b.indexOf(e)&&b[b.indexOf(e)+1][t]},T=function(e){return!!~p.indexOf(e)},E=function(e,t,r,n,i){return e.addEventListener(t,r,{passive:!1!==n,capture:!!i})},S=function(e,t,r,n){return e.removeEventListener(t,r,!!n)},P="scrollLeft",M="scrollTop",O=function(){return d&&d.isPressed||x.cache++},A=function(e,t){var r=function r(n){if(n||0===n){m&&(o.history.scrollRestoration="manual");var i=d&&d.isPressed;e(n=r.v=Math.round(n)||(d&&d.iOS?1:0)),r.cacheID=x.cache,i&&w("ss",n)}else(t||x.cache!==r.cacheID||w("ref"))&&(r.cacheID=x.cache,r.v=e());return r.v+r.offset};return r.offset=0,e&&r},R={s:P,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:A(function(e){return arguments.length?o.scrollTo(e,D.sc()):o.pageXOffset||a[P]||s[P]||l[P]||0})},D={s:M,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:R,sc:A(function(e){return arguments.length?o.scrollTo(R.sc(),e):o.pageYOffset||a[M]||s[M]||l[M]||0})},Y=function(e,t){return(t&&t._ctx&&t._ctx.selector||n.utils.toArray)(e)[0]||("string"==typeof e&&!1!==n.config().nullTargetWarn?console.warn("Element not found:",e):null)},I=function(e,t){for(var r=t.length;r--;)if(t[r]===e||t[r].contains(e))return!0;return!1},B=function(e,t){var r=t.s,i=t.sc;T(e)&&(e=a.scrollingElement||s);var o=x.indexOf(e),l=i===D.sc?1:2;~o||(o=x.push(e)-1),x[o+l]||E(e,"scroll",O);var c=x[o+l],u=c||(x[o+l]=A(C(e,r),!0)||(T(e)?i:A(function(t){return arguments.length?e[r]=t:e[r]})));return u.target=e,c||(u.smooth="smooth"===n.getProperty(e,"scrollBehavior")),u},N=function(e,t,r){var n=e,i=e,o=_(),a=o,s=t||50,l=Math.max(500,3*s),c=function(e,t){var l=_();t||l-o>s?(i=n,n=e,a=o,o=l):r?n+=e:n=i+(e-i)/(l-a)*(o-a)};return{update:c,reset:function(){i=n=r?0:n,a=o=0},getVelocity:function(e){var t=a,s=i,u=_();return(e||0===e)&&e!==n&&c(e),o===a||u-a>l?0:(n+(r?s:-s))/((r?u:o)-t)*1e3}}},X=function(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},z=function(e){var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(r)?t:r},F=function(){(f=n.core.globals().ScrollTrigger)&&f.core&&k()},H=function(e){return n=e||v(),!i&&n&&"undefined"!=typeof document&&document.body&&(o=window,s=(a=document).documentElement,l=a.body,p=[o,a,s,l],n.utils.clamp,g=n.core.context||function(){},u="onpointerenter"in l?"pointer":"mouse",c=L.isTouch=o.matchMedia&&o.matchMedia("(hover: none), (pointer: coarse)").matches?1:2*("ontouchstart"in o||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0),h=L.eventTypes=("ontouchstart"in s?"touchstart,touchmove,touchcancel,touchend":!("onpointerdown"in s)?"mousedown,mousemove,mouseup,mouseup":"pointerdown,pointermove,pointercancel,pointerup").split(","),setTimeout(function(){return m=0},500),F(),i=1),i};R.op=D,x.cache=0;var L=function(){var e;function t(e){this.init(e)}return t.prototype.init=function(e){i||H(n)||console.warn("Please gsap.registerPlugin(Observer)"),f||F();var t=e.tolerance,r=e.dragMinimum,p=e.type,v=e.target,m=e.lineHeight,x=e.debounce,b=e.preventDefault,w=e.onStop,k=e.onStopDelay,C=e.ignore,P=e.wheelSpeed,M=e.event,A=e.onDragStart,L=e.onDragEnd,W=e.onDrag,q=e.onPress,U=e.onRelease,V=e.onRight,G=e.onLeft,j=e.onUp,K=e.onDown,Z=e.onChangeX,$=e.onChangeY,J=e.onChange,Q=e.onToggleX,ee=e.onToggleY,et=e.onHover,er=e.onHoverEnd,en=e.onMove,ei=e.ignoreCheck,eo=e.isNormalizer,ea=e.onGestureStart,es=e.onGestureEnd,el=e.onWheel,ec=e.onEnable,eu=e.onDisable,ef=e.onClick,ep=e.scrollSpeed,ed=e.capture,eh=e.allowClicks,eg=e.lockAxis,ev=e.onLockAxis;this.target=v=Y(v)||s,this.vars=e,C&&(C=n.utils.toArray(C)),t=t||1e-9,r=r||0,P=P||1,ep=ep||1,p=p||"wheel,touch,pointer",x=!1!==x,m||(m=parseFloat(o.getComputedStyle(l).lineHeight)||22);var em,ey,ex,eb,e_,ew,ek,eC=this,eT=0,eE=0,eS=e.passive||!b&&!1!==e.passive,eP=B(v,R),eM=B(v,D),eO=eP(),eA=eM(),eR=~p.indexOf("touch")&&!~p.indexOf("pointer")&&"pointerdown"===h[0],eD=T(v),eY=v.ownerDocument||a,eI=[0,0,0],eB=[0,0,0],eN=0,eX=function(){return eN=_()},ez=function(e,t){return(eC.event=e)&&C&&I(e.target,C)||t&&eR&&"touch"!==e.pointerType||ei&&ei(e,t)},eF=function(){var e=eC.deltaX=z(eI),r=eC.deltaY=z(eB),n=Math.abs(e)>=t,i=Math.abs(r)>=t;J&&(n||i)&&J(eC,e,r,eI,eB),n&&(V&&eC.deltaX>0&&V(eC),G&&eC.deltaX<0&&G(eC),Z&&Z(eC),Q&&eC.deltaX<0!=eT<0&&Q(eC),eT=eC.deltaX,eI[0]=eI[1]=eI[2]=0),i&&(K&&eC.deltaY>0&&K(eC),j&&eC.deltaY<0&&j(eC),$&&$(eC),ee&&eC.deltaY<0!=eE<0&&ee(eC),eE=eC.deltaY,eB[0]=eB[1]=eB[2]=0),(eb||ex)&&(en&&en(eC),ex&&(A&&1===ex&&A(eC),W&&W(eC),ex=0),eb=!1),ew&&(ew=!1,1)&&ev&&ev(eC),e_&&(el(eC),e_=!1),em=0},eH=function(e,t,r){eI[r]+=e,eB[r]+=t,eC._vx.update(e),eC._vy.update(t),x?em||(em=requestAnimationFrame(eF)):eF()},eL=function(e,t){eg&&!ek&&(eC.axis=ek=Math.abs(e)>Math.abs(t)?"x":"y",ew=!0),"y"!==ek&&(eI[2]+=e,eC._vx.update(e,!0)),"x"!==ek&&(eB[2]+=t,eC._vy.update(t,!0)),x?em||(em=requestAnimationFrame(eF)):eF()},eW=function(e){if(!ez(e,1)){var t=(e=X(e,b)).clientX,n=e.clientY,i=t-eC.x,o=n-eC.y,a=eC.isDragging;eC.x=t,eC.y=n,(a||(i||o)&&(Math.abs(eC.startX-t)>=r||Math.abs(eC.startY-n)>=r))&&(ex=a?2:1,a||(eC.isDragging=!0),eL(i,o))}},eq=eC.onPress=function(e){ez(e,1)||e&&e.button||(eC.axis=ek=null,ey.pause(),eC.isPressed=!0,e=X(e),eT=eE=0,eC.startX=eC.x=e.clientX,eC.startY=eC.y=e.clientY,eC._vx.reset(),eC._vy.reset(),E(eo?v:eY,h[1],eW,eS,!0),eC.deltaX=eC.deltaY=0,q&&q(eC))},eU=eC.onRelease=function(e){if(!ez(e,1)){S(eo?v:eY,h[1],eW,!0);var t=!isNaN(eC.y-eC.startY),r=eC.isDragging,i=r&&(Math.abs(eC.x-eC.startX)>3||Math.abs(eC.y-eC.startY)>3),a=X(e);!i&&t&&(eC._vx.reset(),eC._vy.reset(),b&&eh&&n.delayedCall(.08,function(){if(_()-eN>300&&!e.defaultPrevented){if(e.target.click)e.target.click();else if(eY.createEvent){var t=eY.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,o,1,a.screenX,a.screenY,a.clientX,a.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)}}})),eC.isDragging=eC.isGesturing=eC.isPressed=!1,w&&r&&!eo&&ey.restart(!0),ex&&eF(),L&&r&&L(eC),U&&U(eC,i)}},eV=function(e){return e.touches&&e.touches.length>1&&(eC.isGesturing=!0)&&ea(e,eC.isDragging)},eG=function(){return eC.isGesturing=!1,es(eC)},ej=function(e){if(!ez(e)){var t=eP(),r=eM();eH((t-eO)*ep,(r-eA)*ep,1),eO=t,eA=r,w&&ey.restart(!0)}},eK=function(e){if(!ez(e)){e=X(e,b),el&&(e_=!0);var t=(1===e.deltaMode?m:2===e.deltaMode?o.innerHeight:1)*P;eH(e.deltaX*t,e.deltaY*t,0),w&&!eo&&ey.restart(!0)}},eZ=function(e){if(!ez(e)){var t=e.clientX,r=e.clientY,n=t-eC.x,i=r-eC.y;eC.x=t,eC.y=r,eb=!0,w&&ey.restart(!0),(n||i)&&eL(n,i)}},e$=function(e){eC.event=e,et(eC)},eJ=function(e){eC.event=e,er(eC)},eQ=function(e){return ez(e)||X(e,b)&&ef(eC)};ey=eC._dc=n.delayedCall(k||.25,function(){eC._vx.reset(),eC._vy.reset(),ey.pause(),w&&w(eC)}).pause(),eC.deltaX=eC.deltaY=0,eC._vx=N(0,50,!0),eC._vy=N(0,50,!0),eC.scrollX=eP,eC.scrollY=eM,eC.isDragging=eC.isGesturing=eC.isPressed=!1,g(this),eC.enable=function(e){return!eC.isEnabled&&(E(eD?eY:v,"scroll",O),p.indexOf("scroll")>=0&&E(eD?eY:v,"scroll",ej,eS,ed),p.indexOf("wheel")>=0&&E(v,"wheel",eK,eS,ed),(p.indexOf("touch")>=0&&c||p.indexOf("pointer")>=0)&&(E(v,h[0],eq,eS,ed),E(eY,h[2],eU),E(eY,h[3],eU),eh&&E(v,"click",eX,!0,!0),ef&&E(v,"click",eQ),ea&&E(eY,"gesturestart",eV),es&&E(eY,"gestureend",eG),et&&E(v,u+"enter",e$),er&&E(v,u+"leave",eJ),en&&E(v,u+"move",eZ)),eC.isEnabled=!0,eC.isDragging=eC.isGesturing=eC.isPressed=eb=ex=!1,eC._vx.reset(),eC._vy.reset(),eO=eP(),eA=eM(),e&&e.type&&eq(e),ec&&ec(eC)),eC},eC.disable=function(){eC.isEnabled&&(y.filter(function(e){return e!==eC&&T(e.target)}).length||S(eD?eY:v,"scroll",O),eC.isPressed&&(eC._vx.reset(),eC._vy.reset(),S(eo?v:eY,h[1],eW,!0)),S(eD?eY:v,"scroll",ej,ed),S(v,"wheel",eK,ed),S(v,h[0],eq,ed),S(eY,h[2],eU),S(eY,h[3],eU),S(v,"click",eX,!0),S(v,"click",eQ),S(eY,"gesturestart",eV),S(eY,"gestureend",eG),S(v,u+"enter",e$),S(v,u+"leave",eJ),S(v,u+"move",eZ),eC.isEnabled=eC.isPressed=eC.isDragging=!1,eu&&eu(eC))},eC.kill=eC.revert=function(){eC.disable();var e=y.indexOf(eC);e>=0&&y.splice(e,1),d===eC&&(d=0)},y.push(eC),eo&&T(v)&&(d=eC),eC.enable(M)},e=[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),t}();L.version="3.13.0",L.create=function(e){return new L(e)},L.register=H,L.getAll=function(){return y.slice()},L.getById=function(e){return y.filter(function(t){return t.vars.id===e})[0]},v()&&n.registerPlugin(L);var W,q,U,V,G,j,K,Z,$,J,Q,ee,et,er,en,ei,eo,ea,es,el,ec,eu,ef,ep,ed,eh,eg,ev,em,ey,ex,eb,e_,ew,ek,eC,eT,eE,eS=1,eP=Date.now,eM=eP(),eO=0,eA=0,eR=function(e,t,r){var n=eV(e)&&("clamp("===e.substr(0,6)||e.indexOf("max")>-1);return r["_"+t+"Clamp"]=n,n?e.substr(6,e.length-7):e},eD=function(e,t){return t&&(!eV(e)||"clamp("!==e.substr(0,6))?"clamp("+e+")":e},eY=function(){return er=1},eI=function(){return er=0},eB=function(e){return e},eN=function(e){return Math.round(1e5*e)/1e5||0},eX=function(){return"undefined"!=typeof window},ez=function(){return W||eX()&&(W=window.gsap)&&W.registerPlugin&&W},eF=function(e){return!!~K.indexOf(e)},eH=function(e){return("Height"===e?ex:U["inner"+e])||G["client"+e]||j["client"+e]},eL=function(e){return C(e,"getBoundingClientRect")||(eF(e)?function(){return tj.width=U.innerWidth,tj.height=ex,tj}:function(){return ti(e)})},eW=function(e,t,r){var n=r.d,i=r.d2,o=r.a;return(o=C(e,"getBoundingClientRect"))?function(){return o()[n]}:function(){return(t?eH(i):e["client"+i])||0}},eq=function(e,t){var r=t.s,n=t.d2,i=t.d,o=t.a;return Math.max(0,(o=C(e,r="scroll"+n))?o()-eL(e)()[i]:eF(e)?(G[r]||j[r])-eH(n):e[r]-e["offset"+n])},eU=function(e,t){for(var r=0;r<es.length;r+=3)(!t||~t.indexOf(es[r+1]))&&e(es[r],es[r+1],es[r+2])},eV=function(e){return"string"==typeof e},eG=function(e){return"function"==typeof e},ej=function(e){return"number"==typeof e},eK=function(e){return"object"==typeof e},eZ=function(e,t,r){return e&&e.progress(+!t)&&r&&e.pause()},e$=function(e,t){if(e.enabled){var r=e._ctx?e._ctx.add(function(){return t(e)}):t(e);r&&r.totalTime&&(e.callbackAnimation=r)}},eJ=Math.abs,eQ="left",e0="right",e1="bottom",e2="width",e3="height",e5="Right",e9="Left",e6="Bottom",e8="padding",e4="margin",e7="Width",te="Height",tt=function(e){return U.getComputedStyle(e)},tr=function(e){var t=tt(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"},tn=function(e,t){for(var r in t)r in e||(e[r]=t[r]);return e},ti=function(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==tt(e)[en]&&W.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n},to=function(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0},ta=function(e){var t,r=[],n=e.labels,i=e.duration();for(t in n)r.push(n[t]/i);return r},ts=function(e){var t=W.utils.snap(e),r=Array.isArray(e)&&e.slice(0).sort(function(e,t){return e-t});return r?function(e,n,i){var o;if(void 0===i&&(i=.001),!n)return t(e);if(n>0){for(e-=i,o=0;o<r.length;o++)if(r[o]>=e)return r[o];return r[o-1]}for(o=r.length,e+=i;o--;)if(r[o]<=e)return r[o];return r[0]}:function(r,n,i){void 0===i&&(i=.001);var o=t(r);return!n||Math.abs(o-r)<i||o-r<0==n<0?o:t(n<0?r-e:r+e)}},tl=function(e,t,r,n){return r.split(",").forEach(function(r){return e(t,r,n)})},tc=function(e,t,r,n,i){return e.addEventListener(t,r,{passive:!n,capture:!!i})},tu=function(e,t,r,n){return e.removeEventListener(t,r,!!n)},tf=function(e,t,r){(r=r&&r.wheelHandler)&&(e(t,"wheel",r),e(t,"touchmove",r))},tp={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},td={toggleActions:"play",anticipatePin:0},th={top:0,left:0,center:.5,bottom:1,right:1},tg=function(e,t){if(eV(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;~r&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in th?th[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e},tv=function(e,t,r,n,i,o,a,s){var l=i.startColor,c=i.endColor,u=i.fontSize,f=i.indent,p=i.fontWeight,d=V.createElement("div"),h=eF(r)||"fixed"===C(r,"pinType"),g=-1!==e.indexOf("scroller"),v=h?j:r,m=-1!==e.indexOf("start"),y=m?l:c,x="border-color:"+y+";font-size:"+u+";color:"+y+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+((g||s)&&h?"fixed;":"absolute;"),(g||s||!h)&&(x+=(n===D?e0:e1)+":"+(o+parseFloat(f))+"px;"),a&&(x+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),d._isStart=m,d.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),d.style.cssText=x,d.innerText=t||0===t?e+"-"+t:e,v.children[0]?v.insertBefore(d,v.children[0]):v.appendChild(d),d._offset=d["offset"+n.op.d2],tm(d,0,n,m),d},tm=function(e,t,r,n){var i={display:"block"},o=r[n?"os2":"p2"],a=r[n?"p2":"os2"];e._isFlipped=n,i[r.a+"Percent"]=n?-100:0,i[r.a]=n?"1px":0,i["border"+o+e7]=1,i["border"+a+e7]=0,i[r.p]=t+"px",W.set(e,i)},ty=[],tx={},tb=function(){return eP()-eO>34&&(ek||(ek=requestAnimationFrame(tz)))},t_=function(){ef&&ef.isPressed&&!(ef.startX>j.clientWidth)||(x.cache++,ef?ek||(ek=requestAnimationFrame(tz)):tz(),eO||tS("scrollStart"),eO=eP())},tw=function(){eh=U.innerWidth,ed=U.innerHeight},tk=function(e){x.cache++,(!0===e||!et&&!eu&&!V.fullscreenElement&&!V.webkitFullscreenElement&&(!ep||eh!==U.innerWidth||Math.abs(U.innerHeight-ed)>.25*U.innerHeight))&&Z.restart(!0)},tC={},tT=[],tE=function e(){return tu(t1,"scrollEnd",e)||tB(!0)},tS=function(e){return tC[e]&&tC[e].map(function(e){return e()})||tT},tP=[],tM=function(e){for(var t=0;t<tP.length;t+=5)(!e||tP[t+4]&&tP[t+4].query===e)&&(tP[t].style.cssText=tP[t+1],tP[t].getBBox&&tP[t].setAttribute("transform",tP[t+2]||""),tP[t+3].uncache=1)},tO=function(e,t){var r;for(ei=0;ei<ty.length;ei++)(r=ty[ei])&&(!t||r._ctx===t)&&(e?r.kill(1):r.revert(!0,!0));eb=!0,t&&tM(t),t||tS("revert")},tA=function(e,t){x.cache++,(t||!eC)&&x.forEach(function(e){return eG(e)&&e.cacheID++&&(e.rec=0)}),eV(e)&&(U.history.scrollRestoration=em=e)},tR=0,tD=function(){if(eT!==tR){var e=eT=tR;requestAnimationFrame(function(){return e===tR&&tB(!0)})}},tY=function(){j.appendChild(ey),ex=!ef&&ey.offsetHeight||U.innerHeight,j.removeChild(ey)},tI=function(e){return $(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(t){return t.style.display=e?"none":"block"})},tB=function(e,t){if(G=V.documentElement,j=V.body,K=[U,V,G,j],eO&&!e&&!eb)return void tc(t1,"scrollEnd",tE);tY(),eC=t1.isRefreshing=!0,x.forEach(function(e){return eG(e)&&++e.cacheID&&(e.rec=e())});var r=tS("refreshInit");el&&t1.sort(),t||tO(),x.forEach(function(e){eG(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))}),ty.slice(0).forEach(function(e){return e.refresh()}),eb=!1,ty.forEach(function(e){if(e._subPinOffset&&e.pin){var t=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[t];e.revert(!0,1),e.adjustPinSpacing(e.pin[t]-r),e.refresh()}}),e_=1,tI(!0),ty.forEach(function(e){var t=eq(e.scroller,e._dir),r="max"===e.vars.end||e._endClamp&&e.end>t,n=e._startClamp&&e.start>=t;(r||n)&&e.setPositions(n?t-1:e.start,r?Math.max(n?t:e.start+1,t):e.end,!0)}),tI(!1),e_=0,r.forEach(function(e){return e&&e.render&&e.render(-1)}),x.forEach(function(e){eG(e)&&(e.smooth&&requestAnimationFrame(function(){return e.target.style.scrollBehavior="smooth"}),e.rec&&e(e.rec))}),tA(em,1),Z.pause(),tR++,eC=2,tz(2),ty.forEach(function(e){return eG(e.vars.onRefresh)&&e.vars.onRefresh(e)}),eC=t1.isRefreshing=!1,tS("refresh")},tN=0,tX=1,tz=function(e){if(2===e||!eC&&!eb){t1.isUpdating=!0,eE&&eE.update(0);var t=ty.length,r=eP(),n=r-eM>=50,i=t&&ty[0].scroll();if(tX=tN>i?-1:1,eC||(tN=i),n&&(eO&&!er&&r-eO>200&&(eO=0,tS("scrollEnd")),Q=eM,eM=r),tX<0){for(ei=t;ei-- >0;)ty[ei]&&ty[ei].update(0,n);tX=1}else for(ei=0;ei<t;ei++)ty[ei]&&ty[ei].update(0,n);t1.isUpdating=!1}ek=0},tF=[eQ,"top",e1,e0,e4+e6,e4+e5,e4+"Top",e4+e9,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],tH=tF.concat([e2,e3,"boxSizing","max"+e7,"max"+te,"position",e4,e8,e8+"Top",e8+e5,e8+e6,e8+e9]),tL=function(e,t,r){tU(r);var n=e._gsap;if(n.spacerIsNative)tU(n.spacerState);else if(e._gsap.swappedIn){var i=t.parentNode;i&&(i.insertBefore(e,t),i.removeChild(t))}e._gsap.swappedIn=!1},tW=function(e,t,r,n){if(!e._gsap.swappedIn){for(var i,o=tF.length,a=t.style,s=e.style;o--;)a[i=tF[o]]=r[i];a.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(a.display="inline-block"),s[e1]=s[e0]="auto",a.flexBasis=r.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[e2]=to(e,R)+"px",a[e3]=to(e,D)+"px",a[e8]=s[e4]=s.top=s[eQ]="0",tU(n),s[e2]=s["max"+e7]=r[e2],s[e3]=s["max"+te]=r[e3],s[e8]=r[e8],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}},tq=/([A-Z])/g,tU=function(e){if(e){var t,r,n=e.t.style,i=e.length,o=0;for((e.t._gsap||W.core.getCache(e.t)).uncache=1;o<i;o+=2)r=e[o+1],t=e[o],r?n[t]=r:n[t]&&n.removeProperty(t.replace(tq,"-$1").toLowerCase())}},tV=function(e){for(var t=tH.length,r=e.style,n=[],i=0;i<t;i++)n.push(tH[i],r[tH[i]]);return n.t=e,n},tG=function(e,t,r){for(var n,i=[],o=e.length,a=8*!!r;a<o;a+=2)n=e[a],i.push(n,n in t?t[n]:e[a+1]);return i.t=e.t,i},tj={left:0,top:0},tK=function(e,t,r,n,i,o,a,s,l,c,u,f,p,d){eG(e)&&(e=e(s)),eV(e)&&"max"===e.substr(0,3)&&(e=f+("="===e.charAt(4)?tg("0"+e.substr(3),r):0));var h,g,v,m=p?p.time():0;if(p&&p.seek(0),isNaN(e)||(e*=1),ej(e))p&&(e=W.utils.mapRange(p.scrollTrigger.start,p.scrollTrigger.end,0,f,e)),a&&tm(a,r,n,!0);else{eG(t)&&(t=t(s));var y,x,b,_,w=(e||"0").split(" ");(y=ti(v=Y(t,s)||j)||{}).left||y.top||"none"!==tt(v).display||(_=v.style.display,v.style.display="block",y=ti(v),_?v.style.display=_:v.style.removeProperty("display")),x=tg(w[0],y[n.d]),b=tg(w[1]||"0",r),e=y[n.p]-l[n.p]-c+x+i-b,a&&tm(a,b,n,r-b<20||a._isStart&&b>20),r-=r-b}if(d&&(s[d]=e||-.001,e<0&&(e=0)),o){var k=e+r,C=o._isStart;h="scroll"+n.d2,tm(o,k,n,C&&k>20||!C&&(u?Math.max(j[h],G[h]):o.parentNode[h])<=k+1),u&&(l=ti(a),u&&(o.style[n.op.p]=l[n.op.p]-n.op.m-o._offset+"px"))}return p&&v&&(h=ti(v),p.seek(f),g=ti(v),p._caScrollDist=h[n.p]-g[n.p],e=e/p._caScrollDist*f),p&&p.seek(m),p?e:Math.round(e)},tZ=/(webkit|moz|length|cssText|inset)/i,t$=function(e,t,r,n){if(e.parentNode!==t){var i,o,a=e.style;if(t===j){for(i in e._stOrig=a.cssText,o=tt(e))+i||tZ.test(i)||!o[i]||"string"!=typeof a[i]||"0"===i||(a[i]=o[i]);a.top=r,a.left=n}else a.cssText=e._stOrig;W.core.getCache(e).uncache=1,t.appendChild(e)}},tJ=function(e,t,r){var n=t,i=n;return function(t){var o=Math.round(e());return o!==n&&o!==i&&Math.abs(o-n)>3&&Math.abs(o-i)>3&&(t=o,r&&r()),i=n,n=Math.round(t)}},tQ=function(e,t,r){var n={};n[t.p]="+="+r,W.set(e,n)},t0=function(e,t){var r=B(e,t),n="_scroll"+t.p2,i=function t(i,o,a,s,l){var c=t.tween,u=o.onComplete,f={};a=a||r();var p=tJ(r,a,function(){c.kill(),t.tween=0});return l=s&&l||0,s=s||i-a,c&&c.kill(),o[n]=i,o.inherit=!1,o.modifiers=f,f[n]=function(){return p(a+s*c.ratio+l*c.ratio*c.ratio)},o.onUpdate=function(){x.cache++,t.tween&&tz()},o.onComplete=function(){t.tween=0,u&&u.call(c)},c=t.tween=W.to(e,o)};return e[n]=r,r.wheelHandler=function(){return i.tween&&i.tween.kill()&&(i.tween=0)},tc(e,"wheel",r.wheelHandler),t1.isTouch&&tc(e,"touchmove",r.wheelHandler),i},t1=function(){function e(t,r){q||e.register(W)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),ev(this),this.init(t,r)}return e.prototype.init=function(t,r){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!eA){this.update=this.refresh=this.kill=eB;return}var n,i,o,a,s,l,c,u,f,p,d,h,g,v,m,y,_,w,k,T,E,S,P,M,O,A,I,N,X,z,F,H,L,q,K,Z,ee,en,eo,ea,es,eu=t=tn(eV(t)||ej(t)||t.nodeType?{trigger:t}:t,td),ef=eu.onUpdate,ep=eu.toggleClass,ed=eu.id,eh=eu.onToggle,eg=eu.onRefresh,ev=eu.scrub,em=eu.trigger,ey=eu.pin,ex=eu.pinSpacing,eb=eu.invalidateOnRefresh,ek=eu.anticipatePin,eT=eu.onScrubComplete,eM=eu.onSnapComplete,eY=eu.once,eI=eu.snap,eX=eu.pinReparent,ez=eu.pinSpacer,eH=eu.containerAnimation,eU=eu.fastScrollEnd,eQ=eu.preventOverlaps,e0=t.horizontal||t.containerAnimation&&!1!==t.horizontal?R:D,e1=!ev&&0!==ev,tl=Y(t.scroller||U),tf=W.core.getCache(tl),th=eF(tl),tm=("pinType"in t?t.pinType:C(tl,"pinType")||th&&"fixed")==="fixed",tb=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],tw=e1&&t.toggleActions.split(" "),tC="markers"in t?t.markers:td.markers,tT=th?0:parseFloat(tt(tl)["border"+e0.p2+e7])||0,tS=this,tP=t.onRefreshInit&&function(){return t.onRefreshInit(tS)},tM=eW(tl,th,e0),tO=!th||~b.indexOf(tl)?eL(tl):function(){return tj},tA=0,tR=0,tY=0,tI=B(tl,e0);if(tS._startClamp=tS._endClamp=!1,tS._dir=e0,ek*=45,tS.scroller=tl,tS.scroll=eH?eH.time.bind(eH):tI,l=tI(),tS.vars=t,r=r||t.animation,"refreshPriority"in t&&(el=1,-9999===t.refreshPriority&&(eE=tS)),tf.tweenScroll=tf.tweenScroll||{top:t0(tl,D),left:t0(tl,R)},tS.tweenTo=o=tf.tweenScroll[e0.p],tS.scrubDuration=function(e){(K=ej(e)&&e)?q?q.duration(e):q=W.to(r,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:K,paused:!0,onComplete:function(){return eT&&eT(tS)}}):(q&&q.progress(1).kill(),q=0)},r&&(r.vars.lazy=!1,r._initted&&!tS.isReverted||!1!==r.vars.immediateRender&&!1!==t.immediateRender&&r.duration()&&r.render(0,!0,!0),tS.animation=r.pause(),r.scrollTrigger=tS,tS.scrubDuration(ev),H=0,ed||(ed=r.vars.id)),eI&&((!eK(eI)||eI.push)&&(eI={snapTo:eI}),"scrollBehavior"in j.style&&W.set(th?[j,G]:tl,{scrollBehavior:"auto"}),x.forEach(function(e){return eG(e)&&e.target===(th?V.scrollingElement||G:tl)&&(e.smooth=!1)}),s=eG(eI.snapTo)?eI.snapTo:"labels"===eI.snapTo?(n=r,function(e){return W.utils.snap(ta(n),e)}):"labelsDirectional"===eI.snapTo?(i=r,function(e,t){return ts(ta(i))(e,t.direction)}):!1!==eI.directional?function(e,t){return ts(eI.snapTo)(e,eP()-tR<500?0:t.direction)}:W.utils.snap(eI.snapTo),Z=eK(Z=eI.duration||{min:.1,max:2})?J(Z.min,Z.max):J(Z,Z),ee=W.delayedCall(eI.delay||K/2||.1,function(){var e=tI(),t=eP()-tR<500,n=o.tween;if((t||10>Math.abs(tS.getVelocity()))&&!n&&!er&&tA!==e){var i,a,l=(e-u)/y,c=r&&!e1?r.totalProgress():l,p=t?0:(c-L)/(eP()-Q)*1e3||0,d=W.utils.clamp(-l,1-l,eJ(p/2)*p/.185),h=l+(!1===eI.inertia?0:d),g=eI,v=g.onStart,m=g.onInterrupt,x=g.onComplete;if(ej(i=s(h,tS))||(i=h),a=Math.max(0,Math.round(u+i*y)),e<=f&&e>=u&&a!==e){if(n&&!n._initted&&n.data<=eJ(a-e))return;!1===eI.inertia&&(d=i-l),o(a,{duration:Z(eJ(.185*Math.max(eJ(h-c),eJ(i-c))/p/.05||0)),ease:eI.ease||"power3",data:eJ(a-e),onInterrupt:function(){return ee.restart(!0)&&m&&m(tS)},onComplete:function(){tS.update(),tA=tI(),r&&!e1&&(q?q.resetTo("totalProgress",i,r._tTime/r._tDur):r.progress(i)),H=L=r&&!e1?r.totalProgress():tS.progress,eM&&eM(tS),x&&x(tS)}},e,d*y,a-e-d*y),v&&v(tS,o.tween)}}else tS.isActive&&tA!==e&&ee.restart(!0)}).pause()),ed&&(tx[ed]=tS),(es=(em=tS.trigger=Y(em||!0!==ey&&ey))&&em._gsap&&em._gsap.stRevert)&&(es=es(tS)),ey=!0===ey?em:Y(ey),eV(ep)&&(ep={targets:em,className:ep}),ey&&(!1===ex||ex===e4||(ex=(!!ex||!ey.parentNode||!ey.parentNode.style||"flex"!==tt(ey.parentNode).display)&&e8),tS.pin=ey,(a=W.core.getCache(ey)).spacer?_=a.pinState:(ez&&((ez=Y(ez))&&!ez.nodeType&&(ez=ez.current||ez.nativeElement),a.spacerIsNative=!!ez,ez&&(a.spacerState=tV(ez))),a.spacer=T=ez||V.createElement("div"),T.classList.add("pin-spacer"),ed&&T.classList.add("pin-spacer-"+ed),a.pinState=_=tV(ey)),!1!==t.force3D&&W.set(ey,{force3D:!0}),tS.spacer=T=a.spacer,A=(F=tt(ey))[ex+e0.os2],S=W.getProperty(ey),P=W.quickSetter(ey,e0.a,"px"),tW(ey,T,F),k=tV(ey)),tC){v=eK(tC)?tn(tC,tp):tp,h=tv("scroller-start",ed,tl,e0,v,0),g=tv("scroller-end",ed,tl,e0,v,0,h),E=h["offset"+e0.op.d2];var tB=Y(C(tl,"content")||tl);p=this.markerStart=tv("start",ed,tB,e0,v,E,0,eH),d=this.markerEnd=tv("end",ed,tB,e0,v,E,0,eH),eH&&(ea=W.quickSetter([p,d],e0.a,"px")),tm||b.length&&!0===C(tl,"fixedMarkers")||(tr(th?j:tl),W.set([h,g],{force3D:!0}),N=W.quickSetter(h,e0.a,"px"),z=W.quickSetter(g,e0.a,"px"))}if(eH){var tN=eH.vars.onUpdate,tz=eH.vars.onUpdateParams;eH.eventCallback("onUpdate",function(){tS.update(0,0,1),tN&&tN.apply(eH,tz||[])})}if(tS.previous=function(){return ty[ty.indexOf(tS)-1]},tS.next=function(){return ty[ty.indexOf(tS)+1]},tS.revert=function(e,t){if(!t)return tS.kill(!0);var n=!1!==e||!tS.enabled,i=et;n!==tS.isReverted&&(n&&(en=Math.max(tI(),tS.scroll.rec||0),tY=tS.progress,eo=r&&r.progress()),p&&[p,d,h,g].forEach(function(e){return e.style.display=n?"none":"block"}),n&&(et=tS,tS.update(n)),!ey||eX&&tS.isActive||(n?tL(ey,T,_):tW(ey,T,tt(ey),I)),n||tS.update(n),et=i,tS.isReverted=n)},tS.refresh=function(n,i,a,s){if(!et&&tS.enabled||i){if(ey&&n&&eO)return void tc(e,"scrollEnd",tE);!eC&&tP&&tP(tS),et=tS,o.tween&&!a&&(o.tween.kill(),o.tween=0),q&&q.pause(),eb&&r&&(r.revert({kill:!1}).invalidate(),r.getChildren&&r.getChildren(!0,!0,!1).forEach(function(e){return e.vars.immediateRender&&e.render(0,!0,!0)})),tS.isReverted||tS.revert(!0,!0),tS._subPinOffset=!1;var v,x,b,C,E,P,A,N,z,F,H,L,U,K=tM(),Z=tO(),$=eH?eH.duration():eq(tl,e0),J=y<=.01||!y,Q=0,er=s||0,ei=eK(a)?a.end:t.end,ea=t.endTrigger||em,es=eK(a)?a.start:t.start||(0!==t.start&&em?ey?"0 0":"0 100%":0),el=tS.pinnedContainer=t.pinnedContainer&&Y(t.pinnedContainer,tS),eu=em&&Math.max(0,ty.indexOf(tS))||0,ef=eu;for(tC&&eK(a)&&(L=W.getProperty(h,e0.p),U=W.getProperty(g,e0.p));ef-- >0;)(P=ty[ef]).end||P.refresh(0,1)||(et=tS),(A=P.pin)&&(A===em||A===ey||A===el)&&!P.isReverted&&(F||(F=[]),F.unshift(P),P.revert(!0,!0)),P!==ty[ef]&&(eu--,ef--);for(eG(es)&&(es=es(tS)),u=tK(es=eR(es,"start",tS),em,K,e0,tI(),p,h,tS,Z,tT,tm,$,eH,tS._startClamp&&"_startClamp")||(ey?-.001:0),eG(ei)&&(ei=ei(tS)),eV(ei)&&!ei.indexOf("+=")&&(~ei.indexOf(" ")?ei=(eV(es)?es.split(" ")[0]:"")+ei:(Q=tg(ei.substr(2),K),ei=eV(es)?es:(eH?W.utils.mapRange(0,eH.duration(),eH.scrollTrigger.start,eH.scrollTrigger.end,u):u)+Q,ea=em)),ei=eR(ei,"end",tS),f=Math.max(u,tK(ei||(ea?"100% 0":$),ea,K,e0,tI()+Q,d,g,tS,Z,tT,tm,$,eH,tS._endClamp&&"_endClamp"))||-.001,Q=0,ef=eu;ef--;)(A=(P=ty[ef]).pin)&&P.start-P._pinPush<=u&&!eH&&P.end>0&&(v=P.end-(tS._startClamp?Math.max(0,P.start):P.start),(A===em&&P.start-P._pinPush<u||A===el)&&isNaN(es)&&(Q+=v*(1-P.progress)),A===ey&&(er+=v));if(u+=Q,f+=Q,tS._startClamp&&(tS._startClamp+=Q),tS._endClamp&&!eC&&(tS._endClamp=f||-.001,f=Math.min(f,eq(tl,e0))),y=f-u||(u-=.01)&&.001,J&&(tY=W.utils.clamp(0,1,W.utils.normalize(u,f,en))),tS._pinPush=er,p&&Q&&((v={})[e0.a]="+="+Q,el&&(v[e0.p]="-="+tI()),W.set([p,d],v)),ey&&!(e_&&tS.end>=eq(tl,e0)))v=tt(ey),C=e0===D,b=tI(),M=parseFloat(S(e0.a))+er,!$&&f>1&&(H={style:H=(th?V.scrollingElement||G:tl).style,value:H["overflow"+e0.a.toUpperCase()]},th&&"scroll"!==tt(j)["overflow"+e0.a.toUpperCase()]&&(H.style["overflow"+e0.a.toUpperCase()]="scroll")),tW(ey,T,v),k=tV(ey),x=ti(ey,!0),N=tm&&B(tl,C?R:D)(),ex?((I=[ex+e0.os2,y+er+"px"]).t=T,(ef=ex===e8?to(ey,e0)+y+er:0)&&(I.push(e0.d,ef+"px"),"auto"!==T.style.flexBasis&&(T.style.flexBasis=ef+"px")),tU(I),el&&ty.forEach(function(e){e.pin===el&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)}),tm&&tI(en)):(ef=to(ey,e0))&&"auto"!==T.style.flexBasis&&(T.style.flexBasis=ef+"px"),tm&&((E={top:x.top+(C?b-u:N)+"px",left:x.left+(C?N:b-u)+"px",boxSizing:"border-box",position:"fixed"})[e2]=E["max"+e7]=Math.ceil(x.width)+"px",E[e3]=E["max"+te]=Math.ceil(x.height)+"px",E[e4]=E[e4+"Top"]=E[e4+e5]=E[e4+e6]=E[e4+e9]="0",E[e8]=v[e8],E[e8+"Top"]=v[e8+"Top"],E[e8+e5]=v[e8+e5],E[e8+e6]=v[e8+e6],E[e8+e9]=v[e8+e9],w=tG(_,E,eX),eC&&tI(0)),r?(z=r._initted,ec(1),r.render(r.duration(),!0,!0),O=S(e0.a)-M+y+er,X=Math.abs(y-O)>1,tm&&X&&w.splice(w.length-2,2),r.render(0,!0,!0),z||r.invalidate(!0),r.parent||r.totalTime(r.totalTime()),ec(0)):O=y,H&&(H.value?H.style["overflow"+e0.a.toUpperCase()]=H.value:H.style.removeProperty("overflow-"+e0.a));else if(em&&tI()&&!eH)for(x=em.parentNode;x&&x!==j;)x._pinOffset&&(u-=x._pinOffset,f-=x._pinOffset),x=x.parentNode;F&&F.forEach(function(e){return e.revert(!1,!0)}),tS.start=u,tS.end=f,l=c=eC?en:tI(),eH||eC||(l<en&&tI(en),tS.scroll.rec=0),tS.revert(!1,!0),tR=eP(),ee&&(tA=-1,ee.restart(!0)),et=0,r&&e1&&(r._initted||eo)&&r.progress()!==eo&&r.progress(eo||0,!0).render(r.time(),!0,!0),(J||tY!==tS.progress||eH||eb||r&&!r._initted)&&(r&&!e1&&(r._initted||tY||!1!==r.vars.immediateRender)&&r.totalProgress(eH&&u<-.001&&!tY?W.utils.normalize(u,f,0):tY,!0),tS.progress=J||(l-u)/y===tY?0:tY),ey&&ex&&(T._pinOffset=Math.round(tS.progress*O)),q&&q.invalidate(),isNaN(L)||(L-=W.getProperty(h,e0.p),U-=W.getProperty(g,e0.p),tQ(h,e0,L),tQ(p,e0,L-(s||0)),tQ(g,e0,U),tQ(d,e0,U-(s||0))),J&&!eC&&tS.update(),!eg||eC||m||(m=!0,eg(tS),m=!1)}},tS.getVelocity=function(){return(tI()-c)/(eP()-Q)*1e3||0},tS.endAnimation=function(){eZ(tS.callbackAnimation),r&&(q?q.progress(1):r.paused()?e1||eZ(r,tS.direction<0,1):eZ(r,r.reversed()))},tS.labelToScroll=function(e){return r&&r.labels&&(u||tS.refresh()||u)+r.labels[e]/r.duration()*y||0},tS.getTrailing=function(e){var t=ty.indexOf(tS),r=tS.direction>0?ty.slice(0,t).reverse():ty.slice(t+1);return(eV(e)?r.filter(function(t){return t.vars.preventOverlaps===e}):r).filter(function(e){return tS.direction>0?e.end<=u:e.start>=f})},tS.update=function(e,t,n){if(!eH||n||e){var i,a,s,p,d,g,v,m=!0===eC?en:tS.scroll(),x=e?0:(m-u)/y,b=x<0?0:x>1?1:x||0,_=tS.progress;if(t&&(c=l,l=eH?tI():m,eI&&(L=H,H=r&&!e1?r.totalProgress():b)),ek&&ey&&!et&&!eS&&eO&&(!b&&u<m+(m-c)/(eP()-Q)*ek?b=1e-4:1===b&&f>m+(m-c)/(eP()-Q)*ek&&(b=.9999)),b!==_&&tS.enabled){if(p=(d=(i=tS.isActive=!!b&&b<1)!=(!!_&&_<1))||!!b!=!!_,tS.direction=b>_?1:-1,tS.progress=b,p&&!et&&(a=b&&!_?0:1===b?1:1===_?2:3,e1&&(s=!d&&"none"!==tw[a+1]&&tw[a+1]||tw[a],v=r&&("complete"===s||"reset"===s||s in r))),eQ&&(d||v)&&(v||ev||!r)&&(eG(eQ)?eQ(tS):tS.getTrailing(eQ).forEach(function(e){return e.endAnimation()})),!e1&&(!q||et||eS?r&&r.totalProgress(b,!!(et&&(tR||e))):(q._dp._time-q._start!==q._time&&q.render(q._dp._time-q._start),q.resetTo?q.resetTo("totalProgress",b,r._tTime/r._tDur):(q.vars.totalProgress=b,q.invalidate().restart()))),ey)if(e&&ex&&(T.style[ex+e0.os2]=A),tm){if(p){if(g=!e&&b>_&&f+1>m&&m+1>=eq(tl,e0),eX)if(!e&&(i||g)){var C=ti(ey,!0),E=m-u;t$(ey,j,C.top+(e0===D?E:0)+"px",C.left+(e0===D?0:E)+"px")}else t$(ey,T);tU(i||g?w:k),X&&b<1&&i||P(M+(1!==b||g?0:O))}}else P(eN(M+O*b));!eI||o.tween||et||eS||ee.restart(!0),ep&&(d||eY&&b&&(b<1||!ew))&&$(ep.targets).forEach(function(e){return e.classList[i||eY?"add":"remove"](ep.className)}),!ef||e1||e||ef(tS),p&&!et?(e1&&(v&&("complete"===s?r.pause().totalProgress(1):"reset"===s?r.restart(!0).pause():"restart"===s?r.restart(!0):r[s]()),ef&&ef(tS)),(d||!ew)&&(eh&&d&&e$(tS,eh),tb[a]&&e$(tS,tb[a]),eY&&(1===b?tS.kill(!1,1):tb[a]=0),!d&&tb[a=1===b?1:3]&&e$(tS,tb[a])),eU&&!i&&Math.abs(tS.getVelocity())>(ej(eU)?eU:2500)&&(eZ(tS.callbackAnimation),q?q.progress(1):eZ(r,"reverse"===s?1:!b,1))):e1&&ef&&!et&&ef(tS)}if(z){var S=eH?m/eH.duration()*(eH._caScrollDist||0):m;N(S+ +!!h._isFlipped),z(S)}ea&&ea(-m/eH.duration()*(eH._caScrollDist||0))}},tS.enable=function(t,r){tS.enabled||(tS.enabled=!0,tc(tl,"resize",tk),th||tc(tl,"scroll",t_),tP&&tc(e,"refreshInit",tP),!1!==t&&(tS.progress=tY=0,l=c=tA=tI()),!1!==r&&tS.refresh())},tS.getTween=function(e){return e&&o?o.tween:q},tS.setPositions=function(e,t,r,n){if(eH){var i=eH.scrollTrigger,o=eH.duration(),a=i.end-i.start;e=i.start+a*e/o,t=i.start+a*t/o}tS.refresh(!1,!1,{start:eD(e,r&&!!tS._startClamp),end:eD(t,r&&!!tS._endClamp)},n),tS.update()},tS.adjustPinSpacing=function(e){if(I&&e){var t=I.indexOf(e0.d)+1;I[t]=parseFloat(I[t])+e+"px",I[1]=parseFloat(I[1])+e+"px",tU(I)}},tS.disable=function(t,r){if(tS.enabled&&(!1!==t&&tS.revert(!0,!0),tS.enabled=tS.isActive=!1,r||q&&q.pause(),en=0,a&&(a.uncache=1),tP&&tu(e,"refreshInit",tP),ee&&(ee.pause(),o.tween&&o.tween.kill()&&(o.tween=0)),!th)){for(var n=ty.length;n--;)if(ty[n].scroller===tl&&ty[n]!==tS)return;tu(tl,"resize",tk),th||tu(tl,"scroll",t_)}},tS.kill=function(e,n){tS.disable(e,n),q&&!n&&q.kill(),ed&&delete tx[ed];var i=ty.indexOf(tS);i>=0&&ty.splice(i,1),i===ei&&tX>0&&ei--,i=0,ty.forEach(function(e){return e.scroller===tS.scroller&&(i=1)}),i||eC||(tS.scroll.rec=0),r&&(r.scrollTrigger=null,e&&r.revert({kill:!1}),n||r.kill()),p&&[p,d,h,g].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),eE===tS&&(eE=0),ey&&(a&&(a.uncache=1),i=0,ty.forEach(function(e){return e.pin===ey&&i++}),i||(a.spacer=0)),t.onKill&&t.onKill(tS)},ty.push(tS),tS.enable(!1,!1),es&&es(tS),r&&r.add&&!y){var tF=tS.update;tS.update=function(){tS.update=tF,x.cache++,u||f||tS.refresh()},W.delayedCall(.01,tS.update),y=.01,u=f=0}else tS.refresh();ey&&tD()},e.register=function(t){return q||(W=t||ez(),eX()&&window.document&&e.enable(),q=eA),q},e.defaults=function(e){if(e)for(var t in e)td[t]=e[t];return td},e.disable=function(e,t){eA=0,ty.forEach(function(r){return r[t?"kill":"disable"](e)}),tu(U,"wheel",t_),tu(V,"scroll",t_),clearInterval(ee),tu(V,"touchcancel",eB),tu(j,"touchstart",eB),tl(tu,V,"pointerdown,touchstart,mousedown",eY),tl(tu,V,"pointerup,touchend,mouseup",eI),Z.kill(),eU(tu);for(var r=0;r<x.length;r+=3)tf(tu,x[r],x[r+1]),tf(tu,x[r],x[r+2])},e.enable=function(){if(U=window,G=(V=document).documentElement,j=V.body,W&&($=W.utils.toArray,J=W.utils.clamp,ev=W.core.context||eB,ec=W.core.suppressOverwrites||eB,em=U.history.scrollRestoration||"auto",tN=U.pageYOffset||0,W.core.globals("ScrollTrigger",e),j)){eA=1,(ey=document.createElement("div")).style.height="100vh",ey.style.position="absolute",tY(),function e(){return eA&&requestAnimationFrame(e)}(),L.register(W),e.isTouch=L.isTouch,eg=L.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),ep=1===L.isTouch,tc(U,"wheel",t_),K=[U,V,G,j],W.matchMedia?(e.matchMedia=function(e){var t,r=W.matchMedia();for(t in e)r.add(t,e[t]);return r},W.addEventListener("matchMediaInit",function(){return tO()}),W.addEventListener("matchMediaRevert",function(){return tM()}),W.addEventListener("matchMedia",function(){tB(0,1),tS("matchMedia")}),W.matchMedia().add("(orientation: portrait)",function(){return tw(),tw})):console.warn("Requires GSAP 3.11.0 or later"),tw(),tc(V,"scroll",t_);var t,r,n=j.hasAttribute("style"),i=j.style,o=i.borderTopStyle,a=W.core.Animation.prototype;for(a.revert||Object.defineProperty(a,"revert",{value:function(){return this.time(-.01,!0)}}),i.borderTopStyle="solid",D.m=Math.round((t=ti(j)).top+D.sc())||0,R.m=Math.round(t.left+R.sc())||0,o?i.borderTopStyle=o:i.removeProperty("border-top-style"),n||(j.setAttribute("style",""),j.removeAttribute("style")),ee=setInterval(tb,250),W.delayedCall(.5,function(){return eS=0}),tc(V,"touchcancel",eB),tc(j,"touchstart",eB),tl(tc,V,"pointerdown,touchstart,mousedown",eY),tl(tc,V,"pointerup,touchend,mouseup",eI),en=W.utils.checkPrefix("transform"),tH.push(en),q=eP(),Z=W.delayedCall(.2,tB).pause(),es=[V,"visibilitychange",function(){var e=U.innerWidth,t=U.innerHeight;V.hidden?(eo=e,ea=t):(eo!==e||ea!==t)&&tk()},V,"DOMContentLoaded",tB,U,"load",tB,U,"resize",tk],eU(tc),ty.forEach(function(e){return e.enable(0,1)}),r=0;r<x.length;r+=3)tf(tu,x[r],x[r+1]),tf(tu,x[r],x[r+2])}},e.config=function(t){"limitCallbacks"in t&&(ew=!!t.limitCallbacks);var r=t.syncInterval;r&&clearInterval(ee)||(ee=r)&&setInterval(tb,r),"ignoreMobileResize"in t&&(ep=1===e.isTouch&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(eU(tu)||eU(tc,t.autoRefreshEvents||"none"),eu=-1===(t.autoRefreshEvents+"").indexOf("resize"))},e.scrollerProxy=function(e,t){var r=Y(e),n=x.indexOf(r),i=eF(r);~n&&x.splice(n,i?6:2),t&&(i?b.unshift(U,t,j,t,G,t):b.unshift(r,t))},e.clearMatchMedia=function(e){ty.forEach(function(t){return t._ctx&&t._ctx.query===e&&t._ctx.kill(!0,!0)})},e.isInViewport=function(e,t,r){var n=(eV(e)?Y(e):e).getBoundingClientRect(),i=n[r?e2:e3]*t||0;return r?n.right-i>0&&n.left+i<U.innerWidth:n.bottom-i>0&&n.top+i<U.innerHeight},e.positionInViewport=function(e,t,r){eV(e)&&(e=Y(e));var n=e.getBoundingClientRect(),i=n[r?e2:e3],o=null==t?i/2:t in th?th[t]*i:~t.indexOf("%")?parseFloat(t)*i/100:parseFloat(t)||0;return r?(n.left+o)/U.innerWidth:(n.top+o)/U.innerHeight},e.killAll=function(e){if(ty.slice(0).forEach(function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()}),!0!==e){var t=tC.killAll||[];tC={},t.forEach(function(e){return e()})}},e}();t1.version="3.13.0",t1.saveStyles=function(e){return e?$(e).forEach(function(e){if(e&&e.style){var t=tP.indexOf(e);t>=0&&tP.splice(t,5),tP.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),W.core.getCache(e),ev())}}):tP},t1.revert=function(e,t){return tO(!e,t)},t1.create=function(e,t){return new t1(e,t)},t1.refresh=function(e){return e?tk(!0):(q||t1.register())&&tB(!0)},t1.update=function(e){return++x.cache&&tz(2*(!0===e))},t1.clearScrollMemory=tA,t1.maxScroll=function(e,t){return eq(e,t?R:D)},t1.getScrollFunc=function(e,t){return B(Y(e),t?R:D)},t1.getById=function(e){return tx[e]},t1.getAll=function(){return ty.filter(function(e){return"ScrollSmoother"!==e.vars.id})},t1.isScrolling=function(){return!!eO},t1.snapDirectional=ts,t1.addEventListener=function(e,t){var r=tC[e]||(tC[e]=[]);~r.indexOf(t)||r.push(t)},t1.removeEventListener=function(e,t){var r=tC[e],n=r&&r.indexOf(t);n>=0&&r.splice(n,1)},t1.batch=function(e,t){var r,n=[],i={},o=t.interval||.016,a=t.batchMax||1e9,s=function(e,t){var r=[],n=[],i=W.delayedCall(o,function(){t(r,n),r=[],n=[]}).pause();return function(e){r.length||i.restart(!0),r.push(e.trigger),n.push(e),a<=r.length&&i.progress(1)}};for(r in t)i[r]="on"===r.substr(0,2)&&eG(t[r])&&"onRefreshInit"!==r?s(r,t[r]):t[r];return eG(a)&&(a=a(),tc(t1,"refresh",function(){return a=t.batchMax()})),$(e).forEach(function(e){var t={};for(r in i)t[r]=i[r];t.trigger=e,n.push(t1.create(t))}),n};var t2,t3=function(e,t,r,n){return t>n?e(n):t<0&&e(0),r>n?(n-t)/(r-t):r<0?t/(t-r):1},t5=function e(t,r){!0===r?t.style.removeProperty("touch-action"):t.style.touchAction=!0===r?"auto":r?"pan-"+r+(L.isTouch?" pinch-zoom":""):"none",t===G&&e(j,r)},t9={auto:1,scroll:1},t6=function(e){var t,r=e.event,n=e.target,i=e.axis,o=(r.changedTouches?r.changedTouches[0]:r).target,a=o._gsap||W.core.getCache(o),s=eP();if(!a._isScrollT||s-a._isScrollT>2e3){for(;o&&o!==j&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!(t9[(t=tt(o)).overflowY]||t9[t.overflowX]));)o=o.parentNode;a._isScroll=o&&o!==n&&!eF(o)&&(t9[(t=tt(o)).overflowY]||t9[t.overflowX]),a._isScrollT=s}(a._isScroll||"x"===i)&&(r.stopPropagation(),r._gsapAllow=!0)},t8=function(e,t,r,n){return L.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:n=n&&t6,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return r&&tc(V,L.eventTypes[0],t7,!1,!0)},onDisable:function(){return tu(V,L.eventTypes[0],t7,!0)}})},t4=/(input|label|select|textarea)/i,t7=function(e){var t=t4.test(e.target.tagName);(t||t2)&&(e._gsapAllow=!0,t2=t)},re=function(e){eK(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var t,r,n,i,o,a,s,l,c=e,u=c.normalizeScrollX,f=c.momentum,p=c.allowNestedScroll,d=c.onRelease,h=Y(e.target)||G,g=W.core.globals().ScrollSmoother,v=g&&g.get(),m=eg&&(e.content&&Y(e.content)||v&&!1!==e.content&&!v.smooth()&&v.content()),y=B(h,D),b=B(h,R),_=1,w=(L.isTouch&&U.visualViewport?U.visualViewport.scale*U.visualViewport.width:U.outerWidth)/U.innerWidth,k=0,C=eG(f)?function(){return f(t)}:function(){return f||2.8},T=t8(h,e.type,!0,p),E=function(){return i=!1},S=eB,P=eB,M=function(){r=eq(h,D),P=J(+!!eg,r),u&&(S=J(0,eq(h,R))),n=tR},O=function(){m._gsap.y=eN(parseFloat(m._gsap.y)+y.offset)+"px",m.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(m._gsap.y)+", 0, 1)",y.offset=y.cacheID=0},A=function(){if(i){requestAnimationFrame(E);var e=eN(t.deltaY/2),r=P(y.v-e);if(m&&r!==y.v+y.offset){y.offset=r-y.v;var n=eN((parseFloat(m&&m._gsap.y)||0)-y.offset);m.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+n+", 0, 1)",m._gsap.y=n+"px",y.cacheID=x.cache,tz()}return!0}y.offset&&O(),i=!0},I=function(){M(),o.isActive()&&o.vars.scrollY>r&&(y()>r?o.progress(1)&&y(r):o.resetTo("scrollY",r))};return m&&W.set(m,{y:"+=0"}),e.ignoreCheck=function(e){return eg&&"touchmove"===e.type&&A(e)||_>1.05&&"touchstart"!==e.type||t.isGesturing||e.touches&&e.touches.length>1},e.onPress=function(){i=!1;var e=_;_=eN((U.visualViewport&&U.visualViewport.scale||1)/w),o.pause(),e!==_&&t5(h,_>1.01||!u&&"x"),a=b(),s=y(),M(),n=tR},e.onRelease=e.onGestureStart=function(e,t){if(y.offset&&O(),t){x.cache++;var n,i,a=C();u&&(i=(n=b())+-(.05*a*e.velocityX)/.227,a*=t3(b,n,i,eq(h,R)),o.vars.scrollX=S(i)),i=(n=y())+-(.05*a*e.velocityY)/.227,a*=t3(y,n,i,eq(h,D)),o.vars.scrollY=P(i),o.invalidate().duration(a).play(.01),(eg&&o.vars.scrollY>=r||n>=r-1)&&W.to({},{onUpdate:I,duration:a})}else l.restart(!0);d&&d(e)},e.onWheel=function(){o._ts&&o.pause(),eP()-k>1e3&&(n=0,k=eP())},e.onChange=function(e,t,r,i,o){if(tR!==n&&M(),t&&u&&b(S(i[2]===t?a+(e.startX-e.x):b()+t-i[1])),r){y.offset&&O();var l=o[2]===r,c=l?s+e.startY-e.y:y()+r-o[1],f=P(c);l&&c!==f&&(s+=f-c),y(f)}(r||t)&&tz()},e.onEnable=function(){t5(h,!u&&"x"),t1.addEventListener("refresh",I),tc(U,"resize",I),y.smooth&&(y.target.style.scrollBehavior="auto",y.smooth=b.smooth=!1),T.enable()},e.onDisable=function(){t5(h,!0),tu(U,"resize",I),t1.removeEventListener("refresh",I),T.kill()},e.lockAxis=!1!==e.lockAxis,(t=new L(e)).iOS=eg,eg&&!y()&&y(1),eg&&W.ticker.add(eB),l=t._dc,o=W.to(t,{ease:"power4",paused:!0,inherit:!1,scrollX:u?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:tJ(y,y(),function(){return o.pause()})},onUpdate:tz,onComplete:l.vars.onComplete}),t};t1.sort=function(e){if(eG(e))return ty.sort(e);var t=U.pageYOffset||0;return t1.getAll().forEach(function(e){return e._sortY=e.trigger?t+e.trigger.getBoundingClientRect().top:e.start+U.innerHeight}),ty.sort(e||function(e,t){return -1e6*(e.vars.refreshPriority||0)+(e.vars.containerAnimation?1e6:e._sortY)-((t.vars.containerAnimation?1e6:t._sortY)+-1e6*(t.vars.refreshPriority||0))})},t1.observe=function(e){return new L(e)},t1.normalizeScroll=function(e){if(void 0===e)return ef;if(!0===e&&ef)return ef.enable();if(!1===e){ef&&ef.kill(),ef=e;return}var t=e instanceof L?e:re(e);return ef&&ef.target===t.target&&ef.kill(),eF(t.target)&&(ef=t),t},t1.core={_getVelocityProp:N,_inputObserver:t8,_scrollers:x,_proxies:b,bridge:{ss:function(){eO||tS("scrollStart"),eO=eP()},ref:function(){return et}}},ez()&&W.registerPlugin(t1)}}]);