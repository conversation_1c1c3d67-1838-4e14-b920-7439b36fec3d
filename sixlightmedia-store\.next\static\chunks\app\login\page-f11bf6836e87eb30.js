(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{3843:(e,r,s)=>{"use strict";s.d(r,{e9:()=>i,i3:()=>t});var a=s(9509);let t={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};a.env.IMAGEKIT_PRIVATE_KEY,a.env.NEXT_PUBLIC_GA_ID,a.env.NEXT_PUBLIC_GA_ID;let i=e=>{let r=t.BASE_URL.replace(/\/$/,""),s=e.startsWith("/")?e:"/".concat(e);return"".concat(r).concat(s)}},5905:(e,r,s)=>{Promise.resolve().then(s.bind(s,9690))},9690:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var a=s(5155),t=s(2115),i=s(9958),o=s(6874),n=s.n(o),l=s(6766),d=s(1153),c=s(5339),u=s(8749),p=s(2657);let m=d.z.object({email:d.z.string().email("Please enter a valid email address"),password:d.z.string().min(1,"Password is required")});function h(){let[e,r]=(0,t.useState)({email:"",password:""}),[s,o]=(0,t.useState)({}),[d,h]=(0,t.useState)(""),[g,x]=(0,t.useState)(!1),[f,b]=(0,t.useState)(!1),E=(e,a)=>{r(r=>({...r,[e]:a})),s[e]&&o(r=>({...r,[e]:void 0}))};async function w(r){r.preventDefault(),x(!0),h(""),o({});let s=m.safeParse(e);if(!s.success){x(!1);let e={};s.error.errors.forEach(r=>{r.path[0]&&(e[r.path[0]]=r.message)}),o(e);return}try{let r=await (0,i.i)(e.email,e.password);x(!1),r.access_token?(localStorage.setItem("token",r.access_token),window.location.href="/user/dashboard"):h(r.error||"Login failed")}catch(e){x(!1),h("Network error. Please try again.")}}return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100",children:[(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,a.jsx)("h2",{className:"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Sign In"}),(0,a.jsxs)("form",{onSubmit:w,className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("input",{value:e.email,onChange:e=>E("email",e.target.value),placeholder:"Email",type:"email",className:"w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition ".concat(s.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),autoComplete:"email","aria-invalid":!!s.email,"aria-describedby":s.email?"email-error":void 0}),s.email&&(0,a.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,a.jsx)(c.A,{size:16}),s.email]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{value:e.password,onChange:e=>E("password",e.target.value),type:f?"text":"password",placeholder:"Password",className:"w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition ".concat(s.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),autoComplete:"current-password","aria-invalid":!!s.password,"aria-describedby":s.password?"password-error":void 0}),(0,a.jsx)("button",{type:"button",onClick:()=>b(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":f?"Hide password":"Show password",children:f?(0,a.jsx)(u.A,{size:20}):(0,a.jsx)(p.A,{size:20})})]}),s.password&&(0,a.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,a.jsx)(c.A,{size:16}),s.password]})]}),(0,a.jsx)("button",{type:"submit",className:"w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ".concat(g?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"),disabled:g,children:g?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,a.jsx)("span",{children:"Signing in..."})]}):"Sign In"}),d&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,a.jsx)(c.A,{size:16}),d]})]}),(0,a.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,a.jsx)(n(),{href:"/register",className:"text-red-700 font-semibold hover:underline",children:"Register"})]})]})})}},9958:(e,r,s)=>{"use strict";s.d(r,{i:()=>t,k:()=>i});var a=s(3843);async function t(e,r){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})})).json()}async function i(e,r,s){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r,name:s})})).json()}}},e=>{var r=r=>e(e.s=r);e.O(0,[766,874,347,441,684,358],()=>r(5905)),_N_E=e.O()}]);