{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from \"next/font/google\";\nimport <PERSON>rip<PERSON> from \"next/script\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: {\n    default:\n      \"Six Light Media Store - Premium Custom Products & Personalized Gifts\",\n    template: \"%s | Six Light Media Store\",\n  },\n  description:\n    \"Discover premium custom products and personalized gifts at Six Light Media Store. From engraved bottles to custom t-shirts, we create unique items tailored just for you. Fast delivery, premium quality, 100% satisfaction guaranteed.\",\n  keywords: [\n    \"custom products\",\n    \"personalized gifts\",\n    \"engraved bottles\",\n    \"custom t-shirts\",\n    \"Six Light Media\",\n    \"premium quality\",\n    \"custom printing\",\n    \"personalized items\",\n    \"unique gifts\",\n    \"custom design\",\n    \"Zambia\",\n    \"e-commerce\",\n  ],\n  authors: [{ name: \"Six Light Media\" }],\n  creator: \"Six Light Media\",\n  publisher: \"Six Light Media\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL(\n    process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"\n  ),\n  alternates: {\n    canonical: \"/\",\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"/\",\n    title:\n      \"Six Light Media Store - Premium Custom Products & Personalized Gifts\",\n    description:\n      \"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.\",\n    siteName: \"Six Light Media Store\",\n    images: [\n      {\n        url: \"/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"Six Light Media Store - Premium Custom Products\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title:\n      \"Six Light Media Store - Premium Custom Products & Personalized Gifts\",\n    description:\n      \"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.\",\n    images: [\"/og-image.jpg\"],\n    creator: \"@sixlightmedia\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    google: \"your-google-verification-code\", // Replace with actual verification code\n    // yandex: \"your-yandex-verification-code\",\n    // yahoo: \"your-yahoo-verification-code\",\n  },\n  category: \"e-commerce\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning={true}>\n      <head>\n        {/* Additional SEO and Performance Meta Tags */}\n        <meta\n          name=\"viewport\"\n          content=\"width=device-width, initial-scale=1, maximum-scale=5\"\n        />\n        <meta name=\"theme-color\" content=\"#1a237e\" />\n        <meta name=\"msapplication-TileColor\" content=\"#1a237e\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta\n          name=\"apple-mobile-web-app-title\"\n          content=\"Six Light Media Store\"\n        />\n\n        {/* Favicon and App Icons */}\n        <link rel=\"icon\" href=\"/favicon.ico\" sizes=\"any\" />\n        <link rel=\"icon\" href=\"/icon.svg\" type=\"image/svg+xml\" />\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n\n        {/* Preconnect to external domains for performance */}\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link\n          rel=\"preconnect\"\n          href=\"https://fonts.gstatic.com\"\n          crossOrigin=\"anonymous\"\n        />\n\n        {/* DNS Prefetch for better performance */}\n        <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />\n        <link rel=\"dns-prefetch\" href=\"//fonts.gstatic.com\" />\n\n        {/* Structured Data for Rich Snippets */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"Organization\",\n              name: \"Six Light Media Store\",\n              description: \"Premium custom products and personalized gifts\",\n              url: process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n              logo: `${\n                process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"\n              }/6 Light Logo.png`,\n              contactPoint: {\n                \"@type\": \"ContactPoint\",\n                contactType: \"customer service\",\n                availableLanguage: \"English\",\n              },\n              sameAs: [\n                \"https://facebook.com/sixlightmedia\",\n                \"https://twitter.com/sixlightmedia\",\n                \"https://instagram.com/sixlightmedia\",\n              ],\n            }),\n          }}\n        />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        {children}\n\n        {/* Analytics and Performance Monitoring */}\n        {process.env.NODE_ENV === \"production\" &&\n          process.env.NEXT_PUBLIC_GA_ID && (\n            <>\n              {/* Google Analytics - Replace NEXT_PUBLIC_GA_ID with your actual GA4 ID */}\n              <Script\n                src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}\n                strategy=\"afterInteractive\"\n              />\n              <Script id=\"google-analytics\" strategy=\"afterInteractive\">\n                {`\n                window.dataLayer = window.dataLayer || [];\n                function gtag(){dataLayer.push(arguments);}\n                gtag('js', new Date());\n                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');\n              `}\n              </Script>\n            </>\n          )}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;QACL,SACE;QACF,UAAU;IACZ;IACA,aACE;IACF,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;IACtC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAChB,6DAAoC;IAEtC,YAAY;QACV,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OACE;QACF,aACE;QACF,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OACE;QACF,aACE;QACF,QAAQ;YAAC;SAAgB;QACzB,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IAGV;IACA,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,0BAA0B;;0BACxC,8OAAC;;kCAEC,8OAAC;wBACC,MAAK;wBACL,SAAQ;;;;;;kCAEV,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;kCAC7C,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBACC,MAAK;wBACL,SAAQ;;;;;;kCAIV,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,OAAM;;;;;;kCAC3C,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,MAAK;;;;;;kCACvC,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAG1B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,aAAY;;;;;;kCAId,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAG9B,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,MAAM;gCACN,aAAa;gCACb,KAAK,6DAAoC;gCACzC,MAAM,GACJ,6DAAoC,wBACrC,iBAAiB,CAAC;gCACnB,cAAc;oCACZ,SAAS;oCACT,aAAa;oCACb,mBAAmB;gCACrB;gCACA,QAAQ;oCACN;oCACA;oCACA;iCACD;4BACH;wBACF;;;;;;;;;;;;0BAGJ,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;;oBAEnE;oBAGA,oDAAyB,gBACxB,QAAQ,GAAG,CAAC,iBAAiB,kBAC3B;;0CAEE,8OAAC,8HAAA,CAAA,UAAM;gCACL,KAAK,CAAC,4CAA4C,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE;gCACnF,UAAS;;;;;;0CAEX,8OAAC,8HAAA,CAAA,UAAM;gCAAC,IAAG;gCAAmB,UAAS;0CACpC,CAAC;;;;gCAIc,EAAE,QAAQ,GAAG,CAAC,iBAAiB,CAAC;cAClD,CAAC;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/next/dist/client/script.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/next/dist/client/script.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/next/script.js"], "sourcesContent": ["module.exports = require('./dist/client/script')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}