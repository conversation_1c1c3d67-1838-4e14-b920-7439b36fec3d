import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export interface User {
  id: number;
  email: string;
  role: "USER" | "ADMIN";
  name?: string;
  profileImage?: string;
}

export interface AuthResult {
  isAuthenticated: boolean;
  user?: User;
}

/**
 * Verify authentication status by checking JWT token with backend
 */
export async function verifyAuth(): Promise<AuthResult> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("jwt")?.value;

    if (!token) {
      console.log("No JWT token found in cookies");
      return { isAuthenticated: false };
    }

    // Get API URL with fallback
    const apiUrl =
      process.env.NEXT_PUBLIC_API_URL ||
      (process.env.NODE_ENV === "production"
        ? "https://backendapi-sixlight.onrender.com"
        : "http://localhost:3001");

    console.log(`Verifying auth with: ${apiUrl}/auth/verify`);

    // Verify token with backend
    const response = await fetch(`${apiUrl}/auth/verify`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      cache: "no-store", // Always verify fresh
    });

    console.log(`Auth verification response: ${response.status}`);

    if (!response.ok) {
      console.log(
        `Auth verification failed: ${response.status} ${response.statusText}`
      );
      return { isAuthenticated: false };
    }

    const user = await response.json();
    console.log(`Auth verification successful for user: ${user.email}`);

    return {
      isAuthenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        profileImage: user.profileImage,
      },
    };
  } catch (error) {
    console.error("Auth verification failed:", error);
    return { isAuthenticated: false };
  }
}

/**
 * Require authentication - redirect to login if not authenticated
 */
export async function requireAuth(redirectTo = "/login"): Promise<AuthResult> {
  const auth = await verifyAuth();
  if (!auth.isAuthenticated) {
    redirect(redirectTo);
  }
  return auth;
}

/**
 * Require admin role - redirect if not admin
 */
export async function requireAdmin(redirectTo = "/"): Promise<AuthResult> {
  const auth = await requireAuth("/login");
  if (auth.user?.role !== "ADMIN") {
    redirect(redirectTo);
  }
  return auth;
}

/**
 * Get current page URL for redirect after login
 */
export function getCurrentPageUrl(request?: Request): string {
  if (typeof window !== "undefined") {
    return window.location.pathname + window.location.search;
  }

  if (request) {
    const url = new URL(request.url);
    return url.pathname + url.search;
  }

  return "/";
}

/**
 * Create login redirect URL with return path
 */
export function createLoginRedirect(returnPath?: string): string {
  const encodedPath = returnPath ? encodeURIComponent(returnPath) : "";
  return `/login${encodedPath ? `?redirect=${encodedPath}` : ""}`;
}

/**
 * Client-side auth check (for components)
 */
export function getClientAuth(): {
  isAuthenticated: boolean;
  token: string | null;
} {
  if (typeof window === "undefined") {
    return { isAuthenticated: false, token: null };
  }

  const token = localStorage.getItem("token");
  return {
    isAuthenticated: !!token,
    token,
  };
}

/**
 * Client-side logout
 */
export async function logout(): Promise<void> {
  try {
    // Call backend logout endpoint
    await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/logout`, {
      method: "POST",
      credentials: "include",
    });
  } catch (error) {
    console.error("Logout request failed:", error);
  } finally {
    // Clear client-side storage regardless of backend response
    if (typeof window !== "undefined") {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }
  }
}
