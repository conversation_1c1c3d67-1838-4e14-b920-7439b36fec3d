"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("./auth/jwt-auth.guard");
const roles_decorator_1 = require("./auth/roles.decorator");
const roles_guard_1 = require("./auth/roles.guard");
const prisma_service_1 = require("./prisma.service");
let AdminController = class AdminController {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getDashboard() {
        const users = await this.prisma.user.findMany({
            select: { id: true, name: true, email: true, role: true },
            orderBy: { createdAt: 'desc' },
        });
        const products = await this.prisma.product.findMany({
            select: {
                id: true,
                name: true,
                image: true,
                category: true,
                slug: true,
                price: true,
            },
            orderBy: { id: 'desc' },
        });
        const orders = await this.prisma.order.findMany({
            include: {
                user: { select: { id: true, name: true, email: true } },
                product: { select: { id: true, name: true, price: true, image: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
        const stats = {
            users: users.length,
            orders: orders.length,
            products: products.length,
            collectedOrders: orders.filter((o) => o.status === 'COLLECTED').length,
        };
        return { users, products, orders, stats };
    }
    async getUsers() {
        return await this.prisma.user.findMany({
            select: { id: true, name: true, email: true, role: true },
            orderBy: { createdAt: 'desc' },
        });
    }
    async getProductById(id) {
        const product = await this.prisma.product.findUnique({
            where: { id: Number(id) },
        });
        if (!product) {
            throw new Error('Product not found');
        }
        return product;
    }
    async updateProductById(id, data) {
        const product = await this.prisma.product.update({
            where: { id: Number(id) },
            data,
        });
        return product;
    }
    async getOrders() {
        return await this.prisma.order.findMany({
            include: {
                user: { select: { id: true, name: true, email: true } },
                product: { select: { id: true, name: true, price: true, image: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async markOrderCollected(id) {
        return await this.prisma.order.update({
            where: { id: Number(id) },
            data: { status: 'COLLECTED' },
        });
    }
    async updateOrderStatus(id, body) {
        return await this.prisma.order.update({
            where: { id: Number(id) },
            data: { status: body.status },
        });
    }
    async updateUserRole(id, body) {
        if (!['USER', 'ADMIN'].includes(body.role)) {
            throw new Error('Invalid role. Must be USER or ADMIN');
        }
        return await this.prisma.user.update({
            where: { id: Number(id) },
            data: { role: body.role },
            select: { id: true, name: true, email: true, role: true },
        });
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.Get)('dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)('users'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Get)('products/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getProductById", null);
__decorate([
    (0, common_1.Put)('products/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateProductById", null);
__decorate([
    (0, common_1.Get)('orders'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getOrders", null);
__decorate([
    (0, common_1.Put)('orders/:id/collected'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "markOrderCollected", null);
__decorate([
    (0, common_1.Patch)('orders/:id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateOrderStatus", null);
__decorate([
    (0, common_1.Put)('users/:id/role'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateUserRole", null);
exports.AdminController = AdminController = __decorate([
    (0, common_1.Controller)('admin'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('ADMIN'),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AdminController);
//# sourceMappingURL=admin.controller.js.map