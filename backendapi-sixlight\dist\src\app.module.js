"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const product_module_1 = require("./product/product.module");
const admin_controller_1 = require("./admin.controller");
const user_controller_1 = require("./user.controller");
const auth_module_1 = require("./auth/auth.module");
const email_module_1 = require("./email/email.module");
const prisma_service_1 = require("./prisma.service");
const category_controller_1 = require("./category/category.controller");
const category_service_1 = require("./category/category.service");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [product_module_1.ProductModule, auth_module_1.AuthModule, email_module_1.EmailModule],
        controllers: [
            app_controller_1.AppController,
            admin_controller_1.AdminController,
            user_controller_1.UserController,
            category_controller_1.CategoryController,
        ],
        providers: [app_service_1.AppService, prisma_service_1.PrismaService, category_service_1.CategoryService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map