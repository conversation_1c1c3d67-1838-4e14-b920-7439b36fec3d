-- Database Migration Script for Sixlight Media Store
-- This script will update your database to match the current Prisma schema

-- First, let's check if the Order table exists and what columns it has
-- If you're running this manually, you can check with: \d "Order"

-- Add missing columns to Order table if they don't exist
-- Note: This script assumes the Order table exists but is missing columns

-- Add customer information columns
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customerName" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customerPhone" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customerEmail" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customerAddress" TEXT;

-- Add customization columns
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customColor" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customText" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "isCustomized" BOOLEAN DEFAULT false;

-- Add new advanced customization columns
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customizationData" TEXT;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "customizationPreview" TEXT;

-- Add order details columns
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "quantity" INTEGER DEFAULT 1;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "unitPrice" DOUBLE PRECISION;
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "totalPrice" DOUBLE PRECISION;

-- Update existing columns to NOT NULL where required (be careful with existing data)
-- Only run these if you're sure the columns exist and have data

-- For new installations, you might need to create the entire Order table:
-- Uncomment the following if the Order table doesn't exist at all:

/*
CREATE TABLE IF NOT EXISTS "Order" (
    "id" SERIAL PRIMARY KEY,
    "userId" INTEGER NOT NULL,
    "productId" INTEGER NOT NULL,
    "status" TEXT DEFAULT 'PENDING',
    "customerName" TEXT NOT NULL,
    "customerPhone" TEXT NOT NULL,
    "customerEmail" TEXT NOT NULL,
    "customerAddress" TEXT NOT NULL,
    "customColor" TEXT,
    "customText" TEXT,
    "isCustomized" BOOLEAN DEFAULT false,
    "customizationData" TEXT,
    "customizationPreview" TEXT,
    "quantity" INTEGER DEFAULT 1,
    "unitPrice" DOUBLE PRECISION NOT NULL,
    "totalPrice" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY ("userId") REFERENCES "User"("id"),
    FOREIGN KEY ("productId") REFERENCES "Product"("id")
);
*/

-- Create OrderStatus enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE "OrderStatus" AS ENUM ('PENDING', 'COLLECTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update status column to use enum (if needed)
-- ALTER TABLE "Order" ALTER COLUMN "status" TYPE "OrderStatus" USING "status"::"OrderStatus";

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS "Order_userId_idx" ON "Order"("userId");
CREATE INDEX IF NOT EXISTS "Order_productId_idx" ON "Order"("productId");
CREATE INDEX IF NOT EXISTS "Order_status_idx" ON "Order"("status");
CREATE INDEX IF NOT EXISTS "Order_createdAt_idx" ON "Order"("createdAt");

-- Update timestamps trigger (PostgreSQL specific)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for Order table
DROP TRIGGER IF EXISTS update_order_updated_at ON "Order";
CREATE TRIGGER update_order_updated_at
    BEFORE UPDATE ON "Order"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Verify the changes
-- You can run these queries to check if everything was created correctly:
-- SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'Order';
-- SELECT * FROM "Order" LIMIT 1;

COMMIT;
