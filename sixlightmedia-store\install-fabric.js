#!/usr/bin/env node

/**
 * Installation script for Fabric.js customization feature
 * Run with: node install-fabric.js
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🎨 Installing Fabric.js for Advanced Product Customization...\n");

try {
  // Check if package.json exists
  const packageJsonPath = path.join(__dirname, "package.json");
  if (!fs.existsSync(packageJsonPath)) {
    console.error(
      "❌ Error: package.json not found. Please run this script from the project root."
    );
    process.exit(1);
  }

  // Read current package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

  // Check if fabric is already installed
  if (packageJson.dependencies && packageJson.dependencies.fabric) {
    console.log("✅ Fabric.js is already installed!");
    console.log(`   Current version: ${packageJson.dependencies.fabric}`);
  } else {
    console.log("📦 Installing Fabric.js...");

    // Install fabric
    execSync("npm install fabric", { stdio: "inherit" });

    console.log("✅ Fabric.js installed successfully!");
  }

  // Check if @types/fabric is installed
  if (
    packageJson.devDependencies &&
    packageJson.devDependencies["@types/fabric"]
  ) {
    console.log("✅ TypeScript types for Fabric.js are already installed!");
  } else {
    console.log("📦 Installing TypeScript types for Fabric.js...");

    // Install @types/fabric
    execSync("npm install --save-dev @types/fabric", { stdio: "inherit" });

    console.log("✅ TypeScript types installed successfully!");
  }

  console.log("\n🎉 Installation Complete!");
  console.log("\n📋 Next Steps:");
  console.log("1. Restart your development server: npm run dev");
  console.log(
    "2. Navigate to any customizable product page (e.g., /product/custombottles)"
  );
  console.log('3. Toggle "🎨 Customize this product"');
  console.log('4. Toggle "🚀 Use Advanced Designer (Fabric.js)"');
  console.log(
    "5. The system will automatically detect Fabric.js and load the advanced editor!"
  );
  console.log(
    "6. Enjoy the full canvas editor with shapes, images, and advanced tools!"
  );

  console.log("\n🔗 Features Available:");
  console.log("   • Text editing with multiple fonts and sizes");
  console.log("   • Shape tools (rectangles, circles, triangles)");
  console.log("   • Image upload and manipulation");
  console.log("   • Color picker for all elements");
  console.log("   • Undo/Redo functionality");
  console.log("   • Export designs as high-quality images");
  console.log("   • Real-time preview updates");

  console.log(
    "\n📚 Documentation: See CUSTOMIZATION_FEATURE.md for detailed usage instructions"
  );
} catch (error) {
  console.error("❌ Installation failed:", error.message);
  console.log("\n🔧 Manual Installation:");
  console.log("1. Run: npm install fabric");
  console.log("2. Run: npm install --save-dev @types/fabric");
  console.log("3. Restart your development server");

  process.exit(1);
}
