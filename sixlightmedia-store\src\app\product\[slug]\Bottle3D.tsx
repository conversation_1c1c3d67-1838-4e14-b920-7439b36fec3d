import { Canvas } from "@react-three/fiber";
import { OrbitControls, Text, useGLTF } from "@react-three/drei";
import { useState } from "react";
import { HexColorPicker } from "react-colorful";
import React from "react";
import { MeshStandardMaterial } from "three";

type Bottle3DProps = {
  color?: string;
  text?: string;
  onColorChange?: (color: string) => void;
  modelUrl?: string; // Optional prop for dynamic model path
};

export default function Bottle3D({
  color = "white",
  text = "",
  onColorChange,
  modelUrl, // Accept optional modelUrl from props
}: Bottle3DProps) {
  const [showPicker, setShowPicker] = useState(false);
  // Simple 3D bottle shape using primitives. Replace with GLTF model for realism.
  return (
    <div style={{ width: "100%" }}>
      <Canvas
        style={{ height: 320, width: "100%", background: "#f9f9f9" }}
        camera={{ position: [0, 0, 5], fov: 50 }}
      >
        <ambientLight intensity={0.7} />
        <directionalLight position={[5, 5, 5]} intensity={0.7} />
        {/* <PERSON><PERSON> body and neck replaced with realistic model */}
        {modelUrl && modelUrl.trim() !== "" ? (
          <RealisticBottle color={color} modelUrl={modelUrl} />
        ) : (
          <mesh>
            <boxGeometry args={[1, 2, 1]} />
            <meshStandardMaterial color={color} />
          </mesh>
        )}
        {/* Custom text */}
        <ErrorBoundary>
          {typeof text === "string" &&
          text.trim() !== "" &&
          text.length <= 24 ? (
            <Text
              position={[0, 0, 0.75]}
              fontSize={0.25}
              color="#222"
              anchorX="center"
              anchorY="middle"
              maxWidth={1.2}
            >
              {text}
            </Text>
          ) : null}
        </ErrorBoundary>
        <OrbitControls enablePan enableZoom enableRotate />
      </Canvas>
      <div className="flex flex-col items-center mt-2">
        <button
          className="bg-[#ffd600] text-[#171717] font-semibold px-4 py-1 rounded-full shadow hover:bg-[#ffe066] transition mb-2"
          onClick={() => setShowPicker((v) => !v)}
        >
          {showPicker ? "Close Color Picker" : "Pick Custom Color"}
        </button>
        {showPicker && (
          <HexColorPicker
            color={color}
            onChange={onColorChange}
            style={{ width: 180, height: 120 }}
          />
        )}
      </div>
    </div>
  );
}

// ErrorBoundary to catch errors in 3D Text rendering
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  // componentDidCatch is required for error boundaries, but we don't need to use the arguments
  componentDidCatch() {}
  render() {
    if (this.state.hasError) return null;
    return this.props.children;
  }
}

function RealisticBottle({
  color,
  modelUrl,
}: {
  color: string;
  modelUrl: string;
}) {
  const { scene, materials } = useGLTF(modelUrl);
  React.useEffect(() => {
    if (materials && materials.BottleMaterial) {
      const material = materials.BottleMaterial as MeshStandardMaterial;
      if (material.color) {
        material.color.set(color);
      }
    }
  }, [color, materials]);
  return <primitive object={scene} />;
}
