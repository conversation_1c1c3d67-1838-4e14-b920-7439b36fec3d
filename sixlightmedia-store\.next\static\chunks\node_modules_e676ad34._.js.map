{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B;sDACE;gBACE,KAAK,KAAK,GAAG;gBACb,KAAK,WAAW,GAAG;gBACnB,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;qDACA;YAAC;YAAW;YAAO;SAAY;QAEjC;gDACE;gBACE,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;gBACzD,OAAO;wDAAU;wBACf,uBAAuB,SAAS,YAAY;4BAAE,MAAM;wBAAK;oBAC3D;;YACF;+CACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,uHACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,uHACF,0HACA,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,KAAK,oBAAoB,EAChD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import React from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getInitialState,\n    selector,\n    equalityFn\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,EAAE,gCAAgC,EAAE,GAAG,+KAAA,CAAA,UAA2B;AACxE,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,uBAAuB,GAAG,EAAE,WAAW,QAAQ,EAAE,UAAU;IAClE,MAAM,QAAQ,iCACZ,IAAI,SAAS,EACb,IAAI,QAAQ,EACZ,IAAI,eAAe,EACnB,UACA;IAEF,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,2BAA2B,CAAC,aAAa;IAC7C,MAAM,MAAM,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE;IACxB,MAAM,8BAA8B,CAAC,UAAU,aAAa,iBAAiB,GAAK,uBAAuB,KAAK,UAAU;IACxH,OAAO,MAAM,CAAC,6BAA6B;IAC3C,OAAO;AACT;AACA,MAAM,uBAAuB,CAAC,aAAa,oBAAsB,cAAc,yBAAyB,aAAa,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-reconciler/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-reconciler/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/%40react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/%40react-three/fiber/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/suspend-react/index.js"], "sourcesContent": ["const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\n\nconst globalCache = [];\n\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise: // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,YAAY,CAAA,UAAW,OAAO,YAAY,YAAY,OAAO,QAAQ,IAAI,KAAK;AAEpF,MAAM,cAAc,EAAE;AAEtB,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAM,MAAM,CAAC;IAC/D,IAAI,SAAS,MAAM,OAAO;IAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;IAC3B,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO;IAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO;IAEnE,OAAO;AACT;AAEA,SAAS,MAAM,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1D,iDAAiD;IACjD,IAAI,SAAS,MAAM,OAAO;QAAC;KAAG;IAE9B,KAAK,MAAM,SAAS,YAAa;QAC/B,eAAe;QACf,IAAI,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG;YACrD,+DAA+D;YAC/D,IAAI,SAAS,OAAO,WAAW,8BAA8B;YAE7D,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,UAAU,MAAM,MAAM,KAAK,EAAE,uCAAuC;YAEpH,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,aAAa;gBAC3D,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;oBAC1C,IAAI,MAAM,OAAO,EAAE,aAAa,MAAM,OAAO;oBAC7C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;gBAC1D;gBAEA,OAAO,MAAM,QAAQ;YACvB,EAAE,4CAA4C;YAG9C,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;QACnC;IACF,EAAE,qCAAqC;IAGvC,MAAM,QAAQ;QACZ;QACA,OAAO,OAAO,KAAK;QACnB,QAAQ;YACN,MAAM,QAAQ,YAAY,OAAO,CAAC;YAClC,IAAI,UAAU,CAAC,GAAG,YAAY,MAAM,CAAC,OAAO;QAC9C;QACA,SACA,CAAC,UAAU,MAAM,KAAK,MAAM,MAAM,oCAAoC;QACtE,EAAE,IAAI,CAAC,CAAA;YACL,MAAM,QAAQ,GAAG,UAAU,mDAAmD;YAE9E,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;gBAC1C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;YAC1D;QACF,GAAG,6FAA6F;SAC/F,KAAK,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG;IAChC,GAAG,qBAAqB;IAExB,YAAY,IAAI,CAAC,QAAQ,2DAA2D;IAEpF,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;IACjC,OAAO;AACT;AAEA,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,MAAM,IAAI,MAAM,OAAO;AAE7D,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,KAAK,MAAM,IAAI,MAAM,MAAM;AAEjE,MAAM,OAAO,CAAA;IACX,IAAI;IAEJ,OAAO,CAAC,oBAAoB,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ;AACzJ;AAEA,MAAM,QAAQ,CAAA;IACZ,IAAI,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,YAAY,MAAM,CAAC,GAAG,YAAY,MAAM;SAAO;QAC1F,MAAM,QAAQ,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK;QACxF,IAAI,OAAO,MAAM,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/its-fine/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect = /* @__PURE__ */ (() =>\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative'))()\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst FiberContext = /* @__PURE__ */ wrapContext(/* @__PURE__ */ React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\n\r\nconst isContext = <T,>(type: unknown): type is React.Context<T> =>\r\n  type !== null && typeof type === 'object' && '$$typeof' in type && type.$$typeof === REACT_CONTEXT_TYPE\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    const context = node.type\r\n    if (isContext(context) && context !== FiberContext && !contextMap.has(context)) {\r\n      contextMap.set(context, React.use(wrapContext(context)))\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "names": ["useIsomorphicLayoutEffect", "_a", "_b", "React", "traverseFiber", "fiber", "ascending", "selector", "child", "match", "wrapContext", "context", "_", "FiberContext", "FiberProvider", "useFiber", "root", "id", "maybeFiber", "node", "state", "useContainer", "useNearestChild", "type", "childRef", "useNearestParent", "parentRef", "REACT_CONTEXT_TYPE", "isContext", "useContextMap", "contextMap", "useContextBridge", "Prev", "props"], "mappings": ";;;;;;;;;;;;AAYA,MAAMA,IAA6C,aAAA,GAAA,CAAA,MAAA;;IACjD,OAAA,OAAO,UAAW,eAAA,CAAA,CAAA,CAAgBC,IAAA,OAAO,QAAA,KAAP,OAAA,KAAA,IAAAA,EAAiB,aAAA,KAAA,CAAA,CAAiBC,IAAA,OAAO,SAAA,KAAP,OAAA,KAAA,IAAAA,EAAkB,OAAA,MAAY,aAAA;AAAA,CAAA,EAAA,kKAChGC,EAAM,gBAAA,iKACNA,EAAM,UAAA;AAkBM,SAAAC,EAEdC,CAAAA,EAEAC,CAAAA,EAEAC,CAAAA,EACsB;IACtB,IAAI,CAACF,EAAO,CAAA;IACZ,IAAIE,EAASF,CAAK,MAAM,CAAA,EAAa,CAAA,OAAAA;IAErC,IAAIG,IAAQF,IAAYD,EAAM,MAAA,GAASA,EAAM,KAAA;IAC7C,MAAOG,GAAO;QACZ,MAAMC,IAAQL,EAAcI,GAAOF,GAAWC,CAAQ;QACtD,IAAIE,EAAc,CAAA,OAAAA;QAEVD,IAAAF,IAAY,OAAOE,EAAM,OAAA;IAAA;AAErC;AAKA,SAASE,EAAeC,CAAAA,EAA6C;IAC/D,IAAA;QACK,OAAA,OAAO,gBAAA,CAAiBA,GAAS;YACtC,kBAAkB;gBAChB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YACR;YACA,mBAAmB;gBACjB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YAAC;QACT,CACD;IAAA,EAAA,OACMC,GAAG;QACH,OAAAD;IAAA;AAEX;AAEA,MAAME,IAA+B,aAAA,GAAAH,EAAkC,aAAA,qKAAAP,EAAA,cAAA,EAAqB,IAAK,CAAC;AAKrF,MAAAW,wKAAsBX,EAAM,UAAA,CAA0C;IAGjF,SAAS;QACA,OAAA,aAAA,qKAAAA,EAAA,cAAA,EAACU,EAAa,QAAA,EAAb;YAAsB,OAAO,IAAA,CAAK,eAAA;QAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,QAAS;IAAA;AAEpF;AAKO,SAASE,IAAoC;IAC5C,MAAAC,KAAOb,EAAM,4KAAA,EAAWU,CAAY;IAC1C,IAAIG,MAAS,KAAY,CAAA,MAAA,IAAI,MAAM,+DAA+D;IAE5F,MAAAC,sKAAKd,EAAM,MAAA,CAAM;IAehB,WAdOA,EAAM,sKAAA;qBAAQ,MAAM;YAChC,KAAA,MAAWe,KAAc;gBAACF;gBAAMA,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAS;aAAA,CAAG;gBAChD,IAAI,CAACE,EAAY,CAAA;gBACjB,MAAMb,IAAQD,EAAoBc,GAAY,CAAA;mCAAO,CAACC,MAAS;wBAC7D,IAAIC,IAAQD,EAAK,aAAA;wBACjB,MAAOC,GAAO;4BACR,IAAAA,EAAM,aAAA,KAAkBH,EAAW,CAAA,OAAA,CAAA;4BACvCG,IAAQA,EAAM,IAAA;wBAAA;oBAChB,CACD;;gBACD,IAAIf,EAAcA,CAAAA,OAAAA;YAAA;QACpB;oBACC;QAACW;QAAMC,CAAE;KAAC;AAGf;AAcO,SAASI,IAAuC;IACrD,MAAMhB,IAAQU,EAAS,GACjBC,sKAAOb,EAAM,QAAA;wBACjB,IAAMC,EAAoCC,GAAO,CAAA;gCAAM,CAACc,MAAS;;oBAAA,OAAA,CAAA,CAAAlB,IAAAkB,EAAK,SAAA,KAAL,OAAA,KAAA,IAAAlB,EAAgB,aAAA,KAAiB;gBAAA,CAAI;;uBACtG;QAACI,CAAK;KAAA;IAGR,OAAOW,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAA,CAAU,aAAA;AACzB;AAOO,SAASM,EAEdC,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBS,sKAAWrB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE1C,OAAAH,EAA0B,MAAM;;QAC9BwB,EAAS,OAAA,GAAA,CAAUvB,IAAAG,EACjBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH/D,OAAA,KAAA,IAAAtB,EAIhB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHmB;AACT;AAOO,SAASC,EAEdF,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBW,sKAAYvB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE3C,OAAAH,EAA0B,MAAM;;QAC9B0B,EAAU,OAAA,GAAA,CAAUzB,IAAAG,EAClBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH9D,OAAA,KAAA,IAAAtB,EAIjB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHqB;AACT;AAMA,MAAMC,IAAqB,OAAO,GAAA,CAAI,eAAe,GAE/CC,IAAY,CAAKL,IACrBA,MAAS,QAAQ,OAAOA,KAAS,YAAY,cAAcA,KAAQA,EAAK,QAAA,KAAaI;AAKhF,SAASE,IAA4B;IAC1C,MAAMxB,IAAQU,EAAS,GACjB,CAACe,CAAU,CAAA,qKAAI3B,EAAM,SAAA;sBAAS,IAAM,aAAA,GAAA,IAAI,KAA8B;;IAG5E2B,EAAW,KAAA,CAAM;IACjB,IAAIX,IAAOd;IACX,MAAOc,GAAM;QACX,MAAMR,IAAUQ,EAAK,IAAA;QACjBS,EAAUjB,CAAO,KAAKA,MAAYE,KAAgB,CAACiB,EAAW,GAAA,CAAInB,CAAO,KAC3EmB,EAAW,GAAA,CAAInB,qKAASR,EAAM,IAAA,EAAIO,EAAYC,CAAO,CAAC,CAAC,GAGzDQ,IAAOA,EAAK,MAAA;IAAA;IAGP,OAAAW;AACT;AAYO,SAASC,IAAkC;IAChD,MAAMD,IAAaD,EAAc;IAGjC,yKAAO1B,EAAM,QAAA;qBACX,IACE,MAAM,IAAA,CAAK2B,EAAW,IAAA,CAAA,CAAM,EAAE,MAAA;6BAC5B,CAACE,GAAMrB;qCAAY,CAACsB,IAEhB,aAAA,qKAAA9B,EAAA,cAAA,EAAC6B,GAAAA,MACE,aAAA,qKAAA7B,EAAA,cAAA,EAAAQ,EAAQ,QAAA,EAAR;gCAAkB,GAAGsB,CAAAA;gCAAO,OAAOH,EAAW,GAAA,CAAInB,CAAO;4BAAA,CAAG,CAC/D;;;6BAEJ,CAACsB,IAAW,aAAA,qKAAA9B,EAAA,cAAA,EAAAW,GAAA;wBAAe,GAAGmB,CAAAA;oBAAO,CAAA;;oBAEzC;QAACH,CAAU;KAAA;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "names": ["createDebounce", "callback", "ms", "timeoutId", "args", "useMeasure", "debounce", "scroll", "polyfill", "offsetSize", "ResizeObserver", "bounds", "set", "useState", "state", "useRef", "scrollDebounce", "resizeDebounce", "mounted", "useEffect", "forceRefresh", "resizeChange", "scrollChange", "useMemo", "left", "top", "width", "height", "bottom", "right", "x", "y", "size", "areBoundsEqual", "removeListeners", "element", "addListeners", "scrollContainer", "ref", "node", "findScrollContainers", "useOnWindowScroll", "useOnWindowResize", "onWindowResize", "cb", "onScroll", "enabled", "result", "overflow", "overflowX", "overflowY", "prop", "keys", "a", "b", "key"], "mappings": ";;;;;AAEA,SAASA,EAAmDC,CAAAA,EAAaC,CAAAA,CAAY;IAC/EC,IAAAA;IAEJ,OAAO,CAAA,GAAIC,IAA8B;QAChC,OAAA,YAAA,CAAaD,CAAS,GAC7BA,IAAY,OAAO,UAAA,CAAW,IAAMF,EAAS,GAAGG,CAAI,GAAGF,CAAE;IAC3D;AACF;AA0CA,SAASG,EACP,EAAE,UAAAC,CAAAA,EAAU,QAAAC,CAAAA,EAAQ,UAAAC,CAAAA,EAAU,YAAAC,CAAW,EAAA,GAAa;IAAE,UAAU;IAAG,QAAQ,CAAA;IAAO,YAAY,CAAA;AAAA,CAAA,CACxF;IACR,MAAMC,IACJF,KAAAA,CAAa,OAAO,UAAW,cAAc,KAAqB;IAAA,IAAM,OAAe,cAAA;IAEzF,IAAI,CAACE,GACH,MAAM,IAAI,MACR,gJACF;IAGF,MAAM,CAACC,GAAQC,CAAG,CAAA,GAAIC,6KAAAA,EAAuB;QAC3C,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,GAAG;QACH,GAAG;IAAA,CACJ,GAGKC,QAAQC,uKAAAA,EAAc;QAC1B,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,YAAYJ;QACZ,oBAAoB;IAAA,CACrB,GAGKK,IAAiBV,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAC1FW,IAAiBX,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAG1FY,+KAAUH,EAAO,CAAA,CAAK;kLAC5BI,EAAU,IAAA,CACRD,EAAQ,OAAA,GAAU,CAAA,GACX,IAAM,KAAA,CAAMA,EAAQ,OAAA,GAAU,CAAA,CAAA,CAAA,CACtC;IAGD,MAAM,CAACE,GAAcC,GAAcC,CAAY,CAAA,GAAIC,4KAAAA,EAAQ,IAAM;QAC/D,MAAMtB,IAAW,IAAM;YACjB,IAAA,CAACa,EAAM,OAAA,CAAQ,OAAA,EAAS;YACtB,MAAA,EAAE,MAAAU,CAAAA,EAAM,KAAAC,CAAAA,EAAK,OAAAC,CAAAA,EAAO,QAAAC,CAAAA,EAAQ,QAAAC,CAAAA,EAAQ,OAAAC,CAAAA,EAAO,GAAAC,CAAAA,EAAG,GAAAC,CAAE,EAAA,GACpDjB,EAAM,OAAA,CAAQ,OAAA,CAAQ,qBAAA,CAAsB,GAExCkB,IAAO;gBACX,MAAAR;gBACA,KAAAC;gBACA,OAAAC;gBACA,QAAAC;gBACA,QAAAC;gBACA,OAAAC;gBACA,GAAAC;gBACA,GAAAC;YACF;YAEIjB,EAAM,OAAA,CAAQ,OAAA,YAAmB,eAAeL,KAAAA,CAC7CuB,EAAA,MAAA,GAASlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,YAAA,EAC/BkB,EAAA,KAAA,GAAQlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,WAAA,GAGrC,OAAO,MAAA,CAAOkB,CAAI,GACdd,EAAQ,OAAA,IAAW,CAACe,EAAenB,EAAM,OAAA,CAAQ,UAAA,EAAYkB,CAAI,KAAGpB,EAAKE,EAAM,OAAA,CAAQ,UAAA,GAAakB,CAAK;QAC/G;QACO,OAAA;YACL/B;YACAgB,IAAiBjB,EAAeC,GAAUgB,CAAc,IAAIhB;YAC5De,IAAiBhB,EAAeC,GAAUe,CAAc,IAAIf,CAC9D;SAAA;IAAA,GACC;QAACW;QAAKH;QAAYO;QAAgBC,CAAc;KAAC;IAGpD,SAASiB,GAAkB;QACrBpB,EAAM,OAAA,CAAQ,gBAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASqB,KAAYA,EAAQ,mBAAA,CAAoB,UAAUb,GAAc,CAAA,CAAI,CAAC,GAC7GR,EAAM,OAAA,CAAQ,gBAAA,GAAmB,IAAA,GAG/BA,EAAM,OAAA,CAAQ,cAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,cAAA,CAAe,UAAA,CAAW,GACxCA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAA,GAG7BA,EAAM,OAAA,CAAQ,kBAAA,IAAA,CACZ,iBAAiB,UAAU,yBAAyB,OAAO,WAAA,GAC7D,OAAO,WAAA,CAAY,mBAAA,CAAoB,UAAUA,EAAM,OAAA,CAAQ,kBAAkB,IACxE,yBAAyB,UAClC,OAAO,mBAAA,CAAoB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAEpF;IAIF,SAASsB,GAAe;QACjBtB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACnBA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAIJ,EAAeY,CAAY,GAC9DR,EAAM,OAAA,CAAQ,cAAA,CAAgB,OAAA,CAAQA,EAAM,OAAA,CAAQ,OAAO,GACvDP,KAAUO,EAAM,OAAA,CAAQ,gBAAA,IAC1BA,EAAM,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASuB,KACtCA,EAAgB,gBAAA,CAAiB,UAAUf,GAAc;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAM,CAAA,CAC3F,GAIIR,EAAA,OAAA,CAAQ,kBAAA,GAAqB,IAAM;YAC1BQ,EAAA;QACf,GAGI,iBAAiB,UAAU,sBAAsB,OAAO,WAAA,GAC1D,OAAO,WAAA,CAAY,gBAAA,CAAiB,UAAUR,EAAM,OAAA,CAAQ,kBAAkB,IACrE,yBAAyB,UAElC,OAAO,gBAAA,CAAiB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAC/E;IAIIwB,MAAAA,KAAOC,GAAkC;QACzC,CAACA,KAAQA,MAASzB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACpBoB,EAAA,GAChBpB,EAAM,OAAA,CAAQ,OAAA,GAAUyB,GAClBzB,EAAA,OAAA,CAAQ,gBAAA,GAAmB0B,EAAqBD,CAAI,GAC7CH,EAAA,CAAA;IACf;IAGkBK,OAAAA,EAAAnB,GAAc,CAAQf,CAAAA,CAAO,GAC/CmC,EAAkBrB,CAAY,iLAG9BF,EAAU,IAAM;QACEe,EAAA,GACHE,EAAA;IACZ,GAAA;QAAC7B;QAAQe;QAAcD,CAAY;KAAC,IAG7BF,6KAAAA,EAAA,IAAMe,GAAiB,EAAE,GAC5B;QAACI;QAAK3B;QAAQS,CAAY;;AACnC;AAGA,SAASsB,EAAkBC,CAAAA,CAAwC;IACjExB,8KAAAA,EAAU,IAAM;QACd,MAAMyB,IAAKD;QACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUC,CAAE,GAC7B,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,CAAE;IAAA,GACxD;QAACD,CAAc;KAAC;AACrB;AACA,SAASF,EAAkBI,CAAAA,EAAsBC,CAAAA,CAAkB;kLACjE3B,EAAU,IAAM;QACd,IAAI2B,GAAS;YACX,MAAMF,IAAKC;YACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUD,GAAI;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAA,CAAM,GAC/D,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,GAAI,CAAA,CAAI;QAAA;IACjE,GACC;QAACC;QAAUC,CAAO;KAAC;AACxB;AAGA,SAASN,EAAqBL,CAAAA,CAAsD;IAClF,MAAMY,IAA6B,CAAC,CAAA;IACpC,IAAI,CAACZ,KAAWA,MAAY,SAAS,IAAA,EAAaY,OAAAA;IAC5C,MAAA,EAAE,UAAAC,CAAAA,EAAU,WAAAC,CAAAA,EAAW,WAAAC,CAAc,EAAA,GAAA,OAAO,gBAAA,CAAiBf,CAAO;IACtE,OAAA;QAACa;QAAUC;QAAWC,CAAS;KAAA,CAAE,IAAA,CAAMC,KAASA,MAAS,UAAUA,MAAS,QAAQ,KAAGJ,EAAO,IAAA,CAAKZ,CAAO,GACvG,CAAC;WAAGY,EAAQ;WAAGP,EAAqBL,EAAQ,aAAa,CAAC;;AACnE;AAGA,MAAMiB,IAA+B;IAAC;IAAK;IAAK;IAAO;IAAU;IAAQ;IAAS;IAAS,QAAQ;CAAA,EAC7FnB,IAAiB,CAACoB,GAAiBC,IAA6BF,EAAK,KAAA,EAAOG,IAAQF,CAAAA,CAAEE,CAAG,CAAA,KAAMD,CAAAA,CAAEC,CAAG,CAAC", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/%40babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,2CAMjD,SAAS,KAAK,CAAC,MAAM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "file": "EventDispatcher.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AA6BO,MAAM,gBAA2C;IAAjD,aAAA;QAEK,8BAAA;QAAA,cAAA,IAAA,EAAA;IAAA;IAAA;;;;GAAA,GAOX,iBACO,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY,IAAA,CAAK,UAAA,GAAa,CAAA;QAEvD,MAAM,YAAY,IAAA,CAAK,UAAA;QAElB,IAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,GAAY;YAE3B,SAAA,CAAA,IAAK,CAAA,GAAI,EAAA;QAErB;QAEA,IAAK,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA,GAAM;YAEzC,SAAA,CAAA,IAAK,CAAA,CAAE,IAAA,CAAM,QAAS;QAElC;IAED;IAAA;;;;MAAA,GAOG,iBACI,IAAA,EACA,QAAA,EACO;QAEb,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAmB,OAAA;QAE5C,MAAM,YAAY,IAAA,CAAK,UAAA;QAEhB,OAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,KAAa,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA;IAErF;IAAA;;;;MAAA,GAOG,oBACI,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,IAAK,CAAA;QAEtC,IAAK,kBAAkB,KAAA,GAAY;YAE5B,MAAA,QAAQ,cAAc,OAAA,CAAS,QAAS;YAE9C,IAAK,UAAU,CAAA,GAAM;gBAEN,cAAA,MAAA,CAAQ,OAAO,CAAE;YAEhC;QAED;IAED;IAAA;;;MAAA,GAMG,cAA0D,KAAA,EAA0C;QAEtG,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,MAAM,IAAK,CAAA;QAE5C,IAAK,kBAAkB,KAAA,GAAY;YAElC,MAAM,MAAA,GAAS,IAAA;YAGT,MAAA,QAAQ,cAAc,KAAA,CAAO,CAAE;YAErC,IAAA,IAAU,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAEhD,KAAA,CAAO,CAAE,CAAA,CAAE,IAAA,CAAM,IAAA,EAAM,KAAM;YAE9B;YAEA,MAAM,MAAA,GAAS;QAEhB;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "file": "OrbitControls.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgBA,MAAM,OAAA,aAAA,GAAA,uJAA2B,MAAA;AACjC,MAAM,SAAA,aAAA,GAAA,uJAA6B,QAAA;AACnC,MAAM,aAAa,KAAK,GAAA,CAAI,KAAA,CAAM,KAAK,EAAA,GAAK,GAAA,CAAI;AAShD,MAAM,mBAAmB,CAAC,QAAgB,WAAA,CAAuB,SAAS,WAAY,QAAA,IAAY;AAElG,MAAM,wLAAsB,kBAAA,CAA0C;IA6FpE,YAAY,MAAA,EAAgD,UAAA,CAA0B;QAC9E,KAAA;QA7FR,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,uCAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QAEV,sEAAA;QAAA,cAAA,IAAA,EAAA,UAAS,uJAAI,UAAA;QAEb,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc;QAEd,8DAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,WAAU;QAGV,4DAAA;QAAA,iCAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,UAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB,KAAK,EAAA;QAGrB,UAAA;QAAA,8DAAA;QAAA,0GAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB,CAAA;QAClB,UAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB;QAGlB,UAAA;QAAA,0CAAA;QAAA,gFAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,iBAAgB;QAGhB,gGAAA;QAAA,kCAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,aAAY;QAEZ,mCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,eAAc;QAEd,kCAAA;QAAA,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,sBAAqB;QACrB,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,kCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QAGf,wDAAA;QAAA,oFAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,mBAAkB;QAClB,sCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,uFAAA;QAAA,cAAA,IAAA,EAAA,0BAAyB;QACzB,6DAAA;QAAA,cAAA,IAAA,EAAA,wBAAuB;QAEvB,2DAAA;QAAA,sBAAA;QAAA,cAAA,IAAA,EAAA,QAAO;YAAE,MAAM;YAAa,IAAI;YAAW,OAAO;YAAc,QAAQ;QAAA;QAExE,gBAAA;QAAA,cAAA,IAAA,EAAA,gBAIK;YACH,MAAM,2JAAA,CAAM,MAAA;YACZ,2JAAQ,QAAA,CAAM,KAAA;YACd,0JAAO,QAAA,CAAM,GAAA;QAAA;QAGf,gBAAA;QAAA,cAAA,IAAA,EAAA,WAGK;YAAE,wJAAK,QAAA,CAAM,MAAA;YAAQ,wJAAK,QAAA,CAAM,SAAA;QAAA;QACrC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,wCAAA;QAAA,cAAA,IAAA,EAAA,wBAA4B;QAE5B,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,gFAAA;QAAA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAGA,4BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,6BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,wBAAA;QAAA,cAAA,IAAA,EAAA;QAEA,kHAAA;QAAA,cAAA,IAAA,EAAA;QAKE,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAGb,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAA,CAAM;QACvC,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;QAMpB,IAAA,CAAA,aAAA,GAAgB,IAAc,UAAU,GAAA;QAExC,IAAA,CAAA,iBAAA,GAAoB,IAAc,UAAU,KAAA;QAE5C,IAAA,CAAA,aAAA,GAAgB,CAAC,UAAwB;YAE5C,IAAI,MAAM,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC7C,IAAI,aAAa,UAAU,GAAA;YAG3B,IAAI,aAAa,GAAG,cAAc,IAAI,KAAK,EAAA;YAC3C,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,EAAA;YAC7B,IAAI,UAAU,KAAK,GAAA,CAAI,MAAM,UAAU;YACvC,IAAI,IAAI,KAAK,EAAA,GAAK,UAAU,SAAS;gBACnC,IAAI,MAAM,YAAY;oBACpB,OAAO,IAAI,KAAK,EAAA;gBAAA,OACX;oBACL,cAAc,IAAI,KAAK,EAAA;gBACzB;YACF;YACA,eAAe,GAAA,GAAM,MAAM;YAC3B,MAAM,MAAA,CAAO;QAAA;QAGV,IAAA,CAAA,iBAAA,GAAoB,CAAC,UAAwB;YAEhD,IAAI,QAAQ,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC/C,IAAI,eAAe,UAAU,KAAA;YAG7B,IAAI,eAAe,GAAG,gBAAgB,IAAI,KAAK,EAAA;YAC/C,IAAI,QAAQ,GAAG,SAAS,IAAI,KAAK,EAAA;YACjC,IAAI,YAAY,KAAK,GAAA,CAAI,QAAQ,YAAY;YAC7C,IAAI,IAAI,KAAK,EAAA,GAAK,YAAY,WAAW;gBACvC,IAAI,QAAQ,cAAc;oBACxB,SAAS,IAAI,KAAK,EAAA;gBAAA,OACb;oBACL,gBAAgB,IAAI,KAAK,EAAA;gBAC3B;YACF;YACA,eAAe,KAAA,GAAQ,QAAQ;YAC/B,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,WAAA,GAAc,IAAc,MAAM,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,MAAM,MAAM;QAEzE,IAAA,CAAA,iBAAA,GAAoB,CAACA,gBAAkC;YAC1DA,YAAW,gBAAA,CAAiB,WAAW,SAAS;YAChD,IAAA,CAAK,oBAAA,GAAuBA;QAAA;QAG9B,IAAA,CAAK,qBAAA,GAAwB,MAAY;YAClC,IAAA,CAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YAClE,IAAA,CAAK,oBAAA,GAAuB;QAAA;QAG9B,IAAA,CAAK,SAAA,GAAY,MAAY;YACrB,MAAA,OAAA,CAAQ,IAAA,CAAK,MAAM,MAAM;YAC/B,MAAM,SAAA,CAAU,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;YACpC,MAAA,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA;QAAA;QAG7B,IAAA,CAAK,KAAA,GAAQ,MAAY;YACjB,MAAA,MAAA,CAAO,IAAA,CAAK,MAAM,OAAO;YAC/B,MAAM,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,MAAM,SAAS;YACpC,MAAA,MAAA,CAAO,IAAA,GAAO,MAAM,KAAA;YAC1B,MAAM,MAAA,CAAO,sBAAA;YAGb,MAAM,aAAA,CAAc,WAAW;YAE/B,MAAM,MAAA,CAAO;YAEb,QAAQ,MAAM,IAAA;QAAA;QAIhB,IAAA,CAAK,MAAA,GAAA,CAAU,MAAoB;YAC3B,MAAA,SAAS,uJAAI,UAAA;YACnB,MAAM,KAAK,uJAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YAG9B,MAAM,OAAO,uJAAI,aAAA,GAAa,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;YAC9D,MAAM,cAAc,KAAK,KAAA,CAAM,EAAE,MAAA,CAAO;YAElC,MAAA,eAAe,uJAAI,UAAA;YACnB,MAAA,iBAAiB,uJAAI,aAAA;YAErB,MAAA,QAAQ,IAAI,KAAK,EAAA;YAEvB,OAAO,SAAS,SAAkB;gBAC1B,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;gBAGzB,KAAA,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;gBACzB,YAAA,IAAA,CAAK,IAAI,EAAE,MAAA,CAAO;gBAE9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;gBAGtC,OAAO,eAAA,CAAgB,IAAI;gBAG3B,UAAU,cAAA,CAAe,MAAM;gBAE/B,IAAI,MAAM,UAAA,IAAc,UAAU,MAAM,IAAA,EAAM;oBAC5C,WAAW,sBAAsB;gBACnC;gBAEA,IAAI,MAAM,aAAA,EAAe;oBACb,UAAA,KAAA,IAAS,eAAe,KAAA,GAAQ,MAAM,aAAA;oBACtC,UAAA,GAAA,IAAO,eAAe,GAAA,GAAM,MAAM,aAAA;gBAAA,OACvC;oBACL,UAAU,KAAA,IAAS,eAAe,KAAA;oBAClC,UAAU,GAAA,IAAO,eAAe,GAAA;gBAClC;gBAIA,IAAI,MAAM,MAAM,eAAA;gBAChB,IAAI,MAAM,MAAM,eAAA;gBAEhB,IAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;oBAC9B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE3B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE/B,IAAI,OAAO,KAAK;wBACJ,UAAA,KAAA,GAAQ,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,CAAC;oBAAA,OACzD;wBACL,UAAU,KAAA,GACR,UAAU,KAAA,GAAA,CAAS,MAAM,GAAA,IAAO,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK;oBACtG;gBACF;gBAGU,UAAA,GAAA,GAAM,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,UAAU,GAAG,CAAC;gBAC1F,UAAU,QAAA,CAAS;gBAIf,IAAA,MAAM,aAAA,KAAkB,MAAM;oBAChC,MAAM,MAAA,CAAO,eAAA,CAAgB,WAAW,MAAM,aAAa;gBAAA,OACtD;oBACC,MAAA,MAAA,CAAO,GAAA,CAAI,SAAS;gBAC5B;gBAIA,IAAK,MAAM,YAAA,IAAgB,qBAAuB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;oBAChG,UAAA,MAAA,GAAS,cAAc,UAAU,MAAM;gBAAA,OAC5C;oBACL,UAAU,MAAA,GAAS,cAAc,UAAU,MAAA,GAAS,KAAK;gBAC3D;gBAEA,OAAO,gBAAA,CAAiB,SAAS;gBAGjC,OAAO,eAAA,CAAgB,WAAW;gBAElC,SAAS,IAAA,CAAK,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM;gBAElC,IAAA,CAAC,MAAM,MAAA,CAAO,gBAAA,EAAkB,MAAM,MAAA,CAAO,YAAA;gBAC3C,MAAA,MAAA,CAAO,MAAA,CAAO,MAAM,MAAM;gBAE5B,IAAA,MAAM,aAAA,KAAkB,MAAM;oBACjB,eAAA,KAAA,IAAS,IAAI,MAAM,aAAA;oBACnB,eAAA,GAAA,IAAO,IAAI,MAAM,aAAA;oBAEtB,UAAA,cAAA,CAAe,IAAI,MAAM,aAAa;gBAAA,OAC3C;oBACU,eAAA,GAAA,CAAI,GAAG,GAAG,CAAC;oBAEhB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;gBACvB;gBAGA,IAAI,cAAc;gBACd,IAAA,MAAM,YAAA,IAAgB,mBAAmB;oBAC3C,IAAI,YAAY;oBAChB,IAAI,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;wBAG3E,MAAA,aAAa,OAAO,MAAA;wBACd,YAAA,cAAc,aAAa,KAAK;wBAE5C,MAAM,cAAc,aAAa;wBACjC,MAAM,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,gBAAgB,WAAW;wBACjE,MAAM,MAAA,CAAO,iBAAA;oBAAkB,OAAA,IACrB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;wBAEpE,MAAM,cAAc,uJAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,YAAA,SAAA,CAAU,MAAM,MAAM;wBAElC,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;wBACC,cAAA;wBAEd,MAAM,aAAa,uJAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,WAAA,SAAA,CAAU,MAAM,MAAM;wBAEjC,MAAM,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,UAAU,EAAE,GAAA,CAAI,WAAW;wBACrD,MAAM,MAAA,CAAO,iBAAA;wBAEb,YAAY,OAAO,MAAA;oBAAO,OACrB;wBACL,QAAQ,IAAA,CAAK,yFAAyF;wBACtG,MAAM,YAAA,GAAe;oBACvB;oBAGA,IAAI,cAAc,MAAM;wBACtB,IAAI,MAAM,kBAAA,EAAoB;4BAE5B,MAAM,MAAA,CACH,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EACZ,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM,EACtC,cAAA,CAAe,SAAS,EACxB,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ;wBAAA,OACvB;4BAEL,KAAK,MAAA,CAAO,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;4BACjC,KAAA,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EAAE,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM;4BAI/D,IAAA,KAAK,GAAA,CAAI,MAAM,MAAA,CAAO,EAAA,CAAG,GAAA,CAAI,KAAK,SAAS,CAAC,IAAI,YAAY;gCACvD,OAAA,MAAA,CAAO,MAAM,MAAM;4BAAA,OACrB;gCACL,OAAO,6BAAA,CAA8B,MAAM,MAAA,CAAO,EAAA,EAAI,MAAM,MAAM;gCAC7D,KAAA,cAAA,CAAe,QAAQ,MAAM,MAAM;4BAC1C;wBACF;oBACF;gBAAA,OAAA,IACS,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAC1F,cAAc,UAAU;oBAExB,IAAI,aAAa;wBACf,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;oBACf;gBACF;gBAEQ,QAAA;gBACY,oBAAA;gBAMpB,IACE,eACA,aAAa,iBAAA,CAAkB,MAAM,MAAA,CAAO,QAAQ,IAAI,OACxD,IAAA,CAAK,IAAI,eAAe,GAAA,CAAI,MAAM,MAAA,CAAO,UAAU,CAAA,IAAK,KACxD;oBAEA,MAAM,aAAA,CAAc,WAAW;oBAElB,aAAA,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;oBACxB,eAAA,IAAA,CAAK,MAAM,MAAA,CAAO,UAAU;oBAC7B,cAAA;oBAEP,OAAA;gBACT;gBAEO,OAAA;YAAA;QACT,CAAA;QAIG,IAAA,CAAA,OAAA,GAAU,CAACA,gBAAkC;YAChD,MAAM,UAAA,GAAaA;YAIb,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YAC/B,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,WAAW;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,SAAS,YAAY;QAAA;QAGzD,IAAA,CAAK,OAAA,GAAU,MAAY;;YAEzB,IAAI,MAAM,UAAA,EAAY;gBACd,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACvC;YACM,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,iBAAiB;YACjD,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,SAAS;YAC/C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;YACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YAC7D,IAAA,MAAM,oBAAA,KAAyB,MAAM;gBACjC,MAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YACrE;QAAA;QAQF,MAAM,QAAQ,IAAA;QAER,MAAA,cAAc;YAAE,MAAM;QAAA;QACtB,MAAA,aAAa;YAAE,MAAM;QAAA;QACrB,MAAA,WAAW;YAAE,MAAM;QAAA;QAEzB,MAAM,QAAQ;YACZ,MAAM,CAAA;YACN,QAAQ;YACR,OAAO;YACP,KAAK;YACL,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,oBAAoB;QAAA;QAGtB,IAAI,QAAQ,MAAM,IAAA;QAElB,MAAM,MAAM;QAGN,MAAA,YAAY,uJAAI,YAAA;QAChB,MAAA,iBAAiB,uJAAI,YAAA;QAE3B,IAAI,QAAQ;QACN,MAAA,YAAY,uJAAI,UAAA;QAEhB,MAAA,cAAc,uJAAI,UAAA;QAClB,MAAA,YAAY,uJAAI,UAAA;QAChB,MAAA,cAAc,uJAAI,UAAA;QAElB,MAAA,WAAW,uJAAI,UAAA;QACf,MAAA,SAAS,IAAI,6JAAA;QACb,MAAA,WAAW,uJAAI,UAAA;QAEf,MAAA,aAAa,uJAAI,UAAA;QACjB,MAAA,WAAW,uJAAI,UAAA;QACf,MAAA,aAAa,uJAAI,UAAA;QAEjB,MAAA,iBAAiB,uJAAI,UAAA;QACrB,MAAA,QAAQ,uJAAI,UAAA;QAClB,IAAI,oBAAoB;QAExB,MAAM,WAA2B,CAAA,CAAA;QACjC,MAAM,mBAA+C,CAAA;QAErD,SAAS,uBAA+B;YACtC,OAAS,IAAI,KAAK,EAAA,GAAM,KAAK,KAAM,MAAM,eAAA;QAC3C;QAEA,SAAS,eAAuB;YAC9B,OAAO,KAAK,GAAA,CAAI,MAAM,MAAM,SAAS;QACvC;QAEA,SAAS,WAAW,KAAA,EAAqB;YACnC,IAAA,MAAM,YAAA,IAAgB,MAAM,sBAAA,EAAwB;gBACtD,eAAe,KAAA,IAAS;YAAA,OACnB;gBACL,eAAe,KAAA,IAAS;YAC1B;QACF;QAEA,SAAS,SAAS,KAAA,EAAqB;YACjC,IAAA,MAAM,YAAA,IAAgB,MAAM,oBAAA,EAAsB;gBACpD,eAAe,GAAA,IAAO;YAAA,OACjB;gBACL,eAAe,GAAA,IAAO;YACxB;QACF;QAEA,MAAM,UAAA,CAAW,MAAM;YACf,MAAA,IAAI,uJAAI,UAAA;YAEP,OAAA,SAASC,SAAQ,QAAA,EAAkB,YAAA,EAAuB;gBAC7D,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBACnC,EAAA,cAAA,CAAe,CAAC,QAAQ;gBAE1B,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAGF,MAAM,QAAA,CAAS,MAAM;YACb,MAAA,IAAI,uJAAI,UAAA;YAEP,OAAA,SAASC,OAAM,QAAA,EAAkB,YAAA,EAAuB;gBACzD,IAAA,MAAM,kBAAA,KAAuB,MAAM;oBACnC,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBAAA,OAChC;oBACH,EAAA,mBAAA,CAAoB,cAAc,CAAC;oBACrC,EAAE,YAAA,CAAa,MAAM,MAAA,CAAO,EAAA,EAAI,CAAC;gBACnC;gBAEA,EAAE,cAAA,CAAe,QAAQ;gBAEzB,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAIF,MAAM,MAAA,CAAO,MAAM;YACX,MAAA,SAAS,uJAAI,UAAA;YAEZ,OAAA,SAASC,KAAI,MAAA,EAAgB,MAAA,EAAgB;gBAClD,MAAM,UAAU,MAAM,UAAA;gBAEtB,IAAI,WAAW,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;oBAEtF,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;oBAC9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;oBAClC,IAAA,iBAAiB,OAAO,MAAA;oBAGV,kBAAA,KAAK,GAAA,CAAM,MAAM,MAAA,CAAO,GAAA,GAAM,IAAK,KAAK,EAAA,GAAM,GAAK;oBAGrE,QAAS,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;oBACjF,MAAO,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;gBAAA,OAAA,IACtE,WAAW,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAErG,QACG,SAAA,CAAU,MAAM,MAAA,CAAO,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA,IAAS,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,WAAA,EAClF,MAAM,MAAA,CAAO,MAAA;oBAEf,MACG,SAAA,CAAU,MAAM,MAAA,CAAO,GAAA,GAAM,MAAM,MAAA,CAAO,MAAA,IAAW,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,YAAA,EAClF,MAAM,MAAA,CAAO,MAAA;gBACf,OACK;oBAEL,QAAQ,IAAA,CAAK,8EAA8E;oBAC3F,MAAM,SAAA,GAAY;gBACpB;YAAA;QACF,CAAA;QAGF,SAAS,SAAS,QAAA,EAAkB;YAE/B,IAAA,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,IAC1D,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAC5D;gBACQ,QAAA;YAAA,OACH;gBACL,QAAQ,IAAA,CAAK,qFAAqF;gBAClG,MAAM,UAAA,GAAa;YACrB;QACF;QAEA,SAAS,SAAS,UAAA,EAAoB;YACpC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,QAAQ,UAAA,EAAoB;YACnC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,sBAAsB,KAAA,EAAyB;YACtD,IAAI,CAAC,MAAM,YAAA,IAAgB,CAAC,MAAM,UAAA,EAAY;gBAC5C;YACF;YAEoB,oBAAA;YAEd,MAAA,OAAO,MAAM,UAAA,CAAW,qBAAA,CAAsB;YAC9C,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,IAAA;YACzB,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,GAAA;YAC/B,MAAM,IAAI,KAAK,KAAA;YACf,MAAM,IAAI,KAAK,MAAA;YAET,MAAA,CAAA,GAAK,IAAI,IAAK,IAAI;YACxB,MAAM,CAAA,GAAI,CAAA,CAAE,IAAI,CAAA,IAAK,IAAI;YAEzB,eAAe,GAAA,CAAI,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC,EAAE,SAAA,CAAU,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ,EAAE,SAAA;QAC7F;QAEA,SAAS,cAAc,IAAA,EAAsB;YACpC,OAAA,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,IAAI,CAAC;QACtE;QAMA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,YAAY,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC9C;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,sBAAsB,KAAK;YAC3B,WAAW,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC7C;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC3C;QAEA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,UAAU,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC1C,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;YAC1B,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC9B,WAAA,UAAA,CAAW,UAAU,UAAU;YAEtC,IAAA,WAAW,CAAA,GAAI,GAAG;gBACpB,SAAS,cAAc;YAAA,OAAA,IACd,WAAW,CAAA,GAAI,GAAG;gBAC3B,QAAQ,cAAc;YACxB;YAEA,WAAW,IAAA,CAAK,QAAQ;YACxB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,OAAO,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YACvC,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;YACpB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,iBAAiB,KAAA,EAAmB;YAC3C,sBAAsB,KAAK;YAEvB,IAAA,MAAM,MAAA,GAAS,GAAG;gBACpB,QAAQ,cAAc;YAAA,OAAA,IACb,MAAM,MAAA,GAAS,GAAG;gBAC3B,SAAS,cAAc;YACzB;YAEA,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,cAAc,KAAA,EAAsB;YAC3C,IAAI,cAAc;YAElB,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK,MAAM,IAAA,CAAK,EAAA;oBACV,IAAA,GAAG,MAAM,WAAW;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,MAAA;oBACV,IAAA,GAAG,CAAC,MAAM,WAAW;oBACX,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,IAAA;oBACV,IAAA,MAAM,WAAA,EAAa,CAAC;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,KAAA;oBACV,IAAA,CAAC,MAAM,WAAA,EAAa,CAAC;oBACX,cAAA;oBACd;YACJ;YAEA,IAAI,aAAa;gBAEf,MAAM,cAAA,CAAe;gBACrB,MAAM,MAAA,CAAO;YACf;QACF;QAEA,SAAS,yBAAyB;YAC5B,IAAA,SAAS,MAAA,IAAU,GAAG;gBACZ,YAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC/C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAErC,YAAA,GAAA,CAAI,GAAG,CAAC;YACtB;QACF;QAEA,SAAS,sBAAsB;YACzB,IAAA,SAAS,MAAA,IAAU,GAAG;gBACf,SAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC5C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAExC,SAAA,GAAA,CAAI,GAAG,CAAC;YACnB;QACF;QAEA,SAAS,wBAAwB;YAC/B,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEjC,WAAA,GAAA,CAAI,GAAG,QAAQ;QAC5B;QAEA,SAAS,2BAA2B;YAClC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,SAAA,EAA+B;QAC3C;QAEA,SAAS,8BAA8B;YACrC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,YAAA,EAAqC;QACjD;QAEA,SAAS,sBAAsB,KAAA,EAAqB;YAC9C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,UAAU,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OACjC;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBAC9B,UAAA,GAAA,CAAI,GAAG,CAAC;YACpB;YAEA,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;QAC5B;QAEA,SAAS,mBAAmB,KAAA,EAAqB;YAC3C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,OAAO,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OAC9B;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACjC,OAAA,GAAA,CAAI,GAAG,CAAC;YACjB;YAEA,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;QACtB;QAEA,SAAS,qBAAqB,KAAA,EAAqB;YAC3C,MAAA,WAAW,yBAAyB,KAAK;YACzC,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAC5B,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAClC,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEnC,SAAA,GAAA,CAAI,GAAG,QAAQ;YACb,WAAA,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,SAAS,CAAA,GAAI,WAAW,CAAA,EAAG,MAAM,SAAS,CAAC;YACtE,SAAS,WAAW,CAAC;YACrB,WAAW,IAAA,CAAK,QAAQ;QAC1B;QAEA,SAAS,wBAAwB,KAAA,EAAqB;YACpD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,SAAA,EAAW,mBAAmB,KAAK;QAC/C;QAEA,SAAS,2BAA2B,KAAA,EAAqB;YACvD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,YAAA,EAAc,sBAAsB,KAAK;QACrD;QAMA,SAAS,cAAc,KAAA,EAAqB;;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,SAAS,MAAA,KAAW,GAAG;gBACzB,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,eAAe;gBAChE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,aAAa;YAChE;YAEA,WAAW,KAAK;YAEZ,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,aAAa,KAAK;YAAA,OACb;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,cAAc,KAAA,EAAqB;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,YAAY,KAAK;YAAA,OACZ;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;;YACxC,cAAc,KAAK;YAEf,IAAA,SAAS,MAAA,KAAW,GAAG;gBACnB,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,qBAAA,CAAsB,MAAM,SAAA;gBAE9C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;gBACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YACnE;YAGA,MAAM,aAAA,CAAc,QAAQ;YAE5B,QAAQ,MAAM,IAAA;QAChB;QAEA,SAAS,YAAY,KAAA,EAAmB;YAClC,IAAA;YAEJ,OAAQ,MAAM,MAAA,EAAQ;gBACpB,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,IAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,MAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,KAAA;oBACjC;gBAEF;oBACgB,cAAA,CAAA;YAClB;YAEA,OAAQ,aAAa;gBACnB,KAAK,2JAAA,CAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B,QAAQ,MAAM,KAAA;oBACd;gBAEF,wJAAK,QAAA,CAAM,MAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAAA,OACT;wBACL,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAChB;oBACA;gBAEF,wJAAK,QAAA,CAAM,GAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAAA,OACT;wBACL,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAChB;oBACA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAmB;YACtC,IAAI,MAAM,OAAA,KAAY,OAAO;YAE7B,OAAQ,OAAO;gBACb,KAAK,MAAM,MAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B;gBAEF,KAAK,MAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B;gBAEF,KAAK,MAAM,GAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB;YACJ;QACF;QAEA,SAAS,aAAa,KAAA,EAAmB;YACnC,IAAA,MAAM,OAAA,KAAY,SAAS,MAAM,UAAA,KAAe,SAAU,UAAU,MAAM,IAAA,IAAQ,UAAU,MAAM,MAAA,EAAS;gBAC7G;YACF;YAEA,MAAM,cAAA,CAAe;YAGrB,MAAM,aAAA,CAAc,UAAU;YAE9B,iBAAiB,KAAK;YAGtB,MAAM,aAAA,CAAc,QAAQ;QAC9B;QAEA,SAAS,UAAU,KAAA,EAAsB;YACvC,IAAI,MAAM,OAAA,KAAY,SAAS,MAAM,SAAA,KAAc,OAAO;YAC1D,cAAc,KAAK;QACrB;QAEA,SAAS,aAAa,KAAA,EAAqB;YACzC,aAAa,KAAK;YAElB,OAAQ,SAAS,MAAA,EAAQ;gBACvB,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,wJAAK,QAAA,CAAM,MAAA;4BACT,IAAI,MAAM,YAAA,KAAiB,OAAO;4BACX;4BACvB,QAAQ,MAAM,YAAA;4BACd;wBAEF,wJAAK,QAAA,CAAM,GAAA;4BACT,IAAI,MAAM,SAAA,KAAc,OAAO;4BACX;4BACpB,QAAQ,MAAM,SAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,wJAAK,QAAA,CAAM,SAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;4BACpC;4BACzB,QAAQ,MAAM,eAAA;4BACd;wBAEF,wJAAK,QAAA,CAAM,YAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;4BACpC;4BAC5B,QAAQ,MAAM,kBAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;YACxC,aAAa,KAAK;YAElB,OAAQ,OAAO;gBACb,KAAK,MAAM,YAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,SAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,eAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;oBAC7D,wBAAwB,KAAK;oBAC7B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,kBAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;oBAChE,2BAA2B,KAAK;oBAChC,MAAM,MAAA,CAAO;oBACb;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;QACF;QAEA,SAAS,cAAc,KAAA,EAAc;YACnC,IAAI,MAAM,OAAA,KAAY,OAAO;YAC7B,MAAM,cAAA,CAAe;QACvB;QAEA,SAAS,WAAW,KAAA,EAAqB;YACvC,SAAS,IAAA,CAAK,KAAK;QACrB;QAEA,SAAS,cAAc,KAAA,EAAqB;YACnC,OAAA,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;gBACxC,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;oBACnC,SAAA,MAAA,CAAO,GAAG,CAAC;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,aAAa,KAAA,EAAqB;YACrC,IAAA,WAAW,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAE/C,IAAI,aAAa,KAAA,GAAW;gBAC1B,WAAW,uJAAI,UAAA;gBACE,gBAAA,CAAA,MAAM,SAAS,CAAA,GAAI;YACtC;YAEA,SAAS,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;QACvC;QAEA,SAAS,yBAAyB,KAAA,EAAqB;YAC/C,MAAA,UAAU,MAAM,SAAA,KAAc,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,GAAY,QAAA,CAAS,CAAC,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA;YAC7E,OAAA,gBAAA,CAAiB,QAAQ,SAAS,CAAA;QAC3C;QAIA,IAAA,CAAK,OAAA,GAAU,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC9C,QAAQ,UAAU;YAClB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC/C,SAAS,UAAU;YACnB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,MAAM;YACb,OAAA;QAAA;QAGJ,IAAA,CAAA,QAAA,GAAW,CAAC,aAAa;YAC5B,SAAS,QAAQ;YACjB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,YAAA,GAAe,MAAM;YACxB,OAAO,aAAa;QAAA;QAItB,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAErD,IAAA,CAAK,MAAA,CAAO;IACd;AACF;AAUA,MAAM,oBAAoB,cAAc;IACtC,YAAY,MAAA,EAAgD,UAAA,CAA0B;QACpF,KAAA,CAAM,QAAQ,UAAU;QAExB,IAAA,CAAK,kBAAA,GAAqB;QAErB,IAAA,CAAA,YAAA,CAAa,IAAA,sJAAO,QAAA,CAAM,GAAA;QAC1B,IAAA,CAAA,YAAA,CAAa,KAAA,sJAAQ,QAAA,CAAM,MAAA;QAE3B,IAAA,CAAA,OAAA,CAAQ,GAAA,sJAAM,QAAA,CAAM,GAAA;QACpB,IAAA,CAAA,OAAA,CAAQ,GAAA,sJAAM,QAAA,CAAM,YAAA;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/%40react-three/drei/core/OrbitControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\n\nconst OrbitControls = /* @__PURE__ */React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\nexport { OrbitControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,aAAa,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACrD,WAAW,EACX,MAAM,EACN,OAAO,EACP,UAAU,EACV,gBAAgB,IAAI,EACpB,YAAY,KAAK,EACjB,QAAQ,EACR,OAAO,EACP,KAAK,EACL,GAAG,WACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;8CAAE,CAAA,QAAS,MAAM,UAAU;;IACrD,MAAM,gBAAgB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;iDAAE,CAAA,QAAS,MAAM,MAAM;;IACpD,MAAM,KAAK,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,MAAM,SAAS,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;0CAAE,CAAA,QAAS,MAAM,MAAM;;IAC7C,MAAM,YAAY,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA,QAAS,MAAM,SAAS;;IACnD,MAAM,MAAM,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,MAAM,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,cAAc,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;+CAAE,CAAA,QAAS,MAAM,WAAW;;IACvD,MAAM,aAAa,UAAU;IAC7B,MAAM,iBAAiB,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU;IACtE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;2CAAE,IAAM,IAAI,+JAAA,CAAA,gBAAe,CAAC;0CAAa;QAAC;KAAW;IAClF,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE;YACP,IAAI,SAAS,OAAO,EAAE,SAAS,MAAM;QACvC;iCAAG,CAAC;IACJ,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;mCAAE;YACd,IAAI,WAAW;gBACb,SAAS,OAAO,CAAC,cAAc,OAAO,iBAAiB;YACzD;YACA,SAAS,OAAO,CAAC;YACjB;2CAAO,IAAM,KAAK,SAAS,OAAO;;QACpC;kCAAG;QAAC;QAAW;QAAgB;QAAS;QAAU;KAAW;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;mCAAE;YACd,MAAM;oDAAW,CAAA;oBACf;oBACA,IAAI,SAAS,YAAY,OAAO;oBAChC,IAAI,UAAU,SAAS;gBACzB;;YACA,MAAM;qDAAY,CAAA;oBAChB,IAAI,SAAS,QAAQ;gBACvB;;YACA,MAAM;mDAAU,CAAA;oBACd,IAAI,OAAO,MAAM;gBACnB;;YACA,SAAS,gBAAgB,CAAC,UAAU;YACpC,SAAS,gBAAgB,CAAC,SAAS;YACnC,SAAS,gBAAgB,CAAC,OAAO;YACjC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;oBACtC,SAAS,mBAAmB,CAAC,OAAO;oBACpC,SAAS,mBAAmB,CAAC,UAAU;gBACzC;;QACF;kCAAG;QAAC;QAAU;QAAS;QAAO;QAAU;QAAY;KAAU;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;mCAAE;YACd,IAAI,aAAa;gBACf,MAAM,MAAM,MAAM,QAAQ;gBAC1B,qEAAqE;gBACrE,IAAI;oBACF;gBACF;gBACA;+CAAO,IAAM,IAAI;4BACf,UAAU;wBACZ;;YACF;QACF;kCAAG;QAAC;QAAa;KAAS;IAC1B,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,KAAK;QACL,QAAQ;QACR,eAAe;IACjB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/%40react-three/drei/core/Text.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Text as Text$1, preloadFont } from 'troika-three-text';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\n\nconst Text = /* @__PURE__ */React.forwardRef(({\n  sdfGlyphSize = 64,\n  anchorX = 'center',\n  anchorY = 'middle',\n  font,\n  fontSize = 1,\n  children,\n  characters,\n  onSync,\n  ...props\n}, ref) => {\n  const invalidate = useThree(({\n    invalidate\n  }) => invalidate);\n  const [troikaMesh] = React.useState(() => new Text$1());\n  const [nodes, text] = React.useMemo(() => {\n    const n = [];\n    let t = '';\n    React.Children.forEach(children, child => {\n      if (typeof child === 'string' || typeof child === 'number') {\n        t += child;\n      } else {\n        n.push(child);\n      }\n    });\n    return [n, t];\n  }, [children]);\n  suspend(() => new Promise(res => preloadFont({\n    font,\n    characters\n  }, res)), ['troika-text', font, characters]);\n  React.useLayoutEffect(() => void troikaMesh.sync(() => {\n    invalidate();\n    if (onSync) onSync(troikaMesh);\n  }));\n  React.useEffect(() => {\n    return () => troikaMesh.dispose();\n  }, [troikaMesh]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: troikaMesh,\n    ref: ref,\n    font: font,\n    text: text,\n    anchorX: anchorX,\n    anchorY: anchorY,\n    fontSize: fontSize,\n    sdfGlyphSize: sdfGlyphSize\n  }, props), nodes);\n});\n\nexport { Text };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,eAAe,EAAE,EACjB,UAAU,QAAQ,EAClB,UAAU,QAAQ,EAClB,IAAI,EACJ,WAAW,CAAC,EACZ,QAAQ,EACR,UAAU,EACV,MAAM,EACN,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;qCAAE,CAAC,EAC3B,UAAU,EACX,GAAK;;IACN,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD;yBAAE,IAAM,IAAI,oLAAA,CAAA,OAAM;;IACpD,MAAM,CAAC,OAAO,KAAK,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;wBAAE;YAClC,MAAM,IAAI,EAAE;YACZ,IAAI,IAAI;YACR,6JAAA,CAAA,WAAc,CAAC,OAAO,CAAC;gCAAU,CAAA;oBAC/B,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;wBAC1D,KAAK;oBACP,OAAO;wBACL,EAAE,IAAI,CAAC;oBACT;gBACF;;YACA,OAAO;gBAAC;gBAAG;aAAE;QACf;uBAAG;QAAC;KAAS;IACb,CAAA,GAAA,4IAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,QAAQ,CAAA,MAAO,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;gBAC3C;gBACA;YACF,GAAG,OAAO;QAAC;QAAe;QAAM;KAAW;IAC3C,CAAA,GAAA,6JAAA,CAAA,kBAAqB,AAAD;gCAAE,IAAM,KAAK,WAAW,IAAI;wCAAC;oBAC/C;oBACA,IAAI,QAAQ,OAAO;gBACrB;;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;0BAAE;YACd;kCAAO,IAAM,WAAW,OAAO;;QACjC;yBAAG;QAAC;KAAW;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,QAAQ;QACR,KAAK;QACL,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,cAAc;IAChB,GAAG,QAAQ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/troika-worker-utils/dist/troika-worker-utils.esm.js"], "sourcesContent": ["/**\n * Main content for the worker that handles the loading and execution of\n * modules within it.\n */\nfunction workerBootstrap() {\n  var modules = Object.create(null);\n\n  // Handle messages for registering a module\n  function registerModule(ref, callback) {\n    var id = ref.id;\n    var name = ref.name;\n    var dependencies = ref.dependencies; if ( dependencies === void 0 ) dependencies = [];\n    var init = ref.init; if ( init === void 0 ) init = function(){};\n    var getTransferables = ref.getTransferables; if ( getTransferables === void 0 ) getTransferables = null;\n\n    // Only register once\n    if (modules[id]) { return }\n\n    try {\n      // If any dependencies are modules, ensure they're registered and grab their value\n      dependencies = dependencies.map(function (dep) {\n        if (dep && dep.isWorkerModule) {\n          registerModule(dep, function (depResult) {\n            if (depResult instanceof Error) { throw depResult }\n          });\n          dep = modules[dep.id].value;\n        }\n        return dep\n      });\n\n      // Rehydrate functions\n      init = rehydrate((\"<\" + name + \">.init\"), init);\n      if (getTransferables) {\n        getTransferables = rehydrate((\"<\" + name + \">.getTransferables\"), getTransferables);\n      }\n\n      // Initialize the module and store its value\n      var value = null;\n      if (typeof init === 'function') {\n        value = init.apply(void 0, dependencies);\n      } else {\n        console.error('worker module init function failed to rehydrate');\n      }\n      modules[id] = {\n        id: id,\n        value: value,\n        getTransferables: getTransferables\n      };\n      callback(value);\n    } catch(err) {\n      if (!(err && err.noLog)) {\n        console.error(err);\n      }\n      callback(err);\n    }\n  }\n\n  // Handle messages for calling a registered module's result function\n  function callModule(ref, callback) {\n    var ref$1;\n\n    var id = ref.id;\n    var args = ref.args;\n    if (!modules[id] || typeof modules[id].value !== 'function') {\n      callback(new Error((\"Worker module \" + id + \": not found or its 'init' did not return a function\")));\n    }\n    try {\n      var result = (ref$1 = modules[id]).value.apply(ref$1, args);\n      if (result && typeof result.then === 'function') {\n        result.then(handleResult, function (rej) { return callback(rej instanceof Error ? rej : new Error('' + rej)); });\n      } else {\n        handleResult(result);\n      }\n    } catch(err) {\n      callback(err);\n    }\n    function handleResult(result) {\n      try {\n        var tx = modules[id].getTransferables && modules[id].getTransferables(result);\n        if (!tx || !Array.isArray(tx) || !tx.length) {\n          tx = undefined; //postMessage is very picky about not passing null or empty transferables\n        }\n        callback(result, tx);\n      } catch(err) {\n        console.error(err);\n        callback(err);\n      }\n    }\n  }\n\n  function rehydrate(name, str) {\n    var result = void 0;\n    self.troikaDefine = function (r) { return result = r; };\n    var url = URL.createObjectURL(\n      new Blob(\n        [(\"/** \" + (name.replace(/\\*/g, '')) + \" **/\\n\\ntroikaDefine(\\n\" + str + \"\\n)\")],\n        {type: 'application/javascript'}\n      )\n    );\n    try {\n      importScripts(url);\n    } catch(err) {\n      console.error(err);\n    }\n    URL.revokeObjectURL(url);\n    delete self.troikaDefine;\n    return result\n  }\n\n  // Handler for all messages within the worker\n  self.addEventListener('message', function (e) {\n    var ref = e.data;\n    var messageId = ref.messageId;\n    var action = ref.action;\n    var data = ref.data;\n    try {\n      // Module registration\n      if (action === 'registerModule') {\n        registerModule(data, function (result) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: {isCallable: typeof result === 'function'}\n            });\n          }\n        });\n      }\n      // Invocation\n      if (action === 'callModule') {\n        callModule(data, function (result, transferables) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: result\n            }, transferables || undefined);\n          }\n        });\n      }\n    } catch(err) {\n      postMessage({\n        messageId: messageId,\n        success: false,\n        error: err.stack\n      });\n    }\n  });\n}\n\n/**\n * Fallback for `defineWorkerModule` that behaves identically but runs in the main\n * thread, for when the execution environment doesn't support web workers or they\n * are disallowed due to e.g. CSP security restrictions.\n */\nfunction defineMainThreadModule(options) {\n  var moduleFunc = function() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    return moduleFunc._getInitResult().then(function (initResult) {\n      if (typeof initResult === 'function') {\n        return initResult.apply(void 0, args)\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function')\n      }\n    })\n  };\n  moduleFunc._getInitResult = function() {\n    // We can ignore getTransferables in main thread. TODO workerId?\n    var dependencies = options.dependencies;\n    var init = options.init;\n\n    // Resolve dependencies\n    dependencies = Array.isArray(dependencies) ? dependencies.map(function (dep) {\n      if (dep) {\n        // If it's a worker module, use its main thread impl\n        dep = dep.onMainThread || dep;\n        // If it's a main thread worker module, use its init return value\n        if (dep._getInitResult) {\n          dep = dep._getInitResult();\n        }\n      }\n      return dep\n    }) : [];\n\n    // Invoke init with the resolved dependencies\n    var initPromise = Promise.all(dependencies).then(function (deps) {\n      return init.apply(null, deps)\n    });\n\n    // Cache the resolved promise for subsequent calls\n    moduleFunc._getInitResult = function () { return initPromise; };\n\n    return initPromise\n  };\n  return moduleFunc\n}\n\nvar supportsWorkers = function () {\n  var supported = false;\n\n  // Only attempt worker initialization in browsers; elsewhere it would just be\n  // noise e.g. loading into a Node environment for SSR.\n  if (typeof window !== 'undefined' && typeof window.document !== 'undefined') {\n    try {\n      // TODO additional checks for things like importScripts within the worker?\n      //  Would need to be an async check.\n      var worker = new Worker(\n        URL.createObjectURL(new Blob([''], { type: 'application/javascript' }))\n      );\n      worker.terminate();\n      supported = true;\n    } catch (err) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') ; else {\n        console.log(\n          (\"Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: [\" + (err.message) + \"]\")\n        );\n      }\n    }\n  }\n\n  // Cached result\n  supportsWorkers = function () { return supported; };\n  return supported\n};\n\nvar _workerModuleId = 0;\nvar _messageId = 0;\nvar _allowInitAsString = false;\nvar workers = Object.create(null);\nvar registeredModules = Object.create(null); //workerId -> Set<unregisterFn>\nvar openRequests = Object.create(null);\n\n\n/**\n * Define a module of code that will be executed with a web worker. This provides a simple\n * interface for moving chunks of logic off the main thread, and managing their dependencies\n * among one another.\n *\n * @param {object} options\n * @param {function} options.init\n * @param {array} [options.dependencies]\n * @param {function} [options.getTransferables]\n * @param {string} [options.name]\n * @param {string} [options.workerId]\n * @return {function(...[*]): {then}}\n */\nfunction defineWorkerModule(options) {\n  if ((!options || typeof options.init !== 'function') && !_allowInitAsString) {\n    throw new Error('requires `options.init` function')\n  }\n  var dependencies = options.dependencies;\n  var init = options.init;\n  var getTransferables = options.getTransferables;\n  var workerId = options.workerId;\n\n  var onMainThread = defineMainThreadModule(options);\n\n  if (workerId == null) {\n    workerId = '#default';\n  }\n  var id = \"workerModule\" + (++_workerModuleId);\n  var name = options.name || id;\n  var registrationPromise = null;\n\n  dependencies = dependencies && dependencies.map(function (dep) {\n    // Wrap raw functions as worker modules with no dependencies\n    if (typeof dep === 'function' && !dep.workerModuleData) {\n      _allowInitAsString = true;\n      dep = defineWorkerModule({\n        workerId: workerId,\n        name: (\"<\" + name + \"> function dependency: \" + (dep.name)),\n        init: (\"function(){return (\\n\" + (stringifyFunction(dep)) + \"\\n)}\")\n      });\n      _allowInitAsString = false;\n    }\n    // Grab postable data for worker modules\n    if (dep && dep.workerModuleData) {\n      dep = dep.workerModuleData;\n    }\n    return dep\n  });\n\n  function moduleFunc() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    if (!supportsWorkers()) {\n      return onMainThread.apply(void 0, args)\n    }\n\n    // Register this module if needed\n    if (!registrationPromise) {\n      registrationPromise = callWorker(workerId,'registerModule', moduleFunc.workerModuleData);\n      var unregister = function () {\n        registrationPromise = null;\n        registeredModules[workerId].delete(unregister);\n      }\n      ;(registeredModules[workerId] || (registeredModules[workerId] = new Set())).add(unregister);\n    }\n\n    // Invoke the module, returning a promise\n    return registrationPromise.then(function (ref) {\n      var isCallable = ref.isCallable;\n\n      if (isCallable) {\n        return callWorker(workerId,'callModule', {id: id, args: args})\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function')\n      }\n    })\n  }\n  moduleFunc.workerModuleData = {\n    isWorkerModule: true,\n    id: id,\n    name: name,\n    dependencies: dependencies,\n    init: stringifyFunction(init),\n    getTransferables: getTransferables && stringifyFunction(getTransferables)\n  };\n\n  moduleFunc.onMainThread = onMainThread;\n\n  return moduleFunc\n}\n\n/**\n * Terminate an active Worker by a workerId that was passed to defineWorkerModule.\n * This only terminates the Worker itself; the worker module will remain available\n * and if you call it again its Worker will be respawned.\n * @param {string} workerId\n */\nfunction terminateWorker(workerId) {\n  // Unregister all modules that were registered in that worker\n  if (registeredModules[workerId]) {\n    registeredModules[workerId].forEach(function (unregister) {\n      unregister();\n    });\n  }\n  // Terminate the Worker object\n  if (workers[workerId]) {\n    workers[workerId].terminate();\n    delete workers[workerId];\n  }\n}\n\n/**\n * Stringifies a function into a form that can be deserialized in the worker\n * @param fn\n */\nfunction stringifyFunction(fn) {\n  var str = fn.toString();\n  // If it was defined in object method/property format, it needs to be modified\n  if (!/^function/.test(str) && /^\\w+\\s*\\(/.test(str)) {\n    str = 'function ' + str;\n  }\n  return str\n}\n\n\nfunction getWorker(workerId) {\n  var worker = workers[workerId];\n  if (!worker) {\n    // Bootstrap the worker's content\n    var bootstrap = stringifyFunction(workerBootstrap);\n\n    // Create the worker from the bootstrap function content\n    worker = workers[workerId] = new Worker(\n      URL.createObjectURL(\n        new Blob(\n          [(\"/** Worker Module Bootstrap: \" + (workerId.replace(/\\*/g, '')) + \" **/\\n\\n;(\" + bootstrap + \")()\")],\n          {type: 'application/javascript'}\n        )\n      )\n    );\n\n    // Single handler for response messages from the worker\n    worker.onmessage = function (e) {\n      var response = e.data;\n      var msgId = response.messageId;\n      var callback = openRequests[msgId];\n      if (!callback) {\n        throw new Error('WorkerModule response with empty or unknown messageId')\n      }\n      delete openRequests[msgId];\n      callback(response);\n    };\n  }\n  return worker\n}\n\n// Issue a call to the worker with a callback to handle the response\nfunction callWorker(workerId, action, data) {\n  return new Promise(function (resolve, reject) {\n    var messageId = ++_messageId;\n    openRequests[messageId] = function (response) {\n      if (response.success) {\n        resolve(response.result);\n      } else {\n        reject(new Error((\"Error in worker \" + action + \" call: \" + (response.error))));\n      }\n    };\n    getWorker(workerId).postMessage({\n      messageId: messageId,\n      action: action,\n      data: data\n    });\n  })\n}\n\nexport { defineWorkerModule, stringifyFunction, terminateWorker };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AA+NgB;AA9NjB,SAAS;IACP,IAAI,UAAU,OAAO,MAAM,CAAC;IAE5B,2CAA2C;IAC3C,SAAS,eAAe,GAAG,EAAE,QAAQ;QACnC,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,OAAO,IAAI,IAAI;QACnB,IAAI,eAAe,IAAI,YAAY;QAAE,IAAK,iBAAiB,KAAK,GAAI,eAAe,EAAE;QACrF,IAAI,OAAO,IAAI,IAAI;QAAE,IAAK,SAAS,KAAK,GAAI,OAAO,YAAW;QAC9D,IAAI,mBAAmB,IAAI,gBAAgB;QAAE,IAAK,qBAAqB,KAAK,GAAI,mBAAmB;QAEnG,qBAAqB;QACrB,IAAI,OAAO,CAAC,GAAG,EAAE;YAAE;QAAO;QAE1B,IAAI;YACF,kFAAkF;YAClF,eAAe,aAAa,GAAG,CAAC,SAAU,GAAG;gBAC3C,IAAI,OAAO,IAAI,cAAc,EAAE;oBAC7B,eAAe,KAAK,SAAU,SAAS;wBACrC,IAAI,qBAAqB,OAAO;4BAAE,MAAM;wBAAU;oBACpD;oBACA,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK;gBAC7B;gBACA,OAAO;YACT;YAEA,sBAAsB;YACtB,OAAO,UAAW,MAAM,OAAO,UAAW;YAC1C,IAAI,kBAAkB;gBACpB,mBAAmB,UAAW,MAAM,OAAO,sBAAuB;YACpE;YAEA,4CAA4C;YAC5C,IAAI,QAAQ;YACZ,IAAI,OAAO,SAAS,YAAY;gBAC9B,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG;YAC7B,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;YACA,OAAO,CAAC,GAAG,GAAG;gBACZ,IAAI;gBACJ,OAAO;gBACP,kBAAkB;YACpB;YACA,SAAS;QACX,EAAE,OAAM,KAAK;YACX,IAAI,CAAC,CAAC,OAAO,IAAI,KAAK,GAAG;gBACvB,QAAQ,KAAK,CAAC;YAChB;YACA,SAAS;QACX;IACF;IAEA,oEAAoE;IACpE,SAAS,WAAW,GAAG,EAAE,QAAQ;QAC/B,IAAI;QAEJ,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,OAAO,IAAI,IAAI;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,YAAY;YAC3D,SAAS,IAAI,MAAO,mBAAmB,KAAK;QAC9C;QACA,IAAI;YACF,IAAI,SAAS,CAAC,QAAQ,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO;YACtD,IAAI,UAAU,OAAO,OAAO,IAAI,KAAK,YAAY;gBAC/C,OAAO,IAAI,CAAC,cAAc,SAAU,GAAG;oBAAI,OAAO,SAAS,eAAe,QAAQ,MAAM,IAAI,MAAM,KAAK;gBAAO;YAChH,OAAO;gBACL,aAAa;YACf;QACF,EAAE,OAAM,KAAK;YACX,SAAS;QACX;QACA,SAAS,aAAa,MAAM;YAC1B,IAAI;gBACF,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACtE,IAAI,CAAC,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,EAAE;oBAC3C,KAAK,WAAW,yEAAyE;gBAC3F;gBACA,SAAS,QAAQ;YACnB,EAAE,OAAM,KAAK;gBACX,QAAQ,KAAK,CAAC;gBACd,SAAS;YACX;QACF;IACF;IAEA,SAAS,UAAU,IAAI,EAAE,GAAG;QAC1B,IAAI,SAAS,KAAK;QAClB,KAAK,YAAY,GAAG,SAAU,CAAC;YAAI,OAAO,SAAS;QAAG;QACtD,IAAI,MAAM,IAAI,eAAe,CAC3B,IAAI,KACF;YAAE,SAAU,KAAK,OAAO,CAAC,OAAO,MAAO,4BAA4B,MAAM;SAAO,EAChF;YAAC,MAAM;QAAwB;QAGnC,IAAI;YACF,cAAc;QAChB,EAAE,OAAM,KAAK;YACX,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI,eAAe,CAAC;QACpB,OAAO,KAAK,YAAY;QACxB,OAAO;IACT;IAEA,6CAA6C;IAC7C,KAAK,gBAAgB,CAAC,WAAW,SAAU,CAAC;QAC1C,IAAI,MAAM,EAAE,IAAI;QAChB,IAAI,YAAY,IAAI,SAAS;QAC7B,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,OAAO,IAAI,IAAI;QACnB,IAAI;YACF,sBAAsB;YACtB,IAAI,WAAW,kBAAkB;gBAC/B,eAAe,MAAM,SAAU,MAAM;oBACnC,IAAI,kBAAkB,OAAO;wBAC3B,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,OAAO,OAAO,OAAO;wBACvB;oBACF,OAAO;wBACL,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,QAAQ;gCAAC,YAAY,OAAO,WAAW;4BAAU;wBACnD;oBACF;gBACF;YACF;YACA,aAAa;YACb,IAAI,WAAW,cAAc;gBAC3B,WAAW,MAAM,SAAU,MAAM,EAAE,aAAa;oBAC9C,IAAI,kBAAkB,OAAO;wBAC3B,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,OAAO,OAAO,OAAO;wBACvB;oBACF,OAAO;wBACL,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,QAAQ;wBACV,GAAG,iBAAiB;oBACtB;gBACF;YACF;QACF,EAAE,OAAM,KAAK;YACX,YAAY;gBACV,WAAW;gBACX,SAAS;gBACT,OAAO,IAAI,KAAK;YAClB;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,OAAO;IACrC,IAAI,aAAa;QACf,IAAI,OAAO,EAAE,EAAE,MAAM,UAAU,MAAM;QACrC,MAAQ,MAAQ,IAAI,CAAE,IAAK,GAAG,SAAS,CAAE,IAAK;QAE9C,OAAO,WAAW,cAAc,GAAG,IAAI,CAAC,SAAU,UAAU;YAC1D,IAAI,OAAO,eAAe,YAAY;gBACpC,OAAO,WAAW,KAAK,CAAC,KAAK,GAAG;YAClC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IACA,WAAW,cAAc,GAAG;QAC1B,gEAAgE;QAChE,IAAI,eAAe,QAAQ,YAAY;QACvC,IAAI,OAAO,QAAQ,IAAI;QAEvB,uBAAuB;QACvB,eAAe,MAAM,OAAO,CAAC,gBAAgB,aAAa,GAAG,CAAC,SAAU,GAAG;YACzE,IAAI,KAAK;gBACP,oDAAoD;gBACpD,MAAM,IAAI,YAAY,IAAI;gBAC1B,iEAAiE;gBACjE,IAAI,IAAI,cAAc,EAAE;oBACtB,MAAM,IAAI,cAAc;gBAC1B;YACF;YACA,OAAO;QACT,KAAK,EAAE;QAEP,6CAA6C;QAC7C,IAAI,cAAc,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC,SAAU,IAAI;YAC7D,OAAO,KAAK,KAAK,CAAC,MAAM;QAC1B;QAEA,kDAAkD;QAClD,WAAW,cAAc,GAAG;YAAc,OAAO;QAAa;QAE9D,OAAO;IACT;IACA,OAAO;AACT;AAEA,IAAI,kBAAkB;IACpB,IAAI,YAAY;IAEhB,6EAA6E;IAC7E,sDAAsD;IACtD,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,aAAa;QAC3E,IAAI;YACF,0EAA0E;YAC1E,oCAAoC;YACpC,IAAI,SAAS,IAAI,OACf,IAAI,eAAe,CAAC,IAAI,KAAK;gBAAC;aAAG,EAAE;gBAAE,MAAM;YAAyB;YAEtE,OAAO,SAAS;YAChB,YAAY;QACd,EAAE,OAAO,KAAK;YACZ;iBAA8E;gBAC5E,QAAQ,GAAG,CACR,wGAAyG,IAAI,OAAO,GAAI;YAE7H;QACF;IACF;IAEA,gBAAgB;IAChB,kBAAkB;QAAc,OAAO;IAAW;IAClD,OAAO;AACT;AAEA,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,UAAU,OAAO,MAAM,CAAC;AAC5B,IAAI,oBAAoB,OAAO,MAAM,CAAC,OAAO,+BAA+B;AAC5E,IAAI,eAAe,OAAO,MAAM,CAAC;AAGjC;;;;;;;;;;;;CAYC,GACD,SAAS,mBAAmB,OAAO;IACjC,IAAI,CAAC,CAAC,WAAW,OAAO,QAAQ,IAAI,KAAK,UAAU,KAAK,CAAC,oBAAoB;QAC3E,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,eAAe,QAAQ,YAAY;IACvC,IAAI,OAAO,QAAQ,IAAI;IACvB,IAAI,mBAAmB,QAAQ,gBAAgB;IAC/C,IAAI,WAAW,QAAQ,QAAQ;IAE/B,IAAI,eAAe,uBAAuB;IAE1C,IAAI,YAAY,MAAM;QACpB,WAAW;IACb;IACA,IAAI,KAAK,iBAAkB,EAAE;IAC7B,IAAI,OAAO,QAAQ,IAAI,IAAI;IAC3B,IAAI,sBAAsB;IAE1B,eAAe,gBAAgB,aAAa,GAAG,CAAC,SAAU,GAAG;QAC3D,4DAA4D;QAC5D,IAAI,OAAO,QAAQ,cAAc,CAAC,IAAI,gBAAgB,EAAE;YACtD,qBAAqB;YACrB,MAAM,mBAAmB;gBACvB,UAAU;gBACV,MAAO,MAAM,OAAO,4BAA6B,IAAI,IAAI;gBACzD,MAAO,0BAA2B,kBAAkB,OAAQ;YAC9D;YACA,qBAAqB;QACvB;QACA,wCAAwC;QACxC,IAAI,OAAO,IAAI,gBAAgB,EAAE;YAC/B,MAAM,IAAI,gBAAgB;QAC5B;QACA,OAAO;IACT;IAEA,SAAS;QACP,IAAI,OAAO,EAAE,EAAE,MAAM,UAAU,MAAM;QACrC,MAAQ,MAAQ,IAAI,CAAE,IAAK,GAAG,SAAS,CAAE,IAAK;QAE9C,IAAI,CAAC,mBAAmB;YACtB,OAAO,aAAa,KAAK,CAAC,KAAK,GAAG;QACpC;QAEA,iCAAiC;QACjC,IAAI,CAAC,qBAAqB;YACxB,sBAAsB,WAAW,UAAS,kBAAkB,WAAW,gBAAgB;YACvF,IAAI,aAAa;gBACf,sBAAsB;gBACtB,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC;YACrC;YACC,CAAC,iBAAiB,CAAC,SAAS,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC;QAClF;QAEA,yCAAyC;QACzC,OAAO,oBAAoB,IAAI,CAAC,SAAU,GAAG;YAC3C,IAAI,aAAa,IAAI,UAAU;YAE/B,IAAI,YAAY;gBACd,OAAO,WAAW,UAAS,cAAc;oBAAC,IAAI;oBAAI,MAAM;gBAAI;YAC9D,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IACA,WAAW,gBAAgB,GAAG;QAC5B,gBAAgB;QAChB,IAAI;QACJ,MAAM;QACN,cAAc;QACd,MAAM,kBAAkB;QACxB,kBAAkB,oBAAoB,kBAAkB;IAC1D;IAEA,WAAW,YAAY,GAAG;IAE1B,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,QAAQ;IAC/B,6DAA6D;IAC7D,IAAI,iBAAiB,CAAC,SAAS,EAAE;QAC/B,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,SAAU,UAAU;YACtD;QACF;IACF;IACA,8BAA8B;IAC9B,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,OAAO,CAAC,SAAS,CAAC,SAAS;QAC3B,OAAO,OAAO,CAAC,SAAS;IAC1B;AACF;AAEA;;;CAGC,GACD,SAAS,kBAAkB,EAAE;IAC3B,IAAI,MAAM,GAAG,QAAQ;IACrB,8EAA8E;IAC9E,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,MAAM;QACnD,MAAM,cAAc;IACtB;IACA,OAAO;AACT;AAGA,SAAS,UAAU,QAAQ;IACzB,IAAI,SAAS,OAAO,CAAC,SAAS;IAC9B,IAAI,CAAC,QAAQ;QACX,iCAAiC;QACjC,IAAI,YAAY,kBAAkB;QAElC,wDAAwD;QACxD,SAAS,OAAO,CAAC,SAAS,GAAG,IAAI,OAC/B,IAAI,eAAe,CACjB,IAAI,KACF;YAAE,kCAAmC,SAAS,OAAO,CAAC,OAAO,MAAO,eAAe,YAAY;SAAO,EACtG;YAAC,MAAM;QAAwB;QAKrC,uDAAuD;QACvD,OAAO,SAAS,GAAG,SAAU,CAAC;YAC5B,IAAI,WAAW,EAAE,IAAI;YACrB,IAAI,QAAQ,SAAS,SAAS;YAC9B,IAAI,WAAW,YAAY,CAAC,MAAM;YAClC,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,YAAY,CAAC,MAAM;YAC1B,SAAS;QACX;IACF;IACA,OAAO;AACT;AAEA,oEAAoE;AACpE,SAAS,WAAW,QAAQ,EAAE,MAAM,EAAE,IAAI;IACxC,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC1C,IAAI,YAAY,EAAE;QAClB,YAAY,CAAC,UAAU,GAAG,SAAU,QAAQ;YAC1C,IAAI,SAAS,OAAO,EAAE;gBACpB,QAAQ,SAAS,MAAM;YACzB,OAAO;gBACL,OAAO,IAAI,MAAO,qBAAqB,SAAS,YAAa,SAAS,KAAK;YAC7E;QACF;QACA,UAAU,UAAU,WAAW,CAAC;YAC9B,WAAW;YACX,QAAQ;YACR,MAAM;QACR;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/webgl-sdf-generator/dist/webgl-sdf-generator.mjs"], "sourcesContent": ["function SDFGenerator() {\nvar exports = (function (exports) {\n\n  /**\n   * Find the point on a quadratic bezier curve at t where t is in the range [0, 1]\n   */\n  function pointOnQuadraticBezier (x0, y0, x1, y1, x2, y2, t, pointOut) {\n    var t2 = 1 - t;\n    pointOut.x = t2 * t2 * x0 + 2 * t2 * t * x1 + t * t * x2;\n    pointOut.y = t2 * t2 * y0 + 2 * t2 * t * y1 + t * t * y2;\n  }\n\n  /**\n   * Find the point on a cubic bezier curve at t where t is in the range [0, 1]\n   */\n  function pointOnCubicBezier (x0, y0, x1, y1, x2, y2, x3, y3, t, pointOut) {\n    var t2 = 1 - t;\n    pointOut.x = t2 * t2 * t2 * x0 + 3 * t2 * t2 * t * x1 + 3 * t2 * t * t * x2 + t * t * t * x3;\n    pointOut.y = t2 * t2 * t2 * y0 + 3 * t2 * t2 * t * y1 + 3 * t2 * t * t * y2 + t * t * t * y3;\n  }\n\n  /**\n   * Parse a path string into its constituent line/curve commands, invoking a callback for each.\n   * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n   * @param {function(\n   *   command: 'L'|'Q'|'C',\n   *   startX: number,\n   *   startY: number,\n   *   endX: number,\n   *   endY: number,\n   *   ctrl1X?: number,\n   *   ctrl1Y?: number,\n   *   ctrl2X?: number,\n   *   ctrl2Y?: number\n   * )} commandCallback - A callback function that will be called once for each parsed path command, passing the\n   *                      command identifier (only L/Q/C commands) and its numeric arguments.\n   */\n  function forEachPathCommand(pathString, commandCallback) {\n    var segmentRE = /([MLQCZ])([^MLQCZ]*)/g;\n    var match, firstX, firstY, prevX, prevY;\n    while ((match = segmentRE.exec(pathString))) {\n      var args = match[2]\n        .replace(/^\\s*|\\s*$/g, '')\n        .split(/[,\\s]+/)\n        .map(function (v) { return parseFloat(v); });\n      switch (match[1]) {\n        case 'M':\n          prevX = firstX = args[0];\n          prevY = firstY = args[1];\n          break\n        case 'L':\n          if (args[0] !== prevX || args[1] !== prevY) { // yup, some fonts have zero-length line commands\n            commandCallback('L', prevX, prevY, (prevX = args[0]), (prevY = args[1]));\n          }\n          break\n        case 'Q': {\n          commandCallback('Q', prevX, prevY, (prevX = args[2]), (prevY = args[3]), args[0], args[1]);\n          break\n        }\n        case 'C': {\n          commandCallback('C', prevX, prevY, (prevX = args[4]), (prevY = args[5]), args[0], args[1], args[2], args[3]);\n          break\n        }\n        case 'Z':\n          if (prevX !== firstX || prevY !== firstY) {\n            commandCallback('L', prevX, prevY, firstX, firstY);\n          }\n          break\n      }\n    }\n  }\n\n  /**\n   * Convert a path string to a series of straight line segments\n   * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n   * @param {function(x1:number, y1:number, x2:number, y2:number)} segmentCallback - A callback\n   *        function that will be called once for every line segment\n   * @param {number} [curvePoints] - How many straight line segments to use when approximating a\n   *        bezier curve in the path. Defaults to 16.\n   */\n  function pathToLineSegments (pathString, segmentCallback, curvePoints) {\n    if ( curvePoints === void 0 ) curvePoints = 16;\n\n    var tempPoint = { x: 0, y: 0 };\n    forEachPathCommand(pathString, function (command, startX, startY, endX, endY, ctrl1X, ctrl1Y, ctrl2X, ctrl2Y) {\n      switch (command) {\n        case 'L':\n          segmentCallback(startX, startY, endX, endY);\n          break\n        case 'Q': {\n          var prevCurveX = startX;\n          var prevCurveY = startY;\n          for (var i = 1; i < curvePoints; i++) {\n            pointOnQuadraticBezier(\n              startX, startY,\n              ctrl1X, ctrl1Y,\n              endX, endY,\n              i / (curvePoints - 1),\n              tempPoint\n            );\n            segmentCallback(prevCurveX, prevCurveY, tempPoint.x, tempPoint.y);\n            prevCurveX = tempPoint.x;\n            prevCurveY = tempPoint.y;\n          }\n          break\n        }\n        case 'C': {\n          var prevCurveX$1 = startX;\n          var prevCurveY$1 = startY;\n          for (var i$1 = 1; i$1 < curvePoints; i$1++) {\n            pointOnCubicBezier(\n              startX, startY,\n              ctrl1X, ctrl1Y,\n              ctrl2X, ctrl2Y,\n              endX, endY,\n              i$1 / (curvePoints - 1),\n              tempPoint\n            );\n            segmentCallback(prevCurveX$1, prevCurveY$1, tempPoint.x, tempPoint.y);\n            prevCurveX$1 = tempPoint.x;\n            prevCurveY$1 = tempPoint.y;\n          }\n          break\n        }\n      }\n    });\n  }\n\n  var viewportQuadVertex = \"precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n\n  var copyTexFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}\";\n\n  var cache = new WeakMap();\n\n  var glContextParams = {\n    premultipliedAlpha: false,\n    preserveDrawingBuffer: true,\n    antialias: false,\n    depth: false,\n  };\n\n  /**\n   * This is a little helper library for WebGL. It assists with state management for a GL context.\n   * It's pretty tightly wrapped to the needs of this package, not very general-purpose.\n   *\n   * @param { WebGLRenderingContext | HTMLCanvasElement | OffscreenCanvas } glOrCanvas - the GL context to wrap\n   * @param { ({gl, getExtension, withProgram, withTexture, withTextureFramebuffer, handleContextLoss}) => void } callback\n   */\n  function withWebGLContext (glOrCanvas, callback) {\n    var gl = glOrCanvas.getContext ? glOrCanvas.getContext('webgl', glContextParams) : glOrCanvas;\n    var wrapper = cache.get(gl);\n    if (!wrapper) {\n      var isWebGL2 = typeof WebGL2RenderingContext !== 'undefined' && gl instanceof WebGL2RenderingContext;\n      var extensions = {};\n      var programs = {};\n      var textures = {};\n      var textureUnit = -1;\n      var framebufferStack = [];\n\n      gl.canvas.addEventListener('webglcontextlost', function (e) {\n        handleContextLoss();\n        e.preventDefault();\n      }, false);\n\n      function getExtension (name) {\n        var ext = extensions[name];\n        if (!ext) {\n          ext = extensions[name] = gl.getExtension(name);\n          if (!ext) {\n            throw new Error((name + \" not supported\"))\n          }\n        }\n        return ext\n      }\n\n      function compileShader (src, type) {\n        var shader = gl.createShader(type);\n        gl.shaderSource(shader, src);\n        gl.compileShader(shader);\n        // const status = gl.getShaderParameter(shader, gl.COMPILE_STATUS)\n        // if (!status && !gl.isContextLost()) {\n        //   throw new Error(gl.getShaderInfoLog(shader).trim())\n        // }\n        return shader\n      }\n\n      function withProgram (name, vert, frag, func) {\n        if (!programs[name]) {\n          var attributes = {};\n          var uniforms = {};\n          var program = gl.createProgram();\n          gl.attachShader(program, compileShader(vert, gl.VERTEX_SHADER));\n          gl.attachShader(program, compileShader(frag, gl.FRAGMENT_SHADER));\n          gl.linkProgram(program);\n\n          programs[name] = {\n            program: program,\n            transaction: function transaction (func) {\n              gl.useProgram(program);\n              func({\n                setUniform: function setUniform (type, name) {\n                  var values = [], len = arguments.length - 2;\n                  while ( len-- > 0 ) values[ len ] = arguments[ len + 2 ];\n\n                  var uniformLoc = uniforms[name] || (uniforms[name] = gl.getUniformLocation(program, name));\n                  gl[(\"uniform\" + type)].apply(gl, [ uniformLoc ].concat( values ));\n                },\n\n                setAttribute: function setAttribute (name, size, usage, instancingDivisor, data) {\n                  var attr = attributes[name];\n                  if (!attr) {\n                    attr = attributes[name] = {\n                      buf: gl.createBuffer(), // TODO should we destroy our buffers?\n                      loc: gl.getAttribLocation(program, name),\n                      data: null\n                    };\n                  }\n                  gl.bindBuffer(gl.ARRAY_BUFFER, attr.buf);\n                  gl.vertexAttribPointer(attr.loc, size, gl.FLOAT, false, 0, 0);\n                  gl.enableVertexAttribArray(attr.loc);\n                  if (isWebGL2) {\n                    gl.vertexAttribDivisor(attr.loc, instancingDivisor);\n                  } else {\n                    getExtension('ANGLE_instanced_arrays').vertexAttribDivisorANGLE(attr.loc, instancingDivisor);\n                  }\n                  if (data !== attr.data) {\n                    gl.bufferData(gl.ARRAY_BUFFER, data, usage);\n                    attr.data = data;\n                  }\n                }\n              });\n            }\n          };\n        }\n\n        programs[name].transaction(func);\n      }\n\n      function withTexture (name, func) {\n        textureUnit++;\n        try {\n          gl.activeTexture(gl.TEXTURE0 + textureUnit);\n          var texture = textures[name];\n          if (!texture) {\n            texture = textures[name] = gl.createTexture();\n            gl.bindTexture(gl.TEXTURE_2D, texture);\n            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\n            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\n          }\n          gl.bindTexture(gl.TEXTURE_2D, texture);\n          func(texture, textureUnit);\n        } finally {\n          textureUnit--;\n        }\n      }\n\n      function withTextureFramebuffer (texture, textureUnit, func) {\n        var framebuffer = gl.createFramebuffer();\n        framebufferStack.push(framebuffer);\n        gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n        gl.activeTexture(gl.TEXTURE0 + textureUnit);\n        gl.bindTexture(gl.TEXTURE_2D, texture);\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n        try {\n          func(framebuffer);\n        } finally {\n          gl.deleteFramebuffer(framebuffer);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebufferStack[--framebufferStack.length - 1] || null);\n        }\n      }\n\n      function handleContextLoss () {\n        extensions = {};\n        programs = {};\n        textures = {};\n        textureUnit = -1;\n        framebufferStack.length = 0;\n      }\n\n      cache.set(gl, wrapper = {\n        gl: gl,\n        isWebGL2: isWebGL2,\n        getExtension: getExtension,\n        withProgram: withProgram,\n        withTexture: withTexture,\n        withTextureFramebuffer: withTextureFramebuffer,\n        handleContextLoss: handleContextLoss,\n      });\n    }\n    callback(wrapper);\n  }\n\n\n  function renderImageData(glOrCanvas, imageData, x, y, width, height, channels, framebuffer) {\n    if ( channels === void 0 ) channels = 15;\n    if ( framebuffer === void 0 ) framebuffer = null;\n\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var withProgram = ref.withProgram;\n      var withTexture = ref.withTexture;\n\n      withTexture('copy', function (tex, texUnit) {\n        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, imageData);\n        withProgram('copy', viewportQuadVertex, copyTexFragment, function (ref) {\n          var setUniform = ref.setUniform;\n          var setAttribute = ref.setAttribute;\n\n          setAttribute('aUV', 2, gl.STATIC_DRAW, 0, new Float32Array([0, 0, 2, 0, 0, 2]));\n          setUniform('1i', 'image', texUnit);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer || null);\n          gl.disable(gl.BLEND);\n          gl.colorMask(channels & 8, channels & 4, channels & 2, channels & 1);\n          gl.viewport(x, y, width, height);\n          gl.scissor(x, y, width, height);\n          gl.drawArrays(gl.TRIANGLES, 0, 3);\n        });\n      });\n    });\n  }\n\n  /**\n   * Resizing a canvas clears its contents; this utility copies the previous contents over.\n   * @param canvas\n   * @param newWidth\n   * @param newHeight\n   */\n  function resizeWebGLCanvasWithoutClearing(canvas, newWidth, newHeight) {\n    var width = canvas.width;\n    var height = canvas.height;\n    withWebGLContext(canvas, function (ref) {\n      var gl = ref.gl;\n\n      var data = new Uint8Array(width * height * 4);\n      gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, data);\n      canvas.width = newWidth;\n      canvas.height = newHeight;\n      renderImageData(gl, data, 0, 0, width, height);\n    });\n  }\n\n  var webglUtils = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    withWebGLContext: withWebGLContext,\n    renderImageData: renderImageData,\n    resizeWebGLCanvasWithoutClearing: resizeWebGLCanvasWithoutClearing\n  });\n\n  function generate$2 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n\n    var textureData = new Uint8Array(sdfWidth * sdfHeight);\n\n    var viewBoxWidth = viewBox[2] - viewBox[0];\n    var viewBoxHeight = viewBox[3] - viewBox[1];\n\n    // Decompose all paths into straight line segments and add them to an index\n    var segments = [];\n    pathToLineSegments(path, function (x1, y1, x2, y2) {\n      segments.push({\n        x1: x1, y1: y1, x2: x2, y2: y2,\n        minX: Math.min(x1, x2),\n        minY: Math.min(y1, y2),\n        maxX: Math.max(x1, x2),\n        maxY: Math.max(y1, y2)\n      });\n    });\n\n    // Sort segments by maxX, this will let us short-circuit some loops below\n    segments.sort(function (a, b) { return a.maxX - b.maxX; });\n\n    // For each target SDF texel, find the distance from its center to its nearest line segment,\n    // map that distance to an alpha value, and write that alpha to the texel\n    for (var sdfX = 0; sdfX < sdfWidth; sdfX++) {\n      for (var sdfY = 0; sdfY < sdfHeight; sdfY++) {\n        var signedDist = findNearestSignedDistance(\n          viewBox[0] + viewBoxWidth * (sdfX + 0.5) / sdfWidth,\n          viewBox[1] + viewBoxHeight * (sdfY + 0.5) / sdfHeight\n        );\n\n        // Use an exponential scale to ensure the texels very near the glyph path have adequate\n        // precision, while allowing the distance field to cover the entire texture, given that\n        // there are only 8 bits available. Formula visualized: https://www.desmos.com/calculator/uiaq5aqiam\n        var alpha = Math.pow((1 - Math.abs(signedDist) / maxDistance), sdfExponent) / 2;\n        if (signedDist < 0) {\n          alpha = 1 - alpha;\n        }\n\n        alpha = Math.max(0, Math.min(255, Math.round(alpha * 255))); //clamp\n        textureData[sdfY * sdfWidth + sdfX] = alpha;\n      }\n    }\n\n    return textureData\n\n    /**\n     * For a given x/y, search the index for the closest line segment and return\n     * its signed distance. Negative = inside, positive = outside, zero = on edge\n     * @param x\n     * @param y\n     * @returns {number}\n     */\n    function findNearestSignedDistance (x, y) {\n      var closestDistSq = Infinity;\n      var closestDist = Infinity;\n\n      for (var i = segments.length; i--;) {\n        var seg = segments[i];\n        if (seg.maxX + closestDist <= x) { break } //sorting by maxX means no more can be closer, so we can short-circuit\n        if (x + closestDist > seg.minX && y - closestDist < seg.maxY && y + closestDist > seg.minY) {\n          var distSq = absSquareDistanceToLineSegment(x, y, seg.x1, seg.y1, seg.x2, seg.y2);\n          if (distSq < closestDistSq) {\n            closestDistSq = distSq;\n            closestDist = Math.sqrt(closestDistSq);\n          }\n        }\n      }\n\n      // Flip to negative distance if inside the poly\n      if (isPointInPoly(x, y)) {\n        closestDist = -closestDist;\n      }\n      return closestDist\n    }\n\n    /**\n     * Determine whether the given point lies inside or outside the glyph. Uses a simple\n     * winding-number ray casting algorithm using a ray pointing east from the point.\n     */\n    function isPointInPoly (x, y) {\n      var winding = 0;\n      for (var i = segments.length; i--;) {\n        var seg = segments[i];\n        if (seg.maxX <= x) { break } //sorting by maxX means no more can cross, so we can short-circuit\n        var intersects = ((seg.y1 > y) !== (seg.y2 > y)) && (x < (seg.x2 - seg.x1) * (y - seg.y1) / (seg.y2 - seg.y1) + seg.x1);\n        if (intersects) {\n          winding += seg.y1 < seg.y2 ? 1 : -1;\n        }\n      }\n      return winding !== 0\n    }\n  }\n\n  function generateIntoCanvas$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    generateIntoFramebuffer$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n  }\n\n  function generateIntoFramebuffer$1 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    var data = generate$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent);\n    // Expand single-channel data to rbga\n    var rgbaData = new Uint8Array(data.length * 4);\n    for (var i = 0; i < data.length; i++) {\n      rgbaData[i * 4 + channel] = data[i];\n    }\n    renderImageData(glOrCanvas, rgbaData, x, y, sdfWidth, sdfHeight, 1 << (3 - channel), framebuffer);\n  }\n\n  /**\n   * Find the absolute distance from a point to a line segment at closest approach\n   */\n  function absSquareDistanceToLineSegment (x, y, lineX0, lineY0, lineX1, lineY1) {\n    var ldx = lineX1 - lineX0;\n    var ldy = lineY1 - lineY0;\n    var lengthSq = ldx * ldx + ldy * ldy;\n    var t = lengthSq ? Math.max(0, Math.min(1, ((x - lineX0) * ldx + (y - lineY0) * ldy) / lengthSq)) : 0;\n    var dx = x - (lineX0 + t * ldx);\n    var dy = y - (lineY0 + t * ldy);\n    return dx * dx + dy * dy\n  }\n\n  var javascript = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    generate: generate$2,\n    generateIntoCanvas: generateIntoCanvas$2,\n    generateIntoFramebuffer: generateIntoFramebuffer$1\n  });\n\n  var mainVertex = \"precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n\n  var mainFragment = \"precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}\";\n\n  var postFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}\";\n\n  // Single triangle covering viewport\n  var viewportUVs = new Float32Array([0, 0, 2, 0, 0, 2]);\n\n  var implicitContext = null;\n  var isTestingSupport = false;\n  var NULL_OBJECT = {};\n  var supportByCanvas = new WeakMap(); // canvas -> bool\n\n  function validateSupport (glOrCanvas) {\n    if (!isTestingSupport && !isSupported(glOrCanvas)) {\n      throw new Error('WebGL generation not supported')\n    }\n  }\n\n  function generate$1 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( glOrCanvas === void 0 ) glOrCanvas = null;\n\n    if (!glOrCanvas) {\n      glOrCanvas = implicitContext;\n      if (!glOrCanvas) {\n        var canvas = typeof OffscreenCanvas === 'function'\n          ? new OffscreenCanvas(1, 1)\n          : typeof document !== 'undefined'\n            ? document.createElement('canvas')\n            : null;\n        if (!canvas) {\n          throw new Error('OffscreenCanvas or DOM canvas not supported')\n        }\n        glOrCanvas = implicitContext = canvas.getContext('webgl', { depth: false });\n      }\n    }\n\n    validateSupport(glOrCanvas);\n\n    var rgbaData = new Uint8Array(sdfWidth * sdfHeight * 4); //not Uint8ClampedArray, cuz Safari\n\n    // Render into a background texture framebuffer\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var withTexture = ref.withTexture;\n      var withTextureFramebuffer = ref.withTextureFramebuffer;\n\n      withTexture('readable', function (texture, textureUnit) {\n        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, sdfWidth, sdfHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n\n        withTextureFramebuffer(texture, textureUnit, function (framebuffer) {\n          generateIntoFramebuffer(\n            sdfWidth,\n            sdfHeight,\n            path,\n            viewBox,\n            maxDistance,\n            sdfExponent,\n            gl,\n            framebuffer,\n            0,\n            0,\n            0 // red channel\n          );\n          gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, rgbaData);\n        });\n      });\n    });\n\n    // Throw away all but the red channel\n    var data = new Uint8Array(sdfWidth * sdfHeight);\n    for (var i = 0, j = 0; i < rgbaData.length; i += 4) {\n      data[j++] = rgbaData[i];\n    }\n\n    return data\n  }\n\n  function generateIntoCanvas$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    generateIntoFramebuffer(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n  }\n\n  function generateIntoFramebuffer (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    // Verify support\n    validateSupport(glOrCanvas);\n\n    // Compute path segments\n    var lineSegmentCoords = [];\n    pathToLineSegments(path, function (x1, y1, x2, y2) {\n      lineSegmentCoords.push(x1, y1, x2, y2);\n    });\n    lineSegmentCoords = new Float32Array(lineSegmentCoords);\n\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var isWebGL2 = ref.isWebGL2;\n      var getExtension = ref.getExtension;\n      var withProgram = ref.withProgram;\n      var withTexture = ref.withTexture;\n      var withTextureFramebuffer = ref.withTextureFramebuffer;\n      var handleContextLoss = ref.handleContextLoss;\n\n      withTexture('rawDistances', function (intermediateTexture, intermediateTextureUnit) {\n        if (sdfWidth !== intermediateTexture._lastWidth || sdfHeight !== intermediateTexture._lastHeight) {\n          gl.texImage2D(\n            gl.TEXTURE_2D, 0, gl.RGBA,\n            intermediateTexture._lastWidth = sdfWidth,\n            intermediateTexture._lastHeight = sdfHeight,\n            0, gl.RGBA, gl.UNSIGNED_BYTE, null\n          );\n        }\n\n        // Unsigned distance pass\n        withProgram('main', mainVertex, mainFragment, function (ref) {\n          var setAttribute = ref.setAttribute;\n          var setUniform = ref.setUniform;\n\n          // Init extensions\n          var instancingExtension = !isWebGL2 && getExtension('ANGLE_instanced_arrays');\n          var blendMinMaxExtension = !isWebGL2 && getExtension('EXT_blend_minmax');\n\n          // Init/update attributes\n          setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n          setAttribute('aLineSegment', 4, gl.DYNAMIC_DRAW, 1, lineSegmentCoords);\n\n          // Init/update uniforms\n          setUniform.apply(void 0, [ '4f', 'uGlyphBounds' ].concat( viewBox ));\n          setUniform('1f', 'uMaxDistance', maxDistance);\n          setUniform('1f', 'uExponent', sdfExponent);\n\n          // Render initial unsigned distance / winding number info to a texture\n          withTextureFramebuffer(intermediateTexture, intermediateTextureUnit, function (framebuffer) {\n            gl.enable(gl.BLEND);\n            gl.colorMask(true, true, true, true);\n            gl.viewport(0, 0, sdfWidth, sdfHeight);\n            gl.scissor(0, 0, sdfWidth, sdfHeight);\n            gl.blendFunc(gl.ONE, gl.ONE);\n            // Red+Green channels are incremented (FUNC_ADD) for segment-ray crossings to give a \"winding number\".\n            // Alpha holds the closest (MAX) unsigned distance.\n            gl.blendEquationSeparate(gl.FUNC_ADD, isWebGL2 ? gl.MAX : blendMinMaxExtension.MAX_EXT);\n            gl.clear(gl.COLOR_BUFFER_BIT);\n            if (isWebGL2) {\n              gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n            } else {\n              instancingExtension.drawArraysInstancedANGLE(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n            }\n            // Debug\n            // const debug = new Uint8Array(sdfWidth * sdfHeight * 4)\n            // gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, debug)\n            // console.log('intermediate texture data: ', debug)\n          });\n        });\n\n        // Use the data stored in the texture to apply inside/outside and write to the output framebuffer rect+channel.\n        withProgram('post', viewportQuadVertex, postFragment, function (program) {\n          program.setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n          program.setUniform('1i', 'tex', intermediateTextureUnit);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n          gl.disable(gl.BLEND);\n          gl.colorMask(channel === 0, channel === 1, channel === 2, channel === 3);\n          gl.viewport(x, y, sdfWidth, sdfHeight);\n          gl.scissor(x, y, sdfWidth, sdfHeight);\n          gl.drawArrays(gl.TRIANGLES, 0, 3);\n        });\n      });\n\n      // Handle context loss occurring during any of the above calls\n      if (gl.isContextLost()) {\n        handleContextLoss();\n        throw new Error('webgl context lost')\n      }\n    });\n  }\n\n  function isSupported (glOrCanvas) {\n    var key = (!glOrCanvas || glOrCanvas === implicitContext) ? NULL_OBJECT : (glOrCanvas.canvas || glOrCanvas);\n    var supported = supportByCanvas.get(key);\n    if (supported === undefined) {\n      isTestingSupport = true;\n      var failReason = null;\n      try {\n        // Since we can't detect all failure modes up front, let's just do a trial run of a\n        // simple path and compare what we get back to the correct expected result. This will\n        // also serve to prime the shader compilation.\n        var expectedResult = [\n          97, 106, 97, 61,\n          99, 137, 118, 80,\n          80, 118, 137, 99,\n          61, 97, 106, 97\n        ];\n        var testResult = generate$1(\n          4,\n          4,\n          'M8,8L16,8L24,24L16,24Z',\n          [0, 0, 32, 32],\n          24,\n          1,\n          glOrCanvas\n        );\n        supported = testResult && expectedResult.length === testResult.length &&\n          testResult.every(function (val, i) { return val === expectedResult[i]; });\n        if (!supported) {\n          failReason = 'bad trial run results';\n          console.info(expectedResult, testResult);\n        }\n      } catch (err) {\n        // TODO if it threw due to webgl context loss, should we maybe leave isSupported as null and try again later?\n        supported = false;\n        failReason = err.message;\n      }\n      if (failReason) {\n        console.warn('WebGL SDF generation not supported:', failReason);\n      }\n      isTestingSupport = false;\n      supportByCanvas.set(key, supported);\n    }\n    return supported\n  }\n\n  var webgl = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    generate: generate$1,\n    generateIntoCanvas: generateIntoCanvas$1,\n    generateIntoFramebuffer: generateIntoFramebuffer,\n    isSupported: isSupported\n  });\n\n  /**\n   * Generate an SDF texture image for a 2D path.\n   *\n   * @param {number} sdfWidth - width of the SDF output image in pixels.\n   * @param {number} sdfHeight - height of the SDF output image in pixels.\n   * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n   * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n   * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n   *        to half the maximum viewBox dimension.\n   * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n   *        will give greater precision nearer the glyph's path.\n   * @return {Uint8Array}\n   */\n  function generate(\n    sdfWidth,\n    sdfHeight,\n    path,\n    viewBox,\n    maxDistance,\n    sdfExponent\n  ) {\n    if ( maxDistance === void 0 ) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n\n    try {\n      return generate$1.apply(webgl, arguments)\n    } catch(e) {\n      console.info('WebGL SDF generation failed, falling back to JS', e);\n      return generate$2.apply(javascript, arguments)\n    }\n  }\n\n  /**\n   * Generate an SDF texture image for a 2D path, inserting the result into a WebGL `canvas` at a given x/y position\n   * and color channel. This is generally much faster than calling `generate` because it does not require reading pixels\n   * back from the GPU->CPU -- the `canvas` can be used directly as a WebGL texture image, so it all stays on the GPU.\n   *\n   * @param {number} sdfWidth - width of the SDF output image in pixels.\n   * @param {number} sdfHeight - height of the SDF output image in pixels.\n   * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n   * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n   * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n   *        to half the maximum viewBox dimension.\n   * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n   *        will give greater precision nearer the glyph's path.\n   * @param {HTMLCanvasElement|OffscreenCanvas} canvas - a WebGL-enabled canvas into which the SDF will be rendered.\n   *        Only the relevant rect/channel will be modified, the rest will be preserved. To avoid unpredictable results\n   *        due to shared GL context state, this canvas should be dedicated to use by this library alone.\n   * @param {number} x - the x position at which to render the SDF.\n   * @param {number} y - the y position at which to render the SDF.\n   * @param {number} channel - the color channel index (0-4) into which the SDF will be rendered.\n   * @return {Uint8Array}\n   */\n  function generateIntoCanvas(\n    sdfWidth,\n    sdfHeight,\n    path,\n    viewBox,\n    maxDistance,\n    sdfExponent,\n    canvas,\n    x,\n    y,\n    channel\n  ) {\n    if ( maxDistance === void 0 ) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    try {\n      return generateIntoCanvas$1.apply(webgl, arguments)\n    } catch(e) {\n      console.info('WebGL SDF generation failed, falling back to JS', e);\n      return generateIntoCanvas$2.apply(javascript, arguments)\n    }\n  }\n\n  exports.forEachPathCommand = forEachPathCommand;\n  exports.generate = generate;\n  exports.generateIntoCanvas = generateIntoCanvas;\n  exports.javascript = javascript;\n  exports.pathToLineSegments = pathToLineSegments;\n  exports.webgl = webgl;\n  exports.webglUtils = webglUtils;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n}({}));\nreturn exports\n}\n\nexport { SDFGenerator as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACT,IAAI,UAAW,SAAU,OAAO;QAE9B;;GAEC,GACD,SAAS,uBAAwB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ;YAClE,IAAI,KAAK,IAAI;YACb,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;YACtD,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;QACxD;QAEA;;GAEC,GACD,SAAS,mBAAoB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ;YACtE,IAAI,KAAK,IAAI;YACb,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;YAC1F,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;QAC5F;QAEA;;;;;;;;;;;;;;;GAeC,GACD,SAAS,mBAAmB,UAAU,EAAE,eAAe;YACrD,IAAI,YAAY;YAChB,IAAI,OAAO,QAAQ,QAAQ,OAAO;YAClC,MAAQ,QAAQ,UAAU,IAAI,CAAC,YAAc;gBAC3C,IAAI,OAAO,KAAK,CAAC,EAAE,CAChB,OAAO,CAAC,cAAc,IACtB,KAAK,CAAC,UACN,GAAG,CAAC,SAAU,CAAC;oBAAI,OAAO,WAAW;gBAAI;gBAC5C,OAAQ,KAAK,CAAC,EAAE;oBACd,KAAK;wBACH,QAAQ,SAAS,IAAI,CAAC,EAAE;wBACxB,QAAQ,SAAS,IAAI,CAAC,EAAE;wBACxB;oBACF,KAAK;wBACH,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,EAAE,KAAK,OAAO;4BAC1C,gBAAgB,KAAK,OAAO,OAAQ,QAAQ,IAAI,CAAC,EAAE,EAAI,QAAQ,IAAI,CAAC,EAAE;wBACxE;wBACA;oBACF,KAAK;wBAAK;4BACR,gBAAgB,KAAK,OAAO,OAAQ,QAAQ,IAAI,CAAC,EAAE,EAAI,QAAQ,IAAI,CAAC,EAAE,EAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;4BACzF;wBACF;oBACA,KAAK;wBAAK;4BACR,gBAAgB,KAAK,OAAO,OAAQ,QAAQ,IAAI,CAAC,EAAE,EAAI,QAAQ,IAAI,CAAC,EAAE,EAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;4BAC3G;wBACF;oBACA,KAAK;wBACH,IAAI,UAAU,UAAU,UAAU,QAAQ;4BACxC,gBAAgB,KAAK,OAAO,OAAO,QAAQ;wBAC7C;wBACA;gBACJ;YACF;QACF;QAEA;;;;;;;GAOC,GACD,SAAS,mBAAoB,UAAU,EAAE,eAAe,EAAE,WAAW;YACnE,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,IAAI,YAAY;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAC7B,mBAAmB,YAAY,SAAU,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;gBAC1G,OAAQ;oBACN,KAAK;wBACH,gBAAgB,QAAQ,QAAQ,MAAM;wBACtC;oBACF,KAAK;wBAAK;4BACR,IAAI,aAAa;4BACjB,IAAI,aAAa;4BACjB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gCACpC,uBACE,QAAQ,QACR,QAAQ,QACR,MAAM,MACN,IAAI,CAAC,cAAc,CAAC,GACpB;gCAEF,gBAAgB,YAAY,YAAY,UAAU,CAAC,EAAE,UAAU,CAAC;gCAChE,aAAa,UAAU,CAAC;gCACxB,aAAa,UAAU,CAAC;4BAC1B;4BACA;wBACF;oBACA,KAAK;wBAAK;4BACR,IAAI,eAAe;4BACnB,IAAI,eAAe;4BACnB,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;gCAC1C,mBACE,QAAQ,QACR,QAAQ,QACR,QAAQ,QACR,MAAM,MACN,MAAM,CAAC,cAAc,CAAC,GACtB;gCAEF,gBAAgB,cAAc,cAAc,UAAU,CAAC,EAAE,UAAU,CAAC;gCACpE,eAAe,UAAU,CAAC;gCAC1B,eAAe,UAAU,CAAC;4BAC5B;4BACA;wBACF;gBACF;YACF;QACF;QAEA,IAAI,qBAAqB;QAEzB,IAAI,kBAAkB;QAEtB,IAAI,QAAQ,IAAI;QAEhB,IAAI,kBAAkB;YACpB,oBAAoB;YACpB,uBAAuB;YACvB,WAAW;YACX,OAAO;QACT;QAEA;;;;;;GAMC,GACD,SAAS,iBAAkB,UAAU,EAAE,QAAQ;YAC7C,IAAI,KAAK,WAAW,UAAU,GAAG,WAAW,UAAU,CAAC,SAAS,mBAAmB;YACnF,IAAI,UAAU,MAAM,GAAG,CAAC;YACxB,IAAI,CAAC,SAAS;gBACZ,IAAI,WAAW,OAAO,2BAA2B,eAAe,cAAc;gBAC9E,IAAI,aAAa,CAAC;gBAClB,IAAI,WAAW,CAAC;gBAChB,IAAI,WAAW,CAAC;gBAChB,IAAI,cAAc,CAAC;gBACnB,IAAI,mBAAmB,EAAE;gBAEzB,GAAG,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,SAAU,CAAC;oBACxD;oBACA,EAAE,cAAc;gBAClB,GAAG;gBAEH,SAAS,aAAc,IAAI;oBACzB,IAAI,MAAM,UAAU,CAAC,KAAK;oBAC1B,IAAI,CAAC,KAAK;wBACR,MAAM,UAAU,CAAC,KAAK,GAAG,GAAG,YAAY,CAAC;wBACzC,IAAI,CAAC,KAAK;4BACR,MAAM,IAAI,MAAO,OAAO;wBAC1B;oBACF;oBACA,OAAO;gBACT;gBAEA,SAAS,cAAe,GAAG,EAAE,IAAI;oBAC/B,IAAI,SAAS,GAAG,YAAY,CAAC;oBAC7B,GAAG,YAAY,CAAC,QAAQ;oBACxB,GAAG,aAAa,CAAC;oBACjB,kEAAkE;oBAClE,wCAAwC;oBACxC,wDAAwD;oBACxD,IAAI;oBACJ,OAAO;gBACT;gBAEA,SAAS,YAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;oBAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;wBACnB,IAAI,aAAa,CAAC;wBAClB,IAAI,WAAW,CAAC;wBAChB,IAAI,UAAU,GAAG,aAAa;wBAC9B,GAAG,YAAY,CAAC,SAAS,cAAc,MAAM,GAAG,aAAa;wBAC7D,GAAG,YAAY,CAAC,SAAS,cAAc,MAAM,GAAG,eAAe;wBAC/D,GAAG,WAAW,CAAC;wBAEf,QAAQ,CAAC,KAAK,GAAG;4BACf,SAAS;4BACT,aAAa,SAAS,YAAa,IAAI;gCACrC,GAAG,UAAU,CAAC;gCACd,KAAK;oCACH,YAAY,SAAS,WAAY,IAAI,EAAE,IAAI;wCACzC,IAAI,SAAS,EAAE,EAAE,MAAM,UAAU,MAAM,GAAG;wCAC1C,MAAQ,QAAQ,EAAI,MAAM,CAAE,IAAK,GAAG,SAAS,CAAE,MAAM,EAAG;wCAExD,IAAI,aAAa,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,GAAG,kBAAkB,CAAC,SAAS,KAAK;wCACzF,EAAE,CAAE,YAAY,KAAM,CAAC,KAAK,CAAC,IAAI;4CAAE;yCAAY,CAAC,MAAM,CAAE;oCAC1D;oCAEA,cAAc,SAAS,aAAc,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI;wCAC7E,IAAI,OAAO,UAAU,CAAC,KAAK;wCAC3B,IAAI,CAAC,MAAM;4CACT,OAAO,UAAU,CAAC,KAAK,GAAG;gDACxB,KAAK,GAAG,YAAY;gDACpB,KAAK,GAAG,iBAAiB,CAAC,SAAS;gDACnC,MAAM;4CACR;wCACF;wCACA,GAAG,UAAU,CAAC,GAAG,YAAY,EAAE,KAAK,GAAG;wCACvC,GAAG,mBAAmB,CAAC,KAAK,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,GAAG;wCAC3D,GAAG,uBAAuB,CAAC,KAAK,GAAG;wCACnC,IAAI,UAAU;4CACZ,GAAG,mBAAmB,CAAC,KAAK,GAAG,EAAE;wCACnC,OAAO;4CACL,aAAa,0BAA0B,wBAAwB,CAAC,KAAK,GAAG,EAAE;wCAC5E;wCACA,IAAI,SAAS,KAAK,IAAI,EAAE;4CACtB,GAAG,UAAU,CAAC,GAAG,YAAY,EAAE,MAAM;4CACrC,KAAK,IAAI,GAAG;wCACd;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;gBAC7B;gBAEA,SAAS,YAAa,IAAI,EAAE,IAAI;oBAC9B;oBACA,IAAI;wBACF,GAAG,aAAa,CAAC,GAAG,QAAQ,GAAG;wBAC/B,IAAI,UAAU,QAAQ,CAAC,KAAK;wBAC5B,IAAI,CAAC,SAAS;4BACZ,UAAU,QAAQ,CAAC,KAAK,GAAG,GAAG,aAAa;4BAC3C,GAAG,WAAW,CAAC,GAAG,UAAU,EAAE;4BAC9B,GAAG,aAAa,CAAC,GAAG,UAAU,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO;4BACjE,GAAG,aAAa,CAAC,GAAG,UAAU,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO;wBACnE;wBACA,GAAG,WAAW,CAAC,GAAG,UAAU,EAAE;wBAC9B,KAAK,SAAS;oBAChB,SAAU;wBACR;oBACF;gBACF;gBAEA,SAAS,uBAAwB,OAAO,EAAE,WAAW,EAAE,IAAI;oBACzD,IAAI,cAAc,GAAG,iBAAiB;oBACtC,iBAAiB,IAAI,CAAC;oBACtB,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE;oBACnC,GAAG,aAAa,CAAC,GAAG,QAAQ,GAAG;oBAC/B,GAAG,WAAW,CAAC,GAAG,UAAU,EAAE;oBAC9B,GAAG,oBAAoB,CAAC,GAAG,WAAW,EAAE,GAAG,iBAAiB,EAAE,GAAG,UAAU,EAAE,SAAS;oBACtF,IAAI;wBACF,KAAK;oBACP,SAAU;wBACR,GAAG,iBAAiB,CAAC;wBACrB,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE,gBAAgB,CAAC,EAAE,iBAAiB,MAAM,GAAG,EAAE,IAAI;oBACxF;gBACF;gBAEA,SAAS;oBACP,aAAa,CAAC;oBACd,WAAW,CAAC;oBACZ,WAAW,CAAC;oBACZ,cAAc,CAAC;oBACf,iBAAiB,MAAM,GAAG;gBAC5B;gBAEA,MAAM,GAAG,CAAC,IAAI,UAAU;oBACtB,IAAI;oBACJ,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,wBAAwB;oBACxB,mBAAmB;gBACrB;YACF;YACA,SAAS;QACX;QAGA,SAAS,gBAAgB,UAAU,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW;YACxF,IAAK,aAAa,KAAK,GAAI,WAAW;YACtC,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,iBAAiB,YAAY,SAAU,GAAG;gBACxC,IAAI,KAAK,IAAI,EAAE;gBACf,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,cAAc,IAAI,WAAW;gBAEjC,YAAY,QAAQ,SAAU,GAAG,EAAE,OAAO;oBACxC,GAAG,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,IAAI,EAAE,OAAO,QAAQ,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBACtF,YAAY,QAAQ,oBAAoB,iBAAiB,SAAU,GAAG;wBACpE,IAAI,aAAa,IAAI,UAAU;wBAC/B,IAAI,eAAe,IAAI,YAAY;wBAEnC,aAAa,OAAO,GAAG,GAAG,WAAW,EAAE,GAAG,IAAI,aAAa;4BAAC;4BAAG;4BAAG;4BAAG;4BAAG;4BAAG;yBAAE;wBAC7E,WAAW,MAAM,SAAS;wBAC1B,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE,eAAe;wBAClD,GAAG,OAAO,CAAC,GAAG,KAAK;wBACnB,GAAG,SAAS,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW;wBAClE,GAAG,QAAQ,CAAC,GAAG,GAAG,OAAO;wBACzB,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO;wBACxB,GAAG,UAAU,CAAC,GAAG,SAAS,EAAE,GAAG;oBACjC;gBACF;YACF;QACF;QAEA;;;;;GAKC,GACD,SAAS,iCAAiC,MAAM,EAAE,QAAQ,EAAE,SAAS;YACnE,IAAI,QAAQ,OAAO,KAAK;YACxB,IAAI,SAAS,OAAO,MAAM;YAC1B,iBAAiB,QAAQ,SAAU,GAAG;gBACpC,IAAI,KAAK,IAAI,EAAE;gBAEf,IAAI,OAAO,IAAI,WAAW,QAAQ,SAAS;gBAC3C,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,QAAQ,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;gBAC9D,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAChB,gBAAgB,IAAI,MAAM,GAAG,GAAG,OAAO;YACzC;QACF;QAEA,IAAI,aAAa,WAAW,GAAE,OAAO,MAAM,CAAC;YAC1C,WAAW;YACX,kBAAkB;YAClB,iBAAiB;YACjB,kCAAkC;QACpC;QAEA,SAAS,WAAY,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW;YAC/E,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,IAAI,cAAc,IAAI,WAAW,WAAW;YAE5C,IAAI,eAAe,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;YAC1C,IAAI,gBAAgB,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;YAE3C,2EAA2E;YAC3E,IAAI,WAAW,EAAE;YACjB,mBAAmB,MAAM,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC/C,SAAS,IAAI,CAAC;oBACZ,IAAI;oBAAI,IAAI;oBAAI,IAAI;oBAAI,IAAI;oBAC5B,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,MAAM,KAAK,GAAG,CAAC,IAAI;gBACrB;YACF;YAEA,yEAAyE;YACzE,SAAS,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI;YAAE;YAExD,4FAA4F;YAC5F,yEAAyE;YACzE,IAAK,IAAI,OAAO,GAAG,OAAO,UAAU,OAAQ;gBAC1C,IAAK,IAAI,OAAO,GAAG,OAAO,WAAW,OAAQ;oBAC3C,IAAI,aAAa,0BACf,OAAO,CAAC,EAAE,GAAG,eAAe,CAAC,OAAO,GAAG,IAAI,UAC3C,OAAO,CAAC,EAAE,GAAG,gBAAgB,CAAC,OAAO,GAAG,IAAI;oBAG9C,uFAAuF;oBACvF,uFAAuF;oBACvF,oGAAoG;oBACpG,IAAI,QAAQ,KAAK,GAAG,CAAE,IAAI,KAAK,GAAG,CAAC,cAAc,aAAc,eAAe;oBAC9E,IAAI,aAAa,GAAG;wBAClB,QAAQ,IAAI;oBACd;oBAEA,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,QAAQ,OAAO;oBACpE,WAAW,CAAC,OAAO,WAAW,KAAK,GAAG;gBACxC;YACF;YAEA,OAAO;;YAEP;;;;;;KAMC,GACD,SAAS,0BAA2B,CAAC,EAAE,CAAC;gBACtC,IAAI,gBAAgB;gBACpB,IAAI,cAAc;gBAElB,IAAK,IAAI,IAAI,SAAS,MAAM,EAAE,KAAM;oBAClC,IAAI,MAAM,QAAQ,CAAC,EAAE;oBACrB,IAAI,IAAI,IAAI,GAAG,eAAe,GAAG;wBAAE;oBAAM,EAAE,sEAAsE;oBACjH,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,EAAE;wBAC1F,IAAI,SAAS,+BAA+B,GAAG,GAAG,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE;wBAChF,IAAI,SAAS,eAAe;4BAC1B,gBAAgB;4BAChB,cAAc,KAAK,IAAI,CAAC;wBAC1B;oBACF;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,cAAc,GAAG,IAAI;oBACvB,cAAc,CAAC;gBACjB;gBACA,OAAO;YACT;YAEA;;;KAGC,GACD,SAAS,cAAe,CAAC,EAAE,CAAC;gBAC1B,IAAI,UAAU;gBACd,IAAK,IAAI,IAAI,SAAS,MAAM,EAAE,KAAM;oBAClC,IAAI,MAAM,QAAQ,CAAC,EAAE;oBACrB,IAAI,IAAI,IAAI,IAAI,GAAG;wBAAE;oBAAM,EAAE,kEAAkE;oBAC/F,IAAI,aAAa,AAAE,IAAI,EAAE,GAAG,MAAQ,IAAI,EAAE,GAAG,KAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,IAAI,EAAE;oBACtH,IAAI,YAAY;wBACd,WAAW,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;oBACpC;gBACF;gBACA,OAAO,YAAY;YACrB;QACF;QAEA,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YAC/G,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,0BAA0B,UAAU,WAAW,MAAM,SAAS,aAAa,aAAa,QAAQ,MAAM,GAAG,GAAG;QAC9G;QAEA,SAAS,0BAA2B,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YACtI,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,IAAI,OAAO,WAAW,UAAU,WAAW,MAAM,SAAS,aAAa;YACvE,qCAAqC;YACrC,IAAI,WAAW,IAAI,WAAW,KAAK,MAAM,GAAG;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,QAAQ,CAAC,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,EAAE;YACrC;YACA,gBAAgB,YAAY,UAAU,GAAG,GAAG,UAAU,WAAW,KAAM,IAAI,SAAU;QACvF;QAEA;;GAEC,GACD,SAAS,+BAAgC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAC3E,IAAI,MAAM,SAAS;YACnB,IAAI,MAAM,SAAS;YACnB,IAAI,WAAW,MAAM,MAAM,MAAM;YACjC,IAAI,IAAI,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,aAAa;YACpG,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,GAAG;YAC9B,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,GAAG;YAC9B,OAAO,KAAK,KAAK,KAAK;QACxB;QAEA,IAAI,aAAa,WAAW,GAAE,OAAO,MAAM,CAAC;YAC1C,WAAW;YACX,UAAU;YACV,oBAAoB;YACpB,yBAAyB;QAC3B;QAEA,IAAI,aAAa;QAEjB,IAAI,eAAe;QAEnB,IAAI,eAAe;QAEnB,oCAAoC;QACpC,IAAI,cAAc,IAAI,aAAa;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAErD,IAAI,kBAAkB;QACtB,IAAI,mBAAmB;QACvB,IAAI,cAAc,CAAC;QACnB,IAAI,kBAAkB,IAAI,WAAW,iBAAiB;QAEtD,SAAS,gBAAiB,UAAU;YAClC,IAAI,CAAC,oBAAoB,CAAC,YAAY,aAAa;gBACjD,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,SAAS,WAAY,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU;YAC3F,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,eAAe,KAAK,GAAI,aAAa;YAE1C,IAAI,CAAC,YAAY;gBACf,aAAa;gBACb,IAAI,CAAC,YAAY;oBACf,IAAI,SAAS,OAAO,oBAAoB,aACpC,IAAI,gBAAgB,GAAG,KACvB,OAAO,aAAa,cAClB,SAAS,aAAa,CAAC,YACvB;oBACN,IAAI,CAAC,QAAQ;wBACX,MAAM,IAAI,MAAM;oBAClB;oBACA,aAAa,kBAAkB,OAAO,UAAU,CAAC,SAAS;wBAAE,OAAO;oBAAM;gBAC3E;YACF;YAEA,gBAAgB;YAEhB,IAAI,WAAW,IAAI,WAAW,WAAW,YAAY,IAAI,mCAAmC;YAE5F,+CAA+C;YAC/C,iBAAiB,YAAY,SAAU,GAAG;gBACxC,IAAI,KAAK,IAAI,EAAE;gBACf,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,yBAAyB,IAAI,sBAAsB;gBAEvD,YAAY,YAAY,SAAU,OAAO,EAAE,WAAW;oBACpD,GAAG,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,IAAI,EAAE,UAAU,WAAW,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBAE5F,uBAAuB,SAAS,aAAa,SAAU,WAAW;wBAChE,wBACE,UACA,WACA,MACA,SACA,aACA,aACA,IACA,aACA,GACA,GACA,EAAE,cAAc;;wBAElB,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,WAAW,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBACtE;gBACF;YACF;YAEA,qCAAqC;YACrC,IAAI,OAAO,IAAI,WAAW,WAAW;YACrC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;gBAClD,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,EAAE;YACzB;YAEA,OAAO;QACT;QAEA,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YAC/G,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,wBAAwB,UAAU,WAAW,MAAM,SAAS,aAAa,aAAa,QAAQ,MAAM,GAAG,GAAG;QAC5G;QAEA,SAAS,wBAAyB,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YACpI,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,iBAAiB;YACjB,gBAAgB;YAEhB,wBAAwB;YACxB,IAAI,oBAAoB,EAAE;YAC1B,mBAAmB,MAAM,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC/C,kBAAkB,IAAI,CAAC,IAAI,IAAI,IAAI;YACrC;YACA,oBAAoB,IAAI,aAAa;YAErC,iBAAiB,YAAY,SAAU,GAAG;gBACxC,IAAI,KAAK,IAAI,EAAE;gBACf,IAAI,WAAW,IAAI,QAAQ;gBAC3B,IAAI,eAAe,IAAI,YAAY;gBACnC,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,yBAAyB,IAAI,sBAAsB;gBACvD,IAAI,oBAAoB,IAAI,iBAAiB;gBAE7C,YAAY,gBAAgB,SAAU,mBAAmB,EAAE,uBAAuB;oBAChF,IAAI,aAAa,oBAAoB,UAAU,IAAI,cAAc,oBAAoB,WAAW,EAAE;wBAChG,GAAG,UAAU,CACX,GAAG,UAAU,EAAE,GAAG,GAAG,IAAI,EACzB,oBAAoB,UAAU,GAAG,UACjC,oBAAoB,WAAW,GAAG,WAClC,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBAElC;oBAEA,yBAAyB;oBACzB,YAAY,QAAQ,YAAY,cAAc,SAAU,GAAG;wBACzD,IAAI,eAAe,IAAI,YAAY;wBACnC,IAAI,aAAa,IAAI,UAAU;wBAE/B,kBAAkB;wBAClB,IAAI,sBAAsB,CAAC,YAAY,aAAa;wBACpD,IAAI,uBAAuB,CAAC,YAAY,aAAa;wBAErD,yBAAyB;wBACzB,aAAa,OAAO,GAAG,GAAG,WAAW,EAAE,GAAG;wBAC1C,aAAa,gBAAgB,GAAG,GAAG,YAAY,EAAE,GAAG;wBAEpD,uBAAuB;wBACvB,WAAW,KAAK,CAAC,KAAK,GAAG;4BAAE;4BAAM;yBAAgB,CAAC,MAAM,CAAE;wBAC1D,WAAW,MAAM,gBAAgB;wBACjC,WAAW,MAAM,aAAa;wBAE9B,sEAAsE;wBACtE,uBAAuB,qBAAqB,yBAAyB,SAAU,WAAW;4BACxF,GAAG,MAAM,CAAC,GAAG,KAAK;4BAClB,GAAG,SAAS,CAAC,MAAM,MAAM,MAAM;4BAC/B,GAAG,QAAQ,CAAC,GAAG,GAAG,UAAU;4BAC5B,GAAG,OAAO,CAAC,GAAG,GAAG,UAAU;4BAC3B,GAAG,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG;4BAC3B,sGAAsG;4BACtG,mDAAmD;4BACnD,GAAG,qBAAqB,CAAC,GAAG,QAAQ,EAAE,WAAW,GAAG,GAAG,GAAG,qBAAqB,OAAO;4BACtF,GAAG,KAAK,CAAC,GAAG,gBAAgB;4BAC5B,IAAI,UAAU;gCACZ,GAAG,mBAAmB,CAAC,GAAG,SAAS,EAAE,GAAG,GAAG,kBAAkB,MAAM,GAAG;4BACxE,OAAO;gCACL,oBAAoB,wBAAwB,CAAC,GAAG,SAAS,EAAE,GAAG,GAAG,kBAAkB,MAAM,GAAG;4BAC9F;wBACA,QAAQ;wBACR,yDAAyD;wBACzD,6EAA6E;wBAC7E,oDAAoD;wBACtD;oBACF;oBAEA,+GAA+G;oBAC/G,YAAY,QAAQ,oBAAoB,cAAc,SAAU,OAAO;wBACrE,QAAQ,YAAY,CAAC,OAAO,GAAG,GAAG,WAAW,EAAE,GAAG;wBAClD,QAAQ,UAAU,CAAC,MAAM,OAAO;wBAChC,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE;wBACnC,GAAG,OAAO,CAAC,GAAG,KAAK;wBACnB,GAAG,SAAS,CAAC,YAAY,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY;wBACtE,GAAG,QAAQ,CAAC,GAAG,GAAG,UAAU;wBAC5B,GAAG,OAAO,CAAC,GAAG,GAAG,UAAU;wBAC3B,GAAG,UAAU,CAAC,GAAG,SAAS,EAAE,GAAG;oBACjC;gBACF;gBAEA,8DAA8D;gBAC9D,IAAI,GAAG,aAAa,IAAI;oBACtB;oBACA,MAAM,IAAI,MAAM;gBAClB;YACF;QACF;QAEA,SAAS,YAAa,UAAU;YAC9B,IAAI,MAAM,AAAC,CAAC,cAAc,eAAe,kBAAmB,cAAe,WAAW,MAAM,IAAI;YAChG,IAAI,YAAY,gBAAgB,GAAG,CAAC;YACpC,IAAI,cAAc,WAAW;gBAC3B,mBAAmB;gBACnB,IAAI,aAAa;gBACjB,IAAI;oBACF,mFAAmF;oBACnF,qFAAqF;oBACrF,8CAA8C;oBAC9C,IAAI,iBAAiB;wBACnB;wBAAI;wBAAK;wBAAI;wBACb;wBAAI;wBAAK;wBAAK;wBACd;wBAAI;wBAAK;wBAAK;wBACd;wBAAI;wBAAI;wBAAK;qBACd;oBACD,IAAI,aAAa,WACf,GACA,GACA,0BACA;wBAAC;wBAAG;wBAAG;wBAAI;qBAAG,EACd,IACA,GACA;oBAEF,YAAY,cAAc,eAAe,MAAM,KAAK,WAAW,MAAM,IACnE,WAAW,KAAK,CAAC,SAAU,GAAG,EAAE,CAAC;wBAAI,OAAO,QAAQ,cAAc,CAAC,EAAE;oBAAE;oBACzE,IAAI,CAAC,WAAW;wBACd,aAAa;wBACb,QAAQ,IAAI,CAAC,gBAAgB;oBAC/B;gBACF,EAAE,OAAO,KAAK;oBACZ,6GAA6G;oBAC7G,YAAY;oBACZ,aAAa,IAAI,OAAO;gBAC1B;gBACA,IAAI,YAAY;oBACd,QAAQ,IAAI,CAAC,uCAAuC;gBACtD;gBACA,mBAAmB;gBACnB,gBAAgB,GAAG,CAAC,KAAK;YAC3B;YACA,OAAO;QACT;QAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;YACrC,WAAW;YACX,UAAU;YACV,oBAAoB;YACpB,yBAAyB;YACzB,aAAa;QACf;QAEA;;;;;;;;;;;;GAYC,GACD,SAAS,SACP,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,OAAO,EACP,WAAW,EACX,WAAW;YAEX,IAAK,gBAAgB,KAAK,GAAI,cAAc,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI;YACzG,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,IAAI;gBACF,OAAO,WAAW,KAAK,CAAC,OAAO;YACjC,EAAE,OAAM,GAAG;gBACT,QAAQ,IAAI,CAAC,mDAAmD;gBAChE,OAAO,WAAW,KAAK,CAAC,YAAY;YACtC;QACF;QAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,SAAS,mBACP,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,OAAO,EACP,WAAW,EACX,WAAW,EACX,MAAM,EACN,CAAC,EACD,CAAC,EACD,OAAO;YAEP,IAAK,gBAAgB,KAAK,GAAI,cAAc,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI;YACzG,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,IAAI;gBACF,OAAO,qBAAqB,KAAK,CAAC,OAAO;YAC3C,EAAE,OAAM,GAAG;gBACT,QAAQ,IAAI,CAAC,mDAAmD;gBAChE,OAAO,qBAAqB,KAAK,CAAC,YAAY;YAChD;QACF;QAEA,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,QAAQ,GAAG;QACnB,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,UAAU,GAAG;QACrB,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,KAAK,GAAG;QAChB,QAAQ,UAAU,GAAG;QAErB,OAAO,cAAc,CAAC,SAAS,cAAc;YAAE,OAAO;QAAK;QAE3D,OAAO;IAET,EAAE,CAAC;IACH,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/bidi-js/dist/bidi.mjs"], "sourcesContent": ["function bidiFactory() {\nvar bidi = (function (exports) {\n\n  // Bidi character types data, auto generated\n  var DATA = {\n    \"R\": \"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73\",\n    \"EN\": \"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9\",\n    \"ES\": \"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2\",\n    \"ET\": \"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj\",\n    \"AN\": \"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u\",\n    \"CS\": \"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b\",\n    \"B\": \"a,3,f+2,2v,690\",\n    \"S\": \"9,2,k\",\n    \"WS\": \"c,k,4f4,1vk+a,u,1j,335\",\n    \"ON\": \"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i\",\n    \"BN\": \"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1\",\n    \"NSM\": \"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n\",\n    \"AL\": \"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d\",\n    \"LRO\": \"6ct\",\n    \"RLO\": \"6cu\",\n    \"LRE\": \"6cq\",\n    \"RLE\": \"6cr\",\n    \"PDF\": \"6cs\",\n    \"LRI\": \"6ee\",\n    \"RLI\": \"6ef\",\n    \"FSI\": \"6eg\",\n    \"PDI\": \"6eh\"\n  };\n\n  var TYPES = {};\n  var TYPES_TO_NAMES = {};\n  TYPES.L = 1; //L is the default\n  TYPES_TO_NAMES[1] = 'L';\n  Object.keys(DATA).forEach(function (type, i) {\n    TYPES[type] = 1 << (i + 1);\n    TYPES_TO_NAMES[TYPES[type]] = type;\n  });\n  Object.freeze(TYPES);\n\n  var ISOLATE_INIT_TYPES = TYPES.LRI | TYPES.RLI | TYPES.FSI;\n  var STRONG_TYPES = TYPES.L | TYPES.R | TYPES.AL;\n  var NEUTRAL_ISOLATE_TYPES = TYPES.B | TYPES.S | TYPES.WS | TYPES.ON | TYPES.FSI | TYPES.LRI | TYPES.RLI | TYPES.PDI;\n  var BN_LIKE_TYPES = TYPES.BN | TYPES.RLE | TYPES.LRE | TYPES.RLO | TYPES.LRO | TYPES.PDF;\n  var TRAILING_TYPES = TYPES.S | TYPES.WS | TYPES.B | ISOLATE_INIT_TYPES | TYPES.PDI | BN_LIKE_TYPES;\n\n  var map = null;\n\n  function parseData () {\n    if (!map) {\n      //const start = performance.now()\n      map = new Map();\n      var loop = function ( type ) {\n        if (DATA.hasOwnProperty(type)) {\n          var lastCode = 0;\n          DATA[type].split(',').forEach(function (range) {\n            var ref = range.split('+');\n            var skip = ref[0];\n            var step = ref[1];\n            skip = parseInt(skip, 36);\n            step = step ? parseInt(step, 36) : 0;\n            map.set(lastCode += skip, TYPES[type]);\n            for (var i = 0; i < step; i++) {\n              map.set(++lastCode, TYPES[type]);\n            }\n          });\n        }\n      };\n\n      for (var type in DATA) loop( type );\n      //console.log(`char types parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  /**\n   * @param {string} char\n   * @return {number}\n   */\n  function getBidiCharType (char) {\n    parseData();\n    return map.get(char.codePointAt(0)) || TYPES.L\n  }\n\n  function getBidiCharTypeName(char) {\n    return TYPES_TO_NAMES[getBidiCharType(char)]\n  }\n\n  // Bidi bracket pairs data, auto generated\n  var data$1 = {\n    \"pairs\": \"14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1\",\n    \"canonical\": \"6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye\"\n  };\n\n  /**\n   * Parses an string that holds encoded codepoint mappings, e.g. for bracket pairs or\n   * mirroring characters, as encoded by scripts/generateBidiData.js. Returns an object\n   * holding the `map`, and optionally a `reverseMap` if `includeReverse:true`.\n   * @param {string} encodedString\n   * @param {boolean} includeReverse - true if you want reverseMap in the output\n   * @return {{map: Map<number, number>, reverseMap?: Map<number, number>}}\n   */\n  function parseCharacterMap (encodedString, includeReverse) {\n    var radix = 36;\n    var lastCode = 0;\n    var map = new Map();\n    var reverseMap = includeReverse && new Map();\n    var prevPair;\n    encodedString.split(',').forEach(function visit(entry) {\n      if (entry.indexOf('+') !== -1) {\n        for (var i = +entry; i--;) {\n          visit(prevPair);\n        }\n      } else {\n        prevPair = entry;\n        var ref = entry.split('>');\n        var a = ref[0];\n        var b = ref[1];\n        a = String.fromCodePoint(lastCode += parseInt(a, radix));\n        b = String.fromCodePoint(lastCode += parseInt(b, radix));\n        map.set(a, b);\n        includeReverse && reverseMap.set(b, a);\n      }\n    });\n    return { map: map, reverseMap: reverseMap }\n  }\n\n  var openToClose, closeToOpen, canonical;\n\n  function parse$1 () {\n    if (!openToClose) {\n      //const start = performance.now()\n      var ref = parseCharacterMap(data$1.pairs, true);\n      var map = ref.map;\n      var reverseMap = ref.reverseMap;\n      openToClose = map;\n      closeToOpen = reverseMap;\n      canonical = parseCharacterMap(data$1.canonical, false).map;\n      //console.log(`brackets parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  function openingToClosingBracket (char) {\n    parse$1();\n    return openToClose.get(char) || null\n  }\n\n  function closingToOpeningBracket (char) {\n    parse$1();\n    return closeToOpen.get(char) || null\n  }\n\n  function getCanonicalBracket (char) {\n    parse$1();\n    return canonical.get(char) || null\n  }\n\n  // Local type aliases\n  var TYPE_L = TYPES.L;\n  var TYPE_R = TYPES.R;\n  var TYPE_EN = TYPES.EN;\n  var TYPE_ES = TYPES.ES;\n  var TYPE_ET = TYPES.ET;\n  var TYPE_AN = TYPES.AN;\n  var TYPE_CS = TYPES.CS;\n  var TYPE_B = TYPES.B;\n  var TYPE_S = TYPES.S;\n  var TYPE_ON = TYPES.ON;\n  var TYPE_BN = TYPES.BN;\n  var TYPE_NSM = TYPES.NSM;\n  var TYPE_AL = TYPES.AL;\n  var TYPE_LRO = TYPES.LRO;\n  var TYPE_RLO = TYPES.RLO;\n  var TYPE_LRE = TYPES.LRE;\n  var TYPE_RLE = TYPES.RLE;\n  var TYPE_PDF = TYPES.PDF;\n  var TYPE_LRI = TYPES.LRI;\n  var TYPE_RLI = TYPES.RLI;\n  var TYPE_FSI = TYPES.FSI;\n  var TYPE_PDI = TYPES.PDI;\n\n  /**\n   * @typedef {object} GetEmbeddingLevelsResult\n   * @property {{start, end, level}[]} paragraphs\n   * @property {Uint8Array} levels\n   */\n\n  /**\n   * This function applies the Bidirectional Algorithm to a string, returning the resolved embedding levels\n   * in a single Uint8Array plus a list of objects holding each paragraph's start and end indices and resolved\n   * base embedding level.\n   *\n   * @param {string} string - The input string\n   * @param {\"ltr\"|\"rtl\"|\"auto\"} [baseDirection] - Use \"ltr\" or \"rtl\" to force a base paragraph direction,\n   *        otherwise a direction will be chosen automatically from each paragraph's contents.\n   * @return {GetEmbeddingLevelsResult}\n   */\n  function getEmbeddingLevels (string, baseDirection) {\n    var MAX_DEPTH = 125;\n\n    // Start by mapping all characters to their unicode type, as a bitmask integer\n    var charTypes = new Uint32Array(string.length);\n    for (var i = 0; i < string.length; i++) {\n      charTypes[i] = getBidiCharType(string[i]);\n    }\n\n    var charTypeCounts = new Map(); //will be cleared at start of each paragraph\n    function changeCharType(i, type) {\n      var oldType = charTypes[i];\n      charTypes[i] = type;\n      charTypeCounts.set(oldType, charTypeCounts.get(oldType) - 1);\n      if (oldType & NEUTRAL_ISOLATE_TYPES) {\n        charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) - 1);\n      }\n      charTypeCounts.set(type, (charTypeCounts.get(type) || 0) + 1);\n      if (type & NEUTRAL_ISOLATE_TYPES) {\n        charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n      }\n    }\n\n    var embedLevels = new Uint8Array(string.length);\n    var isolationPairs = new Map(); //init->pdi and pdi->init\n\n    // === 3.3.1 The Paragraph Level ===\n    // 3.3.1 P1: Split the text into paragraphs\n    var paragraphs = []; // [{start, end, level}, ...]\n    var paragraph = null;\n    for (var i$1 = 0; i$1 < string.length; i$1++) {\n      if (!paragraph) {\n        paragraphs.push(paragraph = {\n          start: i$1,\n          end: string.length - 1,\n          // 3.3.1 P2-P3: Determine the paragraph level\n          level: baseDirection === 'rtl' ? 1 : baseDirection === 'ltr' ? 0 : determineAutoEmbedLevel(i$1, false)\n        });\n      }\n      if (charTypes[i$1] & TYPE_B) {\n        paragraph.end = i$1;\n        paragraph = null;\n      }\n    }\n\n    var FORMATTING_TYPES = TYPE_RLE | TYPE_LRE | TYPE_RLO | TYPE_LRO | ISOLATE_INIT_TYPES | TYPE_PDI | TYPE_PDF | TYPE_B;\n    var nextEven = function (n) { return n + ((n & 1) ? 1 : 2); };\n    var nextOdd = function (n) { return n + ((n & 1) ? 2 : 1); };\n\n    // Everything from here on will operate per paragraph.\n    for (var paraIdx = 0; paraIdx < paragraphs.length; paraIdx++) {\n      paragraph = paragraphs[paraIdx];\n      var statusStack = [{\n        _level: paragraph.level,\n        _override: 0, //0=neutral, 1=L, 2=R\n        _isolate: 0 //bool\n      }];\n      var stackTop = (void 0);\n      var overflowIsolateCount = 0;\n      var overflowEmbeddingCount = 0;\n      var validIsolateCount = 0;\n      charTypeCounts.clear();\n\n      // === 3.3.2 Explicit Levels and Directions ===\n      for (var i$2 = paragraph.start; i$2 <= paragraph.end; i$2++) {\n        var charType = charTypes[i$2];\n        stackTop = statusStack[statusStack.length - 1];\n\n        // Set initial counts\n        charTypeCounts.set(charType, (charTypeCounts.get(charType) || 0) + 1);\n        if (charType & NEUTRAL_ISOLATE_TYPES) {\n          charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n        }\n\n        // Explicit Embeddings: 3.3.2 X2 - X3\n        if (charType & FORMATTING_TYPES) { //prefilter all formatters\n          if (charType & (TYPE_RLE | TYPE_LRE)) {\n            embedLevels[i$2] = stackTop._level; // 5.2\n            var level = (charType === TYPE_RLE ? nextOdd : nextEven)(stackTop._level);\n            if (level <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n              statusStack.push({\n                _level: level,\n                _override: 0,\n                _isolate: 0\n              });\n            } else if (!overflowIsolateCount) {\n              overflowEmbeddingCount++;\n            }\n          }\n\n          // Explicit Overrides: 3.3.2 X4 - X5\n          else if (charType & (TYPE_RLO | TYPE_LRO)) {\n            embedLevels[i$2] = stackTop._level; // 5.2\n            var level$1 = (charType === TYPE_RLO ? nextOdd : nextEven)(stackTop._level);\n            if (level$1 <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n              statusStack.push({\n                _level: level$1,\n                _override: (charType & TYPE_RLO) ? TYPE_R : TYPE_L,\n                _isolate: 0\n              });\n            } else if (!overflowIsolateCount) {\n              overflowEmbeddingCount++;\n            }\n          }\n\n          // Isolates: 3.3.2 X5a - X5c\n          else if (charType & ISOLATE_INIT_TYPES) {\n            // X5c - FSI becomes either RLI or LRI\n            if (charType & TYPE_FSI) {\n              charType = determineAutoEmbedLevel(i$2 + 1, true) === 1 ? TYPE_RLI : TYPE_LRI;\n            }\n\n            embedLevels[i$2] = stackTop._level;\n            if (stackTop._override) {\n              changeCharType(i$2, stackTop._override);\n            }\n            var level$2 = (charType === TYPE_RLI ? nextOdd : nextEven)(stackTop._level);\n            if (level$2 <= MAX_DEPTH && overflowIsolateCount === 0 && overflowEmbeddingCount === 0) {\n              validIsolateCount++;\n              statusStack.push({\n                _level: level$2,\n                _override: 0,\n                _isolate: 1,\n                _isolInitIndex: i$2\n              });\n            } else {\n              overflowIsolateCount++;\n            }\n          }\n\n          // Terminating Isolates: 3.3.2 X6a\n          else if (charType & TYPE_PDI) {\n            if (overflowIsolateCount > 0) {\n              overflowIsolateCount--;\n            } else if (validIsolateCount > 0) {\n              overflowEmbeddingCount = 0;\n              while (!statusStack[statusStack.length - 1]._isolate) {\n                statusStack.pop();\n              }\n              // Add to isolation pairs bidirectional mapping:\n              var isolInitIndex = statusStack[statusStack.length - 1]._isolInitIndex;\n              if (isolInitIndex != null) {\n                isolationPairs.set(isolInitIndex, i$2);\n                isolationPairs.set(i$2, isolInitIndex);\n              }\n              statusStack.pop();\n              validIsolateCount--;\n            }\n            stackTop = statusStack[statusStack.length - 1];\n            embedLevels[i$2] = stackTop._level;\n            if (stackTop._override) {\n              changeCharType(i$2, stackTop._override);\n            }\n          }\n\n\n          // Terminating Embeddings and Overrides: 3.3.2 X7\n          else if (charType & TYPE_PDF) {\n            if (overflowIsolateCount === 0) {\n              if (overflowEmbeddingCount > 0) {\n                overflowEmbeddingCount--;\n              } else if (!stackTop._isolate && statusStack.length > 1) {\n                statusStack.pop();\n                stackTop = statusStack[statusStack.length - 1];\n              }\n            }\n            embedLevels[i$2] = stackTop._level; // 5.2\n          }\n\n          // End of Paragraph: 3.3.2 X8\n          else if (charType & TYPE_B) {\n            embedLevels[i$2] = paragraph.level;\n          }\n        }\n\n        // Non-formatting characters: 3.3.2 X6\n        else {\n          embedLevels[i$2] = stackTop._level;\n          // NOTE: This exclusion of BN seems to go against what section 5.2 says, but is required for test passage\n          if (stackTop._override && charType !== TYPE_BN) {\n            changeCharType(i$2, stackTop._override);\n          }\n        }\n      }\n\n      // === 3.3.3 Preparations for Implicit Processing ===\n\n      // Remove all RLE, LRE, RLO, LRO, PDF, and BN characters: 3.3.3 X9\n      // Note: Due to section 5.2, we won't remove them, but we'll use the BN_LIKE_TYPES bitset to\n      // easily ignore them all from here on out.\n\n      // 3.3.3 X10\n      // Compute the set of isolating run sequences as specified by BD13\n      var levelRuns = [];\n      var currentRun = null;\n      for (var i$3 = paragraph.start; i$3 <= paragraph.end; i$3++) {\n        var charType$1 = charTypes[i$3];\n        if (!(charType$1 & BN_LIKE_TYPES)) {\n          var lvl = embedLevels[i$3];\n          var isIsolInit = charType$1 & ISOLATE_INIT_TYPES;\n          var isPDI = charType$1 === TYPE_PDI;\n          if (currentRun && lvl === currentRun._level) {\n            currentRun._end = i$3;\n            currentRun._endsWithIsolInit = isIsolInit;\n          } else {\n            levelRuns.push(currentRun = {\n              _start: i$3,\n              _end: i$3,\n              _level: lvl,\n              _startsWithPDI: isPDI,\n              _endsWithIsolInit: isIsolInit\n            });\n          }\n        }\n      }\n      var isolatingRunSeqs = []; // [{seqIndices: [], sosType: L|R, eosType: L|R}]\n      for (var runIdx = 0; runIdx < levelRuns.length; runIdx++) {\n        var run = levelRuns[runIdx];\n        if (!run._startsWithPDI || (run._startsWithPDI && !isolationPairs.has(run._start))) {\n          var seqRuns = [currentRun = run];\n          for (var pdiIndex = (void 0); currentRun && currentRun._endsWithIsolInit && (pdiIndex = isolationPairs.get(currentRun._end)) != null;) {\n            for (var i$4 = runIdx + 1; i$4 < levelRuns.length; i$4++) {\n              if (levelRuns[i$4]._start === pdiIndex) {\n                seqRuns.push(currentRun = levelRuns[i$4]);\n                break\n              }\n            }\n          }\n          // build flat list of indices across all runs:\n          var seqIndices = [];\n          for (var i$5 = 0; i$5 < seqRuns.length; i$5++) {\n            var run$1 = seqRuns[i$5];\n            for (var j = run$1._start; j <= run$1._end; j++) {\n              seqIndices.push(j);\n            }\n          }\n          // determine the sos/eos types:\n          var firstLevel = embedLevels[seqIndices[0]];\n          var prevLevel = paragraph.level;\n          for (var i$6 = seqIndices[0] - 1; i$6 >= 0; i$6--) {\n            if (!(charTypes[i$6] & BN_LIKE_TYPES)) { //5.2\n              prevLevel = embedLevels[i$6];\n              break\n            }\n          }\n          var lastIndex = seqIndices[seqIndices.length - 1];\n          var lastLevel = embedLevels[lastIndex];\n          var nextLevel = paragraph.level;\n          if (!(charTypes[lastIndex] & ISOLATE_INIT_TYPES)) {\n            for (var i$7 = lastIndex + 1; i$7 <= paragraph.end; i$7++) {\n              if (!(charTypes[i$7] & BN_LIKE_TYPES)) { //5.2\n                nextLevel = embedLevels[i$7];\n                break\n              }\n            }\n          }\n          isolatingRunSeqs.push({\n            _seqIndices: seqIndices,\n            _sosType: Math.max(prevLevel, firstLevel) % 2 ? TYPE_R : TYPE_L,\n            _eosType: Math.max(nextLevel, lastLevel) % 2 ? TYPE_R : TYPE_L\n          });\n        }\n      }\n\n      // The next steps are done per isolating run sequence\n      for (var seqIdx = 0; seqIdx < isolatingRunSeqs.length; seqIdx++) {\n        var ref = isolatingRunSeqs[seqIdx];\n        var seqIndices$1 = ref._seqIndices;\n        var sosType = ref._sosType;\n        var eosType = ref._eosType;\n        /**\n         * All the level runs in an isolating run sequence have the same embedding level.\n         * \n         * DO NOT change any `embedLevels[i]` within the current scope.\n         */\n        var embedDirection = ((embedLevels[seqIndices$1[0]]) & 1) ? TYPE_R : TYPE_L;\n\n        // === 3.3.4 Resolving Weak Types ===\n\n        // W1 + 5.2. Search backward from each NSM to the first character in the isolating run sequence whose\n        // bidirectional type is not BN, and set the NSM to ON if it is an isolate initiator or PDI, and to its\n        // type otherwise. If the NSM is the first non-BN character, change the NSM to the type of sos.\n        if (charTypeCounts.get(TYPE_NSM)) {\n          for (var si = 0; si < seqIndices$1.length; si++) {\n            var i$8 = seqIndices$1[si];\n            if (charTypes[i$8] & TYPE_NSM) {\n              var prevType = sosType;\n              for (var sj = si - 1; sj >= 0; sj--) {\n                if (!(charTypes[seqIndices$1[sj]] & BN_LIKE_TYPES)) { //5.2 scan back to first non-BN\n                  prevType = charTypes[seqIndices$1[sj]];\n                  break\n                }\n              }\n              changeCharType(i$8, (prevType & (ISOLATE_INIT_TYPES | TYPE_PDI)) ? TYPE_ON : prevType);\n            }\n          }\n        }\n\n        // W2. Search backward from each instance of a European number until the first strong type (R, L, AL, or sos)\n        // is found. If an AL is found, change the type of the European number to Arabic number.\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$1 = 0; si$1 < seqIndices$1.length; si$1++) {\n            var i$9 = seqIndices$1[si$1];\n            if (charTypes[i$9] & TYPE_EN) {\n              for (var sj$1 = si$1 - 1; sj$1 >= -1; sj$1--) {\n                var prevCharType = sj$1 === -1 ? sosType : charTypes[seqIndices$1[sj$1]];\n                if (prevCharType & STRONG_TYPES) {\n                  if (prevCharType === TYPE_AL) {\n                    changeCharType(i$9, TYPE_AN);\n                  }\n                  break\n                }\n              }\n            }\n          }\n        }\n\n        // W3. Change all ALs to R\n        if (charTypeCounts.get(TYPE_AL)) {\n          for (var si$2 = 0; si$2 < seqIndices$1.length; si$2++) {\n            var i$10 = seqIndices$1[si$2];\n            if (charTypes[i$10] & TYPE_AL) {\n              changeCharType(i$10, TYPE_R);\n            }\n          }\n        }\n\n        // W4. A single European separator between two European numbers changes to a European number. A single common\n        // separator between two numbers of the same type changes to that type.\n        if (charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n          for (var si$3 = 1; si$3 < seqIndices$1.length - 1; si$3++) {\n            var i$11 = seqIndices$1[si$3];\n            if (charTypes[i$11] & (TYPE_ES | TYPE_CS)) {\n              var prevType$1 = 0, nextType = 0;\n              for (var sj$2 = si$3 - 1; sj$2 >= 0; sj$2--) {\n                prevType$1 = charTypes[seqIndices$1[sj$2]];\n                if (!(prevType$1 & BN_LIKE_TYPES)) { //5.2\n                  break\n                }\n              }\n              for (var sj$3 = si$3 + 1; sj$3 < seqIndices$1.length; sj$3++) {\n                nextType = charTypes[seqIndices$1[sj$3]];\n                if (!(nextType & BN_LIKE_TYPES)) { //5.2\n                  break\n                }\n              }\n              if (prevType$1 === nextType && (charTypes[i$11] === TYPE_ES ? prevType$1 === TYPE_EN : (prevType$1 & (TYPE_EN | TYPE_AN)))) {\n                changeCharType(i$11, prevType$1);\n              }\n            }\n          }\n        }\n\n        // W5. A sequence of European terminators adjacent to European numbers changes to all European numbers.\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$4 = 0; si$4 < seqIndices$1.length; si$4++) {\n            var i$12 = seqIndices$1[si$4];\n            if (charTypes[i$12] & TYPE_EN) {\n              for (var sj$4 = si$4 - 1; sj$4 >= 0 && (charTypes[seqIndices$1[sj$4]] & (TYPE_ET | BN_LIKE_TYPES)); sj$4--) {\n                changeCharType(seqIndices$1[sj$4], TYPE_EN);\n              }\n              for (si$4++; si$4 < seqIndices$1.length && (charTypes[seqIndices$1[si$4]] & (TYPE_ET | BN_LIKE_TYPES | TYPE_EN)); si$4++) {\n                if (charTypes[seqIndices$1[si$4]] !== TYPE_EN) {\n                  changeCharType(seqIndices$1[si$4], TYPE_EN);\n                }\n              }\n            }\n          }\n        }\n\n        // W6. Otherwise, separators and terminators change to Other Neutral.\n        if (charTypeCounts.get(TYPE_ET) || charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n          for (var si$5 = 0; si$5 < seqIndices$1.length; si$5++) {\n            var i$13 = seqIndices$1[si$5];\n            if (charTypes[i$13] & (TYPE_ET | TYPE_ES | TYPE_CS)) {\n              changeCharType(i$13, TYPE_ON);\n              // 5.2 transform adjacent BNs too:\n              for (var sj$5 = si$5 - 1; sj$5 >= 0 && (charTypes[seqIndices$1[sj$5]] & BN_LIKE_TYPES); sj$5--) {\n                changeCharType(seqIndices$1[sj$5], TYPE_ON);\n              }\n              for (var sj$6 = si$5 + 1; sj$6 < seqIndices$1.length && (charTypes[seqIndices$1[sj$6]] & BN_LIKE_TYPES); sj$6++) {\n                changeCharType(seqIndices$1[sj$6], TYPE_ON);\n              }\n            }\n          }\n        }\n\n        // W7. Search backward from each instance of a European number until the first strong type (R, L, or sos)\n        // is found. If an L is found, then change the type of the European number to L.\n        // NOTE: implemented in single forward pass for efficiency\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$6 = 0, prevStrongType = sosType; si$6 < seqIndices$1.length; si$6++) {\n            var i$14 = seqIndices$1[si$6];\n            var type = charTypes[i$14];\n            if (type & TYPE_EN) {\n              if (prevStrongType === TYPE_L) {\n                changeCharType(i$14, TYPE_L);\n              }\n            } else if (type & STRONG_TYPES) {\n              prevStrongType = type;\n            }\n          }\n        }\n\n        // === 3.3.5 Resolving Neutral and Isolate Formatting Types ===\n\n        if (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES)) {\n          // N0. Process bracket pairs in an isolating run sequence sequentially in the logical order of the text\n          // positions of the opening paired brackets using the logic given below. Within this scope, bidirectional\n          // types EN and AN are treated as R.\n          var R_TYPES_FOR_N_STEPS = (TYPE_R | TYPE_EN | TYPE_AN);\n          var STRONG_TYPES_FOR_N_STEPS = R_TYPES_FOR_N_STEPS | TYPE_L;\n\n          // * Identify the bracket pairs in the current isolating run sequence according to BD16.\n          var bracketPairs = [];\n          {\n            var openerStack = [];\n            for (var si$7 = 0; si$7 < seqIndices$1.length; si$7++) {\n              // NOTE: for any potential bracket character we also test that it still carries a NI\n              // type, as that may have been changed earlier. This doesn't seem to be explicitly\n              // called out in the spec, but is required for passage of certain tests.\n              if (charTypes[seqIndices$1[si$7]] & NEUTRAL_ISOLATE_TYPES) {\n                var char = string[seqIndices$1[si$7]];\n                var oppositeBracket = (void 0);\n                // Opening bracket\n                if (openingToClosingBracket(char) !== null) {\n                  if (openerStack.length < 63) {\n                    openerStack.push({ char: char, seqIndex: si$7 });\n                  } else {\n                    break\n                  }\n                }\n                // Closing bracket\n                else if ((oppositeBracket = closingToOpeningBracket(char)) !== null) {\n                  for (var stackIdx = openerStack.length - 1; stackIdx >= 0; stackIdx--) {\n                    var stackChar = openerStack[stackIdx].char;\n                    if (stackChar === oppositeBracket ||\n                      stackChar === closingToOpeningBracket(getCanonicalBracket(char)) ||\n                      openingToClosingBracket(getCanonicalBracket(stackChar)) === char\n                    ) {\n                      bracketPairs.push([openerStack[stackIdx].seqIndex, si$7]);\n                      openerStack.length = stackIdx; //pop the matching bracket and all following\n                      break\n                    }\n                  }\n                }\n              }\n            }\n            bracketPairs.sort(function (a, b) { return a[0] - b[0]; });\n          }\n          // * For each bracket-pair element in the list of pairs of text positions\n          for (var pairIdx = 0; pairIdx < bracketPairs.length; pairIdx++) {\n            var ref$1 = bracketPairs[pairIdx];\n            var openSeqIdx = ref$1[0];\n            var closeSeqIdx = ref$1[1];\n            // a. Inspect the bidirectional types of the characters enclosed within the bracket pair.\n            // b. If any strong type (either L or R) matching the embedding direction is found, set the type for both\n            // brackets in the pair to match the embedding direction.\n            var foundStrongType = false;\n            var useStrongType = 0;\n            for (var si$8 = openSeqIdx + 1; si$8 < closeSeqIdx; si$8++) {\n              var i$15 = seqIndices$1[si$8];\n              if (charTypes[i$15] & STRONG_TYPES_FOR_N_STEPS) {\n                foundStrongType = true;\n                var lr = (charTypes[i$15] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                if (lr === embedDirection) {\n                  useStrongType = lr;\n                  break\n                }\n              }\n            }\n            // c. Otherwise, if there is a strong type it must be opposite the embedding direction. Therefore, test\n            // for an established context with a preceding strong type by checking backwards before the opening paired\n            // bracket until the first strong type (L, R, or sos) is found.\n            //    1. If the preceding strong type is also opposite the embedding direction, context is established, so\n            //    set the type for both brackets in the pair to that direction.\n            //    2. Otherwise set the type for both brackets in the pair to the embedding direction.\n            if (foundStrongType && !useStrongType) {\n              useStrongType = sosType;\n              for (var si$9 = openSeqIdx - 1; si$9 >= 0; si$9--) {\n                var i$16 = seqIndices$1[si$9];\n                if (charTypes[i$16] & STRONG_TYPES_FOR_N_STEPS) {\n                  var lr$1 = (charTypes[i$16] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  if (lr$1 !== embedDirection) {\n                    useStrongType = lr$1;\n                  } else {\n                    useStrongType = embedDirection;\n                  }\n                  break\n                }\n              }\n            }\n            if (useStrongType) {\n              charTypes[seqIndices$1[openSeqIdx]] = charTypes[seqIndices$1[closeSeqIdx]] = useStrongType;\n              // * Any number of characters that had original bidirectional character type NSM prior to the application\n              // of W1 that immediately follow a paired bracket which changed to L or R under N0 should change to match\n              // the type of their preceding bracket.\n              if (useStrongType !== embedDirection) {\n                for (var si$10 = openSeqIdx + 1; si$10 < seqIndices$1.length; si$10++) {\n                  if (!(charTypes[seqIndices$1[si$10]] & BN_LIKE_TYPES)) {\n                    if (getBidiCharType(string[seqIndices$1[si$10]]) & TYPE_NSM) {\n                      charTypes[seqIndices$1[si$10]] = useStrongType;\n                    }\n                    break\n                  }\n                }\n              }\n              if (useStrongType !== embedDirection) {\n                for (var si$11 = closeSeqIdx + 1; si$11 < seqIndices$1.length; si$11++) {\n                  if (!(charTypes[seqIndices$1[si$11]] & BN_LIKE_TYPES)) {\n                    if (getBidiCharType(string[seqIndices$1[si$11]]) & TYPE_NSM) {\n                      charTypes[seqIndices$1[si$11]] = useStrongType;\n                    }\n                    break\n                  }\n                }\n              }\n            }\n          }\n\n          // N1. A sequence of NIs takes the direction of the surrounding strong text if the text on both sides has the\n          // same direction.\n          // N2. Any remaining NIs take the embedding direction.\n          for (var si$12 = 0; si$12 < seqIndices$1.length; si$12++) {\n            if (charTypes[seqIndices$1[si$12]] & NEUTRAL_ISOLATE_TYPES) {\n              var niRunStart = si$12, niRunEnd = si$12;\n              var prevType$2 = sosType; //si === 0 ? sosType : (charTypes[seqIndices[si - 1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L\n              for (var si2 = si$12 - 1; si2 >= 0; si2--) {\n                if (charTypes[seqIndices$1[si2]] & BN_LIKE_TYPES) {\n                  niRunStart = si2; //5.2 treat BNs adjacent to NIs as NIs\n                } else {\n                  prevType$2 = (charTypes[seqIndices$1[si2]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  break\n                }\n              }\n              var nextType$1 = eosType;\n              for (var si2$1 = si$12 + 1; si2$1 < seqIndices$1.length; si2$1++) {\n                if (charTypes[seqIndices$1[si2$1]] & (NEUTRAL_ISOLATE_TYPES | BN_LIKE_TYPES)) {\n                  niRunEnd = si2$1;\n                } else {\n                  nextType$1 = (charTypes[seqIndices$1[si2$1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  break\n                }\n              }\n              for (var sj$7 = niRunStart; sj$7 <= niRunEnd; sj$7++) {\n                charTypes[seqIndices$1[sj$7]] = prevType$2 === nextType$1 ? prevType$2 : embedDirection;\n              }\n              si$12 = niRunEnd;\n            }\n          }\n        }\n      }\n\n      // === 3.3.6 Resolving Implicit Levels ===\n\n      for (var i$17 = paragraph.start; i$17 <= paragraph.end; i$17++) {\n        var level$3 = embedLevels[i$17];\n        var type$1 = charTypes[i$17];\n        // I2. For all characters with an odd (right-to-left) embedding level, those of type L, EN or AN go up one level.\n        if (level$3 & 1) {\n          if (type$1 & (TYPE_L | TYPE_EN | TYPE_AN)) {\n            embedLevels[i$17]++;\n          }\n        }\n          // I1. For all characters with an even (left-to-right) embedding level, those of type R go up one level\n        // and those of type AN or EN go up two levels.\n        else {\n          if (type$1 & TYPE_R) {\n            embedLevels[i$17]++;\n          } else if (type$1 & (TYPE_AN | TYPE_EN)) {\n            embedLevels[i$17] += 2;\n          }\n        }\n\n        // 5.2: Resolve any LRE, RLE, LRO, RLO, PDF, or BN to the level of the preceding character if there is one,\n        // and otherwise to the base level.\n        if (type$1 & BN_LIKE_TYPES) {\n          embedLevels[i$17] = i$17 === 0 ? paragraph.level : embedLevels[i$17 - 1];\n        }\n\n        // 3.4 L1.1-4: Reset the embedding level of segment/paragraph separators, and any sequence of whitespace or\n        // isolate formatting characters preceding them or the end of the paragraph, to the paragraph level.\n        // NOTE: this will also need to be applied to each individual line ending after line wrapping occurs.\n        if (i$17 === paragraph.end || getBidiCharType(string[i$17]) & (TYPE_S | TYPE_B)) {\n          for (var j$1 = i$17; j$1 >= 0 && (getBidiCharType(string[j$1]) & TRAILING_TYPES); j$1--) {\n            embedLevels[j$1] = paragraph.level;\n          }\n        }\n      }\n    }\n\n    // DONE! The resolved levels can then be used, after line wrapping, to flip runs of characters\n    // according to section 3.4 Reordering Resolved Levels\n    return {\n      levels: embedLevels,\n      paragraphs: paragraphs\n    }\n\n    function determineAutoEmbedLevel (start, isFSI) {\n      // 3.3.1 P2 - P3\n      for (var i = start; i < string.length; i++) {\n        var charType = charTypes[i];\n        if (charType & (TYPE_R | TYPE_AL)) {\n          return 1\n        }\n        if ((charType & (TYPE_B | TYPE_L)) || (isFSI && charType === TYPE_PDI)) {\n          return 0\n        }\n        if (charType & ISOLATE_INIT_TYPES) {\n          var pdi = indexOfMatchingPDI(i);\n          i = pdi === -1 ? string.length : pdi;\n        }\n      }\n      return 0\n    }\n\n    function indexOfMatchingPDI (isolateStart) {\n      // 3.1.2 BD9\n      var isolationLevel = 1;\n      for (var i = isolateStart + 1; i < string.length; i++) {\n        var charType = charTypes[i];\n        if (charType & TYPE_B) {\n          break\n        }\n        if (charType & TYPE_PDI) {\n          if (--isolationLevel === 0) {\n            return i\n          }\n        } else if (charType & ISOLATE_INIT_TYPES) {\n          isolationLevel++;\n        }\n      }\n      return -1\n    }\n  }\n\n  // Bidi mirrored chars data, auto generated\n  var data = \"14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1\";\n\n  var mirrorMap;\n\n  function parse () {\n    if (!mirrorMap) {\n      //const start = performance.now()\n      var ref = parseCharacterMap(data, true);\n      var map = ref.map;\n      var reverseMap = ref.reverseMap;\n      // Combine both maps into one\n      reverseMap.forEach(function (value, key) {\n        map.set(key, value);\n      });\n      mirrorMap = map;\n      //console.log(`mirrored chars parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  function getMirroredCharacter (char) {\n    parse();\n    return mirrorMap.get(char) || null\n  }\n\n  /**\n   * Given a string and its resolved embedding levels, build a map of indices to replacement chars\n   * for any characters in right-to-left segments that have defined mirrored characters.\n   * @param string\n   * @param embeddingLevels\n   * @param [start]\n   * @param [end]\n   * @return {Map<number, string>}\n   */\n  function getMirroredCharactersMap(string, embeddingLevels, start, end) {\n    var strLen = string.length;\n    start = Math.max(0, start == null ? 0 : +start);\n    end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n\n    var map = new Map();\n    for (var i = start; i <= end; i++) {\n      if (embeddingLevels[i] & 1) { //only odd (rtl) levels\n        var mirror = getMirroredCharacter(string[i]);\n        if (mirror !== null) {\n          map.set(i, mirror);\n        }\n      }\n    }\n    return map\n  }\n\n  /**\n   * Given a start and end denoting a single line within a string, and a set of precalculated\n   * bidi embedding levels, produce a list of segments whose ordering should be flipped, in sequence.\n   * @param {string} string - the full input string\n   * @param {GetEmbeddingLevelsResult} embeddingLevelsResult - the result object from getEmbeddingLevels\n   * @param {number} [start] - first character in a subset of the full string\n   * @param {number} [end] - last character in a subset of the full string\n   * @return {number[][]} - the list of start/end segments that should be flipped, in order.\n   */\n  function getReorderSegments(string, embeddingLevelsResult, start, end) {\n    var strLen = string.length;\n    start = Math.max(0, start == null ? 0 : +start);\n    end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n\n    var segments = [];\n    embeddingLevelsResult.paragraphs.forEach(function (paragraph) {\n      var lineStart = Math.max(start, paragraph.start);\n      var lineEnd = Math.min(end, paragraph.end);\n      if (lineStart < lineEnd) {\n        // Local slice for mutation\n        var lineLevels = embeddingLevelsResult.levels.slice(lineStart, lineEnd + 1);\n\n        // 3.4 L1.4: Reset any sequence of whitespace characters and/or isolate formatting characters at the\n        // end of the line to the paragraph level.\n        for (var i = lineEnd; i >= lineStart && (getBidiCharType(string[i]) & TRAILING_TYPES); i--) {\n          lineLevels[i] = paragraph.level;\n        }\n\n        // L2. From the highest level found in the text to the lowest odd level on each line, including intermediate levels\n        // not actually present in the text, reverse any contiguous sequence of characters that are at that level or higher.\n        var maxLevel = paragraph.level;\n        var minOddLevel = Infinity;\n        for (var i$1 = 0; i$1 < lineLevels.length; i$1++) {\n          var level = lineLevels[i$1];\n          if (level > maxLevel) { maxLevel = level; }\n          if (level < minOddLevel) { minOddLevel = level | 1; }\n        }\n        for (var lvl = maxLevel; lvl >= minOddLevel; lvl--) {\n          for (var i$2 = 0; i$2 < lineLevels.length; i$2++) {\n            if (lineLevels[i$2] >= lvl) {\n              var segStart = i$2;\n              while (i$2 + 1 < lineLevels.length && lineLevels[i$2 + 1] >= lvl) {\n                i$2++;\n              }\n              if (i$2 > segStart) {\n                segments.push([segStart + lineStart, i$2 + lineStart]);\n              }\n            }\n          }\n        }\n      }\n    });\n    return segments\n  }\n\n  /**\n   * @param {string} string\n   * @param {GetEmbeddingLevelsResult} embedLevelsResult\n   * @param {number} [start]\n   * @param {number} [end]\n   * @return {string} the new string with bidi segments reordered\n   */\n  function getReorderedString(string, embedLevelsResult, start, end) {\n    var indices = getReorderedIndices(string, embedLevelsResult, start, end);\n    var chars = [].concat( string );\n    indices.forEach(function (charIndex, i) {\n      chars[i] = (\n        (embedLevelsResult.levels[charIndex] & 1) ? getMirroredCharacter(string[charIndex]) : null\n      ) || string[charIndex];\n    });\n    return chars.join('')\n  }\n\n  /**\n   * @param {string} string\n   * @param {GetEmbeddingLevelsResult} embedLevelsResult\n   * @param {number} [start]\n   * @param {number} [end]\n   * @return {number[]} an array with character indices in their new bidi order\n   */\n  function getReorderedIndices(string, embedLevelsResult, start, end) {\n    var segments = getReorderSegments(string, embedLevelsResult, start, end);\n    // Fill an array with indices\n    var indices = [];\n    for (var i = 0; i < string.length; i++) {\n      indices[i] = i;\n    }\n    // Reverse each segment in order\n    segments.forEach(function (ref) {\n      var start = ref[0];\n      var end = ref[1];\n\n      var slice = indices.slice(start, end + 1);\n      for (var i = slice.length; i--;) {\n        indices[end - i] = slice[i];\n      }\n    });\n    return indices\n  }\n\n  exports.closingToOpeningBracket = closingToOpeningBracket;\n  exports.getBidiCharType = getBidiCharType;\n  exports.getBidiCharTypeName = getBidiCharTypeName;\n  exports.getCanonicalBracket = getCanonicalBracket;\n  exports.getEmbeddingLevels = getEmbeddingLevels;\n  exports.getMirroredCharacter = getMirroredCharacter;\n  exports.getMirroredCharactersMap = getMirroredCharactersMap;\n  exports.getReorderSegments = getReorderSegments;\n  exports.getReorderedIndices = getReorderedIndices;\n  exports.getReorderedString = getReorderedString;\n  exports.openingToClosingBracket = openingToClosingBracket;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n}({}));\nreturn bidi}\n\nexport default bidiFactory;\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACT,IAAI,OAAQ,SAAU,OAAO;QAE3B,4CAA4C;QAC5C,IAAI,OAAO;YACT,KAAK;YACL,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QAEA,IAAI,QAAQ,CAAC;QACb,IAAI,iBAAiB,CAAC;QACtB,MAAM,CAAC,GAAG,GAAG,kBAAkB;QAC/B,cAAc,CAAC,EAAE,GAAG;QACpB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,SAAU,IAAI,EAAE,CAAC;YACzC,KAAK,CAAC,KAAK,GAAG,KAAM,IAAI;YACxB,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;QAChC;QACA,OAAO,MAAM,CAAC;QAEd,IAAI,qBAAqB,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;QAC1D,IAAI,eAAe,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE;QAC/C,IAAI,wBAAwB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;QACnH,IAAI,gBAAgB,MAAM,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;QACxF,IAAI,iBAAiB,MAAM,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,qBAAqB,MAAM,GAAG,GAAG;QAErF,IAAI,MAAM;QAEV,SAAS;YACP,IAAI,CAAC,KAAK;gBACR,iCAAiC;gBACjC,MAAM,IAAI;gBACV,IAAI,OAAO,SAAW,IAAI;oBACxB,IAAI,KAAK,cAAc,CAAC,OAAO;wBAC7B,IAAI,WAAW;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,SAAU,KAAK;4BAC3C,IAAI,MAAM,MAAM,KAAK,CAAC;4BACtB,IAAI,OAAO,GAAG,CAAC,EAAE;4BACjB,IAAI,OAAO,GAAG,CAAC,EAAE;4BACjB,OAAO,SAAS,MAAM;4BACtB,OAAO,OAAO,SAAS,MAAM,MAAM;4BACnC,IAAI,GAAG,CAAC,YAAY,MAAM,KAAK,CAAC,KAAK;4BACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gCAC7B,IAAI,GAAG,CAAC,EAAE,UAAU,KAAK,CAAC,KAAK;4BACjC;wBACF;oBACF;gBACF;gBAEA,IAAK,IAAI,QAAQ,KAAM,KAAM;YAC7B,oEAAoE;YACtE;QACF;QAEA;;;GAGC,GACD,SAAS,gBAAiB,IAAI;YAC5B;YACA,OAAO,IAAI,GAAG,CAAC,KAAK,WAAW,CAAC,OAAO,MAAM,CAAC;QAChD;QAEA,SAAS,oBAAoB,IAAI;YAC/B,OAAO,cAAc,CAAC,gBAAgB,MAAM;QAC9C;QAEA,0CAA0C;QAC1C,IAAI,SAAS;YACX,SAAS;YACT,aAAa;QACf;QAEA;;;;;;;GAOC,GACD,SAAS,kBAAmB,aAAa,EAAE,cAAc;YACvD,IAAI,QAAQ;YACZ,IAAI,WAAW;YACf,IAAI,MAAM,IAAI;YACd,IAAI,aAAa,kBAAkB,IAAI;YACvC,IAAI;YACJ,cAAc,KAAK,CAAC,KAAK,OAAO,CAAC,SAAS,MAAM,KAAK;gBACnD,IAAI,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;oBAC7B,IAAK,IAAI,IAAI,CAAC,OAAO,KAAM;wBACzB,MAAM;oBACR;gBACF,OAAO;oBACL,WAAW;oBACX,IAAI,MAAM,MAAM,KAAK,CAAC;oBACtB,IAAI,IAAI,GAAG,CAAC,EAAE;oBACd,IAAI,IAAI,GAAG,CAAC,EAAE;oBACd,IAAI,OAAO,aAAa,CAAC,YAAY,SAAS,GAAG;oBACjD,IAAI,OAAO,aAAa,CAAC,YAAY,SAAS,GAAG;oBACjD,IAAI,GAAG,CAAC,GAAG;oBACX,kBAAkB,WAAW,GAAG,CAAC,GAAG;gBACtC;YACF;YACA,OAAO;gBAAE,KAAK;gBAAK,YAAY;YAAW;QAC5C;QAEA,IAAI,aAAa,aAAa;QAE9B,SAAS;YACP,IAAI,CAAC,aAAa;gBAChB,iCAAiC;gBACjC,IAAI,MAAM,kBAAkB,OAAO,KAAK,EAAE;gBAC1C,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAI,aAAa,IAAI,UAAU;gBAC/B,cAAc;gBACd,cAAc;gBACd,YAAY,kBAAkB,OAAO,SAAS,EAAE,OAAO,GAAG;YAC1D,kEAAkE;YACpE;QACF;QAEA,SAAS,wBAAyB,IAAI;YACpC;YACA,OAAO,YAAY,GAAG,CAAC,SAAS;QAClC;QAEA,SAAS,wBAAyB,IAAI;YACpC;YACA,OAAO,YAAY,GAAG,CAAC,SAAS;QAClC;QAEA,SAAS,oBAAqB,IAAI;YAChC;YACA,OAAO,UAAU,GAAG,CAAC,SAAS;QAChC;QAEA,qBAAqB;QACrB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QAExB;;;;GAIC,GAED;;;;;;;;;GASC,GACD,SAAS,mBAAoB,MAAM,EAAE,aAAa;YAChD,IAAI,YAAY;YAEhB,8EAA8E;YAC9E,IAAI,YAAY,IAAI,YAAY,OAAO,MAAM;YAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,SAAS,CAAC,EAAE,GAAG,gBAAgB,MAAM,CAAC,EAAE;YAC1C;YAEA,IAAI,iBAAiB,IAAI,OAAO,4CAA4C;YAC5E,SAAS,eAAe,CAAC,EAAE,IAAI;gBAC7B,IAAI,UAAU,SAAS,CAAC,EAAE;gBAC1B,SAAS,CAAC,EAAE,GAAG;gBACf,eAAe,GAAG,CAAC,SAAS,eAAe,GAAG,CAAC,WAAW;gBAC1D,IAAI,UAAU,uBAAuB;oBACnC,eAAe,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,yBAAyB;gBACxF;gBACA,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,SAAS,CAAC,IAAI;gBAC3D,IAAI,OAAO,uBAAuB;oBAChC,eAAe,GAAG,CAAC,uBAAuB,CAAC,eAAe,GAAG,CAAC,0BAA0B,CAAC,IAAI;gBAC/F;YACF;YAEA,IAAI,cAAc,IAAI,WAAW,OAAO,MAAM;YAC9C,IAAI,iBAAiB,IAAI,OAAO,yBAAyB;YAEzD,oCAAoC;YACpC,2CAA2C;YAC3C,IAAI,aAAa,EAAE,EAAE,6BAA6B;YAClD,IAAI,YAAY;YAChB,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,MAAM,EAAE,MAAO;gBAC5C,IAAI,CAAC,WAAW;oBACd,WAAW,IAAI,CAAC,YAAY;wBAC1B,OAAO;wBACP,KAAK,OAAO,MAAM,GAAG;wBACrB,6CAA6C;wBAC7C,OAAO,kBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,IAAI,wBAAwB,KAAK;oBAClG;gBACF;gBACA,IAAI,SAAS,CAAC,IAAI,GAAG,QAAQ;oBAC3B,UAAU,GAAG,GAAG;oBAChB,YAAY;gBACd;YACF;YAEA,IAAI,mBAAmB,WAAW,WAAW,WAAW,WAAW,qBAAqB,WAAW,WAAW;YAC9G,IAAI,WAAW,SAAU,CAAC;gBAAI,OAAO,IAAI,CAAC,AAAC,IAAI,IAAK,IAAI,CAAC;YAAG;YAC5D,IAAI,UAAU,SAAU,CAAC;gBAAI,OAAO,IAAI,CAAC,AAAC,IAAI,IAAK,IAAI,CAAC;YAAG;YAE3D,sDAAsD;YACtD,IAAK,IAAI,UAAU,GAAG,UAAU,WAAW,MAAM,EAAE,UAAW;gBAC5D,YAAY,UAAU,CAAC,QAAQ;gBAC/B,IAAI,cAAc;oBAAC;wBACjB,QAAQ,UAAU,KAAK;wBACvB,WAAW;wBACX,UAAU,EAAE,MAAM;oBACpB;iBAAE;gBACF,IAAI,WAAY,KAAK;gBACrB,IAAI,uBAAuB;gBAC3B,IAAI,yBAAyB;gBAC7B,IAAI,oBAAoB;gBACxB,eAAe,KAAK;gBAEpB,+CAA+C;gBAC/C,IAAK,IAAI,MAAM,UAAU,KAAK,EAAE,OAAO,UAAU,GAAG,EAAE,MAAO;oBAC3D,IAAI,WAAW,SAAS,CAAC,IAAI;oBAC7B,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;oBAE9C,qBAAqB;oBACrB,eAAe,GAAG,CAAC,UAAU,CAAC,eAAe,GAAG,CAAC,aAAa,CAAC,IAAI;oBACnE,IAAI,WAAW,uBAAuB;wBACpC,eAAe,GAAG,CAAC,uBAAuB,CAAC,eAAe,GAAG,CAAC,0BAA0B,CAAC,IAAI;oBAC/F;oBAEA,qCAAqC;oBACrC,IAAI,WAAW,kBAAkB;wBAC/B,IAAI,WAAW,CAAC,WAAW,QAAQ,GAAG;4BACpC,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,MAAM;4BAC1C,IAAI,QAAQ,CAAC,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,MAAM;4BACxE,IAAI,SAAS,aAAa,CAAC,wBAAwB,CAAC,wBAAwB;gCAC1E,YAAY,IAAI,CAAC;oCACf,QAAQ;oCACR,WAAW;oCACX,UAAU;gCACZ;4BACF,OAAO,IAAI,CAAC,sBAAsB;gCAChC;4BACF;wBACF,OAGK,IAAI,WAAW,CAAC,WAAW,QAAQ,GAAG;4BACzC,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,MAAM;4BAC1C,IAAI,UAAU,CAAC,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,MAAM;4BAC1E,IAAI,WAAW,aAAa,CAAC,wBAAwB,CAAC,wBAAwB;gCAC5E,YAAY,IAAI,CAAC;oCACf,QAAQ;oCACR,WAAW,AAAC,WAAW,WAAY,SAAS;oCAC5C,UAAU;gCACZ;4BACF,OAAO,IAAI,CAAC,sBAAsB;gCAChC;4BACF;wBACF,OAGK,IAAI,WAAW,oBAAoB;4BACtC,sCAAsC;4BACtC,IAAI,WAAW,UAAU;gCACvB,WAAW,wBAAwB,MAAM,GAAG,UAAU,IAAI,WAAW;4BACvE;4BAEA,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM;4BAClC,IAAI,SAAS,SAAS,EAAE;gCACtB,eAAe,KAAK,SAAS,SAAS;4BACxC;4BACA,IAAI,UAAU,CAAC,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,MAAM;4BAC1E,IAAI,WAAW,aAAa,yBAAyB,KAAK,2BAA2B,GAAG;gCACtF;gCACA,YAAY,IAAI,CAAC;oCACf,QAAQ;oCACR,WAAW;oCACX,UAAU;oCACV,gBAAgB;gCAClB;4BACF,OAAO;gCACL;4BACF;wBACF,OAGK,IAAI,WAAW,UAAU;4BAC5B,IAAI,uBAAuB,GAAG;gCAC5B;4BACF,OAAO,IAAI,oBAAoB,GAAG;gCAChC,yBAAyB;gCACzB,MAAO,CAAC,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAE;oCACpD,YAAY,GAAG;gCACjB;gCACA,gDAAgD;gCAChD,IAAI,gBAAgB,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,cAAc;gCACtE,IAAI,iBAAiB,MAAM;oCACzB,eAAe,GAAG,CAAC,eAAe;oCAClC,eAAe,GAAG,CAAC,KAAK;gCAC1B;gCACA,YAAY,GAAG;gCACf;4BACF;4BACA,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;4BAC9C,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM;4BAClC,IAAI,SAAS,SAAS,EAAE;gCACtB,eAAe,KAAK,SAAS,SAAS;4BACxC;wBACF,OAIK,IAAI,WAAW,UAAU;4BAC5B,IAAI,yBAAyB,GAAG;gCAC9B,IAAI,yBAAyB,GAAG;oCAC9B;gCACF,OAAO,IAAI,CAAC,SAAS,QAAQ,IAAI,YAAY,MAAM,GAAG,GAAG;oCACvD,YAAY,GAAG;oCACf,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;gCAChD;4BACF;4BACA,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,MAAM;wBAC5C,OAGK,IAAI,WAAW,QAAQ;4BAC1B,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK;wBACpC;oBACF,OAGK;wBACH,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM;wBAClC,yGAAyG;wBACzG,IAAI,SAAS,SAAS,IAAI,aAAa,SAAS;4BAC9C,eAAe,KAAK,SAAS,SAAS;wBACxC;oBACF;gBACF;gBAEA,qDAAqD;gBAErD,kEAAkE;gBAClE,4FAA4F;gBAC5F,2CAA2C;gBAE3C,YAAY;gBACZ,kEAAkE;gBAClE,IAAI,YAAY,EAAE;gBAClB,IAAI,aAAa;gBACjB,IAAK,IAAI,MAAM,UAAU,KAAK,EAAE,OAAO,UAAU,GAAG,EAAE,MAAO;oBAC3D,IAAI,aAAa,SAAS,CAAC,IAAI;oBAC/B,IAAI,CAAC,CAAC,aAAa,aAAa,GAAG;wBACjC,IAAI,MAAM,WAAW,CAAC,IAAI;wBAC1B,IAAI,aAAa,aAAa;wBAC9B,IAAI,QAAQ,eAAe;wBAC3B,IAAI,cAAc,QAAQ,WAAW,MAAM,EAAE;4BAC3C,WAAW,IAAI,GAAG;4BAClB,WAAW,iBAAiB,GAAG;wBACjC,OAAO;4BACL,UAAU,IAAI,CAAC,aAAa;gCAC1B,QAAQ;gCACR,MAAM;gCACN,QAAQ;gCACR,gBAAgB;gCAChB,mBAAmB;4BACrB;wBACF;oBACF;gBACF;gBACA,IAAI,mBAAmB,EAAE,EAAE,iDAAiD;gBAC5E,IAAK,IAAI,SAAS,GAAG,SAAS,UAAU,MAAM,EAAE,SAAU;oBACxD,IAAI,MAAM,SAAS,CAAC,OAAO;oBAC3B,IAAI,CAAC,IAAI,cAAc,IAAK,IAAI,cAAc,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,MAAM,GAAI;wBAClF,IAAI,UAAU;4BAAC,aAAa;yBAAI;wBAChC,IAAK,IAAI,WAAY,KAAK,GAAI,cAAc,WAAW,iBAAiB,IAAI,CAAC,WAAW,eAAe,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,MAAO;4BACrI,IAAK,IAAI,MAAM,SAAS,GAAG,MAAM,UAAU,MAAM,EAAE,MAAO;gCACxD,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU;oCACtC,QAAQ,IAAI,CAAC,aAAa,SAAS,CAAC,IAAI;oCACxC;gCACF;4BACF;wBACF;wBACA,8CAA8C;wBAC9C,IAAI,aAAa,EAAE;wBACnB,IAAK,IAAI,MAAM,GAAG,MAAM,QAAQ,MAAM,EAAE,MAAO;4BAC7C,IAAI,QAAQ,OAAO,CAAC,IAAI;4BACxB,IAAK,IAAI,IAAI,MAAM,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,IAAK;gCAC/C,WAAW,IAAI,CAAC;4BAClB;wBACF;wBACA,+BAA+B;wBAC/B,IAAI,aAAa,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;wBAC3C,IAAI,YAAY,UAAU,KAAK;wBAC/B,IAAK,IAAI,MAAM,UAAU,CAAC,EAAE,GAAG,GAAG,OAAO,GAAG,MAAO;4BACjD,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,GAAG,aAAa,GAAG;gCACrC,YAAY,WAAW,CAAC,IAAI;gCAC5B;4BACF;wBACF;wBACA,IAAI,YAAY,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;wBACjD,IAAI,YAAY,WAAW,CAAC,UAAU;wBACtC,IAAI,YAAY,UAAU,KAAK;wBAC/B,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,GAAG,kBAAkB,GAAG;4BAChD,IAAK,IAAI,MAAM,YAAY,GAAG,OAAO,UAAU,GAAG,EAAE,MAAO;gCACzD,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,GAAG,aAAa,GAAG;oCACrC,YAAY,WAAW,CAAC,IAAI;oCAC5B;gCACF;4BACF;wBACF;wBACA,iBAAiB,IAAI,CAAC;4BACpB,aAAa;4BACb,UAAU,KAAK,GAAG,CAAC,WAAW,cAAc,IAAI,SAAS;4BACzD,UAAU,KAAK,GAAG,CAAC,WAAW,aAAa,IAAI,SAAS;wBAC1D;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,IAAK,IAAI,SAAS,GAAG,SAAS,iBAAiB,MAAM,EAAE,SAAU;oBAC/D,IAAI,MAAM,gBAAgB,CAAC,OAAO;oBAClC,IAAI,eAAe,IAAI,WAAW;oBAClC,IAAI,UAAU,IAAI,QAAQ;oBAC1B,IAAI,UAAU,IAAI,QAAQ;oBAC1B;;;;SAIC,GACD,IAAI,iBAAiB,AAAC,AAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC,GAAI,IAAK,SAAS;oBAErE,qCAAqC;oBAErC,qGAAqG;oBACrG,uGAAuG;oBACvG,+FAA+F;oBAC/F,IAAI,eAAe,GAAG,CAAC,WAAW;wBAChC,IAAK,IAAI,KAAK,GAAG,KAAK,aAAa,MAAM,EAAE,KAAM;4BAC/C,IAAI,MAAM,YAAY,CAAC,GAAG;4BAC1B,IAAI,SAAS,CAAC,IAAI,GAAG,UAAU;gCAC7B,IAAI,WAAW;gCACf,IAAK,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,KAAM;oCACnC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG;wCAClD,WAAW,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC;wCACtC;oCACF;gCACF;gCACA,eAAe,KAAK,AAAC,WAAW,CAAC,qBAAqB,QAAQ,IAAK,UAAU;4BAC/E;wBACF;oBACF;oBAEA,6GAA6G;oBAC7G,wFAAwF;oBACxF,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,MAAM,YAAY,CAAC,KAAK;4BAC5B,IAAI,SAAS,CAAC,IAAI,GAAG,SAAS;gCAC5B,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,CAAC,GAAG,OAAQ;oCAC5C,IAAI,eAAe,SAAS,CAAC,IAAI,UAAU,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC;oCACxE,IAAI,eAAe,cAAc;wCAC/B,IAAI,iBAAiB,SAAS;4CAC5B,eAAe,KAAK;wCACtB;wCACA;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,0BAA0B;oBAC1B,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS;gCAC7B,eAAe,MAAM;4BACvB;wBACF;oBACF;oBAEA,6GAA6G;oBAC7G,uEAAuE;oBACvE,IAAI,eAAe,GAAG,CAAC,YAAY,eAAe,GAAG,CAAC,UAAU;wBAC9D,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,GAAG,GAAG,OAAQ;4BACzD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,UAAU,OAAO,GAAG;gCACzC,IAAI,aAAa,GAAG,WAAW;gCAC/B,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,GAAG,OAAQ;oCAC3C,aAAa,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC;oCAC1C,IAAI,CAAC,CAAC,aAAa,aAAa,GAAG;wCACjC;oCACF;gCACF;gCACA,IAAK,IAAI,OAAO,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;oCAC5D,WAAW,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC;oCACxC,IAAI,CAAC,CAAC,WAAW,aAAa,GAAG;wCAC/B;oCACF;gCACF;gCACA,IAAI,eAAe,YAAY,CAAC,SAAS,CAAC,KAAK,KAAK,UAAU,eAAe,UAAW,aAAa,CAAC,UAAU,OAAO,CAAE,GAAG;oCAC1H,eAAe,MAAM;gCACvB;4BACF;wBACF;oBACF;oBAEA,uGAAuG;oBACvG,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS;gCAC7B,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,KAAM,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,aAAa,GAAI,OAAQ;oCAC1G,eAAe,YAAY,CAAC,KAAK,EAAE;gCACrC;gCACA,IAAK,QAAQ,OAAO,aAAa,MAAM,IAAK,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,gBAAgB,OAAO,GAAI,OAAQ;oCACxH,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS;wCAC7C,eAAe,YAAY,CAAC,KAAK,EAAE;oCACrC;gCACF;4BACF;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,IAAI,eAAe,GAAG,CAAC,YAAY,eAAe,GAAG,CAAC,YAAY,eAAe,GAAG,CAAC,UAAU;wBAC7F,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,UAAU,UAAU,OAAO,GAAG;gCACnD,eAAe,MAAM;gCACrB,kCAAkC;gCAClC,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,KAAM,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,eAAgB,OAAQ;oCAC9F,eAAe,YAAY,CAAC,KAAK,EAAE;gCACrC;gCACA,IAAK,IAAI,OAAO,OAAO,GAAG,OAAO,aAAa,MAAM,IAAK,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,eAAgB,OAAQ;oCAC/G,eAAe,YAAY,CAAC,KAAK,EAAE;gCACrC;4BACF;wBACF;oBACF;oBAEA,yGAAyG;oBACzG,gFAAgF;oBAChF,0DAA0D;oBAC1D,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,iBAAiB,SAAS,OAAO,aAAa,MAAM,EAAE,OAAQ;4BAC/E,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,OAAO,SAAS,CAAC,KAAK;4BAC1B,IAAI,OAAO,SAAS;gCAClB,IAAI,mBAAmB,QAAQ;oCAC7B,eAAe,MAAM;gCACvB;4BACF,OAAO,IAAI,OAAO,cAAc;gCAC9B,iBAAiB;4BACnB;wBACF;oBACF;oBAEA,+DAA+D;oBAE/D,IAAI,eAAe,GAAG,CAAC,wBAAwB;wBAC7C,uGAAuG;wBACvG,yGAAyG;wBACzG,oCAAoC;wBACpC,IAAI,sBAAuB,SAAS,UAAU;wBAC9C,IAAI,2BAA2B,sBAAsB;wBAErD,wFAAwF;wBACxF,IAAI,eAAe,EAAE;wBACrB;4BACE,IAAI,cAAc,EAAE;4BACpB,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;gCACrD,oFAAoF;gCACpF,kFAAkF;gCAClF,wEAAwE;gCACxE,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,uBAAuB;oCACzD,IAAI,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;oCACrC,IAAI,kBAAmB,KAAK;oCAC5B,kBAAkB;oCAClB,IAAI,wBAAwB,UAAU,MAAM;wCAC1C,IAAI,YAAY,MAAM,GAAG,IAAI;4CAC3B,YAAY,IAAI,CAAC;gDAAE,MAAM;gDAAM,UAAU;4CAAK;wCAChD,OAAO;4CACL;wCACF;oCACF,OAEK,IAAI,CAAC,kBAAkB,wBAAwB,KAAK,MAAM,MAAM;wCACnE,IAAK,IAAI,WAAW,YAAY,MAAM,GAAG,GAAG,YAAY,GAAG,WAAY;4CACrE,IAAI,YAAY,WAAW,CAAC,SAAS,CAAC,IAAI;4CAC1C,IAAI,cAAc,mBAChB,cAAc,wBAAwB,oBAAoB,UAC1D,wBAAwB,oBAAoB,gBAAgB,MAC5D;gDACA,aAAa,IAAI,CAAC;oDAAC,WAAW,CAAC,SAAS,CAAC,QAAQ;oDAAE;iDAAK;gDACxD,YAAY,MAAM,GAAG,UAAU,4CAA4C;gDAC3E;4CACF;wCACF;oCACF;gCACF;4BACF;4BACA,aAAa,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;gCAAI,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;4BAAE;wBAC1D;wBACA,yEAAyE;wBACzE,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,MAAM,EAAE,UAAW;4BAC9D,IAAI,QAAQ,YAAY,CAAC,QAAQ;4BACjC,IAAI,aAAa,KAAK,CAAC,EAAE;4BACzB,IAAI,cAAc,KAAK,CAAC,EAAE;4BAC1B,yFAAyF;4BACzF,yGAAyG;4BACzG,yDAAyD;4BACzD,IAAI,kBAAkB;4BACtB,IAAI,gBAAgB;4BACpB,IAAK,IAAI,OAAO,aAAa,GAAG,OAAO,aAAa,OAAQ;gCAC1D,IAAI,OAAO,YAAY,CAAC,KAAK;gCAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,0BAA0B;oCAC9C,kBAAkB;oCAClB,IAAI,KAAK,AAAC,SAAS,CAAC,KAAK,GAAG,sBAAuB,SAAS;oCAC5D,IAAI,OAAO,gBAAgB;wCACzB,gBAAgB;wCAChB;oCACF;gCACF;4BACF;4BACA,uGAAuG;4BACvG,0GAA0G;4BAC1G,+DAA+D;4BAC/D,0GAA0G;4BAC1G,mEAAmE;4BACnE,yFAAyF;4BACzF,IAAI,mBAAmB,CAAC,eAAe;gCACrC,gBAAgB;gCAChB,IAAK,IAAI,OAAO,aAAa,GAAG,QAAQ,GAAG,OAAQ;oCACjD,IAAI,OAAO,YAAY,CAAC,KAAK;oCAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,0BAA0B;wCAC9C,IAAI,OAAO,AAAC,SAAS,CAAC,KAAK,GAAG,sBAAuB,SAAS;wCAC9D,IAAI,SAAS,gBAAgB;4CAC3B,gBAAgB;wCAClB,OAAO;4CACL,gBAAgB;wCAClB;wCACA;oCACF;gCACF;4BACF;4BACA,IAAI,eAAe;gCACjB,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG;gCAC7E,yGAAyG;gCACzG,yGAAyG;gCACzG,uCAAuC;gCACvC,IAAI,kBAAkB,gBAAgB;oCACpC,IAAK,IAAI,QAAQ,aAAa,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;wCACrE,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG;4CACrD,IAAI,gBAAgB,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,UAAU;gDAC3D,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;4CACnC;4CACA;wCACF;oCACF;gCACF;gCACA,IAAI,kBAAkB,gBAAgB;oCACpC,IAAK,IAAI,QAAQ,cAAc,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;wCACtE,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG;4CACrD,IAAI,gBAAgB,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,UAAU;gDAC3D,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;4CACnC;4CACA;wCACF;oCACF;gCACF;4BACF;wBACF;wBAEA,6GAA6G;wBAC7G,kBAAkB;wBAClB,sDAAsD;wBACtD,IAAK,IAAI,QAAQ,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;4BACxD,IAAI,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,uBAAuB;gCAC1D,IAAI,aAAa,OAAO,WAAW;gCACnC,IAAI,aAAa,SAAS,8FAA8F;gCACxH,IAAK,IAAI,MAAM,QAAQ,GAAG,OAAO,GAAG,MAAO;oCACzC,IAAI,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,eAAe;wCAChD,aAAa,KAAK,sCAAsC;oCAC1D,OAAO;wCACL,aAAa,AAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,sBAAuB,SAAS;wCAC7E;oCACF;gCACF;gCACA,IAAI,aAAa;gCACjB,IAAK,IAAI,QAAQ,QAAQ,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;oCAChE,IAAI,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,aAAa,GAAG;wCAC5E,WAAW;oCACb,OAAO;wCACL,aAAa,AAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,sBAAuB,SAAS;wCAC/E;oCACF;gCACF;gCACA,IAAK,IAAI,OAAO,YAAY,QAAQ,UAAU,OAAQ;oCACpD,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,eAAe,aAAa,aAAa;gCAC3E;gCACA,QAAQ;4BACV;wBACF;oBACF;gBACF;gBAEA,0CAA0C;gBAE1C,IAAK,IAAI,OAAO,UAAU,KAAK,EAAE,QAAQ,UAAU,GAAG,EAAE,OAAQ;oBAC9D,IAAI,UAAU,WAAW,CAAC,KAAK;oBAC/B,IAAI,SAAS,SAAS,CAAC,KAAK;oBAC5B,iHAAiH;oBACjH,IAAI,UAAU,GAAG;wBACf,IAAI,SAAS,CAAC,SAAS,UAAU,OAAO,GAAG;4BACzC,WAAW,CAAC,KAAK;wBACnB;oBACF,OAGK;wBACH,IAAI,SAAS,QAAQ;4BACnB,WAAW,CAAC,KAAK;wBACnB,OAAO,IAAI,SAAS,CAAC,UAAU,OAAO,GAAG;4BACvC,WAAW,CAAC,KAAK,IAAI;wBACvB;oBACF;oBAEA,2GAA2G;oBAC3G,mCAAmC;oBACnC,IAAI,SAAS,eAAe;wBAC1B,WAAW,CAAC,KAAK,GAAG,SAAS,IAAI,UAAU,KAAK,GAAG,WAAW,CAAC,OAAO,EAAE;oBAC1E;oBAEA,2GAA2G;oBAC3G,oGAAoG;oBACpG,qGAAqG;oBACrG,IAAI,SAAS,UAAU,GAAG,IAAI,gBAAgB,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;wBAC/E,IAAK,IAAI,MAAM,MAAM,OAAO,KAAM,gBAAgB,MAAM,CAAC,IAAI,IAAI,gBAAiB,MAAO;4BACvF,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK;wBACpC;oBACF;gBACF;YACF;YAEA,8FAA8F;YAC9F,sDAAsD;YACtD,OAAO;gBACL,QAAQ;gBACR,YAAY;YACd;;YAEA,SAAS,wBAAyB,KAAK,EAAE,KAAK;gBAC5C,gBAAgB;gBAChB,IAAK,IAAI,IAAI,OAAO,IAAI,OAAO,MAAM,EAAE,IAAK;oBAC1C,IAAI,WAAW,SAAS,CAAC,EAAE;oBAC3B,IAAI,WAAW,CAAC,SAAS,OAAO,GAAG;wBACjC,OAAO;oBACT;oBACA,IAAI,AAAC,WAAW,CAAC,SAAS,MAAM,KAAO,SAAS,aAAa,UAAW;wBACtE,OAAO;oBACT;oBACA,IAAI,WAAW,oBAAoB;wBACjC,IAAI,MAAM,mBAAmB;wBAC7B,IAAI,QAAQ,CAAC,IAAI,OAAO,MAAM,GAAG;oBACnC;gBACF;gBACA,OAAO;YACT;YAEA,SAAS,mBAAoB,YAAY;gBACvC,YAAY;gBACZ,IAAI,iBAAiB;gBACrB,IAAK,IAAI,IAAI,eAAe,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACrD,IAAI,WAAW,SAAS,CAAC,EAAE;oBAC3B,IAAI,WAAW,QAAQ;wBACrB;oBACF;oBACA,IAAI,WAAW,UAAU;wBACvB,IAAI,EAAE,mBAAmB,GAAG;4BAC1B,OAAO;wBACT;oBACF,OAAO,IAAI,WAAW,oBAAoB;wBACxC;oBACF;gBACF;gBACA,OAAO,CAAC;YACV;QACF;QAEA,2CAA2C;QAC3C,IAAI,OAAO;QAEX,IAAI;QAEJ,SAAS;YACP,IAAI,CAAC,WAAW;gBACd,iCAAiC;gBACjC,IAAI,MAAM,kBAAkB,MAAM;gBAClC,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAI,aAAa,IAAI,UAAU;gBAC/B,6BAA6B;gBAC7B,WAAW,OAAO,CAAC,SAAU,KAAK,EAAE,GAAG;oBACrC,IAAI,GAAG,CAAC,KAAK;gBACf;gBACA,YAAY;YACZ,wEAAwE;YAC1E;QACF;QAEA,SAAS,qBAAsB,IAAI;YACjC;YACA,OAAO,UAAU,GAAG,CAAC,SAAS;QAChC;QAEA;;;;;;;;GAQC,GACD,SAAS,yBAAyB,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG;YACnE,IAAI,SAAS,OAAO,MAAM;YAC1B,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO,IAAI,CAAC;YACzC,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,OAAO,OAAO,SAAS,IAAI,CAAC;YAEvD,IAAI,MAAM,IAAI;YACd,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,IAAI,eAAe,CAAC,EAAE,GAAG,GAAG;oBAC1B,IAAI,SAAS,qBAAqB,MAAM,CAAC,EAAE;oBAC3C,IAAI,WAAW,MAAM;wBACnB,IAAI,GAAG,CAAC,GAAG;oBACb;gBACF;YACF;YACA,OAAO;QACT;QAEA;;;;;;;;GAQC,GACD,SAAS,mBAAmB,MAAM,EAAE,qBAAqB,EAAE,KAAK,EAAE,GAAG;YACnE,IAAI,SAAS,OAAO,MAAM;YAC1B,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO,IAAI,CAAC;YACzC,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,OAAO,OAAO,SAAS,IAAI,CAAC;YAEvD,IAAI,WAAW,EAAE;YACjB,sBAAsB,UAAU,CAAC,OAAO,CAAC,SAAU,SAAS;gBAC1D,IAAI,YAAY,KAAK,GAAG,CAAC,OAAO,UAAU,KAAK;gBAC/C,IAAI,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU,GAAG;gBACzC,IAAI,YAAY,SAAS;oBACvB,2BAA2B;oBAC3B,IAAI,aAAa,sBAAsB,MAAM,CAAC,KAAK,CAAC,WAAW,UAAU;oBAEzE,oGAAoG;oBACpG,0CAA0C;oBAC1C,IAAK,IAAI,IAAI,SAAS,KAAK,aAAc,gBAAgB,MAAM,CAAC,EAAE,IAAI,gBAAiB,IAAK;wBAC1F,UAAU,CAAC,EAAE,GAAG,UAAU,KAAK;oBACjC;oBAEA,mHAAmH;oBACnH,oHAAoH;oBACpH,IAAI,WAAW,UAAU,KAAK;oBAC9B,IAAI,cAAc;oBAClB,IAAK,IAAI,MAAM,GAAG,MAAM,WAAW,MAAM,EAAE,MAAO;wBAChD,IAAI,QAAQ,UAAU,CAAC,IAAI;wBAC3B,IAAI,QAAQ,UAAU;4BAAE,WAAW;wBAAO;wBAC1C,IAAI,QAAQ,aAAa;4BAAE,cAAc,QAAQ;wBAAG;oBACtD;oBACA,IAAK,IAAI,MAAM,UAAU,OAAO,aAAa,MAAO;wBAClD,IAAK,IAAI,MAAM,GAAG,MAAM,WAAW,MAAM,EAAE,MAAO;4BAChD,IAAI,UAAU,CAAC,IAAI,IAAI,KAAK;gCAC1B,IAAI,WAAW;gCACf,MAAO,MAAM,IAAI,WAAW,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,IAAK;oCAChE;gCACF;gCACA,IAAI,MAAM,UAAU;oCAClB,SAAS,IAAI,CAAC;wCAAC,WAAW;wCAAW,MAAM;qCAAU;gCACvD;4BACF;wBACF;oBACF;gBACF;YACF;YACA,OAAO;QACT;QAEA;;;;;;GAMC,GACD,SAAS,mBAAmB,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG;YAC/D,IAAI,UAAU,oBAAoB,QAAQ,mBAAmB,OAAO;YACpE,IAAI,QAAQ,EAAE,CAAC,MAAM,CAAE;YACvB,QAAQ,OAAO,CAAC,SAAU,SAAS,EAAE,CAAC;gBACpC,KAAK,CAAC,EAAE,GAAG,CACT,AAAC,kBAAkB,MAAM,CAAC,UAAU,GAAG,IAAK,qBAAqB,MAAM,CAAC,UAAU,IAAI,IACxF,KAAK,MAAM,CAAC,UAAU;YACxB;YACA,OAAO,MAAM,IAAI,CAAC;QACpB;QAEA;;;;;;GAMC,GACD,SAAS,oBAAoB,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG;YAChE,IAAI,WAAW,mBAAmB,QAAQ,mBAAmB,OAAO;YACpE,6BAA6B;YAC7B,IAAI,UAAU,EAAE;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,OAAO,CAAC,EAAE,GAAG;YACf;YACA,gCAAgC;YAChC,SAAS,OAAO,CAAC,SAAU,GAAG;gBAC5B,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAClB,IAAI,MAAM,GAAG,CAAC,EAAE;gBAEhB,IAAI,QAAQ,QAAQ,KAAK,CAAC,OAAO,MAAM;gBACvC,IAAK,IAAI,IAAI,MAAM,MAAM,EAAE,KAAM;oBAC/B,OAAO,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;gBAC7B;YACF;YACA,OAAO;QACT;QAEA,QAAQ,uBAAuB,GAAG;QAClC,QAAQ,eAAe,GAAG;QAC1B,QAAQ,mBAAmB,GAAG;QAC9B,QAAQ,mBAAmB,GAAG;QAC9B,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,oBAAoB,GAAG;QAC/B,QAAQ,wBAAwB,GAAG;QACnC,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,mBAAmB,GAAG;QAC9B,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,uBAAuB,GAAG;QAElC,OAAO,cAAc,CAAC,SAAS,cAAc;YAAE,OAAO;QAAK;QAE3D,OAAO;IAET,EAAE,CAAC;IACH,OAAO;AAAI;uCAEI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/troika-three-utils/dist/troika-three-utils.esm.js"], "sourcesContent": ["import { ShaderChunk, UniformsUtils, MeshDepthMaterial, RGBADepthPacking, MeshDistanceMaterial, ShaderLib, Matrix4, Vector3, Mesh, CylinderGeometry, Vector2, MeshStandardMaterial, DoubleSide } from 'three';\n\n/**\n * Regular expression for matching the `void main() {` opener line in GLSL.\n * @type {RegExp}\n */\nconst voidMainRegExp = /\\bvoid\\s+main\\s*\\(\\s*\\)\\s*{/g;\n\n/**\n * Recursively expands all `#include <xyz>` statements within string of shader code.\n * Copied from three's WebGLProgram#parseIncludes for external use.\n *\n * @param {string} source - The GLSL source code to evaluate\n * @return {string} The GLSL code with all includes expanded\n */\nfunction expandShaderIncludes( source ) {\n  const pattern = /^[ \\t]*#include +<([\\w\\d./]+)>/gm;\n  function replace(match, include) {\n    let chunk = ShaderChunk[include];\n    return chunk ? expandShaderIncludes(chunk) : match\n  }\n  return source.replace( pattern, replace )\n}\n\n/*\n * This is a direct copy of MathUtils.generateUUID from Three.js, to preserve compatibility with three\n * versions before 0.113.0 as it was changed from Math to MathUtils in that version.\n * https://github.com/mrdoob/three.js/blob/dd8b5aa3b270c17096b90945cd2d6d1b13aaec53/src/math/MathUtils.js#L16\n */\n\nconst _lut = [];\n\nfor (let i = 0; i < 256; i++) {\n  _lut[i] = (i < 16 ? '0' : '') + (i).toString(16);\n}\n\nfunction generateUUID() {\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/21963136#21963136\n\n  const d0 = Math.random() * 0xffffffff | 0;\n  const d1 = Math.random() * 0xffffffff | 0;\n  const d2 = Math.random() * 0xffffffff | 0;\n  const d3 = Math.random() * 0xffffffff | 0;\n  const uuid = _lut[d0 & 0xff] + _lut[d0 >> 8 & 0xff] + _lut[d0 >> 16 & 0xff] + _lut[d0 >> 24 & 0xff] + '-' +\n    _lut[d1 & 0xff] + _lut[d1 >> 8 & 0xff] + '-' + _lut[d1 >> 16 & 0x0f | 0x40] + _lut[d1 >> 24 & 0xff] + '-' +\n    _lut[d2 & 0x3f | 0x80] + _lut[d2 >> 8 & 0xff] + '-' + _lut[d2 >> 16 & 0xff] + _lut[d2 >> 24 & 0xff] +\n    _lut[d3 & 0xff] + _lut[d3 >> 8 & 0xff] + _lut[d3 >> 16 & 0xff] + _lut[d3 >> 24 & 0xff];\n\n  // .toUpperCase() here flattens concatenated strings to save heap memory space.\n  return uuid.toUpperCase()\n\n}\n\n// Local assign polyfill to avoid importing troika-core\nconst assign = Object.assign || function(/*target, ...sources*/) {\n  let target = arguments[0];\n  for (let i = 1, len = arguments.length; i < len; i++) {\n    let source = arguments[i];\n    if (source) {\n      for (let prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          target[prop] = source[prop];\n        }\n      }\n    }\n  }\n  return target\n};\n\n\nconst epoch = Date.now();\nconst CONSTRUCTOR_CACHE = new WeakMap();\nconst SHADER_UPGRADE_CACHE = new Map();\n\n// Material ids must be integers, but we can't access the increment from Three's `Material` module,\n// so let's choose a sufficiently large starting value that should theoretically never collide.\nlet materialInstanceId = 1e10;\n\n/**\n * A utility for creating a custom shader material derived from another material's\n * shaders. This allows you to inject custom shader logic and transforms into the\n * builtin ThreeJS materials without having to recreate them from scratch.\n *\n * @param {THREE.Material} baseMaterial - the original material to derive from\n *\n * @param {Object} options - How the base material should be modified.\n * @param {Object=} options.defines - Custom `defines` for the material\n * @param {Object=} options.extensions - Custom `extensions` for the material, e.g. `{derivatives: true}`\n * @param {Object=} options.uniforms - Custom `uniforms` for use in the modified shader. These can\n *        be accessed and manipulated via the resulting material's `uniforms` property, just like\n *        in a ShaderMaterial. You do not need to repeat the base material's own uniforms here.\n * @param {String=} options.timeUniform - If specified, a uniform of this name will be injected into\n *        both shaders, and it will automatically be updated on each render frame with a number of\n *        elapsed milliseconds. The \"zero\" epoch time is not significant so don't rely on this as a\n *        true calendar time.\n * @param {String=} options.vertexDefs - Custom GLSL code to inject into the vertex shader's top-level\n *        definitions, above the `void main()` function.\n * @param {String=} options.vertexMainIntro - Custom GLSL code to inject at the top of the vertex\n *        shader's `void main` function.\n * @param {String=} options.vertexMainOutro - Custom GLSL code to inject at the end of the vertex\n *        shader's `void main` function.\n * @param {String=} options.vertexTransform - Custom GLSL code to manipulate the `position`, `normal`,\n *        and/or `uv` vertex attributes. This code will be wrapped within a standalone function with\n *        those attributes exposed by their normal names as read/write values.\n * @param {String=} options.fragmentDefs - Custom GLSL code to inject into the fragment shader's top-level\n *        definitions, above the `void main()` function.\n * @param {String=} options.fragmentMainIntro - Custom GLSL code to inject at the top of the fragment\n *        shader's `void main` function.\n * @param {String=} options.fragmentMainOutro - Custom GLSL code to inject at the end of the fragment\n *        shader's `void main` function. You can manipulate `gl_FragColor` here but keep in mind it goes\n *        after any of ThreeJS's color postprocessing shader chunks (tonemapping, fog, etc.), so if you\n *        want those to apply to your changes use `fragmentColorTransform` instead.\n * @param {String=} options.fragmentColorTransform - Custom GLSL code to manipulate the `gl_FragColor`\n *        output value. Will be injected near the end of the `void main` function, but before any\n *        of ThreeJS's color postprocessing shader chunks (tonemapping, fog, etc.), and before the\n *        `fragmentMainOutro`.\n * @param {function({fragmentShader: string, vertexShader:string}):\n *        {fragmentShader: string, vertexShader:string}} options.customRewriter - A function\n *        for performing custom rewrites of the full shader code. Useful if you need to do something\n *        special that's not covered by the other builtin options. This function will be executed before\n *        any other transforms are applied.\n * @param {boolean=} options.chained - Set to `true` to prototype-chain the derived material to the base\n *        material, rather than the default behavior of copying it. This allows the derived material to\n *        automatically pick up changes made to the base material and its properties. This can be useful\n *        where the derived material is hidden from the user as an implementation detail, allowing them\n *        to work with the original material like normal. But it can result in unexpected behavior if not\n *        handled carefully.\n *\n * @return {THREE.Material}\n *\n * The returned material will also have two new methods, `getDepthMaterial()` and `getDistanceMaterial()`,\n * which can be called to get a variant of the derived material for use in shadow casting. If the\n * target mesh is expected to cast shadows, then you can assign these to the mesh's `customDepthMaterial`\n * (for directional and spot lights) and/or `customDistanceMaterial` (for point lights) properties to\n * allow the cast shadow to honor your derived shader's vertex transforms and discarded fragments. These\n * will also set a custom `#define IS_DEPTH_MATERIAL` or `#define IS_DISTANCE_MATERIAL` that you can look\n * for in your derived shaders with `#ifdef` to customize their behavior for the depth or distance\n * scenarios, e.g. skipping antialiasing or expensive shader logic.\n */\nfunction createDerivedMaterial(baseMaterial, options) {\n  // Generate a key that is unique to the content of these `options`. We'll use this\n  // throughout for caching and for generating the upgraded shader code. This increases\n  // the likelihood that the resulting shaders will line up across multiple calls so\n  // their GL programs can be shared and cached.\n  const optionsKey = getKeyForOptions(options);\n\n  // First check to see if we've already derived from this baseMaterial using this\n  // unique set of options, and if so reuse the constructor to avoid some allocations.\n  let ctorsByDerivation = CONSTRUCTOR_CACHE.get(baseMaterial);\n  if (!ctorsByDerivation) {\n    CONSTRUCTOR_CACHE.set(baseMaterial, (ctorsByDerivation = Object.create(null)));\n  }\n  if (ctorsByDerivation[optionsKey]) {\n    return new ctorsByDerivation[optionsKey]()\n  }\n\n  const privateBeforeCompileProp = `_onBeforeCompile${optionsKey}`;\n\n  // Private onBeforeCompile handler that injects the modified shaders and uniforms when\n  // the renderer switches to this material's program\n  const onBeforeCompile = function (shaderInfo, renderer) {\n    baseMaterial.onBeforeCompile.call(this, shaderInfo, renderer);\n\n    // Upgrade the shaders, caching the result by incoming source code\n    const cacheKey = this.customProgramCacheKey() + '|' + shaderInfo.vertexShader + '|' + shaderInfo.fragmentShader;\n    let upgradedShaders = SHADER_UPGRADE_CACHE[cacheKey];\n    if (!upgradedShaders) {\n      const upgraded = upgradeShaders(this, shaderInfo, options, optionsKey);\n      upgradedShaders = SHADER_UPGRADE_CACHE[cacheKey] = upgraded;\n    }\n\n    // Inject upgraded shaders and uniforms into the program\n    shaderInfo.vertexShader = upgradedShaders.vertexShader;\n    shaderInfo.fragmentShader = upgradedShaders.fragmentShader;\n    assign(shaderInfo.uniforms, this.uniforms);\n\n    // Inject auto-updating time uniform if requested\n    if (options.timeUniform) {\n      shaderInfo.uniforms[options.timeUniform] = {\n        get value() {return Date.now() - epoch}\n      };\n    }\n\n    // Users can still add their own handlers on top of ours\n    if (this[privateBeforeCompileProp]) {\n      this[privateBeforeCompileProp](shaderInfo);\n    }\n  };\n\n  const DerivedMaterial = function DerivedMaterial() {\n    return derive(options.chained ? baseMaterial : baseMaterial.clone())\n  };\n\n  const derive = function(base) {\n    // Prototype chain to the base material\n    const derived = Object.create(base, descriptor);\n\n    // Store the baseMaterial for reference; this is always the original even when cloning\n    Object.defineProperty(derived, 'baseMaterial', { value: baseMaterial });\n\n    // Needs its own ids\n    Object.defineProperty(derived, 'id', { value: materialInstanceId++ });\n    derived.uuid = generateUUID();\n\n    // Merge uniforms, defines, and extensions\n    derived.uniforms = assign({}, base.uniforms, options.uniforms);\n    derived.defines = assign({}, base.defines, options.defines);\n    derived.defines[`TROIKA_DERIVED_MATERIAL_${optionsKey}`] = ''; //force a program change from the base material\n    derived.extensions = assign({}, base.extensions, options.extensions);\n\n    // Don't inherit EventDispatcher listeners\n    derived._listeners = undefined;\n\n    return derived\n  };\n\n  const descriptor = {\n    constructor: {value: DerivedMaterial},\n    isDerivedMaterial: {value: true},\n\n    type: {\n      get: () => baseMaterial.type,\n      set: (value) => {baseMaterial.type = value;}\n    },\n\n    isDerivedFrom: {\n      writable: true,\n      configurable: true,\n      value: function (testMaterial) {\n        const base = this.baseMaterial;\n        return testMaterial === base || (base.isDerivedMaterial && base.isDerivedFrom(testMaterial)) || false\n      }\n    },\n\n    customProgramCacheKey: {\n      writable: true,\n      configurable: true,\n      value: function () {\n        return baseMaterial.customProgramCacheKey() + '|' + optionsKey\n      }\n    },\n\n    onBeforeCompile: {\n      get() {\n        return onBeforeCompile\n      },\n      set(fn) {\n        this[privateBeforeCompileProp] = fn;\n      }\n    },\n\n    copy: {\n      writable: true,\n      configurable: true,\n      value: function (source) {\n        baseMaterial.copy.call(this, source);\n        if (!baseMaterial.isShaderMaterial && !baseMaterial.isDerivedMaterial) {\n          assign(this.extensions, source.extensions);\n          assign(this.defines, source.defines);\n          assign(this.uniforms, UniformsUtils.clone(source.uniforms));\n        }\n        return this\n      }\n    },\n\n    clone: {\n      writable: true,\n      configurable: true,\n      value: function () {\n        const newBase = new baseMaterial.constructor();\n        return derive(newBase).copy(this)\n      }\n    },\n\n    /**\n     * Utility to get a MeshDepthMaterial that will honor this derived material's vertex\n     * transformations and discarded fragments.\n     */\n    getDepthMaterial: {\n      writable: true,\n      configurable: true,\n      value: function() {\n        let depthMaterial = this._depthMaterial;\n        if (!depthMaterial) {\n          depthMaterial = this._depthMaterial = createDerivedMaterial(\n            baseMaterial.isDerivedMaterial\n              ? baseMaterial.getDepthMaterial()\n              : new MeshDepthMaterial({ depthPacking: RGBADepthPacking }),\n            options\n          );\n          depthMaterial.defines.IS_DEPTH_MATERIAL = '';\n          depthMaterial.uniforms = this.uniforms; //automatically recieve same uniform values\n        }\n        return depthMaterial\n      }\n    },\n\n    /**\n     * Utility to get a MeshDistanceMaterial that will honor this derived material's vertex\n     * transformations and discarded fragments.\n     */\n    getDistanceMaterial: {\n      writable: true,\n      configurable: true,\n      value: function() {\n        let distanceMaterial = this._distanceMaterial;\n        if (!distanceMaterial) {\n          distanceMaterial = this._distanceMaterial = createDerivedMaterial(\n            baseMaterial.isDerivedMaterial\n              ? baseMaterial.getDistanceMaterial()\n              : new MeshDistanceMaterial(),\n            options\n          );\n          distanceMaterial.defines.IS_DISTANCE_MATERIAL = '';\n          distanceMaterial.uniforms = this.uniforms; //automatically recieve same uniform values\n        }\n        return distanceMaterial\n      }\n    },\n\n    dispose: {\n      writable: true,\n      configurable: true,\n      value() {\n        const {_depthMaterial, _distanceMaterial} = this;\n        if (_depthMaterial) _depthMaterial.dispose();\n        if (_distanceMaterial) _distanceMaterial.dispose();\n        baseMaterial.dispose.call(this);\n      }\n    }\n  };\n\n  ctorsByDerivation[optionsKey] = DerivedMaterial;\n  return new DerivedMaterial()\n}\n\n\nfunction upgradeShaders(material, {vertexShader, fragmentShader}, options, key) {\n  let {\n    vertexDefs,\n    vertexMainIntro,\n    vertexMainOutro,\n    vertexTransform,\n    fragmentDefs,\n    fragmentMainIntro,\n    fragmentMainOutro,\n    fragmentColorTransform,\n    customRewriter,\n    timeUniform\n  } = options;\n\n  vertexDefs = vertexDefs || '';\n  vertexMainIntro = vertexMainIntro || '';\n  vertexMainOutro = vertexMainOutro || '';\n  fragmentDefs = fragmentDefs || '';\n  fragmentMainIntro = fragmentMainIntro || '';\n  fragmentMainOutro = fragmentMainOutro || '';\n\n  // Expand includes if needed\n  if (vertexTransform || customRewriter) {\n    vertexShader = expandShaderIncludes(vertexShader);\n  }\n  if (fragmentColorTransform || customRewriter) {\n    // We need to be able to find postprocessing chunks after include expansion in order to\n    // put them after the fragmentColorTransform, so mark them with comments first. Even if\n    // this particular derivation doesn't have a fragmentColorTransform, other derivations may,\n    // so we still mark them.\n    fragmentShader = fragmentShader.replace(\n      /^[ \\t]*#include <((?:tonemapping|encodings|colorspace|fog|premultiplied_alpha|dithering)_fragment)>/gm,\n      '\\n//!BEGIN_POST_CHUNK $1\\n$&\\n//!END_POST_CHUNK\\n'\n    );\n    fragmentShader = expandShaderIncludes(fragmentShader);\n  }\n\n  // Apply custom rewriter function\n  if (customRewriter) {\n    let res = customRewriter({vertexShader, fragmentShader});\n    vertexShader = res.vertexShader;\n    fragmentShader = res.fragmentShader;\n  }\n\n  // The fragmentColorTransform needs to go before any postprocessing chunks, so extract\n  // those and re-insert them into the outro in the correct place:\n  if (fragmentColorTransform) {\n    let postChunks = [];\n    fragmentShader = fragmentShader.replace(\n      /^\\/\\/!BEGIN_POST_CHUNK[^]+?^\\/\\/!END_POST_CHUNK/gm, // [^]+? = non-greedy match of any chars including newlines\n      match => {\n        postChunks.push(match);\n        return ''\n      }\n    );\n    fragmentMainOutro = `${fragmentColorTransform}\\n${postChunks.join('\\n')}\\n${fragmentMainOutro}`;\n  }\n\n  // Inject auto-updating time uniform if requested\n  if (timeUniform) {\n    const code = `\\nuniform float ${timeUniform};\\n`;\n    vertexDefs = code + vertexDefs;\n    fragmentDefs = code + fragmentDefs;\n  }\n\n  // Inject a function for the vertexTransform and rename all usages of position/normal/uv\n  if (vertexTransform) {\n    // Hoist these defs to the very top so they work in other function defs\n    vertexShader = `vec3 troika_position_${key};\nvec3 troika_normal_${key};\nvec2 troika_uv_${key};\n${vertexShader}\n`;\n    vertexDefs = `${vertexDefs}\nvoid troikaVertexTransform${key}(inout vec3 position, inout vec3 normal, inout vec2 uv) {\n  ${vertexTransform}\n}\n`;\n    vertexMainIntro = `\ntroika_position_${key} = vec3(position);\ntroika_normal_${key} = vec3(normal);\ntroika_uv_${key} = vec2(uv);\ntroikaVertexTransform${key}(troika_position_${key}, troika_normal_${key}, troika_uv_${key});\n${vertexMainIntro}\n`;\n    vertexShader = vertexShader.replace(/\\b(position|normal|uv)\\b/g, (match, match1, index, fullStr) => {\n      return /\\battribute\\s+vec[23]\\s+$/.test(fullStr.substr(0, index)) ? match1 : `troika_${match1}_${key}`\n    });\n\n    // Three r152 introduced the MAP_UV token, replace it too if it's pointing to the main 'uv'\n    // Perhaps the other textures too going forward?\n    if (!(material.map && material.map.channel > 0)) {\n      vertexShader = vertexShader.replace(/\\bMAP_UV\\b/g, `troika_uv_${key}`);\n    }\n  }\n\n  // Inject defs and intro/outro snippets\n  vertexShader = injectIntoShaderCode(vertexShader, key, vertexDefs, vertexMainIntro, vertexMainOutro);\n  fragmentShader = injectIntoShaderCode(fragmentShader, key, fragmentDefs, fragmentMainIntro, fragmentMainOutro);\n\n  return {\n    vertexShader,\n    fragmentShader\n  }\n}\n\nfunction injectIntoShaderCode(shaderCode, id, defs, intro, outro) {\n  if (intro || outro || defs) {\n    shaderCode = shaderCode.replace(voidMainRegExp, `\n${defs}\nvoid troikaOrigMain${id}() {`\n    );\n    shaderCode += `\nvoid main() {\n  ${intro}\n  troikaOrigMain${id}();\n  ${outro}\n}`;\n  }\n  return shaderCode\n}\n\n\nfunction optionsJsonReplacer(key, value) {\n  return key === 'uniforms' ? undefined : typeof value === 'function' ? value.toString() : value\n}\n\nlet _idCtr = 0;\nconst optionsHashesToIds = new Map();\nfunction getKeyForOptions(options) {\n  const optionsHash = JSON.stringify(options, optionsJsonReplacer);\n  let id = optionsHashesToIds.get(optionsHash);\n  if (id == null) {\n    optionsHashesToIds.set(optionsHash, (id = ++_idCtr));\n  }\n  return id\n}\n\n// Copied from threejs WebGLPrograms.js so we can resolve builtin materials to their shaders\n// TODO how can we keep this from getting stale?\nconst MATERIAL_TYPES_TO_SHADERS = {\n  MeshDepthMaterial: 'depth',\n  MeshDistanceMaterial: 'distanceRGBA',\n  MeshNormalMaterial: 'normal',\n  MeshBasicMaterial: 'basic',\n  MeshLambertMaterial: 'lambert',\n  MeshPhongMaterial: 'phong',\n  MeshToonMaterial: 'toon',\n  MeshStandardMaterial: 'physical',\n  MeshPhysicalMaterial: 'physical',\n  MeshMatcapMaterial: 'matcap',\n  LineBasicMaterial: 'basic',\n  LineDashedMaterial: 'dashed',\n  PointsMaterial: 'points',\n  ShadowMaterial: 'shadow',\n  SpriteMaterial: 'sprite'\n};\n\n/**\n * Given a Three.js `Material` instance, find the shaders/uniforms that will be\n * used to render that material.\n *\n * @param material - the Material instance\n * @return {object} - the material's shader info: `{uniforms:{}, fragmentShader:'', vertexShader:''}`\n */\nfunction getShadersForMaterial(material) {\n  let builtinType = MATERIAL_TYPES_TO_SHADERS[material.type];\n  return builtinType ? ShaderLib[builtinType] : material //TODO fallback for unknown type?\n}\n\n/**\n * Find all uniforms and their types within a shader code string.\n *\n * @param {string} shader - The shader code to parse\n * @return {object} mapping of uniform names to their glsl type\n */\nfunction getShaderUniformTypes(shader) {\n  let uniformRE = /\\buniform\\s+(int|float|vec[234]|mat[34])\\s+([A-Za-z_][\\w]*)/g;\n  let uniforms = Object.create(null);\n  let match;\n  while ((match = uniformRE.exec(shader)) !== null) {\n    uniforms[match[2]] = match[1];\n  }\n  return uniforms\n}\n\n/**\n * Helper for smoothing out the `m.getInverse(x)` --> `m.copy(x).invert()` conversion\n * that happened in ThreeJS r123.\n * @param {Matrix4} srcMatrix\n * @param {Matrix4} [tgtMatrix]\n */\nfunction invertMatrix4(srcMatrix, tgtMatrix = new Matrix4()) {\n  if (typeof tgtMatrix.invert === 'function') {\n    tgtMatrix.copy(srcMatrix).invert();\n  } else {\n    tgtMatrix.getInverse(srcMatrix);\n  }\n  return tgtMatrix\n}\n\n/*\nInput geometry is a cylinder with r=1, height in y dimension from 0 to 1,\ndivided into a reasonable number of height segments.\n*/\n\nconst vertexDefs = `\nuniform vec3 pointA;\nuniform vec3 controlA;\nuniform vec3 controlB;\nuniform vec3 pointB;\nuniform float radius;\nvarying float bezierT;\n\nvec3 cubicBezier(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {\n  float t2 = 1.0 - t;\n  float b0 = t2 * t2 * t2;\n  float b1 = 3.0 * t * t2 * t2;\n  float b2 = 3.0 * t * t * t2;\n  float b3 = t * t * t;\n  return b0 * p1 + b1 * c1 + b2 * c2 + b3 * p2;\n}\n\nvec3 cubicBezierDerivative(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {\n  float t2 = 1.0 - t;\n  return -3.0 * p1 * t2 * t2 +\n    c1 * (3.0 * t2 * t2 - 6.0 * t2 * t) +\n    c2 * (6.0 * t2 * t - 3.0 * t * t) +\n    3.0 * p2 * t * t;\n}\n`;\n\nconst vertexTransform = `\nfloat t = position.y;\nbezierT = t;\nvec3 bezierCenterPos = cubicBezier(pointA, controlA, controlB, pointB, t);\nvec3 bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t));\n\n// Make \"sideways\" always perpendicular to the camera ray; this ensures that any twists\n// in the cylinder occur where you won't see them: \nvec3 viewDirection = normalMatrix * vec3(0.0, 0.0, 1.0);\nif (bezierDir == viewDirection) {\n  bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t == 1.0 ? t - 0.0001 : t + 0.0001));\n}\nvec3 sideways = normalize(cross(bezierDir, viewDirection));\nvec3 upish = normalize(cross(sideways, bezierDir));\n\n// Build a matrix for transforming this disc in the cylinder:\nmat4 discTx;\ndiscTx[0].xyz = sideways * radius;\ndiscTx[1].xyz = bezierDir * radius;\ndiscTx[2].xyz = upish * radius;\ndiscTx[3].xyz = bezierCenterPos;\ndiscTx[3][3] = 1.0;\n\n// Apply transform, ignoring original y\nposition = (discTx * vec4(position.x, 0.0, position.z, 1.0)).xyz;\nnormal = normalize(mat3(discTx) * normal);\n`;\n\nconst fragmentDefs = `\nuniform vec3 dashing;\nvarying float bezierT;\n`;\n\nconst fragmentMainIntro = `\nif (dashing.x + dashing.y > 0.0) {\n  float dashFrac = mod(bezierT - dashing.z, dashing.x + dashing.y);\n  if (dashFrac > dashing.x) {\n    discard;\n  }\n}\n`;\n\n// Debugging: separate color for each of the 6 sides:\n// const fragmentColorTransform = `\n// float sideNum = floor(vUV.x * 6.0);\n// vec3 mixColor = sideNum < 1.0 ? vec3(1.0, 0.0, 0.0) :\n//   sideNum < 2.0 ? vec3(0.0, 1.0, 1.0) :\n//   sideNum < 3.0 ? vec3(1.0, 1.0, 0.0) :\n//   sideNum < 4.0 ? vec3(0.0, 0.0, 1.0) :\n//   sideNum < 5.0 ? vec3(0.0, 1.0, 0.0) :\n//   vec3(1.0, 0.0, 1.0);\n// gl_FragColor.xyz = mix(gl_FragColor.xyz, mixColor, 0.5);\n// `\n\n\n\nfunction createBezierMeshMaterial(baseMaterial) {\n  return createDerivedMaterial(\n    baseMaterial,\n    {\n      chained: true,\n      uniforms: {\n        pointA: {value: new Vector3()},\n        controlA: {value: new Vector3()},\n        controlB: {value: new Vector3()},\n        pointB: {value: new Vector3()},\n        radius: {value: 0.01},\n        dashing: {value: new Vector3()} //on, off, offset\n      },\n      vertexDefs,\n      vertexTransform,\n      fragmentDefs,\n      fragmentMainIntro\n    }\n  )\n}\n\nlet geometry = null;\n\nconst defaultBaseMaterial = /*#__PURE__*/new MeshStandardMaterial({color: 0xffffff, side: DoubleSide});\n\n\n/**\n * A ThreeJS `Mesh` that bends a tube shape along a 3D cubic bezier path. The bending is done\n * by deforming a straight cylindrical geometry in the vertex shader based on a set of four\n * control point uniforms. It patches the necessary GLSL into the mesh's assigned `material`\n * automatically.\n *\n * The cubiz bezier path is determined by its four `Vector3` properties:\n * - `pointA`\n * - `controlA`\n * - `controlB`\n * - `pointB`\n *\n * The tube's radius is controlled by its `radius` property, which defaults to `0.01`.\n *\n * You can also give the tube a dashed appearance with two properties:\n *\n * - `dashArray` - an array of two numbers, defining the length of \"on\" and \"off\" parts of\n *   the dash. Each is a 0-1 ratio of the entire path's length. (Actually this is the `t` length\n *   used as input to the cubic bezier function, not its visible length.)\n * - `dashOffset` - offset of where the dash starts. You can animate this to make the dashes move.\n *\n * Note that the dashes will appear like a hollow tube, not solid. This will be more apparent on\n * thicker tubes.\n *\n * TODO: proper geometry bounding sphere and raycasting\n * TODO: allow control of the geometry's segment counts\n */\nclass BezierMesh extends Mesh {\n  static getGeometry() {\n    return geometry || (geometry =\n      new CylinderGeometry(1, 1, 1, 6, 64).translate(0, 0.5, 0)\n    )\n  }\n\n  constructor() {\n    super(\n      BezierMesh.getGeometry(),\n      defaultBaseMaterial\n    );\n\n    this.pointA = new Vector3();\n    this.controlA = new Vector3();\n    this.controlB = new Vector3();\n    this.pointB = new Vector3();\n    this.radius = 0.01;\n    this.dashArray = new Vector2();\n    this.dashOffset = 0;\n\n    // TODO - disabling frustum culling until I figure out how to customize the\n    //  geometry's bounding sphere that gets used\n    this.frustumCulled = false;\n  }\n\n  // Handler for automatically wrapping the base material with our upgrades. We do the wrapping\n  // lazily on _read_ rather than write to avoid unnecessary wrapping on transient values.\n  get material() {\n    let derivedMaterial = this._derivedMaterial;\n    const baseMaterial = this._baseMaterial || this._defaultMaterial || (this._defaultMaterial = defaultBaseMaterial.clone());\n    if (!derivedMaterial || derivedMaterial.baseMaterial !== baseMaterial) {\n      derivedMaterial = this._derivedMaterial = createBezierMeshMaterial(baseMaterial);\n      // dispose the derived material when its base material is disposed:\n      baseMaterial.addEventListener('dispose', function onDispose() {\n        baseMaterial.removeEventListener('dispose', onDispose);\n        derivedMaterial.dispose();\n      });\n    }\n    return derivedMaterial\n  }\n  set material(baseMaterial) {\n    this._baseMaterial = baseMaterial;\n  }\n\n  // Create and update material for shadows upon request:\n  get customDepthMaterial() {\n    return this.material.getDepthMaterial()\n  }\n  set customDepthMaterial(m) {\n    // future: let the user override with their own?\n  }\n  get customDistanceMaterial() {\n    return this.material.getDistanceMaterial()\n  }\n  set customDistanceMaterial(m) {\n    // future: let the user override with their own?\n  }\n\n  onBeforeRender() {\n    const {uniforms} = this.material;\n    const {pointA, controlA, controlB, pointB, radius, dashArray, dashOffset} = this;\n    uniforms.pointA.value.copy(pointA);\n    uniforms.controlA.value.copy(controlA);\n    uniforms.controlB.value.copy(controlB);\n    uniforms.pointB.value.copy(pointB);\n    uniforms.radius.value = radius;\n    uniforms.dashing.value.set(dashArray.x, dashArray.y, dashOffset || 0);\n  }\n\n  raycast(/*raycaster, intersects*/) {\n    // TODO - just fail for now\n  }\n}\n\nexport { BezierMesh, createDerivedMaterial, expandShaderIncludes, getShaderUniformTypes, getShadersForMaterial, invertMatrix4, voidMainRegExp };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAEA;;;CAGC,GACD,MAAM,iBAAiB;AAEvB;;;;;;CAMC,GACD,SAAS,qBAAsB,MAAM;IACnC,MAAM,UAAU;IAChB,SAAS,QAAQ,KAAK,EAAE,OAAO;QAC7B,IAAI,QAAQ,oKAAA,CAAA,cAAW,CAAC,QAAQ;QAChC,OAAO,QAAQ,qBAAqB,SAAS;IAC/C;IACA,OAAO,OAAO,OAAO,CAAE,SAAS;AAClC;AAEA;;;;CAIC,GAED,MAAM,OAAO,EAAE;AAEf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;IAC5B,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,IAAI,AAAC,EAAG,QAAQ,CAAC;AAC/C;AAEA,SAAS;IAEP,sGAAsG;IAEtG,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,MACpG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,MACtG,IAAI,CAAC,KAAK,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GACnG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK;IAExF,+EAA+E;IAC/E,OAAO,KAAK,WAAW;AAEzB;AAEA,uDAAuD;AACvD,MAAM,SAAS,OAAO,MAAM,IAAI;IAC9B,IAAI,SAAS,SAAS,CAAC,EAAE;IACzB,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,IAAK;QACpD,IAAI,SAAS,SAAS,CAAC,EAAE;QACzB,IAAI,QAAQ;YACV,IAAK,IAAI,QAAQ,OAAQ;gBACvB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,OAAO;oBACtD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;gBAC7B;YACF;QACF;IACF;IACA,OAAO;AACT;AAGA,MAAM,QAAQ,KAAK,GAAG;AACtB,MAAM,oBAAoB,IAAI;AAC9B,MAAM,uBAAuB,IAAI;AAEjC,mGAAmG;AACnG,+FAA+F;AAC/F,IAAI,qBAAqB;AAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4DC,GACD,SAAS,sBAAsB,YAAY,EAAE,OAAO;IAClD,kFAAkF;IAClF,qFAAqF;IACrF,kFAAkF;IAClF,8CAA8C;IAC9C,MAAM,aAAa,iBAAiB;IAEpC,gFAAgF;IAChF,oFAAoF;IACpF,IAAI,oBAAoB,kBAAkB,GAAG,CAAC;IAC9C,IAAI,CAAC,mBAAmB;QACtB,kBAAkB,GAAG,CAAC,cAAe,oBAAoB,OAAO,MAAM,CAAC;IACzE;IACA,IAAI,iBAAiB,CAAC,WAAW,EAAE;QACjC,OAAO,IAAI,iBAAiB,CAAC,WAAW;IAC1C;IAEA,MAAM,2BAA2B,CAAC,gBAAgB,EAAE,YAAY;IAEhE,sFAAsF;IACtF,mDAAmD;IACnD,MAAM,kBAAkB,SAAU,UAAU,EAAE,QAAQ;QACpD,aAAa,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY;QAEpD,kEAAkE;QAClE,MAAM,WAAW,IAAI,CAAC,qBAAqB,KAAK,MAAM,WAAW,YAAY,GAAG,MAAM,WAAW,cAAc;QAC/G,IAAI,kBAAkB,oBAAoB,CAAC,SAAS;QACpD,IAAI,CAAC,iBAAiB;YACpB,MAAM,WAAW,eAAe,IAAI,EAAE,YAAY,SAAS;YAC3D,kBAAkB,oBAAoB,CAAC,SAAS,GAAG;QACrD;QAEA,wDAAwD;QACxD,WAAW,YAAY,GAAG,gBAAgB,YAAY;QACtD,WAAW,cAAc,GAAG,gBAAgB,cAAc;QAC1D,OAAO,WAAW,QAAQ,EAAE,IAAI,CAAC,QAAQ;QAEzC,iDAAiD;QACjD,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,QAAQ,CAAC,QAAQ,WAAW,CAAC,GAAG;gBACzC,IAAI,SAAQ;oBAAC,OAAO,KAAK,GAAG,KAAK;gBAAK;YACxC;QACF;QAEA,wDAAwD;QACxD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC;QACjC;IACF;IAEA,MAAM,kBAAkB,SAAS;QAC/B,OAAO,OAAO,QAAQ,OAAO,GAAG,eAAe,aAAa,KAAK;IACnE;IAEA,MAAM,SAAS,SAAS,IAAI;QAC1B,uCAAuC;QACvC,MAAM,UAAU,OAAO,MAAM,CAAC,MAAM;QAEpC,sFAAsF;QACtF,OAAO,cAAc,CAAC,SAAS,gBAAgB;YAAE,OAAO;QAAa;QAErE,oBAAoB;QACpB,OAAO,cAAc,CAAC,SAAS,MAAM;YAAE,OAAO;QAAqB;QACnE,QAAQ,IAAI,GAAG;QAEf,0CAA0C;QAC1C,QAAQ,QAAQ,GAAG,OAAO,CAAC,GAAG,KAAK,QAAQ,EAAE,QAAQ,QAAQ;QAC7D,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,OAAO,EAAE,QAAQ,OAAO;QAC1D,QAAQ,OAAO,CAAC,CAAC,wBAAwB,EAAE,YAAY,CAAC,GAAG,IAAI,+CAA+C;QAC9G,QAAQ,UAAU,GAAG,OAAO,CAAC,GAAG,KAAK,UAAU,EAAE,QAAQ,UAAU;QAEnE,0CAA0C;QAC1C,QAAQ,UAAU,GAAG;QAErB,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,aAAa;YAAC,OAAO;QAAe;QACpC,mBAAmB;YAAC,OAAO;QAAI;QAE/B,MAAM;YACJ,KAAK,IAAM,aAAa,IAAI;YAC5B,KAAK,CAAC;gBAAW,aAAa,IAAI,GAAG;YAAM;QAC7C;QAEA,eAAe;YACb,UAAU;YACV,cAAc;YACd,OAAO,SAAU,YAAY;gBAC3B,MAAM,OAAO,IAAI,CAAC,YAAY;gBAC9B,OAAO,iBAAiB,QAAS,KAAK,iBAAiB,IAAI,KAAK,aAAa,CAAC,iBAAkB;YAClG;QACF;QAEA,uBAAuB;YACrB,UAAU;YACV,cAAc;YACd,OAAO;gBACL,OAAO,aAAa,qBAAqB,KAAK,MAAM;YACtD;QACF;QAEA,iBAAiB;YACf;gBACE,OAAO;YACT;YACA,KAAI,EAAE;gBACJ,IAAI,CAAC,yBAAyB,GAAG;YACnC;QACF;QAEA,MAAM;YACJ,UAAU;YACV,cAAc;YACd,OAAO,SAAU,MAAM;gBACrB,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC7B,IAAI,CAAC,aAAa,gBAAgB,IAAI,CAAC,aAAa,iBAAiB,EAAE;oBACrE,OAAO,IAAI,CAAC,UAAU,EAAE,OAAO,UAAU;oBACzC,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,OAAO;oBACnC,OAAO,IAAI,CAAC,QAAQ,EAAE,kJAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,OAAO,QAAQ;gBAC3D;gBACA,OAAO,IAAI;YACb;QACF;QAEA,OAAO;YACL,UAAU;YACV,cAAc;YACd,OAAO;gBACL,MAAM,UAAU,IAAI,aAAa,WAAW;gBAC5C,OAAO,OAAO,SAAS,IAAI,CAAC,IAAI;YAClC;QACF;QAEA;;;KAGC,GACD,kBAAkB;YAChB,UAAU;YACV,cAAc;YACd,OAAO;gBACL,IAAI,gBAAgB,IAAI,CAAC,cAAc;gBACvC,IAAI,CAAC,eAAe;oBAClB,gBAAgB,IAAI,CAAC,cAAc,GAAG,sBACpC,aAAa,iBAAiB,GAC1B,aAAa,gBAAgB,KAC7B,IAAI,kJAAA,CAAA,oBAAiB,CAAC;wBAAE,cAAc,kJAAA,CAAA,mBAAgB;oBAAC,IAC3D;oBAEF,cAAc,OAAO,CAAC,iBAAiB,GAAG;oBAC1C,cAAc,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,2CAA2C;gBACrF;gBACA,OAAO;YACT;QACF;QAEA;;;KAGC,GACD,qBAAqB;YACnB,UAAU;YACV,cAAc;YACd,OAAO;gBACL,IAAI,mBAAmB,IAAI,CAAC,iBAAiB;gBAC7C,IAAI,CAAC,kBAAkB;oBACrB,mBAAmB,IAAI,CAAC,iBAAiB,GAAG,sBAC1C,aAAa,iBAAiB,GAC1B,aAAa,mBAAmB,KAChC,IAAI,kJAAA,CAAA,uBAAoB,IAC5B;oBAEF,iBAAiB,OAAO,CAAC,oBAAoB,GAAG;oBAChD,iBAAiB,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,2CAA2C;gBACxF;gBACA,OAAO;YACT;QACF;QAEA,SAAS;YACP,UAAU;YACV,cAAc;YACd;gBACE,MAAM,EAAC,cAAc,EAAE,iBAAiB,EAAC,GAAG,IAAI;gBAChD,IAAI,gBAAgB,eAAe,OAAO;gBAC1C,IAAI,mBAAmB,kBAAkB,OAAO;gBAChD,aAAa,OAAO,CAAC,IAAI,CAAC,IAAI;YAChC;QACF;IACF;IAEA,iBAAiB,CAAC,WAAW,GAAG;IAChC,OAAO,IAAI;AACb;AAGA,SAAS,eAAe,QAAQ,EAAE,EAAC,YAAY,EAAE,cAAc,EAAC,EAAE,OAAO,EAAE,GAAG;IAC5E,IAAI,EACF,UAAU,EACV,eAAe,EACf,eAAe,EACf,eAAe,EACf,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,sBAAsB,EACtB,cAAc,EACd,WAAW,EACZ,GAAG;IAEJ,aAAa,cAAc;IAC3B,kBAAkB,mBAAmB;IACrC,kBAAkB,mBAAmB;IACrC,eAAe,gBAAgB;IAC/B,oBAAoB,qBAAqB;IACzC,oBAAoB,qBAAqB;IAEzC,4BAA4B;IAC5B,IAAI,mBAAmB,gBAAgB;QACrC,eAAe,qBAAqB;IACtC;IACA,IAAI,0BAA0B,gBAAgB;QAC5C,uFAAuF;QACvF,uFAAuF;QACvF,2FAA2F;QAC3F,yBAAyB;QACzB,iBAAiB,eAAe,OAAO,CACrC,yGACA;QAEF,iBAAiB,qBAAqB;IACxC;IAEA,iCAAiC;IACjC,IAAI,gBAAgB;QAClB,IAAI,MAAM,eAAe;YAAC;YAAc;QAAc;QACtD,eAAe,IAAI,YAAY;QAC/B,iBAAiB,IAAI,cAAc;IACrC;IAEA,sFAAsF;IACtF,gEAAgE;IAChE,IAAI,wBAAwB;QAC1B,IAAI,aAAa,EAAE;QACnB,iBAAiB,eAAe,OAAO,CACrC,qDACA,CAAA;YACE,WAAW,IAAI,CAAC;YAChB,OAAO;QACT;QAEF,oBAAoB,GAAG,uBAAuB,EAAE,EAAE,WAAW,IAAI,CAAC,MAAM,EAAE,EAAE,mBAAmB;IACjG;IAEA,iDAAiD;IACjD,IAAI,aAAa;QACf,MAAM,OAAO,CAAC,gBAAgB,EAAE,YAAY,GAAG,CAAC;QAChD,aAAa,OAAO;QACpB,eAAe,OAAO;IACxB;IAEA,wFAAwF;IACxF,IAAI,iBAAiB;QACnB,uEAAuE;QACvE,eAAe,CAAC,qBAAqB,EAAE,IAAI;mBAC5B,EAAE,IAAI;eACV,EAAE,IAAI;AACrB,EAAE,aAAa;AACf,CAAC;QACG,aAAa,GAAG,WAAW;0BACL,EAAE,IAAI;EAC9B,EAAE,gBAAgB;;AAEpB,CAAC;QACG,kBAAkB,CAAC;gBACP,EAAE,IAAI;cACR,EAAE,IAAI;UACV,EAAE,IAAI;qBACK,EAAE,IAAI,iBAAiB,EAAE,IAAI,gBAAgB,EAAE,IAAI,YAAY,EAAE,IAAI;AAC1F,EAAE,gBAAgB;AAClB,CAAC;QACG,eAAe,aAAa,OAAO,CAAC,6BAA6B,CAAC,OAAO,QAAQ,OAAO;YACtF,OAAO,4BAA4B,IAAI,CAAC,QAAQ,MAAM,CAAC,GAAG,UAAU,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,KAAK;QACxG;QAEA,2FAA2F;QAC3F,gDAAgD;QAChD,IAAI,CAAC,CAAC,SAAS,GAAG,IAAI,SAAS,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG;YAC/C,eAAe,aAAa,OAAO,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK;QACvE;IACF;IAEA,uCAAuC;IACvC,eAAe,qBAAqB,cAAc,KAAK,YAAY,iBAAiB;IACpF,iBAAiB,qBAAqB,gBAAgB,KAAK,cAAc,mBAAmB;IAE5F,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,qBAAqB,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;IAC9D,IAAI,SAAS,SAAS,MAAM;QAC1B,aAAa,WAAW,OAAO,CAAC,gBAAgB,CAAC;AACrD,EAAE,KAAK;mBACY,EAAE,GAAG,IAAI,CAAC;QAEzB,cAAc,CAAC;;EAEjB,EAAE,MAAM;gBACM,EAAE,GAAG;EACnB,EAAE,MAAM;CACT,CAAC;IACA;IACA,OAAO;AACT;AAGA,SAAS,oBAAoB,GAAG,EAAE,KAAK;IACrC,OAAO,QAAQ,aAAa,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ,KAAK;AAC3F;AAEA,IAAI,SAAS;AACb,MAAM,qBAAqB,IAAI;AAC/B,SAAS,iBAAiB,OAAO;IAC/B,MAAM,cAAc,KAAK,SAAS,CAAC,SAAS;IAC5C,IAAI,KAAK,mBAAmB,GAAG,CAAC;IAChC,IAAI,MAAM,MAAM;QACd,mBAAmB,GAAG,CAAC,aAAc,KAAK,EAAE;IAC9C;IACA,OAAO;AACT;AAEA,4FAA4F;AAC5F,gDAAgD;AAChD,MAAM,4BAA4B;IAChC,mBAAmB;IACnB,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,sBAAsB;IACtB,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB;IACpB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;AAClB;AAEA;;;;;;CAMC,GACD,SAAS,sBAAsB,QAAQ;IACrC,IAAI,cAAc,yBAAyB,CAAC,SAAS,IAAI,CAAC;IAC1D,OAAO,cAAc,oKAAA,CAAA,YAAS,CAAC,YAAY,GAAG,SAAS,iCAAiC;;AAC1F;AAEA;;;;;CAKC,GACD,SAAS,sBAAsB,MAAM;IACnC,IAAI,YAAY;IAChB,IAAI,WAAW,OAAO,MAAM,CAAC;IAC7B,IAAI;IACJ,MAAO,CAAC,QAAQ,UAAU,IAAI,CAAC,OAAO,MAAM,KAAM;QAChD,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE;IAC/B;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,cAAc,SAAS,EAAE,YAAY,IAAI,kJAAA,CAAA,UAAO,EAAE;IACzD,IAAI,OAAO,UAAU,MAAM,KAAK,YAAY;QAC1C,UAAU,IAAI,CAAC,WAAW,MAAM;IAClC,OAAO;QACL,UAAU,UAAU,CAAC;IACvB;IACA,OAAO;AACT;AAEA;;;AAGA,GAEA,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBpB,CAAC;AAED,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BzB,CAAC;AAED,MAAM,eAAe,CAAC;;;AAGtB,CAAC;AAED,MAAM,oBAAoB,CAAC;;;;;;;AAO3B,CAAC;AAED,qDAAqD;AACrD,mCAAmC;AACnC,sCAAsC;AACtC,wDAAwD;AACxD,0CAA0C;AAC1C,0CAA0C;AAC1C,0CAA0C;AAC1C,0CAA0C;AAC1C,yBAAyB;AACzB,2DAA2D;AAC3D,IAAI;AAIJ,SAAS,yBAAyB,YAAY;IAC5C,OAAO,sBACL,cACA;QACE,SAAS;QACT,UAAU;YACR,QAAQ;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YAC7B,UAAU;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YAC/B,UAAU;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YAC/B,QAAQ;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YAC7B,QAAQ;gBAAC,OAAO;YAAI;YACpB,SAAS;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE,EAAE,iBAAiB;QACnD;QACA;QACA;QACA;QACA;IACF;AAEJ;AAEA,IAAI,WAAW;AAEf,MAAM,sBAAsB,WAAW,GAAE,IAAI,kJAAA,CAAA,uBAAoB,CAAC;IAAC,OAAO;IAAU,MAAM,kJAAA,CAAA,aAAU;AAAA;AAGpG;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,MAAM,mBAAmB,kJAAA,CAAA,OAAI;IAC3B,OAAO,cAAc;QACnB,OAAO,YAAY,CAAC,WAClB,IAAI,kJAAA,CAAA,mBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,SAAS,CAAC,GAAG,KAAK,EACzD;IACF;IAEA,aAAc;QACZ,KAAK,CACH,WAAW,WAAW,IACtB;QAGF,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAA,CAAA,UAAO;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,kJAAA,CAAA,UAAO;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,kJAAA,CAAA,UAAO;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAA,CAAA,UAAO;QACzB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,kJAAA,CAAA,UAAO;QAC5B,IAAI,CAAC,UAAU,GAAG;QAElB,2EAA2E;QAC3E,6CAA6C;QAC7C,IAAI,CAAC,aAAa,GAAG;IACvB;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI,kBAAkB,IAAI,CAAC,gBAAgB;QAC3C,MAAM,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,oBAAoB,KAAK,EAAE;QACxH,IAAI,CAAC,mBAAmB,gBAAgB,YAAY,KAAK,cAAc;YACrE,kBAAkB,IAAI,CAAC,gBAAgB,GAAG,yBAAyB;YACnE,mEAAmE;YACnE,aAAa,gBAAgB,CAAC,WAAW,SAAS;gBAChD,aAAa,mBAAmB,CAAC,WAAW;gBAC5C,gBAAgB,OAAO;YACzB;QACF;QACA,OAAO;IACT;IACA,IAAI,SAAS,YAAY,EAAE;QACzB,IAAI,CAAC,aAAa,GAAG;IACvB;IAEA,uDAAuD;IACvD,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB;IACvC;IACA,IAAI,oBAAoB,CAAC,EAAE;IACzB,gDAAgD;IAClD;IACA,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IAC1C;IACA,IAAI,uBAAuB,CAAC,EAAE;IAC5B,gDAAgD;IAClD;IAEA,iBAAiB;QACf,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAC,GAAG,IAAI;QAChF,SAAS,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,SAAS,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,SAAS,MAAM,CAAC,KAAK,GAAG;QACxB,SAAS,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,cAAc;IACrE;IAEA,UAAmC;IACjC,2BAA2B;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5089, "column": 0}, "map": {"version": 3, "file": "index.module.js", "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/hooks/useEventCallback.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/clamp.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/Interactive.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/format.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/Pointer.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/round.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/convert.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/Hue.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/Saturation.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/compare.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/hooks/useColorManipulation.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/nonce.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/hooks/useIsomorphicLayoutEffect.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/hooks/useStyleSheet.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/ColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HexColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/Alpha.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/AlphaColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HexAlphaColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HslaColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HslaStringColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HslColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HslStringColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HsvaColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HsvaStringColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HsvColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HsvStringColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/RgbaColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/RgbaStringColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/RgbColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/RgbStringColorPicker.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/utils/validate.ts", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/common/ColorInput.tsx", "file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/node_modules/react-colorful/src/components/HexColorInput.tsx"], "sourcesContent": ["import { useRef } from \"react\";\n\n// Saves incoming handler to the ref in order to avoid \"useCallback hell\"\nexport function useEventCallback<T>(handler?: (value: T) => void): (value: T) => void {\n  const callbackRef = useRef(handler);\n  const fn = useRef((value: T) => {\n    callbackRef.current && callbackRef.current(value);\n  });\n  callbackRef.current = handler;\n\n  return fn.current;\n}\n", "// Clamps a value between an upper and lower bound.\n// We use ternary operators because it makes the minified code\n// 2 times shorter then `Math.min(Math.max(a,b),c)`\nexport const clamp = (number: number, min = 0, max = 1): number => {\n  return number > max ? max : number < min ? min : number;\n};\n", "import React, { useRef, useMemo, useEffect } from \"react\";\n\nimport { useEventCallback } from \"../../hooks/useEventCallback\";\nimport { clamp } from \"../../utils/clamp\";\n\nexport interface Interaction {\n  left: number;\n  top: number;\n}\n\n// Check if an event was triggered by touch\nconst isTouch = (event: MouseEvent | TouchEvent): event is TouchEvent => \"touches\" in event;\n\n// Finds a proper touch point by its identifier\nconst getTouchPoint = (touches: TouchList, touchId: null | number): Touch => {\n  for (let i = 0; i < touches.length; i++) {\n    if (touches[i].identifier === touchId) return touches[i];\n  }\n  return touches[0];\n};\n\n// Finds the proper window object to fix iframe embedding issues\nconst getParentWindow = (node?: HTMLDivElement | null): Window => {\n  return (node && node.ownerDocument.defaultView) || self;\n};\n\n// Returns a relative position of the pointer inside the node's bounding box\nconst getRelativePosition = (\n  node: HTMLDivElement,\n  event: MouseEvent | TouchEvent,\n  touchId: null | number\n): Interaction => {\n  const rect = node.getBoundingClientRect();\n\n  // Get user's pointer position from `touches` array if it's a `TouchEvent`\n  const pointer = isTouch(event) ? getTouchPoint(event.touches, touchId) : (event as MouseEvent);\n\n  return {\n    left: clamp((pointer.pageX - (rect.left + getParentWindow(node).pageXOffset)) / rect.width),\n    top: clamp((pointer.pageY - (rect.top + getParentWindow(node).pageYOffset)) / rect.height),\n  };\n};\n\n// Browsers introduced an intervention, making touch events passive by default.\n// This workaround removes `preventDefault` call from the touch handlers.\n// https://github.com/facebook/react/issues/19651\nconst preventDefaultMove = (event: MouseEvent | TouchEvent): void => {\n  !isTouch(event) && event.preventDefault();\n};\n\n// Prevent mobile browsers from handling mouse events (conflicting with touch ones).\n// If we detected a touch interaction before, we prefer reacting to touch events only.\nconst isInvalid = (event: MouseEvent | TouchEvent, hasTouch: boolean): boolean => {\n  return hasTouch && !isTouch(event);\n};\n\ninterface Props {\n  onMove: (interaction: Interaction) => void;\n  onKey: (offset: Interaction) => void;\n  children: React.ReactNode;\n}\n\nconst InteractiveBase = ({ onMove, onKey, ...rest }: Props) => {\n  const container = useRef<HTMLDivElement>(null);\n  const onMoveCallback = useEventCallback<Interaction>(onMove);\n  const onKeyCallback = useEventCallback<Interaction>(onKey);\n  const touchId = useRef<null | number>(null);\n  const hasTouch = useRef(false);\n\n  const [handleMoveStart, handleKeyDown, toggleDocumentEvents] = useMemo(() => {\n    const handleMoveStart = ({ nativeEvent }: React.MouseEvent | React.TouchEvent) => {\n      const el = container.current;\n      if (!el) return;\n\n      // Prevent text selection\n      preventDefaultMove(nativeEvent);\n\n      if (isInvalid(nativeEvent, hasTouch.current) || !el) return;\n\n      if (isTouch(nativeEvent)) {\n        hasTouch.current = true;\n        const changedTouches = nativeEvent.changedTouches || [];\n        if (changedTouches.length) touchId.current = changedTouches[0].identifier;\n      }\n\n      el.focus();\n      onMoveCallback(getRelativePosition(el, nativeEvent, touchId.current));\n      toggleDocumentEvents(true);\n    };\n\n    const handleMove = (event: MouseEvent | TouchEvent) => {\n      // Prevent text selection\n      preventDefaultMove(event);\n\n      // If user moves the pointer outside of the window or iframe bounds and release it there,\n      // `mouseup`/`touchend` won't be fired. In order to stop the picker from following the cursor\n      // after the user has moved the mouse/finger back to the document, we check `event.buttons`\n      // and `event.touches`. It allows us to detect that the user is just moving his pointer\n      // without pressing it down\n      const isDown = isTouch(event) ? event.touches.length > 0 : event.buttons > 0;\n\n      if (isDown && container.current) {\n        onMoveCallback(getRelativePosition(container.current, event, touchId.current));\n      } else {\n        toggleDocumentEvents(false);\n      }\n    };\n\n    const handleMoveEnd = () => toggleDocumentEvents(false);\n\n    const handleKeyDown = (event: React.KeyboardEvent) => {\n      const keyCode = event.which || event.keyCode;\n\n      // Ignore all keys except arrow ones\n      if (keyCode < 37 || keyCode > 40) return;\n      // Do not scroll page by arrow keys when document is focused on the element\n      event.preventDefault();\n      // Send relative offset to the parent component.\n      // We use codes (37←, 38↑, 39→, 40↓) instead of keys ('ArrowRight', 'ArrowDown', etc)\n      // to reduce the size of the library\n      onKeyCallback({\n        left: keyCode === 39 ? 0.05 : keyCode === 37 ? -0.05 : 0,\n        top: keyCode === 40 ? 0.05 : keyCode === 38 ? -0.05 : 0,\n      });\n    };\n\n    function toggleDocumentEvents(state?: boolean) {\n      const touch = hasTouch.current;\n      const el = container.current;\n      const parentWindow = getParentWindow(el);\n\n      // Add or remove additional pointer event listeners\n      const toggleEvent = state ? parentWindow.addEventListener : parentWindow.removeEventListener;\n      toggleEvent(touch ? \"touchmove\" : \"mousemove\", handleMove);\n      toggleEvent(touch ? \"touchend\" : \"mouseup\", handleMoveEnd);\n    }\n\n    return [handleMoveStart, handleKeyDown, toggleDocumentEvents];\n  }, [onKeyCallback, onMoveCallback]);\n\n  // Remove window event listeners before unmounting\n  useEffect(() => toggleDocumentEvents, [toggleDocumentEvents]);\n\n  return (\n    <div\n      {...rest}\n      onTouchStart={handleMoveStart}\n      onMouseDown={handleMoveStart}\n      className=\"react-colorful__interactive\"\n      ref={container}\n      onKeyDown={handleKeyDown}\n      tabIndex={0}\n      role=\"slider\"\n    />\n  );\n};\n\nexport const Interactive = React.memo(InteractiveBase);\n", "export const formatClassName = (names: unknown[]): string => names.filter(Boolean).join(\" \");\n", "import React from \"react\";\nimport { formatClassName } from \"../../utils/format\";\n\ninterface Props {\n  className?: string;\n  top?: number;\n  left: number;\n  color: string;\n}\n\nexport const Pointer = ({ className, color, left, top = 0.5 }: Props): JSX.Element => {\n  const nodeClassName = formatClassName([\"react-colorful__pointer\", className]);\n\n  const style = {\n    top: `${top * 100}%`,\n    left: `${left * 100}%`,\n  };\n\n  return (\n    <div className={nodeClassName} style={style}>\n      <div className=\"react-colorful__pointer-fill\" style={{ backgroundColor: color }} />\n    </div>\n  );\n};\n", "export const round = (number: number, digits = 0, base = Math.pow(10, digits)): number => {\n  return Math.round(base * number) / base;\n};\n", "import { round } from \"./round\";\nimport { RgbaColor, RgbColor, HslaColor, HslColor, HsvaColor, HsvColor } from \"../types\";\n\n/**\n * Valid CSS <angle> units.\n * https://developer.mozilla.org/en-US/docs/Web/CSS/angle\n */\nconst angleUnits: Record<string, number> = {\n  grad: 360 / 400,\n  turn: 360,\n  rad: 360 / (Math.PI * 2),\n};\n\nexport const hexToHsva = (hex: string): HsvaColor => rgbaToHsva(hexToRgba(hex));\n\nexport const hexToRgba = (hex: string): RgbaColor => {\n  if (hex[0] === \"#\") hex = hex.substring(1);\n\n  if (hex.length < 6) {\n    return {\n      r: parseInt(hex[0] + hex[0], 16),\n      g: parseInt(hex[1] + hex[1], 16),\n      b: parseInt(hex[2] + hex[2], 16),\n      a: hex.length === 4 ? round(parseInt(hex[3] + hex[3], 16) / 255, 2) : 1,\n    };\n  }\n\n  return {\n    r: parseInt(hex.substring(0, 2), 16),\n    g: parseInt(hex.substring(2, 4), 16),\n    b: parseInt(hex.substring(4, 6), 16),\n    a: hex.length === 8 ? round(parseInt(hex.substring(6, 8), 16) / 255, 2) : 1,\n  };\n};\n\nexport const parseHue = (value: string, unit = \"deg\"): number => {\n  return Number(value) * (angleUnits[unit] || 1);\n};\n\nexport const hslaStringToHsva = (hslString: string): HsvaColor => {\n  const matcher = /hsla?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(hslString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return hslaToHsva({\n    h: parseHue(match[1], match[2]),\n    s: Number(match[3]),\n    l: Number(match[4]),\n    a: match[5] === undefined ? 1 : Number(match[5]) / (match[6] ? 100 : 1),\n  });\n};\n\nexport const hslStringToHsva = hslaStringToHsva;\n\nexport const hslaToHsva = ({ h, s, l, a }: HslaColor): HsvaColor => {\n  s *= (l < 50 ? l : 100 - l) / 100;\n\n  return {\n    h: h,\n    s: s > 0 ? ((2 * s) / (l + s)) * 100 : 0,\n    v: l + s,\n    a,\n  };\n};\n\nexport const hsvaToHex = (hsva: HsvaColor): string => rgbaToHex(hsvaToRgba(hsva));\n\nexport const hsvaToHsla = ({ h, s, v, a }: HsvaColor): HslaColor => {\n  const hh = ((200 - s) * v) / 100;\n\n  return {\n    h: round(h),\n    s: round(hh > 0 && hh < 200 ? ((s * v) / 100 / (hh <= 100 ? hh : 200 - hh)) * 100 : 0),\n    l: round(hh / 2),\n    a: round(a, 2),\n  };\n};\n\nexport const hsvaToHslString = (hsva: HsvaColor): string => {\n  const { h, s, l } = hsvaToHsla(hsva);\n  return `hsl(${h}, ${s}%, ${l}%)`;\n};\n\nexport const hsvaToHsvString = (hsva: HsvaColor): string => {\n  const { h, s, v } = roundHsva(hsva);\n  return `hsv(${h}, ${s}%, ${v}%)`;\n};\n\nexport const hsvaToHsvaString = (hsva: HsvaColor): string => {\n  const { h, s, v, a } = roundHsva(hsva);\n  return `hsva(${h}, ${s}%, ${v}%, ${a})`;\n};\n\nexport const hsvaToHslaString = (hsva: HsvaColor): string => {\n  const { h, s, l, a } = hsvaToHsla(hsva);\n  return `hsla(${h}, ${s}%, ${l}%, ${a})`;\n};\n\nexport const hsvaToRgba = ({ h, s, v, a }: HsvaColor): RgbaColor => {\n  h = (h / 360) * 6;\n  s = s / 100;\n  v = v / 100;\n\n  const hh = Math.floor(h),\n    b = v * (1 - s),\n    c = v * (1 - (h - hh) * s),\n    d = v * (1 - (1 - h + hh) * s),\n    module = hh % 6;\n\n  return {\n    r: round([v, c, b, b, d, v][module] * 255),\n    g: round([d, v, v, c, b, b][module] * 255),\n    b: round([b, b, d, v, v, c][module] * 255),\n    a: round(a, 2),\n  };\n};\n\nexport const hsvaToRgbString = (hsva: HsvaColor): string => {\n  const { r, g, b } = hsvaToRgba(hsva);\n  return `rgb(${r}, ${g}, ${b})`;\n};\n\nexport const hsvaToRgbaString = (hsva: HsvaColor): string => {\n  const { r, g, b, a } = hsvaToRgba(hsva);\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n};\n\nexport const hsvaStringToHsva = (hsvString: string): HsvaColor => {\n  const matcher = /hsva?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(hsvString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return roundHsva({\n    h: parseHue(match[1], match[2]),\n    s: Number(match[3]),\n    v: Number(match[4]),\n    a: match[5] === undefined ? 1 : Number(match[5]) / (match[6] ? 100 : 1),\n  });\n};\n\nexport const hsvStringToHsva = hsvaStringToHsva;\n\nexport const rgbaStringToHsva = (rgbaString: string): HsvaColor => {\n  const matcher = /rgba?\\(?\\s*(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(rgbaString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return rgbaToHsva({\n    r: Number(match[1]) / (match[2] ? 100 / 255 : 1),\n    g: Number(match[3]) / (match[4] ? 100 / 255 : 1),\n    b: Number(match[5]) / (match[6] ? 100 / 255 : 1),\n    a: match[7] === undefined ? 1 : Number(match[7]) / (match[8] ? 100 : 1),\n  });\n};\n\nexport const rgbStringToHsva = rgbaStringToHsva;\n\nconst format = (number: number) => {\n  const hex = number.toString(16);\n  return hex.length < 2 ? \"0\" + hex : hex;\n};\n\nexport const rgbaToHex = ({ r, g, b, a }: RgbaColor): string => {\n  const alphaHex = a < 1 ? format(round(a * 255)) : \"\";\n  return \"#\" + format(r) + format(g) + format(b) + alphaHex;\n};\n\nexport const rgbaToHsva = ({ r, g, b, a }: RgbaColor): HsvaColor => {\n  const max = Math.max(r, g, b);\n  const delta = max - Math.min(r, g, b);\n\n  // prettier-ignore\n  const hh = delta\n    ? max === r\n      ? (g - b) / delta\n      : max === g\n        ? 2 + (b - r) / delta\n        : 4 + (r - g) / delta\n    : 0;\n\n  return {\n    h: round(60 * (hh < 0 ? hh + 6 : hh)),\n    s: round(max ? (delta / max) * 100 : 0),\n    v: round((max / 255) * 100),\n    a,\n  };\n};\n\nexport const roundHsva = (hsva: HsvaColor): HsvaColor => ({\n  h: round(hsva.h),\n  s: round(hsva.s),\n  v: round(hsva.v),\n  a: round(hsva.a, 2),\n});\n\nexport const rgbaToRgb = ({ r, g, b }: RgbaColor): RgbColor => ({ r, g, b });\n\nexport const hslaToHsl = ({ h, s, l }: HslaColor): HslColor => ({ h, s, l });\n\nexport const hsvaToHsv = (hsva: HsvaColor): HsvColor => {\n  const { h, s, v } = roundHsva(hsva);\n  return { h, s, v };\n};\n", "import React from \"react\";\n\nimport { Interactive, Interaction } from \"./Interactive\";\nimport { Pointer } from \"./Pointer\";\n\nimport { hsvaToHslString } from \"../../utils/convert\";\nimport { formatClassName } from \"../../utils/format\";\nimport { clamp } from \"../../utils/clamp\";\nimport { round } from \"../../utils/round\";\n\ninterface Props {\n  className?: string;\n  hue: number;\n  onChange: (newHue: { h: number }) => void;\n}\n\nconst HueBase = ({ className, hue, onChange }: Props) => {\n  const handleMove = (interaction: Interaction) => {\n    onChange({ h: 360 * interaction.left });\n  };\n\n  const handleKey = (offset: Interaction) => {\n    // Hue measured in degrees of the color circle ranging from 0 to 360\n    onChange({\n      h: clamp(hue + offset.left * 360, 0, 360),\n    });\n  };\n\n  const nodeClassName = formatClassName([\"react-colorful__hue\", className]);\n\n  return (\n    <div className={nodeClassName}>\n      <Interactive\n        onMove={handleMove}\n        onKey={handleKey}\n        aria-label=\"Hue\"\n        aria-valuenow={round(hue)}\n        aria-valuemax=\"360\"\n        aria-valuemin=\"0\"\n      >\n        <Pointer\n          className=\"react-colorful__hue-pointer\"\n          left={hue / 360}\n          color={hsvaToHslString({ h: hue, s: 100, v: 100, a: 1 })}\n        />\n      </Interactive>\n    </div>\n  );\n};\n\nexport const Hue = React.memo(HueBase);\n", "import React from \"react\";\nimport { Interactive, Interaction } from \"./Interactive\";\nimport { Pointer } from \"./Pointer\";\nimport { HsvaColor } from \"../../types\";\nimport { hsvaToHslString } from \"../../utils/convert\";\nimport { clamp } from \"../../utils/clamp\";\nimport { round } from \"../../utils/round\";\n\ninterface Props {\n  hsva: HsvaColor;\n  onChange: (newColor: { s: number; v: number }) => void;\n}\n\nconst SaturationBase = ({ hsva, onChange }: Props) => {\n  const handleMove = (interaction: Interaction) => {\n    onChange({\n      s: interaction.left * 100,\n      v: 100 - interaction.top * 100,\n    });\n  };\n\n  const handleKey = (offset: Interaction) => {\n    // Saturation and brightness always fit into [0, 100] range\n    onChange({\n      s: clamp(hsva.s + offset.left * 100, 0, 100),\n      v: clamp(hsva.v - offset.top * 100, 0, 100),\n    });\n  };\n\n  const containerStyle = {\n    backgroundColor: hsvaToHslString({ h: hsva.h, s: 100, v: 100, a: 1 }),\n  };\n\n  return (\n    <div className=\"react-colorful__saturation\" style={containerStyle}>\n      <Interactive\n        onMove={handleMove}\n        onKey={handleKey}\n        aria-label=\"Color\"\n        aria-valuetext={`Saturation ${round(hsva.s)}%, Brightness ${round(hsva.v)}%`}\n      >\n        <Pointer\n          className=\"react-colorful__saturation-pointer\"\n          top={1 - hsva.v / 100}\n          left={hsva.s / 100}\n          color={hsvaToHslString(hsva)}\n        />\n      </Interactive>\n    </div>\n  );\n};\n\nexport const Saturation = React.memo(SaturationBase);\n", "import { hexToRgba } from \"./convert\";\nimport { ObjectColor } from \"../types\";\n\nexport const equalColorObjects = (first: ObjectColor, second: ObjectColor): boolean => {\n  if (first === second) return true;\n\n  for (const prop in first) {\n    // The following allows for a type-safe calling of this function (first & second have to be HSL, HSV, or RGB)\n    // with type-unsafe iterating over object keys. TS does not allow this without an index (`[key: string]: number`)\n    // on an object to define how iteration is normally done. To ensure extra keys are not allowed on our types,\n    // we must cast our object to unknown (as RGB demands `r` be a key, while `Record<string, x>` does not care if\n    // there is or not), and then as a type TS can iterate over.\n    if (\n      ((first as unknown) as Record<string, number>)[prop] !==\n      ((second as unknown) as Record<string, number>)[prop]\n    )\n      return false;\n  }\n\n  return true;\n};\n\nexport const equalColorString = (first: string, second: string): boolean => {\n  return first.replace(/\\s/g, \"\") === second.replace(/\\s/g, \"\");\n};\n\nexport const equalHex = (first: string, second: string): boolean => {\n  if (first.toLowerCase() === second.toLowerCase()) return true;\n\n  // To compare colors like `#FFF` and `ffffff` we convert them into RGB objects\n  return equalColorObjects(hexToRgba(first), hexToRgba(second));\n};\n", "import { useState, useEffect, useCallback, useRef } from \"react\";\nimport { ColorModel, AnyColor, HsvaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { useEventCallback } from \"./useEventCallback\";\n\nexport function useColorManipulation<T extends AnyColor>(\n  colorModel: ColorModel<T>,\n  color: T,\n  onChange?: (color: T) => void\n): [HsvaColor, (color: Partial<HsvaColor>) => void] {\n  // Save onChange callback in the ref for avoiding \"useCallback hell\"\n  const onChangeCallback = useEventCallback<T>(onChange);\n\n  // No matter which color model is used (HEX, RGB(A) or HSL(A)),\n  // all internal calculations are based on HSVA model\n  const [hsva, updateHsva] = useState<HsvaColor>(() => colorModel.toHsva(color));\n\n  // By using this ref we're able to prevent extra updates\n  // and the effects recursion during the color conversion\n  const cache = useRef({ color, hsva });\n\n  // Update local HSVA-value if `color` property value is changed,\n  // but only if that's not the same color that we just sent to the parent\n  useEffect(() => {\n    if (!colorModel.equal(color, cache.current.color)) {\n      const newHsva = colorModel.toHsva(color);\n      cache.current = { hsva: newHsva, color };\n      updateHsva(newHsva);\n    }\n  }, [color, colorModel]);\n\n  // Trigger `onChange` callback only if an updated color is different from cached one;\n  // save the new color to the ref to prevent unnecessary updates\n  useEffect(() => {\n    let newColor;\n    if (\n      !equalColorObjects(hsva, cache.current.hsva) &&\n      !colorModel.equal((newColor = colorModel.fromHsva(hsva)), cache.current.color)\n    ) {\n      cache.current = { hsva, color: newColor };\n      onChangeCallback(newColor);\n    }\n  }, [hsva, colorModel, onChangeCallback]);\n\n  // Merge the current HSVA color object with updated params.\n  // For example, when a child component sends `h` or `s` only\n  const handleChange = useCallback((params: Partial<HsvaColor>) => {\n    updateHsva((current) => Object.assign({}, current, params));\n  }, []);\n\n  return [hsva, handleChange];\n}\n", "declare const __webpack_nonce__: string | undefined;\nlet nonce: string | undefined;\n\n/**\n * Returns a nonce hash included by Webpack or the one defined manually by developer.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/nonce\n * https://webpack.js.org/guides/csp/\n */\nexport const getNonce = (): string | undefined => {\n  if (nonce) return nonce;\n  if (typeof __webpack_nonce__ !== \"undefined\") return __webpack_nonce__;\n  return undefined;\n};\n\n/**\n * Signs the style tag with a base64-encoded string (nonce) to conforms to Content Security Policies.\n * This function has to be invoked before any picker is rendered if you aren't using Webpack for CSP.\n */\nexport const setNonce = (hash: string): void => {\n  nonce = hash;\n};\n", "import { useLayoutEffect, useEffect } from \"react\";\n\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nexport const useIsomorphicLayoutEffect =\n  typeof window !== \"undefined\" ? useLayoutEffect : useEffect;\n", "import { RefObject } from \"react\";\n\nimport { useIsomorphicLayoutEffect } from \"./useIsomorphicLayoutEffect\";\nimport { getNonce } from \"../utils/nonce\";\n\n// Bund<PERSON> is configured to load this as a processed minified CSS-string\nimport styles from \"../css/styles.css\";\n\nconst styleElementMap: Map<Document, HTMLStyleElement> = new Map();\n\n/**\n * Injects CSS code into the document's <head>\n */\nexport const useStyleSheet = (nodeRef: RefObject<HTMLDivElement>): void => {\n  useIsomorphicLayoutEffect(() => {\n    const parentDocument = nodeRef.current ? nodeRef.current.ownerDocument : document;\n\n    if (typeof parentDocument !== \"undefined\" && !styleElementMap.has(parentDocument)) {\n      const styleElement = parentDocument.createElement(\"style\");\n      styleElement.innerHTML = styles;\n      styleElementMap.set(parentDocument, styleElement);\n\n      // Conform to CSP rules by setting `nonce` attribute to the inline styles\n      const nonce = getNonce();\n      if (nonce) styleElement.setAttribute(\"nonce\", nonce);\n\n      parentDocument.head.appendChild(styleElement);\n    }\n  }, []);\n};\n", "import React, { useRef } from \"react\";\n\nimport { Hu<PERSON> } from \"./Hue\";\nimport { Saturation } from \"./Saturation\";\n\nimport { ColorModel, ColorPickerBaseProps, AnyColor } from \"../../types\";\nimport { useColorManipulation } from \"../../hooks/useColorManipulation\";\nimport { useStyleSheet } from \"../../hooks/useStyleSheet\";\nimport { formatClassName } from \"../../utils/format\";\n\ninterface Props<T extends AnyColor> extends Partial<ColorPickerBaseProps<T>> {\n  colorModel: ColorModel<T>;\n}\n\nexport const ColorPicker = <T extends AnyColor>({\n  className,\n  colorModel,\n  color = colorModel.defaultColor,\n  onChange,\n  ...rest\n}: Props<T>): JSX.Element => {\n  const nodeRef = useRef<HTMLDivElement>(null);\n  useStyleSheet(nodeRef);\n\n  const [hsva, updateHsva] = useColorManipulation<T>(colorModel, color, onChange);\n\n  const nodeClassName = formatClassName([\"react-colorful\", className]);\n\n  return (\n    <div {...rest} ref={nodeRef} className={nodeClassName}>\n      <Saturation hsva={hsva} onChange={updateHsva} />\n      <Hue hue={hsva.h} onChange={updateHsva} className=\"react-colorful__last-control\" />\n    </div>\n  );\n};\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalHex } from \"../utils/compare\";\nimport { hexToHsva, hsvaToHex } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"000\",\n  toHsva: hexToHsva,\n  fromHsva: ({ h, s, v }) => hsvaToHex({ h, s, v, a: 1 }),\n  equal: equalHex,\n};\n\nexport const HexColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { Interactive, Interaction } from \"./Interactive\";\nimport { Pointer } from \"./Pointer\";\n\nimport { hsvaToHslaString } from \"../../utils/convert\";\nimport { formatClassName } from \"../../utils/format\";\nimport { clamp } from \"../../utils/clamp\";\nimport { round } from \"../../utils/round\";\nimport { HsvaColor } from \"../../types\";\n\ninterface Props {\n  className?: string;\n  hsva: HsvaColor;\n  onChange: (newAlpha: { a: number }) => void;\n}\n\nexport const Alpha = ({ className, hsva, onChange }: Props): JSX.Element => {\n  const handleMove = (interaction: Interaction) => {\n    onChange({ a: interaction.left });\n  };\n\n  const handleKey = (offset: Interaction) => {\n    // Alpha always fit into [0, 1] range\n    onChange({ a: clamp(hsva.a + offset.left) });\n  };\n\n  // We use `Object.assign` instead of the spread operator\n  // to prevent adding the polyfill (about 150 bytes gzipped)\n  const colorFrom = hsvaToHslaString(Object.assign({}, hsva, { a: 0 }));\n  const colorTo = hsvaToHslaString(Object.assign({}, hsva, { a: 1 }));\n\n  const gradientStyle = {\n    backgroundImage: `linear-gradient(90deg, ${colorFrom}, ${colorTo})`,\n  };\n\n  const nodeClassName = formatClassName([\"react-colorful__alpha\", className]);\n  const ariaValue = round(hsva.a * 100);\n\n  return (\n    <div className={nodeClassName}>\n      <div className=\"react-colorful__alpha-gradient\" style={gradientStyle} />\n      <Interactive\n        onMove={handleMove}\n        onKey={handleKey}\n        aria-label=\"Alpha\"\n        aria-valuetext={`${ariaValue}%`}\n        aria-valuenow={ariaValue}\n        aria-valuemin=\"0\"\n        aria-valuemax=\"100\"\n      >\n        <Pointer\n          className=\"react-colorful__alpha-pointer\"\n          left={hsva.a}\n          color={hsvaToHslaString(hsva)}\n        />\n      </Interactive>\n    </div>\n  );\n};\n", "import React, { useRef } from \"react\";\n\nimport { Hu<PERSON> } from \"./Hue\";\nimport { Saturation } from \"./Saturation\";\nimport { Alpha } from \"./Alpha\";\n\nimport { ColorModel, ColorPickerBaseProps, AnyColor } from \"../../types\";\nimport { useColorManipulation } from \"../../hooks/useColorManipulation\";\nimport { useStyleSheet } from \"../../hooks/useStyleSheet\";\nimport { formatClassName } from \"../../utils/format\";\n\ninterface Props<T extends AnyColor> extends Partial<ColorPickerBaseProps<T>> {\n  colorModel: ColorModel<T>;\n}\n\nexport const AlphaColorPicker = <T extends AnyColor>({\n  className,\n  colorModel,\n  color = colorModel.defaultColor,\n  onChange,\n  ...rest\n}: Props<T>): JSX.Element => {\n  const nodeRef = useRef<HTMLDivElement>(null);\n  useStyleSheet(nodeRef);\n\n  const [hsva, updateHsva] = useColorManipulation<T>(colorModel, color, onChange);\n\n  const nodeClassName = formatClassName([\"react-colorful\", className]);\n\n  return (\n    <div {...rest} ref={nodeRef} className={nodeClassName}>\n      <Saturation hsva={hsva} onChange={updateHsva} />\n      <Hue hue={hsva.h} onChange={updateHsva} />\n      <Alpha hsva={hsva} onChange={updateHsva} className=\"react-colorful__last-control\" />\n    </div>\n  );\n};\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalHex } from \"../utils/compare\";\nimport { hexToHsva, hsvaToHex } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"0001\",\n  toHsva: hexToHsva,\n  fromHsva: hsvaToHex,\n  equal: equalHex,\n};\n\nexport const HexAlphaColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HslaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { hslaToHsva, hsvaToHsla } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HslaColor> = {\n  defaultColor: { h: 0, s: 0, l: 0, a: 1 },\n  toHsva: hslaToHsva,\n  fromHsva: hsvaToHsla,\n  equal: equalColorObjects,\n};\n\nexport const HslaColorPicker = (props: Partial<ColorPickerBaseProps<HslaColor>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hslaStringToHsva, hsvaToHslaString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsla(0, 0%, 0%, 1)\",\n  toHsva: hslaStringToHsva,\n  fromHsva: hsvaToHslaString,\n  equal: equalColorString,\n};\n\nexport const HslaStringColorPicker = (\n  props: Partial<ColorPickerBaseProps<string>>\n): JSX.Element => <AlphaColorPicker {...props} colorModel={colorModel} />;\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HslColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { hslaToHsva, hsvaToHsla, hslaToHsl } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HslColor> = {\n  defaultColor: { h: 0, s: 0, l: 0 },\n  toHsva: ({ h, s, l }) => hslaToHsva({ h, s, l, a: 1 }),\n  fromHsva: (hsva) => hslaToHsl(hsvaToHsla(hsva)),\n  equal: equalColorObjects,\n};\n\nexport const HslColorPicker = (props: Partial<ColorPickerBaseProps<HslColor>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hslStringToHsva, hsvaToHslString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsl(0, 0%, 0%)\",\n  toHsva: hslStringToHsva,\n  fromHsva: hsvaToHslString,\n  equal: equalColorString,\n};\n\nexport const HslStringColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HsvaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { roundHsva } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HsvaColor> = {\n  defaultColor: { h: 0, s: 0, v: 0, a: 1 },\n  toHsva: (hsva) => hsva,\n  fromHsva: roundHsva,\n  equal: equalColorObjects,\n};\n\nexport const HsvaColorPicker = (props: Partial<ColorPickerBaseProps<HsvaColor>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hsvaStringToHsva, hsvaToHsvaString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsva(0, 0%, 0%, 1)\",\n  toHsva: hsvaStringToHsva,\n  fromHsva: hsvaToHsvaString,\n  equal: equalColorString,\n};\n\nexport const HsvaStringColorPicker = (\n  props: Partial<ColorPickerBaseProps<string>>\n): JSX.Element => <AlphaColorPicker {...props} colorModel={colorModel} />;\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, HsvColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { hsvaToHsv } from \"../utils/convert\";\n\nconst colorModel: ColorModel<HsvColor> = {\n  defaultColor: { h: 0, s: 0, v: 0 },\n  toHsva: ({ h, s, v }) => ({ h, s, v, a: 1 }),\n  fromHsva: hsvaToHsv,\n  equal: equalColorObjects,\n};\n\nexport const HsvColorPicker = (props: Partial<ColorPickerBaseProps<HsvColor>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { hsvStringToHsva, hsvaToHsvString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"hsv(0, 0%, 0%)\",\n  toHsva: hsvStringToHsva,\n  fromHsva: hsvaToHsvString,\n  equal: equalColorString,\n};\n\nexport const HsvStringColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, RgbaColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { rgbaToHsva, hsvaToRgba } from \"../utils/convert\";\n\nconst colorModel: ColorModel<RgbaColor> = {\n  defaultColor: { r: 0, g: 0, b: 0, a: 1 },\n  toHsva: rgbaToHsva,\n  fromHsva: hsvaToRgba,\n  equal: equalColorObjects,\n};\n\nexport const RgbaColorPicker = (props: Partial<ColorPickerBaseProps<RgbaColor>>): JSX.Element => (\n  <AlphaColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { AlphaColorPicker } from \"./common/AlphaColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { rgbaStringToHsva, hsvaToRgbaString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"rgba(0, 0, 0, 1)\",\n  toHsva: rgbaStringToHsva,\n  fromHsva: hsvaToRgbaString,\n  equal: equalColorString,\n};\n\nexport const RgbaStringColorPicker = (\n  props: Partial<ColorPickerBaseProps<string>>\n): JSX.Element => <AlphaColorPicker {...props} colorModel={colorModel} />;\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps, RgbColor } from \"../types\";\nimport { equalColorObjects } from \"../utils/compare\";\nimport { rgbaToHsva, hsvaToRgba, rgbaToRgb } from \"../utils/convert\";\n\nconst colorModel: ColorModel<RgbColor> = {\n  defaultColor: { r: 0, g: 0, b: 0 },\n  toHsva: ({ r, g, b }) => rgbaToHsva({ r, g, b, a: 1 }),\n  fromHsva: (hsva) => rgbaToRgb(hsvaToRgba(hsva)),\n  equal: equalColorObjects,\n};\n\nexport const RgbColorPicker = (props: Partial<ColorPickerBaseProps<RgbColor>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "import React from \"react\";\n\nimport { ColorPicker } from \"./common/ColorPicker\";\nimport { ColorModel, ColorPickerBaseProps } from \"../types\";\nimport { equalColorString } from \"../utils/compare\";\nimport { rgbStringToHsva, hsvaToRgbString } from \"../utils/convert\";\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: \"rgb(0, 0, 0)\",\n  toHsva: rgbStringToHsva,\n  fromHsva: hsvaToRgbString,\n  equal: equalColorString,\n};\n\nexport const RgbStringColorPicker = (props: Partial<ColorPickerBaseProps<string>>): JSX.Element => (\n  <ColorPicker {...props} colorModel={colorModel} />\n);\n", "const matcher = /^#?([0-9A-F]{3,8})$/i;\n\nexport const validHex = (value: string, alpha?: boolean): boolean => {\n  const match = matcher.exec(value);\n  const length = match ? match[1].length : 0;\n\n  return (\n    length === 3 || // '#rgb' format\n    length === 6 || // '#rrggbb' format\n    (!!alpha && length === 4) || // '#rgba' format\n    (!!alpha && length === 8) // '#rrggbbaa' format\n  );\n};\n", "import React, { useState, useEffect, useCallback } from \"react\";\n\nimport { useEventCallback } from \"../../hooks/useEventCallback\";\nimport { ColorInputBaseProps } from \"../../types\";\n\ninterface Props extends ColorInputBaseProps {\n  /** Blocks typing invalid characters and limits string length */\n  escape: (value: string) => string;\n  /** Checks that value is valid color string */\n  validate: (value: string) => boolean;\n  /** Processes value before displaying it in the input */\n  format?: (value: string) => string;\n  /** Processes value before sending it in `onChange` */\n  process?: (value: string) => string;\n}\n\nexport const ColorInput = (props: Props): JSX.Element => {\n  const { color = \"\", onChange, onBlur, escape, validate, format, process, ...rest } = props;\n  const [value, setValue] = useState(() => escape(color));\n  const onChangeCallback = useEventCallback<string>(onChange);\n  const onBlurCallback = useEventCallback<React.FocusEvent<HTMLInputElement>>(onBlur);\n\n  // Trigger `onChange` handler only if the input value is a valid color\n  const handleChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      const inputValue = escape(e.target.value);\n      setValue(inputValue);\n      if (validate(inputValue)) onChangeCallback(process ? process(inputValue) : inputValue);\n    },\n    [escape, process, validate, onChangeCallback]\n  );\n\n  // Take the color from props if the last typed color (in local state) is not valid\n  const handleBlur = useCallback(\n    (e: React.FocusEvent<HTMLInputElement>) => {\n      if (!validate(e.target.value)) setValue(escape(color));\n      onBlurCallback(e);\n    },\n    [color, escape, validate, onBlurCallback]\n  );\n\n  // Update the local state when `color` property value is changed\n  useEffect(() => {\n    setValue(escape(color));\n  }, [color, escape]);\n\n  return (\n    <input\n      {...rest}\n      value={format ? format(value) : value}\n      spellCheck=\"false\" // the element should not be checked for spelling errors\n      onChange={handleChange}\n      onBlur={handleBlur}\n    />\n  );\n};\n", "import React, { useCallback } from \"react\";\nimport { ColorInputBaseProps } from \"../types\";\n\nimport { validHex } from \"../utils/validate\";\nimport { ColorInput } from \"./common/ColorInput\";\n\ninterface HexColorInputProps extends ColorInputBaseProps {\n  /** Enables `#` prefix displaying */\n  prefixed?: boolean;\n  /** Allows `#rgba` and `#rrggbbaa` color formats */\n  alpha?: boolean;\n}\n\n/** Adds \"#\" symbol to the beginning of the string */\nconst prefix = (value: string) => \"#\" + value;\n\nexport const HexColorInput = (props: HexColorInputProps): JSX.Element => {\n  const { prefixed, alpha, ...rest } = props;\n\n  /** Escapes all non-hexadecimal characters including \"#\" */\n  const escape = useCallback(\n    (value: string) => value.replace(/([^0-9A-F]+)/gi, \"\").substring(0, alpha ? 8 : 6),\n    [alpha]\n  );\n\n  /** Validates hexadecimal strings */\n  const validate = useCallback((value: string) => validHex(value, alpha), [alpha]);\n\n  return (\n    <ColorInput\n      {...rest}\n      escape={escape}\n      format={prefixed ? prefix : undefined}\n      process={prefix}\n      validate={validate}\n    />\n  );\n};\n"], "names": ["useEventCallback", "handler", "callback<PERSON><PERSON>", "useRef", "fn", "value", "current", "clamp", "number", "min", "max", "is<PERSON><PERSON>ch", "event", "getParentWindow", "node", "ownerDocument", "defaultView", "self", "getRelativePosition", "touchId", "rect", "getBoundingClientRect", "pointer", "touches", "i", "length", "identifier", "getTouchPoint", "left", "pageX", "pageXOffset", "width", "top", "pageY", "pageYOffset", "height", "preventDefaultMove", "preventDefault", "Interactive", "React", "memo", "onMove", "onKey", "rest", "container", "onMoveCallback", "onKeyCallback", "has<PERSON><PERSON><PERSON>", "useMemo", "handleMove", "buttons", "toggleDocumentEvents", "handleMoveEnd", "state", "touch", "parentWindow", "toggleEvent", "addEventListener", "removeEventListener", "nativeEvent", "el", "isInvalid", "changedTouches", "focus", "keyCode", "which", "handleMoveStart", "handleKeyDown", "useEffect", "onTouchStart", "onMouseDown", "className", "ref", "onKeyDown", "tabIndex", "role", "formatClassName", "names", "filter", "Boolean", "join", "Pointer", "color", "nodeClassName", "style", "backgroundColor", "round", "digits", "base", "Math", "pow", "angleUnits", "grad", "turn", "rad", "PI", "hexToHsva", "hex", "rgbaToHsva", "hexToRgba", "substring", "r", "parseInt", "g", "b", "a", "parseHue", "unit", "Number", "hslaStringToHsva", "hslString", "match", "exec", "hslaToHsva", "h", "s", "l", "undefined", "v", "hslStringToHsva", "hsvaToHex", "hsva", "rgbaToHex", "hsvaToRgba", "hsvaToHsla", "hh", "hsvaToHslString", "hsvaToHslaString", "floor", "c", "d", "module", "hsvaStringToHsva", "hsvString", "roundHsva", "hsvStringToHsva", "rgbaStringToHsva", "rgbaString", "rgbStringToHsva", "format", "toString", "alphaHex", "delta", "<PERSON><PERSON>", "hue", "onChange", "interaction", "offset", "aria-label", "aria-valuenow", "aria-valuemax", "aria-valuemin", "Saturation", "containerStyle", "aria-valuetext", "equalColorObjects", "first", "second", "prop", "equalColorString", "replace", "equalHex", "toLowerCase", "useColorManipulation", "colorModel", "onChangeCallback", "useState", "toHsva", "updateHsva", "cache", "equal", "newHsva", "newColor", "fromHsva", "handleChange", "useCallback", "params", "Object", "assign", "nonce", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "getNonce", "__webpack_nonce__", "setNonce", "hash", "styleElementMap", "Map", "useStyleSheet", "nodeRef", "parentDocument", "document", "has", "styleElement", "createElement", "innerHTML", "set", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "ColorPicker", "defaultColor", "HexColorPicker", "props", "Alpha", "gradientStyle", "backgroundImage", "ariaValue", "AlphaColorPicker", "HexAlphaColorPicker", "HslaColorPicker", "HslaStringColorPicker", "HslColorPicker", "HslStringColorPicker", "HsvaColorPicker", "HsvaStringColorPicker", "HsvColorPicker", "HsvStringColorPicker", "RgbaColorPicker", "RgbaStringColorPicker", "RgbColorPicker", "RgbStringColorPicker", "matcher", "ColorInput", "onBlur", "escape", "validate", "process", "setValue", "onBlurCallback", "e", "inputValue", "target", "handleBlur", "spell<PERSON>heck", "prefix", "HexColorInput", "prefixed", "alpha", "validHex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAGgBA,EAAoBC,CAAAA;IAClC,IAAMC,sKAAcC,SAAAA,EAAOF,IACrBG,+KAAKD,EAAO,SAACE,CAAAA;QACjBH,EAAYI,OAAAA,IAAWJ,EAAYI,OAAAA,CAAQD;IAAAA;IAI7C,OAFAH,EAAYI,OAAAA,GAAUL,GAEfG,EAAGE,OAAAA;AAAAA;AAAAA,ICPCC,IAAQ,SAACC,CAAAA,EAAgBC,CAAAA,EAASC,CAAAA;IAC7C,OAAA,KAAA,MADoCD,KAAAA,CAAAA,IAAM,CAAA,GAAA,KAAA,MAAGC,KAAAA,CAAAA,IAAM,CAAA,GAC5CF,IAASE,IAAMA,IAAMF,IAASC,IAAMA,IAAMD;AAAAA,GCO7CG,IAAU,SAACC,CAAAA;IAAAA,OAAwD,aAAaA;AAAAA,GAWhFC,IAAkB,SAACC,CAAAA;IACvB,OAAQA,KAAQA,EAAKC,aAAAA,CAAcC,WAAAA,IAAgBC;AAAAA,GAI/CC,IAAsB,SAC1BJ,CAAAA,EACAF,CAAAA,EACAO,CAAAA;IAEA,IAAMC,IAAON,EAAKO,qBAAAA,IAGZC,IAAUX,EAAQC,KArBJ,SAACW,CAAAA,EAAoBJ,CAAAA;QACzC,IAAK,IAAIK,IAAI,GAAGA,IAAID,EAAQE,MAAAA,EAAQD,IAClC,IAAID,CAAAA,CAAQC,EAAAA,CAAGE,UAAAA,KAAeP,GAAS,OAAOI,CAAAA,CAAQC,EAAAA;QAExD,OAAOD,CAAAA,CAAQ,EAAA;IAAA,CAiBkBI,CAAcf,EAAMW,OAAAA,EAASJ,KAAYP;IAE1E,OAAO;QACLgB,MAAMrB,EAAAA,CAAOe,EAAQO,KAAAA,GAAAA,CAAST,EAAKQ,IAAAA,GAAOf,EAAgBC,GAAMgB,WAAAA,CAAAA,IAAgBV,EAAKW,KAAAA;QACrFC,KAAKzB,EAAAA,CAAOe,EAAQW,KAAAA,GAAAA,CAASb,EAAKY,GAAAA,GAAMnB,EAAgBC,GAAMoB,WAAAA,CAAAA,IAAgBd,EAAKe,MAAAA;IAAAA;AAAAA,GAOjFC,IAAqB,SAACxB,CAAAA;IAAAA,CACzBD,EAAQC,MAAUA,EAAMyB,cAAAA;AAAAA,GA8GdC,IAAcC,wKAAAA,CAAMC,IAAAA,CA/FT,SAAA,CAAA;IAAA,IAAGC,IAAAA,EAAAA,MAAAA,EAAQC,IAAAA,EAAAA,KAAAA,EAAUC,IAAAA,EAAAA,GAAAA;QAAAA;QAAAA;KAAAA,GACrCC,IAAYzC,2KAAAA,EAAuB,OACnC0C,IAAiB7C,EAA8ByC,IAC/CK,IAAgB9C,EAA8B0C,IAC9CvB,qKAAUhB,UAAAA,EAAsB,OAChC4C,+KAAW5C,EAAAA,CAAO,IAAA,gLAEuC6C,EAAQ;QACrE,IAoBMC,IAAa,SAACrC,CAAAA;YAElBwB,EAAmBxB,IAAAA,CAOJD,EAAQC,KAASA,EAAMW,OAAAA,CAAQE,MAAAA,GAAS,IAAIb,EAAMsC,OAAAA,GAAU,CAAA,KAE7DN,EAAUtC,OAAAA,GACtBuC,EAAe3B,EAAoB0B,EAAUtC,OAAAA,EAASM,GAAOO,EAAQb,OAAAA,KAErE6C,EAAAA,CAAqB;QAAA,GAInBC,IAAgB;YAAA,OAAMD,EAAAA,CAAqB;QAAA;QAkBjD,SAASA,EAAqBE,CAAAA;YAC5B,IAAMC,IAAQP,EAASzC,OAAAA,EAEjBiD,IAAe1C,EADV+B,EAAUtC,OAAAA,GAIfkD,IAAcH,IAAQE,EAAaE,gBAAAA,GAAmBF,EAAaG,mBAAAA;YACzEF,EAAYF,IAAQ,cAAc,aAAaL,IAC/CO,EAAYF,IAAQ,aAAa,WAAWF;QAAAA;QAG9C,OAAO;YAnEiB,SAAA,CAAA;gBAAA,IAAGO,IAAAA,EAAAA,WAAAA,EACnBC,IAAKhB,EAAUtC,OAAAA;gBACrB,IAAKsD,KAAAA,CAGLxB,EAAmBuB,IAAAA,CAvBP,SAAC/C,CAAAA,EAAgCmC,CAAAA;oBACjD,OAAOA,KAAAA,CAAapC,EAAQC;gBAAAA,CAwBpBiD,CAAUF,GAAaZ,EAASzC,OAAAA,KAAasD,CAAAA,GAAjD;oBAEA,IAAIjD,EAAQgD,IAAc;wBACxBZ,EAASzC,OAAAA,GAAAA,CAAU;wBACnB,IAAMwD,IAAiBH,EAAYG,cAAAA,IAAkB,EAAA;wBACjDA,EAAerC,MAAAA,IAAAA,CAAQN,EAAQb,OAAAA,GAAUwD,CAAAA,CAAe,EAAA,CAAGpC,UAAAA;oBAAAA;oBAGjEkC,EAAGG,KAAAA,IACHlB,EAAe3B,EAAoB0C,GAAID,GAAaxC,EAAQb,OAAAA,IAC5D6C,EAAAA,CAAqB;gBAAA;YAAA;YAuBD,SAACvC,CAAAA;gBACrB,IAAMoD,IAAUpD,EAAMqD,KAAAA,IAASrD,EAAMoD,OAAAA;gBAGjCA,IAAU,MAAMA,IAAU,MAAA,CAE9BpD,EAAMyB,cAAAA,IAINS,EAAc;oBACZlB,MAAkB,OAAZoC,IAAiB,MAAmB,OAAZA,IAAAA,CAAkB,MAAO;oBACvDhC,KAAiB,OAAZgC,IAAiB,MAAmB,OAAZA,IAAAA,CAAkB,MAAO;gBAAA,EAAA;YAAA;YAelBb;SAAAA;IAAAA,GACvC;QAACL;QAAeD;KAAAA,GArEZqB,IAAAA,CAAAA,CAAAA,EAAAA,EAAiBC,IAAAA,CAAAA,CAAAA,EAAAA,EAAehB,IAAAA,CAAAA,CAAAA,EAAAA;IA0EvC,qLAFAiB,EAAU;QAAA,OAAMjB;IAAAA,GAAsB;QAACA;KAAAA,GAGrCZ,wKAAAA,CAAAA,aAAAA,CAAAA,OAAAA,EAAAA,CAAAA,GACMI,GAAAA;QACJ0B,cAAcH;QACdI,aAAaJ;QACbK,WAAU;QACVC,KAAK5B;QACL6B,WAAWN;QACXO,UAAU;QACVC,MAAK;IAAA;AAAA,ICxJEC,IAAkB,SAACC,CAAAA;IAAAA,OAA6BA,EAAMC,MAAAA,CAAOC,SAASC,IAAAA,CAAK;AAAA,GCU3EC,IAAU,SAAA,CAAA;IAAA,IAAcC,IAAAA,EAAAA,KAAAA,EAAOtD,IAAAA,EAAAA,IAAAA,EAAAA,IAAAA,EAAMI,GAAAA,EAAAA,IAAAA,KAAAA,MAAAA,IAAM,KAAA,GAChDmD,IAAgBP,EAAgB;QAAC;QAAA,EADfL,SAAAA;KAAAA;IAQxB,qKACEhC,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA;QAAKgC,WAAWY;QAAeC,OANnB;YACZpD,KAAc,MAANA,IAAAA;YACRJ,MAAgB,MAAPA,IAAAA;QAAAA;IAAAA,iKAKPW,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA;QAAKgC,WAAU;QAA+Ba,OAAO;YAAEC,iBAAiBH;QAAAA;IAAAA;AAAAA,GCpBjEI,IAAQ,SAAC9E,CAAAA,EAAgB+E,CAAAA,EAAYC,CAAAA;IAChD,OAAA,KAAA,MADoCD,KAAAA,CAAAA,IAAS,CAAA,GAAA,KAAA,MAAGC,KAAAA,CAAAA,IAAOC,KAAKC,GAAAA,CAAI,IAAIH,EAAAA,GAC7DE,KAAKH,KAAAA,CAAME,IAAOhF,KAAUgF;AAAAA,GCM/BG,IAAqC;IACzCC,MAAM;IACNC,MAAM;IACNC,KAAK,MAAA,CAAiB,IAAVL,KAAKM,EAAAA;AAAAA,GAGNC,IAAY,SAACC,CAAAA;IAAAA,OAA2BC,EAAWC,EAAUF;AAAAA,GAE7DE,IAAY,SAACF,CAAAA;IAGxB,OAFe,QAAXA,CAAAA,CAAI,EAAA,IAAA,CAAYA,IAAMA,EAAIG,SAAAA,CAAU,EAAA,GAEpCH,EAAIxE,MAAAA,GAAS,IACR;QACL4E,GAAGC,SAASL,CAAAA,CAAI,EAAA,GAAKA,CAAAA,CAAI,EAAA,EAAI;QAC7BM,GAAGD,SAASL,CAAAA,CAAI,EAAA,GAAKA,CAAAA,CAAI,EAAA,EAAI;QAC7BO,GAAGF,SAASL,CAAAA,CAAI,EAAA,GAAKA,CAAAA,CAAI,EAAA,EAAI;QAC7BQ,GAAkB,MAAfR,EAAIxE,MAAAA,GAAe6D,EAAMgB,SAASL,CAAAA,CAAI,EAAA,GAAKA,CAAAA,CAAI,EAAA,EAAI,MAAM,KAAK,KAAK;IAAA,IAInE;QACLI,GAAGC,SAASL,EAAIG,SAAAA,CAAU,GAAG,IAAI;QACjCG,GAAGD,SAASL,EAAIG,SAAAA,CAAU,GAAG,IAAI;QACjCI,GAAGF,SAASL,EAAIG,SAAAA,CAAU,GAAG,IAAI;QACjCK,GAAkB,MAAfR,EAAIxE,MAAAA,GAAe6D,EAAMgB,SAASL,EAAIG,SAAAA,CAAU,GAAG,IAAI,MAAM,KAAK,KAAK;IAAA;AAAA,GAIjEM,IAAW,SAACrG,CAAAA,EAAesG,CAAAA;IACtC,OAAA,KAAA,MADsCA,KAAAA,CAAAA,IAAO,KAAA,GACtCC,OAAOvG,KAAAA,CAAUsF,CAAAA,CAAWgB,EAAAA,IAAS,CAAA;AAAA,GAGjCE,IAAmB,SAACC,CAAAA;IAC/B,IACMC,IADU,6HACMC,IAAAA,CAAKF;IAE3B,OAAKC,IAEEE,EAAW;QAChBC,GAAGR,EAASK,CAAAA,CAAM,EAAA,EAAIA,CAAAA,CAAM,EAAA;QAC5BI,GAAGP,OAAOG,CAAAA,CAAM,EAAA;QAChBK,GAAGR,OAAOG,CAAAA,CAAM,EAAA;QAChBN,GAAAA,KAAgBY,MAAbN,CAAAA,CAAM,EAAA,GAAmB,IAAIH,OAAOG,CAAAA,CAAM,EAAA,IAAA,CAAOA,CAAAA,CAAM,EAAA,GAAK,MAAM,CAAA;IAAA,KANpD;QAAEG,GAAG;QAAGC,GAAG;QAAGG,GAAG;QAAGb,GAAG;IAAA;AAAA,GAU/Bc,IAAkBV,GAElBI,IAAa,SAAA,CAAA;IAAA,IAAME,IAAAA,EAAAA,CAAAA,EAAGC,IAAAA,EAAAA,CAAAA;IAGjC,OAAO;QACLF,GAAAA,EAJyBA,CAAAA;QAKzBC,GAAAA,CAJFA,KAAAA,CAAMC,IAAI,KAAKA,IAAI,MAAMA,CAAAA,IAAK,GAAA,IAIrB,IAAM,IAAID,IAAAA,CAAMC,IAAID,CAAAA,IAAM,MAAM;QACvCG,GAAGF,IAAID;QACPV,GAAAA,EAPkCA,CAAAA;IAAAA;AAAAA,GAWzBe,IAAY,SAACC,CAAAA;IAAAA,OAA4BC,EAAUC,EAAWF;AAAAA,GAE9DG,IAAa,SAAA,CAAA;IAAA,IAAMT,IAAAA,EAAAA,CAAAA,EAAGG,IAAAA,EAAAA,CAAAA,EAAGb,IAAAA,EAAAA,CAAAA,EAC9BoB,IAAAA,CAAO,MAAMV,CAAAA,IAAKG,IAAK;IAE7B,OAAO;QACLJ,GAAG5B,EAAAA,EAJsB4B,CAAAA;QAKzBC,GAAG7B,EAAMuC,IAAK,KAAKA,IAAK,MAAQV,IAAIG,IAAK,MAAA,CAAOO,KAAM,MAAMA,IAAK,MAAMA,CAAAA,IAAO,MAAM;QACpFT,GAAG9B,EAAMuC,IAAK;QACdpB,GAAGnB,EAAMmB,GAAG;IAAA;AAAA,GAIHqB,IAAkB,SAACL,CAAAA;IAAAA,IAAAA,IACVG,EAAWH;IAC/B,OAAA,SAAA,EADQP,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA,QAAAA,EAAGC,CAAAA,GAAAA;AAAAA,GAcHW,IAAmB,SAACN,CAAAA;IAAAA,IAAAA,IACRG,EAAWH;IAClC,OAAA,UAAA,EADQP,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA,QAAAA,EAAGC,CAAAA,GAAAA,QAAAA,EAAGX,CAAAA,GAAAA;AAAAA,GAINkB,IAAa,SAAA,CAAA;IAAA,IAAGT,IAAAA,EAAAA,CAAAA,EAAGC,IAAAA,EAAAA,CAAAA,EAAGG,IAAAA,EAAAA,CAAAA,EAAGb,IAAAA,EAAAA,CAAAA;IACpCS,IAAKA,IAAI,MAAO,GAChBC,KAAQ,KACRG,KAAQ;IAER,IAAMO,IAAKpC,KAAKuC,KAAAA,CAAMd,IACpBV,IAAIc,IAAAA,CAAK,IAAIH,CAAAA,GACbc,IAAIX,IAAAA,CAAK,IAAA,CAAKJ,IAAIW,CAAAA,IAAMV,CAAAA,GACxBe,IAAIZ,IAAAA,CAAK,IAAA,CAAK,IAAIJ,IAAIW,CAAAA,IAAMV,CAAAA,GAC5BgB,IAASN,IAAK;IAEhB,OAAO;QACLxB,GAAGf,EAAmC,MAA7B;YAACgC;YAAGW;YAAGzB;YAAGA;YAAG0B;YAAGZ;SAAAA,CAAGa,EAAAA;QAC5B5B,GAAGjB,EAAmC,MAA7B;YAAC4C;YAAGZ;YAAGA;YAAGW;YAAGzB;YAAGA;SAAAA,CAAG2B,EAAAA;QAC5B3B,GAAGlB,EAAmC,MAA7B;YAACkB;YAAGA;YAAG0B;YAAGZ;YAAGA;YAAGW;SAAAA,CAAGE,EAAAA;QAC5B1B,GAAGnB,EAAMmB,GAAG;IAAA;AAAA,GAcH2B,IAAmB,SAACC,CAAAA;IAC/B,IACMtB,IADU,6HACMC,IAAAA,CAAKqB;IAE3B,OAAKtB,IAEEuB,EAAU;QACfpB,GAAGR,EAASK,CAAAA,CAAM,EAAA,EAAIA,CAAAA,CAAM,EAAA;QAC5BI,GAAGP,OAAOG,CAAAA,CAAM,EAAA;QAChBO,GAAGV,OAAOG,CAAAA,CAAM,EAAA;QAChBN,GAAAA,KAAgBY,MAAbN,CAAAA,CAAM,EAAA,GAAmB,IAAIH,OAAOG,CAAAA,CAAM,EAAA,IAAA,CAAOA,CAAAA,CAAM,EAAA,GAAK,MAAM,CAAA;IAAA,KANpD;QAAEG,GAAG;QAAGC,GAAG;QAAGG,GAAG;QAAGb,GAAG;IAAA;AAAA,GAU/B8B,IAAkBH,GAElBI,IAAmB,SAACC,CAAAA;IAC/B,IACM1B,IADU,iHACMC,IAAAA,CAAKyB;IAE3B,OAAK1B,IAEEb,EAAW;QAChBG,GAAGO,OAAOG,CAAAA,CAAM,EAAA,IAAA,CAAOA,CAAAA,CAAM,EAAA,GAAK,MAAM,MAAM,CAAA;QAC9CR,GAAGK,OAAOG,CAAAA,CAAM,EAAA,IAAA,CAAOA,CAAAA,CAAM,EAAA,GAAK,MAAM,MAAM,CAAA;QAC9CP,GAAGI,OAAOG,CAAAA,CAAM,EAAA,IAAA,CAAOA,CAAAA,CAAM,EAAA,GAAK,MAAM,MAAM,CAAA;QAC9CN,GAAAA,KAAgBY,MAAbN,CAAAA,CAAM,EAAA,GAAmB,IAAIH,OAAOG,CAAAA,CAAM,EAAA,IAAA,CAAOA,CAAAA,CAAM,EAAA,GAAK,MAAM,CAAA;IAAA,KANpD;QAAEG,GAAG;QAAGC,GAAG;QAAGG,GAAG;QAAGb,GAAG;IAAA;AAAA,GAU/BiC,IAAkBF,GAEzBG,IAAS,SAACnI,CAAAA;IACd,IAAMyF,IAAMzF,EAAOoI,QAAAA,CAAS;IAC5B,OAAO3C,EAAIxE,MAAAA,GAAS,IAAI,MAAMwE,IAAMA;AAAAA,GAGzByB,IAAY,SAAA,CAAA;IAAA,IAAGrB,IAAAA,EAAAA,CAAAA,EAAGE,IAAAA,EAAAA,CAAAA,EAAGC,IAAAA,EAAAA,CAAAA,EAAGC,IAAAA,EAAAA,CAAAA,EAC7BoC,IAAWpC,IAAI,IAAIkC,EAAOrD,EAAU,MAAJmB,MAAY;IAClD,OAAO,MAAMkC,EAAOtC,KAAKsC,EAAOpC,KAAKoC,EAAOnC,KAAKqC;AAAAA,GAGtC3C,IAAa,SAAA,CAAA;IAAA,IAAGG,IAAAA,EAAAA,CAAAA,EAAGE,IAAAA,EAAAA,CAAAA,EAAGC,IAAAA,EAAAA,CAAAA,EAAGC,IAAAA,EAAAA,CAAAA,EAC9B/F,IAAM+E,KAAK/E,GAAAA,CAAI2F,GAAGE,GAAGC,IACrBsC,IAAQpI,IAAM+E,KAAKhF,GAAAA,CAAI4F,GAAGE,GAAGC,IAG7BqB,IAAKiB,IACPpI,MAAQ2F,IAAAA,CACLE,IAAIC,CAAAA,IAAKsC,IACVpI,MAAQ6F,IACN,IAAA,CAAKC,IAAIH,CAAAA,IAAKyC,IACd,IAAA,CAAKzC,IAAIE,CAAAA,IAAKuC,IAClB;IAEJ,OAAO;QACL5B,GAAG5B,EAAM,KAAA,CAAMuC,IAAK,IAAIA,IAAK,IAAIA,CAAAA;QACjCV,GAAG7B,EAAM5E,IAAOoI,IAAQpI,IAAO,MAAM;QACrC4G,GAAGhC,EAAO5E,IAAM,MAAO;QACvB+F,GAAAA;IAAAA;AAAAA,GAIS6B,IAAY,SAACb,CAAAA;IAAAA,OAAgC;QACxDP,GAAG5B,EAAMmC,EAAKP,CAAAA;QACdC,GAAG7B,EAAMmC,EAAKN,CAAAA;QACdG,GAAGhC,EAAMmC,EAAKH,CAAAA;QACdb,GAAGnB,EAAMmC,EAAKhB,CAAAA,EAAG;IAAA;AAAA,GCjJNsC,kKAAMxG,UAAAA,CAAMC,IAAAA,CAlCT,SAAA,CAAA;IAAA,IAAcwG,IAAAA,EAAAA,GAAAA,EAAKC,IAAAA,EAAAA,QAAAA,EAY3B9D,IAAgBP,EAAgB;QAAC;QAAA,EAZtBL,SAAAA;KAAAA;IAcjB,qKACEhC,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA;QAAKgC,WAAWY;IAAAA,iKACd5C,UAAAA,CAAAA,aAAAA,CAACD,GAAAA;QACCG,QAhBa,SAACyG,CAAAA;YAClBD,EAAS;gBAAE/B,GAAG,MAAMgC,EAAYtH,IAAAA;YAAAA;QAAAA;QAgB5Bc,OAbY,SAACyG,CAAAA;YAEjBF,EAAS;gBACP/B,GAAG3G,EAAMyI,IAAoB,MAAdG,EAAOvH,IAAAA,EAAY,GAAG;YAAA;QAAA;QAWnCwH,cAAW;QACXC,iBAAe/D,EAAM0D;QACrBM,iBAAc;QACdC,iBAAc;IAAA,GAEdhH,wKAAAA,CAAAA,aAAAA,CAAC0C,GAAAA;QACCV,WAAU;QACV3C,MAAMoH,IAAM;QACZ9D,OAAO4C,EAAgB;YAAEZ,GAAG8B;YAAK7B,GAAG;YAAKG,GAAG;YAAKb,GAAG;QAAA;IAAA;AAAA,ICSjD+C,kKAAajH,UAAAA,CAAMC,IAAAA,CAvCT,SAAA,CAAA;IAAA,IAAGiF,IAAAA,EAAAA,IAAAA,EAAMwB,IAAAA,EAAAA,QAAAA,EAgBxBQ,IAAiB;QACrBpE,iBAAiByC,EAAgB;YAAEZ,GAAGO,EAAKP,CAAAA;YAAGC,GAAG;YAAKG,GAAG;YAAKb,GAAG;QAAA;IAAA;IAGnE,OACElE,wKAAAA,CAAAA,aAAAA,CAAAA,OAAAA;QAAKgC,WAAU;QAA6Ba,OAAOqE;IAAAA,iKACjDlH,UAAAA,CAAAA,aAAAA,CAACD,GAAAA;QACCG,QAtBa,SAACyG,CAAAA;YAClBD,EAAS;gBACP9B,GAAsB,MAAnB+B,EAAYtH,IAAAA;gBACf0F,GAAG,MAAwB,MAAlB4B,EAAYlH,GAAAA;YAAAA;QAAAA;QAoBnBU,OAhBY,SAACyG,CAAAA;YAEjBF,EAAS;gBACP9B,GAAG5G,EAAMkH,EAAKN,CAAAA,GAAkB,MAAdgC,EAAOvH,IAAAA,EAAY,GAAG;gBACxC0F,GAAG/G,EAAMkH,EAAKH,CAAAA,GAAiB,MAAb6B,EAAOnH,GAAAA,EAAW,GAAG;YAAA;QAAA;QAarCoH,cAAW;QACXM,kBAAAA,gBAA8BpE,EAAMmC,EAAKN,CAAAA,IAAAA,mBAAmB7B,EAAMmC,EAAKH,CAAAA,IAAAA;IAAAA,iKAEvE/E,UAAAA,CAAAA,aAAAA,CAAC0C,GAAAA;QACCV,WAAU;QACVvC,KAAK,IAAIyF,EAAKH,CAAAA,GAAI;QAClB1F,MAAM6F,EAAKN,CAAAA,GAAI;QACfjC,OAAO4C,EAAgBL;IAAAA;AAAAA,IC1CpBkC,IAAoB,SAACC,CAAAA,EAAoBC,CAAAA;IACpD,IAAID,MAAUC,GAAQ,OAAA,CAAA;IAEtB,IAAK,IAAMC,KAAQF,EAMjB,IACIA,CAAAA,CAA6CE,EAAAA,KAC7CD,CAAAA,CAA8CC,EAAAA,EAEhD,OAAA,CAAA;IAGJ,OAAA,CAAA;AAAA,GAGWC,IAAmB,SAACH,CAAAA,EAAeC,CAAAA;IAC9C,OAAOD,EAAMI,OAAAA,CAAQ,OAAO,QAAQH,EAAOG,OAAAA,CAAQ,OAAO;AAAA,GAG/CC,IAAW,SAACL,CAAAA,EAAeC,CAAAA;IACtC,OAAID,EAAMM,WAAAA,OAAkBL,EAAOK,WAAAA,MAG5BP,EAAkBxD,EAAUyD,IAAQzD,EAAU0D;AAAAA;AAAAA,SCzBvCM,EACdC,CAAAA,EACAlF,CAAAA,EACA+D,CAAAA;IAGA,IAAMoB,IAAmBrK,EAAoBiJ,IAAAA,iLAIlBqB,EAAoB;QAAA,OAAMF,EAAWG,MAAAA,CAAOrF;IAAAA,IAAhEuC,IAAAA,CAAAA,CAAAA,EAAAA,EAAM+C,IAAAA,CAAAA,CAAAA,EAAAA,EAIPC,+KAAQtK,EAAO;QAAE+E,OAAAA;QAAOuC,MAAAA;IAAAA;kLAI9BrD,EAAU;QACR,IAAA,CAAKgG,EAAWM,KAAAA,CAAMxF,GAAOuF,EAAMnK,OAAAA,CAAQ4E,KAAAA,GAAQ;YACjD,IAAMyF,IAAUP,EAAWG,MAAAA,CAAOrF;YAClCuF,EAAMnK,OAAAA,GAAU;gBAAEmH,MAAMkD;gBAASzF,OAAAA;YAAAA,GACjCsF,EAAWG;QAAAA;IAAAA,GAEZ;QAACzF;QAAOkF;KAAAA,iLAIXhG,EAAU;QACR,IAAIwG;QAEDjB,EAAkBlC,GAAMgD,EAAMnK,OAAAA,CAAQmH,IAAAA,KACtC2C,EAAWM,KAAAA,CAAOE,IAAWR,EAAWS,QAAAA,CAASpD,IAAQgD,EAAMnK,OAAAA,CAAQ4E,KAAAA,KAAAA,CAExEuF,EAAMnK,OAAAA,GAAU;YAAEmH,MAAAA;YAAMvC,OAAO0F;QAAAA,GAC/BP,EAAiBO,EAAAA;IAAAA,GAElB;QAACnD;QAAM2C;QAAYC;KAAAA;IAItB,IAAMS,KAAeC,+KAAAA,EAAY,SAACC,CAAAA;QAChCR,EAAW,SAAClK,CAAAA;YAAAA,OAAY2K,OAAOC,MAAAA,CAAO,CAAA,GAAI5K,GAAS0K;QAAAA;IAAAA,GAClD,EAAA;IAEH,OAAO;QAACvD;QAAMqD;KAAAA;AAAAA;AAAAA,ICjDZK,GCISC,IACO,eAAA,OAAXC,uKAAyBC,kBAAAA,iKAAkBlH,YAAAA,EDEvCmH,IAAW;IACtB,OAAIJ,KAAAA,CAC6B,eAAA,OAAtBK,oBAA0CA,oBAAAA,KAArD,CAAA;AAAA,GAQWC,IAAW,SAACC,CAAAA;IACvBP,IAAQO;AAAAA,GEXJC,IAAmD,IAAIC,KAKhDC,IAAgB,SAACC,CAAAA;IAC5BV,EAA0B;QACxB,IAAMW,IAAiBD,EAAQxL,OAAAA,GAAUwL,EAAQxL,OAAAA,CAAQS,aAAAA,GAAgBiL;QAEzE,IAAA,KAA8B,MAAnBD,KAAAA,CAAmCJ,EAAgBM,GAAAA,CAAIF,IAAiB;YACjF,IAAMG,IAAeH,EAAeI,aAAAA,CAAc;YAClDD,EAAaE,SAAAA,GAAAA,mtDACbT,EAAgBU,GAAAA,CAAIN,GAAgBG;YAGpC,IAAMf,IAAQI;YACVJ,KAAOe,EAAaI,YAAAA,CAAa,SAASnB,IAE9CY,EAAeQ,IAAAA,CAAKC,WAAAA,CAAYN;QAAAA;IAAAA,GAEjC,EAAA;AAAA,GCdQO,IAAc,SAAA,CAAA;IAAA,IACzBlI,IAAAA,EAAAA,SAAAA,EACA6F,IAAAA,EAAAA,UAAAA,EAAAA,IAAAA,EACAlF,KAAAA,EAAAA,IAAAA,KAAAA,MAAAA,IAAQkF,EAAWsC,YAAAA,GAAAA,GACnBzD,IAAAA,EAAAA,QAAAA,EACGtG,IAAAA,EAAAA,GAAAA;QAAAA;QAAAA;QAAAA;QAAAA;KAAAA,GAEGmJ,IAAU3L,2KAAAA,EAAuB;IACvC0L,EAAcC;IAAAA,IAAAA,IAEa3B,EAAwBC,GAAYlF,GAAO+D,IAA/DxB,IAAAA,CAAAA,CAAAA,EAAAA,EAAM+C,IAAAA,CAAAA,CAAAA,EAAAA,EAEPrF,IAAgBP,EAAgB;QAAC;QAAkBL;KAAAA;IAEzD,qKACEhC,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA,EAAAA,CAAAA,GAASI,GAAAA;QAAM6B,KAAKsH;QAASvH,WAAWY;IAAAA,kKACtC5C,UAAAA,CAAAA,aAAAA,CAACiH,GAAAA;QAAW/B,MAAMA;QAAMwB,UAAUuB;IAAAA,kKAClCjI,UAAAA,CAAAA,aAAAA,CAACwG,GAAAA;QAAIC,KAAKvB,EAAKP,CAAAA;QAAG+B,UAAUuB;QAAYjG,WAAU;IAAA;AAAA,GCxBlD6F,IAAiC;IACrCsC,cAAc;IACdnC,QAAQvE;IACR6E,UAAU,SAAA,CAAA;QAAA,OAAiBrD,EAAU;YAAEN,GAAAA,EAA1BA,CAAAA;YAA6BC,GAAAA,EAA1BA,CAAAA;YAA6BG,GAAAA,EAA1BA,CAAAA;YAA6Bb,GAAG;QAAA;IAAA;IACnDiE,OAAOT;AAAAA,GAGI0C,IAAiB,SAACC,CAAAA;IAAAA,qKAC7BrK,UAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCEzByC,KAAQ,SAAA,CAAA;IAAA,IAAGtI,IAAAA,EAAAA,SAAAA,EAAWkD,IAAAA,EAAAA,IAAAA,EAAMwB,IAAAA,EAAAA,QAAAA,EAejC6D,IAAgB;QACpBC,iBAAAA,4BAJgBhF,EAAiBkD,OAAOC,MAAAA,CAAO,CAAA,GAAIzD,GAAM;YAAEhB,GAAG;QAAA,MAAA,OAChDsB,EAAiBkD,OAAOC,MAAAA,CAAO,CAAA,GAAIzD,GAAM;YAAEhB,GAAG;QAAA,MAAA;IAAA,GAMxDtB,IAAgBP,EAAgB;QAAC;QAAyBL;KAAAA,GAC1DyI,IAAY1H,EAAe,MAATmC,EAAKhB,CAAAA;IAE7B,qKACElE,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA;QAAKgC,WAAWY;IAAAA,iKACd5C,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA;QAAKgC,WAAU;QAAiCa,OAAO0H;IAAAA,kKACvDvK,UAAAA,CAAAA,aAAAA,CAACD,GAAAA;QACCG,QAzBa,SAACyG,CAAAA;YAClBD,EAAS;gBAAExC,GAAGyC,EAAYtH,IAAAA;YAAAA;QAAAA;QAyBtBc,OAtBY,SAACyG,CAAAA;YAEjBF,EAAS;gBAAExC,GAAGlG,EAAMkH,EAAKhB,CAAAA,GAAI0C,EAAOvH,IAAAA;YAAAA;QAAAA;QAqBhCwH,cAAW;QACXM,kBAAmBsD,IAAAA;QACnB3D,iBAAe2D;QACfzD,iBAAc;QACdD,iBAAc;IAAA,iKAEd/G,UAAAA,CAAAA,aAAAA,CAAC0C,GAAAA;QACCV,WAAU;QACV3C,MAAM6F,EAAKhB,CAAAA;QACXvB,OAAO6C,EAAiBN;IAAAA;AAAAA,GCvCrBwF,KAAmB,SAAA,CAAA;IAAA,IAC9B1I,IAAAA,EAAAA,SAAAA,EACA6F,IAAAA,EAAAA,UAAAA,EAAAA,IAAAA,EACAlF,KAAAA,EAAAA,IAAAA,KAAAA,MAAAA,IAAQkF,EAAWsC,YAAAA,GAAAA,GACnBzD,IAAAA,EAAAA,QAAAA,EACGtG,IAAAA,EAAAA,GAAAA;QAAAA;QAAAA;QAAAA;QAAAA;KAAAA,GAEGmJ,QAAU3L,uKAAAA,EAAuB;IACvC0L,EAAcC;IAAAA,IAAAA,IAEa3B,EAAwBC,GAAYlF,GAAO+D,IAA/DxB,IAAAA,CAAAA,CAAAA,EAAAA,EAAM+C,IAAAA,CAAAA,CAAAA,EAAAA,EAEPrF,IAAgBP,EAAgB;QAAC;QAAkBL;KAAAA;IAEzD,qKACEhC,UAAAA,CAAAA,aAAAA,CAAAA,OAAAA,EAAAA,CAAAA,GAASI,GAAAA;QAAM6B,KAAKsH;QAASvH,WAAWY;IAAAA,kKACtC5C,UAAAA,CAAAA,aAAAA,CAACiH,GAAAA;QAAW/B,MAAMA;QAAMwB,UAAUuB;IAAAA,kKAClCjI,UAAAA,CAAAA,aAAAA,CAACwG,GAAAA;QAAIC,KAAKvB,EAAKP,CAAAA;QAAG+B,UAAUuB;IAAAA,kKAC5BjI,UAAAA,CAAAA,aAAAA,CAACsK,IAAAA;QAAMpF,MAAMA;QAAMwB,UAAUuB;QAAYjG,WAAU;IAAA;AAAA,GC1BnD6F,KAAiC;IACrCsC,cAAc;IACdnC,QAAQvE;IACR6E,UAAUrD;IACVkD,OAAOT;AAAAA,GAGIiD,KAAsB,SAACN,CAAAA;IAAAA,qKAClCrK,UAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRrCA,KAAoC;IACxCsC,cAAc;QAAExF,GAAG;QAAGC,GAAG;QAAGC,GAAG;QAAGX,GAAG;IAAA;IACrC8D,QAAQtD;IACR4D,UAAUjD;IACV8C,OAAOf;AAAAA,GAGIwD,KAAkB,SAACP,CAAAA;IAAAA,OAC9BrK,wKAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRrCA,KAAiC;IACrCsC,cAAc;IACdnC,QAAQ1D;IACRgE,UAAU9C;IACV2C,OAAOX;AAAAA,GAGIqD,KAAwB,SACnCR,CAAAA;IAAAA,qKACgBrK,UAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCTrDA,KAAmC;IACvCsC,cAAc;QAAExF,GAAG;QAAGC,GAAG;QAAGC,GAAG;IAAA;IAC/BmD,QAAQ,SAAA,CAAA;QAAA,OAAiBtD,EAAW;YAAEC,GAAAA,EAA3BA,CAAAA;YAA8BC,GAAAA,EAA3BA,CAAAA;YAA8BC,GAAAA,EAA3BA,CAAAA;YAA8BX,GAAG;QAAA;IAAA;IAClDoE,UAAU,SAACpD,CAAAA;QAAAA,Of8LmD;YAAEP,GAAAA,CAAAA,Ie9LlCU,EAAWH,EAAAA,Ef8LfP,CAAAA;YAAyCC,GAAAA,EAAtCA,CAAAA;YAAyCC,GAAAA,EAAtCA,CAAAA;QAAAA;;QAAT,IAAA;IAAA;Ie7LvBsD,OAAOf;AAAAA,GAGI0D,KAAiB,SAACT,CAAAA;IAAAA,qKAC7BrK,UAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRhCA,KAAiC;IACrCsC,cAAc;IACdnC,QAAQhD;IACRsD,UAAU/C;IACV4C,OAAOX;AAAAA,GAGIuD,KAAuB,SAACV,CAAAA;IAAAA,oKACnCrK,WAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRhCA,KAAoC;IACxCsC,cAAc;QAAExF,GAAG;QAAGC,GAAG;QAAGG,GAAG;QAAGb,GAAG;IAAA;IACrC8D,QAAQ,SAAC9C,CAAAA;QAAAA,OAASA;IAAAA;IAClBoD,UAAUvC;IACVoC,OAAOf;AAAAA,GAGI4D,KAAkB,SAACX,CAAAA;IAAAA,qKAC9BrK,UAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRrCA,KAAiC;IACrCsC,cAAc;IACdnC,QAAQnC;IACRyC,UlB+E8B,SAACpD,CAAAA;QAAAA,IAAAA,IACRa,EAAUb;QACjC,OAAA,UAAA,EADQP,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA,QAAAA,EAAGG,CAAAA,GAAAA,QAAAA,EAAGb,CAAAA,GAAAA;IAAAA;IkB/EjBiE,OAAOX;AAAAA,GAGIyD,KAAwB,SACnCZ,CAAAA;IAAAA,OACgBrK,wKAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCTrDA,KAAmC;IACvCsC,cAAc;QAAExF,GAAG;QAAGC,GAAG;QAAGG,GAAG;IAAA;IAC/BiD,QAAQ,SAAA,CAAA;QAAA,OAAkB;YAAErD,GAAAA,EAAjBA,CAAAA;YAAoBC,GAAAA,EAAjBA,CAAAA;YAAoBG,GAAAA,EAAjBA,CAAAA;YAAoBb,GAAG;QAAA;IAAA;IACxCoE,UnBgMuB,SAACpD,CAAAA;QAAAA,IAAAA,IACJa,EAAUb;QAC9B,OAAO;YAAEP,GAAAA,EADDA,CAAAA;YACIC,GAAAA,EADDA,CAAAA;YACIG,GAAAA,EADDA,CAAAA;QAAAA;IAAAA;ImBhMdoD,OAAOf;AAAAA,GAGI8D,KAAiB,SAACb,CAAAA;IAAAA,qKAC7BrK,UAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRhCA,KAAiC;IACrCsC,cAAc;IACdnC,QAAQhC;IACRsC,UpB0E6B,SAACpD,CAAAA;QAAAA,IAAAA,IACVa,EAAUb;QAC9B,OAAA,SAAA,EADQP,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA,QAAAA,EAAGG,CAAAA,GAAAA;IAAAA;IoB1EdoD,OAAOX;AAAAA,GAGI2D,KAAuB,SAACd,CAAAA;IAAAA,qKACnCrK,UAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRhCA,KAAoC;IACxCsC,cAAc;QAAErG,GAAG;QAAGE,GAAG;QAAGC,GAAG;QAAGC,GAAG;IAAA;IACrC8D,QAAQrE;IACR2E,UAAUlD;IACV+C,OAAOf;AAAAA,GAGIgE,KAAkB,SAACf,CAAAA;IAAAA,qKAC9BrK,UAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRrCA,KAAiC;IACrCsC,cAAc;IACdnC,QAAQ/B;IACRqC,UtBiH8B,SAACpD,CAAAA;QAAAA,IAAAA,IACRE,EAAWF;QAClC,OAAA,UAAA,EADQpB,CAAAA,GAAAA,OAAAA,EAAGE,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA;IAAAA;IsBjHjBiE,OAAOX;AAAAA,GAGI6D,KAAwB,SACnChB,CAAAA;IAAAA,qKACgBrK,UAAAA,CAAAA,aAAAA,CAAC0K,IAAAA,EAAAA,CAAAA,GAAqBL,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCTrDA,KAAmC;IACvCsC,cAAc;QAAErG,GAAG;QAAGE,GAAG;QAAGC,GAAG;IAAA;IAC/B+D,QAAQ,SAAA,CAAA;QAAA,OAAiBrE,EAAW;YAAEG,GAAAA,EAA3BA,CAAAA;YAA8BE,GAAAA,EAA3BA,CAAAA;YAA8BC,GAAAA,EAA3BA,CAAAA;YAA8BC,GAAG;QAAA;IAAA;IAClDoE,UAAU,SAACpD,CAAAA;QAAAA,OvB4LmD;YAAEpB,GAAAA,CAAAA,IuB5LlCsB,EAAWF,EAAAA,EvB4LfpB,CAAAA;YAAyCE,GAAAA,EAAtCA,CAAAA;YAAyCC,GAAAA,EAAtCA,CAAAA;QAAAA;;QAAT,IAAA;IAAA;IuB3LvBkE,OAAOf;AAAAA,GAGIkE,KAAiB,SAACjB,CAAAA;IAAAA,qKAC7BrK,UAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCRhCA,KAAiC;IACrCsC,cAAc;IACdnC,QAAQ7B;IACRmC,UxB4G6B,SAACpD,CAAAA;QAAAA,IAAAA,IACVE,EAAWF;QAC/B,OAAA,SAAA,EADQpB,CAAAA,GAAAA,OAAAA,EAAGE,CAAAA,GAAAA,OAAAA,EAAGC,CAAAA,GAAAA;IAAAA;IwB5GdkE,OAAOX;AAAAA,GAGI+D,KAAuB,SAAClB,CAAAA;IAAAA,qKACnCrK,UAAAA,CAAAA,aAAAA,CAACkK,GAAAA,EAAAA,CAAAA,GAAgBG,GAAAA;QAAOxC,YAAYA;IAAAA;AAAAA,GCfhC2D,KAAU,wBCgBHC,KAAa,SAACpB,CAAAA;IAAAA,IAAAA,IAC4DA,EAA7E1H,KAAAA,EAAAA,IAAAA,KAAAA,MAAAA,IAAQ,KAAA,GAAI+D,IAAiE2D,EAAjE3D,QAAAA,EAAUgF,IAAuDrB,EAAvDqB,MAAAA,EAAQC,IAA+CtB,EAA/CsB,MAAAA,EAAQC,IAAuCvB,EAAvCuB,QAAAA,EAAUxF,IAA6BiE,EAA7BjE,MAAAA,EAAQyF,IAAqBxB,EAArBwB,OAAAA,EAAYzL,IAAAA,EAASiK,GAAAA;QAAAA;QAAAA;QAAAA;QAAAA;QAAAA;QAAAA;QAAAA;KAAAA,GAAAA,iLAC3DtC,EAAS;QAAA,OAAM4D,EAAOhJ;IAAAA,IAAzC7E,IAAAA,CAAAA,CAAAA,EAAAA,EAAOgO,IAAAA,CAAAA,CAAAA,EAAAA,EACRhE,IAAmBrK,EAAyBiJ,IAC5CqF,IAAiBtO,EAAqDiO,IAGtEnD,oLAAeC,EACnB,SAACwD,CAAAA;QACC,IAAMC,IAAaN,EAAOK,EAAEE,MAAAA,CAAOpO,KAAAA;QACnCgO,EAASG,IACLL,EAASK,MAAanE,EAAiB+D,IAAUA,EAAQI,KAAcA;IAAAA,GAE7E;QAACN;QAAQE;QAASD;QAAU9D;KAAAA,GAIxBqE,oLAAa3D,EACjB,SAACwD,CAAAA;QACMJ,EAASI,EAAEE,MAAAA,CAAOpO,KAAAA,KAAQgO,EAASH,EAAOhJ,KAC/CoJ,EAAeC;IAAAA,GAEjB;QAACrJ;QAAOgJ;QAAQC;QAAUG;KAAAA;IAQ5B,qLAJAlK,EAAU;QACRiK,EAASH,EAAOhJ;IAAAA,GACf;QAACA;QAAOgJ;KAAAA,iKAGT3L,UAAAA,CAAAA,aAAAA,CAAAA,SAAAA,EAAAA,CAAAA,GACMI,GAAAA;QACJtC,OAAOsI,IAASA,EAAOtI,KAASA;QAChCsO,YAAW;QACX1F,UAAU6B;QACVmD,QAAQS;IAAAA;AAAAA,GCtCRE,KAAS,SAACvO,CAAAA;IAAAA,OAAkB,MAAMA;AAAAA,GAE3BwO,KAAgB,SAACjC,CAAAA;IAAAA,IACpBkC,IAA6BlC,EAA7BkC,QAAAA,EAAUC,IAAmBnC,EAAnBmC,KAAAA,EAAUpM,IAAAA,EAASiK,GAAAA;QAAAA;QAAAA;KAAAA,GAG/BsB,oLAASnD,EACb,SAAC1K,CAAAA;QAAAA,OAAkBA,EAAM2J,OAAAA,CAAQ,kBAAkB,IAAI5D,SAAAA,CAAU,GAAG2I,IAAQ,IAAI;IAAA,GAChF;QAACA;KAAAA,GAIGZ,KAAWpD,+KAAAA,EAAY,SAAC1K,CAAAA;QAAAA,OFxBR,SAACA,CAAAA,EAAe0O,CAAAA;YACtC,IAAMhI,IAAQgH,GAAQ/G,IAAAA,CAAK3G,IACrBoB,IAASsF,IAAQA,CAAAA,CAAM,EAAA,CAAGtF,MAAAA,GAAS;YAEzC,OACa,MAAXA,KACW,MAAXA,KAAAA,CAAAA,CACGsN,KAAoB,MAAXtN,KAAAA,CAAAA,CACTsN,KAAoB,MAAXtN;QAAAA,CEgBkCuN,CAAS3O,GAAO0O;IAAAA,GAAQ;QAACA;KAAAA;IAEzE,qKACExM,UAAAA,CAAAA,aAAAA,CAACyL,IAAAA,EAAAA,CAAAA,GACKrL,GAAAA;QACJuL,QAAQA;QACRvF,QAAQmG,IAAWF,KAAAA,KAASvH;QAC5B+G,SAASQ;QACTT,UAAUA;IAAAA;AAAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33], "debugId": null}}]}