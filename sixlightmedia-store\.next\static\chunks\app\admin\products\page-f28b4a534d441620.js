(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[575],{3843:(e,a,t)=>{"use strict";t.d(a,{e9:()=>o,i3:()=>s});var r=t(9509);let s={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};r.env.IMAGEKIT_PRIVATE_KEY,r.env.NEXT_PUBLIC_GA_ID,r.env.NEXT_PUBLIC_GA_ID;let o=e=>{let a=s.BASE_URL.replace(/\/$/,""),t=e.startsWith("/")?e:"/".concat(e);return"".concat(a).concat(t)}},6589:(e,a,t)=>{Promise.resolve().then(t.bind(t,9589))},9589:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var r=t(5155),s=t(2115),o=t(5089),n=t(6766),i=t(6874),d=t.n(i),l=t(3843);function c(){let[e,a]=(0,s.useState)({name:"",image:"",description:"",customizable:!1,slug:"",categoryId:""}),[t,i]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[m,h]=(0,s.useState)(0),[p,g]=(0,s.useState)([]),[x,b]=(0,s.useState)(!0),[E,N]=(0,s.useState)("");function f(e){let{name:t,value:r,type:s}=e.target;a(a=>({...a,[t]:"checkbox"===s?e.target.checked:r}))}async function S(t){t.preventDefault(),i(""),u("");let r=localStorage.getItem("token");if(!r)return void u("Not authenticated");let s=await fetch((0,l.e9)(l.i3.ENDPOINTS.PRODUCTS),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify({...e,price:m,categoryId:Number(e.categoryId)})}),o=await s.json();s.ok?(i("Product created!"),a({name:"",image:"",description:"",customizable:!1,slug:"",categoryId:""}),h(0)):u(o.error||"Failed to create product")}return(0,s.useEffect)(()=>{b(!0),fetch((0,l.e9)(l.i3.ENDPOINTS.CATEGORIES)).then(e=>{if(!e.ok)throw Error("Failed to fetch categories");return e.json()}).then(e=>{g(e),N("")}).catch(()=>{g([]),N("Failed to load categories")}).finally(()=>b(!1))},[]),(0,r.jsxs)("div",{className:"max-w-xl mx-auto mt-16",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Add New Product"}),(0,r.jsx)(d(),{href:"/admin/categories",className:"bg-gray-200 text-gray-800 px-4 py-2 rounded-full font-semibold shadow hover:bg-gray-300 transition text-sm",children:"Manage Categories"})]}),(0,r.jsx)(o.IKContext,{publicKey:"public_kFR0vTVL7DIDI8YW9eF6/luGjB4=".replace(/"/g,""),urlEndpoint:"https://ik.imagekit.io/fwbvmq9re".replace(/"/g,""),authenticator:async()=>(await fetch("/api/imagekit-auth")).json(),children:(0,r.jsxs)("form",{onSubmit:S,className:"flex flex-col gap-4",children:[(0,r.jsx)("input",{name:"name",value:e.name,onChange:f,placeholder:"Name",className:"border rounded px-3 py-2",required:!0}),(0,r.jsx)("input",{name:"slug",value:e.slug,onChange:f,placeholder:"Slug (unique)",className:"border rounded px-3 py-2",required:!0}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block mb-1 font-semibold",children:"Product Image"}),(0,r.jsx)(o.IKUpload,{fileName:e.slug?"".concat(e.slug,".jpg"):"product.jpg",onSuccess:e=>a(a=>({...a,image:e.url})),onError:()=>u("Image upload failed"),className:"border rounded px-3 py-2 w-full"}),e.image&&(0,r.jsx)(n.default,{src:e.image,alt:"Preview",width:96,height:96,className:"mt-2 h-24 w-auto rounded shadow"})]}),(0,r.jsx)("textarea",{name:"description",value:e.description,onChange:f,placeholder:"Description",className:"border rounded px-3 py-2",required:!0}),(0,r.jsxs)("select",{name:"categoryId",value:e.categoryId||"",onChange:f,className:"border rounded px-3 py-2",required:!0,disabled:x||!!E,children:[(0,r.jsx)("option",{value:"",disabled:!0,children:x?"Loading categories...":"Select category"}),p.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]}),E&&(0,r.jsx)("div",{className:"text-red-600 text-sm",children:E}),(0,r.jsxs)("label",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{type:"checkbox",name:"customizable",checked:e.customizable,onChange:f}),"Customizable"]}),(0,r.jsxs)("label",{className:"font-semibold",children:["Price",(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)("span",{className:"px-2 py-2 bg-gray-100 border border-r-0 rounded-l",children:"K"}),(0,r.jsx)("input",{type:"number",min:"0",step:"0.01",className:"w-full border rounded-r px-3 py-2 focus:outline-none",value:m,onChange:e=>h(Number(e.target.value)),required:!0})]})]}),(0,r.jsx)("button",{type:"submit",className:"bg-[#1a237e] text-white font-semibold px-6 py-2 rounded-full shadow hover:bg-[#283593] transition",children:"Add Product"}),t&&(0,r.jsx)("div",{className:"text-green-600",children:t}),c&&(0,r.jsx)("div",{className:"text-red-600",children:c})]})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[766,874,89,441,684,358],()=>a(6589)),_N_E=e.O()}]);