{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/SmartProductCustomizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport dynamic from \"next/dynamic\";\n\ninterface CustomizationData {\n  canvasData?: string;\n  preview?: string;\n}\n\ninterface ProductCustomizerProps {\n  productImage: string;\n  onCustomizationChange: (customization: CustomizationData) => void;\n  initialCustomization?: CustomizationData | null;\n}\n\n// Dynamically import the fallback customizer\nconst ProductCustomizerFallback = dynamic(\n  () => import(\"@/components/ProductCustomizerFallback\"),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading Customizer...</p>\n          </div>\n        </div>\n      </div>\n    ),\n  }\n);\n\nconst SmartProductCustomizer: React.FC<ProductCustomizerProps> = (props) => {\n  const [fabricAvailable, setFabricAvailable] = useState<boolean | null>(null);\n  const [AdvancedCustomizer, setAdvancedCustomizer] = useState<React.ComponentType<ProductCustomizerProps> | null>(null);\n\n  useEffect(() => {\n    // Check if Fabric.js is available\n    const checkFabricAvailability = async () => {\n      try {\n        // Try to dynamically import fabric\n        await import(\"fabric\");\n        \n        // If successful, try to import the advanced customizer\n        const { default: ProductCustomizer } = await import(\"@/components/ProductCustomizer\");\n        setAdvancedCustomizer(() => ProductCustomizer);\n        setFabricAvailable(true);\n      } catch (error) {\n        // Fabric.js or advanced customizer not available\n        setFabricAvailable(false);\n      }\n    };\n\n    checkFabricAvailability();\n  }, []);\n\n  // Show loading state while checking\n  if (fabricAvailable === null) {\n    return (\n      <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Checking customizer capabilities...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Use advanced customizer if available, otherwise use fallback\n  if (fabricAvailable && AdvancedCustomizer) {\n    return <AdvancedCustomizer {...props} />;\n  }\n\n  return <ProductCustomizerFallback {...props} />;\n};\n\nexport default SmartProductCustomizer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;AAHA;;;AAgBA,6CAA6C;AAC7C,MAAM,4BAA4B,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACtC;;;;;;IAEE,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;KATnC;AAiBN,MAAM,yBAA2D,CAAC;;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IAEjH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,kCAAkC;YAClC,MAAM;4EAA0B;oBAC9B,IAAI;wBACF,mCAAmC;wBACnC;wBAEA,uDAAuD;wBACvD,MAAM,EAAE,SAAS,iBAAiB,EAAE,GAAG;wBACvC;wFAAsB,IAAM;;wBAC5B,mBAAmB;oBACrB,EAAE,OAAO,OAAO;wBACd,iDAAiD;wBACjD,mBAAmB;oBACrB;gBACF;;YAEA;QACF;2CAAG,EAAE;IAEL,oCAAoC;IACpC,IAAI,oBAAoB,MAAM;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,+DAA+D;IAC/D,IAAI,mBAAmB,oBAAoB;QACzC,qBAAO,6LAAC;YAAoB,GAAG,KAAK;;;;;;IACtC;IAEA,qBAAO,6LAAC;QAA2B,GAAG,KAAK;;;;;;AAC7C;GA5CM;MAAA;uCA8CS", "debugId": null}}]}