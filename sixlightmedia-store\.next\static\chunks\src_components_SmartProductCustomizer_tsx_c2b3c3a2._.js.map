{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/SmartProductCustomizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport dynamic from \"next/dynamic\";\n\ninterface CustomizationData {\n  canvasData?: string;\n  preview?: string;\n}\n\ninterface ProductCustomizerProps {\n  productImage: string;\n  onCustomizationChange: (customization: CustomizationData) => void;\n  initialCustomization?: CustomizationData | null;\n  productId?: string | number; // Add productId for localStorage persistence\n}\n\n// Dynamically import the fallback customizer\nconst ProductCustomizerFallback = dynamic(\n  () => import(\"@/components/ProductCustomizerFallback\"),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading Customizer...</p>\n          </div>\n        </div>\n      </div>\n    ),\n  }\n);\n\nconst SmartProductCustomizer: React.FC<ProductCustomizerProps> = (props) => {\n  const [fabricAvailable, setFabricAvailable] = useState<boolean | null>(null);\n  const [AdvancedCustomizer, setAdvancedCustomizer] =\n    useState<React.ComponentType<ProductCustomizerProps> | null>(null);\n\n  useEffect(() => {\n    // Check if Fabric.js is available\n    const checkFabricAvailability = async () => {\n      try {\n        console.log(\n          \"SmartProductCustomizer: Checking Fabric.js availability...\"\n        );\n\n        // Try to dynamically import fabric\n        const fabricModule = await import(\"fabric\");\n        console.log(\n          \"SmartProductCustomizer: Fabric.js imported successfully:\",\n          !!fabricModule.fabric\n        );\n\n        // Verify fabric is actually usable\n        if (!fabricModule.fabric) {\n          throw new Error(\"Fabric.js imported but fabric object is null\");\n        }\n\n        // If successful, try to import the advanced customizer\n        console.log(\"SmartProductCustomizer: Importing ProductCustomizer...\");\n        const { default: ProductCustomizer } = await import(\n          \"@/components/ProductCustomizer\"\n        );\n        console.log(\n          \"SmartProductCustomizer: ProductCustomizer imported successfully:\",\n          !!ProductCustomizer\n        );\n\n        if (!ProductCustomizer) {\n          throw new Error(\"ProductCustomizer imported but is null\");\n        }\n\n        setAdvancedCustomizer(() => ProductCustomizer);\n        setFabricAvailable(true);\n        console.log(\"SmartProductCustomizer: Advanced customizer ready!\");\n      } catch (error) {\n        // Fabric.js or advanced customizer not available\n        console.error(\n          \"SmartProductCustomizer: Error loading advanced customizer:\",\n          error\n        );\n        setFabricAvailable(false);\n      }\n    };\n\n    // Add timeout to prevent infinite loading\n    const timeout = setTimeout(() => {\n      console.error(\n        \"SmartProductCustomizer: Timeout - falling back to basic customizer\"\n      );\n      setFabricAvailable(false);\n    }, 3000); // Reduced to 3 seconds for faster fallback\n\n    checkFabricAvailability().finally(() => {\n      clearTimeout(timeout);\n    });\n  }, []);\n\n  // Show loading state while checking\n  if (fabricAvailable === null) {\n    return (\n      <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Checking customizer capabilities...</p>\n            <p className=\"text-sm text-gray-500 mt-2\">\n              Loading advanced design tools...\n            </p>\n            <p className=\"text-xs text-gray-400 mt-2\">\n              If this takes too long, we'll automatically switch to basic mode\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Use advanced customizer if available, otherwise use fallback\n  if (fabricAvailable && AdvancedCustomizer) {\n    return <AdvancedCustomizer {...props} />;\n  }\n\n  return <ProductCustomizerFallback {...props} />;\n};\n\nexport default SmartProductCustomizer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;AAHA;;;AAiBA,6CAA6C;AAC7C,MAAM,4BAA4B,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACtC;;;;;;IAEE,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;KATnC;AAiBN,MAAM,yBAA2D,CAAC;;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,kCAAkC;YAClC,MAAM;4EAA0B;oBAC9B,IAAI;wBACF,QAAQ,GAAG,CACT;wBAGF,mCAAmC;wBACnC,MAAM,eAAe;wBACrB,QAAQ,GAAG,CACT,4DACA,CAAC,CAAC,aAAa,MAAM;wBAGvB,mCAAmC;wBACnC,IAAI,CAAC,aAAa,MAAM,EAAE;4BACxB,MAAM,IAAI,MAAM;wBAClB;wBAEA,uDAAuD;wBACvD,QAAQ,GAAG,CAAC;wBACZ,MAAM,EAAE,SAAS,iBAAiB,EAAE,GAAG;wBAGvC,QAAQ,GAAG,CACT,oEACA,CAAC,CAAC;wBAGJ,IAAI,CAAC,mBAAmB;4BACtB,MAAM,IAAI,MAAM;wBAClB;wBAEA;wFAAsB,IAAM;;wBAC5B,mBAAmB;wBACnB,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,iDAAiD;wBACjD,QAAQ,KAAK,CACX,8DACA;wBAEF,mBAAmB;oBACrB;gBACF;;YAEA,0CAA0C;YAC1C,MAAM,UAAU;4DAAW;oBACzB,QAAQ,KAAK,CACX;oBAEF,mBAAmB;gBACrB;2DAAG,OAAO,2CAA2C;YAErD,0BAA0B,OAAO;oDAAC;oBAChC,aAAa;gBACf;;QACF;2CAAG,EAAE;IAEL,oCAAoC;IACpC,IAAI,oBAAoB,MAAM;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,+DAA+D;IAC/D,IAAI,mBAAmB,oBAAoB;QACzC,qBAAO,6LAAC;YAAoB,GAAG,KAAK;;;;;;IACtC;IAEA,qBAAO,6LAAC;QAA2B,GAAG,KAAK;;;;;;AAC7C;GA3FM;MAAA;uCA6FS", "debugId": null}}]}