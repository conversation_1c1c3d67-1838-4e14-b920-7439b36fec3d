[{"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\api\\auth.ts": "1", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\categories\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Bottle3D.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Tshirt3D.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Cart.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ConfirmationModal.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Header.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Hero.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\OrderForm.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\pages\\api\\imagekit-auth.ts": "22", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\lib\\config.ts": "23", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\AdminDashboardClient.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\AdminOrdersClient.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\forgot-password\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\reset-password\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\test-customization\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\test-customization\\TestCustomizationClient.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\UserDashboardClient.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\UserProfileClient.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\verify-email\\page.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\CustomizationWarningModal.tsx": "33", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\CustomizerExtensions.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ProductCustomizer.fixed.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ProductCustomizer.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ProductCustomizerFallback.tsx": "37", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\SmartProductCustomizer.tsx": "38", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\lib\\auth.ts": "39"}, {"size": 2170, "mtime": 1751291729402, "results": "40", "hashOfConfig": "41"}, {"size": 13262, "mtime": 1748676041880, "results": "42", "hashOfConfig": "41"}, {"size": 307, "mtime": 1751289182492, "results": "43", "hashOfConfig": "41"}, {"size": 897, "mtime": 1751289217955, "results": "44", "hashOfConfig": "41"}, {"size": 7105, "mtime": 1748607358891, "results": "45", "hashOfConfig": "41"}, {"size": 7677, "mtime": 1748607466929, "results": "46", "hashOfConfig": "41"}, {"size": 4026, "mtime": 1748607976778, "results": "47", "hashOfConfig": "41"}, {"size": 6226, "mtime": 1748590995584, "results": "48", "hashOfConfig": "41"}, {"size": 8039, "mtime": 1751292026477, "results": "49", "hashOfConfig": "41"}, {"size": 34045, "mtime": 1748607316732, "results": "50", "hashOfConfig": "41"}, {"size": 3573, "mtime": 1748590559740, "results": "51", "hashOfConfig": "41"}, {"size": 28202, "mtime": 1751284031346, "results": "52", "hashOfConfig": "41"}, {"size": 2430, "mtime": 1748369774207, "results": "53", "hashOfConfig": "41"}, {"size": 16945, "mtime": 1751291974324, "results": "54", "hashOfConfig": "41"}, {"size": 912, "mtime": 1751289089541, "results": "55", "hashOfConfig": "41"}, {"size": 414, "mtime": 1751289122573, "results": "56", "hashOfConfig": "41"}, {"size": 9986, "mtime": 1748644653848, "results": "57", "hashOfConfig": "41"}, {"size": 3782, "mtime": 1748559645908, "results": "58", "hashOfConfig": "41"}, {"size": 21167, "mtime": 1748607657622, "results": "59", "hashOfConfig": "41"}, {"size": 843, "mtime": 1748438908141, "results": "60", "hashOfConfig": "41"}, {"size": 11235, "mtime": 1748644706368, "results": "61", "hashOfConfig": "41"}, {"size": 639, "mtime": 1748512727239, "results": "62", "hashOfConfig": "41"}, {"size": 4164, "mtime": 1751291704948, "results": "63", "hashOfConfig": "41"}, {"size": 10520, "mtime": 1751287067793, "results": "64", "hashOfConfig": "41"}, {"size": 14588, "mtime": 1751287353870, "results": "65", "hashOfConfig": "41"}, {"size": 8064, "mtime": 1751291814308, "results": "66", "hashOfConfig": "41"}, {"size": 12101, "mtime": 1751311173890, "results": "67", "hashOfConfig": "41"}, {"size": 405, "mtime": 1751289154007, "results": "68", "hashOfConfig": "41"}, {"size": 8996, "mtime": 1751311281069, "results": "69", "hashOfConfig": "41"}, {"size": 11385, "mtime": 1751289565263, "results": "70", "hashOfConfig": "41"}, {"size": 16481, "mtime": 1751289738509, "results": "71", "hashOfConfig": "41"}, {"size": 7278, "mtime": 1751311211635, "results": "72", "hashOfConfig": "41"}, {"size": 5426, "mtime": 1748679874979, "results": "73", "hashOfConfig": "41"}, {"size": 9050, "mtime": 1751311485517, "results": "74", "hashOfConfig": "41"}, {"size": 2858, "mtime": 1748700060147, "results": "75", "hashOfConfig": "41"}, {"size": 54001, "mtime": 1751283440196, "results": "76", "hashOfConfig": "41"}, {"size": 7549, "mtime": 1751284124623, "results": "77", "hashOfConfig": "41"}, {"size": 4322, "mtime": 1751283365670, "results": "78", "hashOfConfig": "41"}, {"size": 3394, "mtime": 1751289408452, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g4uj7b", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 47, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\api\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\categories\\page.tsx", ["197"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx", ["198"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Bottle3D.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx", ["199", "200", "201"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Tshirt3D.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx", ["202", "203"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx", ["204"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Cart.tsx", ["205"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ConfirmationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\OrderForm.tsx", ["206"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\pages\\api\\imagekit-auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\AdminDashboardClient.tsx", ["207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\AdminOrdersClient.tsx", ["219"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\forgot-password\\page.tsx", ["220", "221", "222", "223"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\reset-password\\page.tsx", ["224"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\test-customization\\page.tsx", ["225"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\test-customization\\TestCustomizationClient.tsx", ["226"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\UserDashboardClient.tsx", ["227"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\UserProfileClient.tsx", ["228"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\verify-email\\page.tsx", ["229"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\CustomizationWarningModal.tsx", ["230", "231", "232", "233"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\CustomizerExtensions.tsx", ["234", "235", "236", "237", "238"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ProductCustomizer.fixed.tsx", ["239", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ProductCustomizer.tsx", ["286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ProductCustomizerFallback.tsx", ["306", "307", "308", "309", "310", "311", "312", "313"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\SmartProductCustomizer.tsx", ["314"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\lib\\auth.ts", [], [], {"ruleId": "315", "severity": 2, "message": "316", "line": 60, "column": 16, "nodeType": null, "messageId": "317", "endLine": 60, "endColumn": 17}, {"ruleId": "315", "severity": 2, "message": "318", "line": 4, "column": 6, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 11}, {"ruleId": "315", "severity": 2, "message": "319", "line": 14, "column": 7, "nodeType": null, "messageId": "317", "endLine": 14, "endColumn": 15}, {"ruleId": "315", "severity": 2, "message": "320", "line": 15, "column": 7, "nodeType": null, "messageId": "317", "endLine": 15, "endColumn": 15}, {"ruleId": "321", "severity": 1, "message": "322", "line": 359, "column": 21, "nodeType": "323", "endLine": 363, "endColumn": 23}, {"ruleId": "315", "severity": 2, "message": "324", "line": 4, "column": 6, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 10}, {"ruleId": "315", "severity": 2, "message": "318", "line": 10, "column": 6, "nodeType": null, "messageId": "317", "endLine": 10, "endColumn": 11}, {"ruleId": "315", "severity": 2, "message": "324", "line": 4, "column": 6, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 10}, {"ruleId": "321", "severity": 1, "message": "322", "line": 160, "column": 27, "nodeType": "323", "endLine": 164, "endColumn": 29}, {"ruleId": "321", "severity": 1, "message": "322", "line": 168, "column": 23, "nodeType": "323", "endLine": 172, "endColumn": 25}, {"ruleId": "315", "severity": 2, "message": "325", "line": 5, "column": 8, "nodeType": null, "messageId": "317", "endLine": 5, "endColumn": 13}, {"ruleId": "315", "severity": 2, "message": "326", "line": 6, "column": 10, "nodeType": null, "messageId": "317", "endLine": 6, "endColumn": 18}, {"ruleId": "315", "severity": 2, "message": "327", "line": 74, "column": 23, "nodeType": null, "messageId": "317", "endLine": 74, "endColumn": 37}, {"ruleId": "315", "severity": 2, "message": "328", "line": 75, "column": 10, "nodeType": null, "messageId": "317", "endLine": 75, "endColumn": 13}, {"ruleId": "315", "severity": 2, "message": "329", "line": 75, "column": 15, "nodeType": null, "messageId": "317", "endLine": 75, "endColumn": 21}, {"ruleId": "315", "severity": 2, "message": "330", "line": 97, "column": 9, "nodeType": null, "messageId": "317", "endLine": 97, "endColumn": 32}, {"ruleId": "315", "severity": 2, "message": "331", "line": 135, "column": 12, "nodeType": null, "messageId": "317", "endLine": 135, "endColumn": 29}, {"ruleId": "315", "severity": 2, "message": "332", "line": 139, "column": 18, "nodeType": null, "messageId": "317", "endLine": 139, "endColumn": 37}, {"ruleId": "315", "severity": 2, "message": "333", "line": 199, "column": 9, "nodeType": null, "messageId": "317", "endLine": 199, "endColumn": 19}, {"ruleId": "315", "severity": 2, "message": "334", "line": 200, "column": 9, "nodeType": null, "messageId": "317", "endLine": 200, "endColumn": 23}, {"ruleId": "315", "severity": 2, "message": "335", "line": 212, "column": 9, "nodeType": null, "messageId": "317", "endLine": 212, "endColumn": 21}, {"ruleId": "315", "severity": 2, "message": "336", "line": 223, "column": 9, "nodeType": null, "messageId": "317", "endLine": 223, "endColumn": 24}, {"ruleId": "321", "severity": 1, "message": "322", "line": 270, "column": 39, "nodeType": "323", "endLine": 274, "endColumn": 41}, {"ruleId": "315", "severity": 2, "message": "337", "line": 58, "column": 14, "nodeType": null, "messageId": "317", "endLine": 58, "endColumn": 19}, {"ruleId": "338", "severity": 2, "message": "339", "line": 84, "column": 57, "nodeType": "340", "messageId": "341", "suggestions": "342"}, {"ruleId": "338", "severity": 2, "message": "339", "line": 131, "column": 44, "nodeType": "340", "messageId": "341", "suggestions": "343"}, {"ruleId": "338", "severity": 2, "message": "339", "line": 211, "column": 16, "nodeType": "340", "messageId": "341", "suggestions": "344"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 96, "column": 21, "nodeType": "347", "messageId": "348", "endLine": 96, "endColumn": 24, "suggestions": "349"}, {"ruleId": "315", "severity": 2, "message": "350", "line": 4, "column": 11, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 28}, {"ruleId": "321", "severity": 1, "message": "322", "line": 180, "column": 19, "nodeType": "323", "endLine": 184, "endColumn": 21}, {"ruleId": "315", "severity": 2, "message": "351", "line": 51, "column": 10, "nodeType": null, "messageId": "317", "endLine": 51, "endColumn": 18}, {"ruleId": "315", "severity": 2, "message": "352", "line": 111, "column": 13, "nodeType": null, "messageId": "317", "endLine": 111, "endColumn": 18}, {"ruleId": "315", "severity": 2, "message": "337", "line": 80, "column": 14, "nodeType": null, "messageId": "317", "endLine": 80, "endColumn": 19}, {"ruleId": "338", "severity": 2, "message": "339", "line": 94, "column": 19, "nodeType": "340", "messageId": "341", "suggestions": "353"}, {"ruleId": "338", "severity": 2, "message": "339", "line": 94, "column": 31, "nodeType": "340", "messageId": "341", "suggestions": "354"}, {"ruleId": "338", "severity": 2, "message": "355", "line": 115, "column": 30, "nodeType": "340", "messageId": "341", "suggestions": "356"}, {"ruleId": "338", "severity": 2, "message": "355", "line": 115, "column": 42, "nodeType": "340", "messageId": "341", "suggestions": "357"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 69, "column": 31, "nodeType": "347", "messageId": "348", "endLine": 69, "endColumn": 34, "suggestions": "358"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 147, "column": 12, "nodeType": "347", "messageId": "348", "endLine": 147, "endColumn": 15, "suggestions": "359"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 289, "column": 22, "nodeType": "347", "messageId": "348", "endLine": 289, "endColumn": 25, "suggestions": "360"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 296, "column": 22, "nodeType": "347", "messageId": "348", "endLine": 296, "endColumn": 25, "suggestions": "361"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 297, "column": 20, "nodeType": "347", "messageId": "348", "endLine": 297, "endColumn": 23, "suggestions": "362"}, {"ruleId": "315", "severity": 2, "message": "363", "line": 3, "column": 17, "nodeType": null, "messageId": "317", "endLine": 3, "endColumn": 26}, {"ruleId": "315", "severity": 2, "message": "364", "line": 4, "column": 8, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 15}, {"ruleId": "315", "severity": 2, "message": "365", "line": 6, "column": 3, "nodeType": null, "messageId": "317", "endLine": 6, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "366", "line": 7, "column": 12, "nodeType": null, "messageId": "317", "endLine": 7, "endColumn": 21}, {"ruleId": "315", "severity": 2, "message": "367", "line": 8, "column": 3, "nodeType": null, "messageId": "317", "endLine": 8, "endColumn": 10}, {"ruleId": "315", "severity": 2, "message": "368", "line": 9, "column": 3, "nodeType": null, "messageId": "317", "endLine": 9, "endColumn": 11}, {"ruleId": "315", "severity": 2, "message": "369", "line": 10, "column": 3, "nodeType": null, "messageId": "317", "endLine": 10, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "370", "line": 11, "column": 3, "nodeType": null, "messageId": "317", "endLine": 11, "endColumn": 9}, {"ruleId": "315", "severity": 2, "message": "371", "line": 12, "column": 3, "nodeType": null, "messageId": "317", "endLine": 12, "endColumn": 11}, {"ruleId": "315", "severity": 2, "message": "372", "line": 13, "column": 3, "nodeType": null, "messageId": "317", "endLine": 13, "endColumn": 9}, {"ruleId": "315", "severity": 2, "message": "373", "line": 14, "column": 3, "nodeType": null, "messageId": "317", "endLine": 14, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "374", "line": 15, "column": 3, "nodeType": null, "messageId": "317", "endLine": 15, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "375", "line": 16, "column": 3, "nodeType": null, "messageId": "317", "endLine": 16, "endColumn": 7}, {"ruleId": "345", "severity": 2, "message": "346", "line": 20, "column": 13, "nodeType": "347", "messageId": "348", "endLine": 20, "endColumn": 16, "suggestions": "376"}, {"ruleId": "315", "severity": 2, "message": "377", "line": 22, "column": 3, "nodeType": null, "messageId": "317", "endLine": 22, "endColumn": 9}, {"ruleId": "378", "severity": 2, "message": "379", "line": 22, "column": 12, "nodeType": "380", "messageId": "381", "endLine": 22, "endColumn": 29}, {"ruleId": "315", "severity": 2, "message": "382", "line": 38, "column": 3, "nodeType": null, "messageId": "317", "endLine": 38, "endColumn": 15}, {"ruleId": "315", "severity": 2, "message": "383", "line": 39, "column": 3, "nodeType": null, "messageId": "317", "endLine": 39, "endColumn": 24}, {"ruleId": "315", "severity": 2, "message": "384", "line": 40, "column": 3, "nodeType": null, "messageId": "317", "endLine": 40, "endColumn": 23}, {"ruleId": "315", "severity": 2, "message": "385", "line": 41, "column": 3, "nodeType": null, "messageId": "317", "endLine": 41, "endColumn": 12}, {"ruleId": "315", "severity": 2, "message": "386", "line": 43, "column": 9, "nodeType": null, "messageId": "317", "endLine": 43, "endColumn": 18}, {"ruleId": "315", "severity": 2, "message": "387", "line": 44, "column": 10, "nodeType": null, "messageId": "317", "endLine": 44, "endColumn": 16}, {"ruleId": "315", "severity": 2, "message": "388", "line": 44, "column": 18, "nodeType": null, "messageId": "317", "endLine": 44, "endColumn": 27}, {"ruleId": "345", "severity": 2, "message": "346", "line": 44, "column": 40, "nodeType": "347", "messageId": "348", "endLine": 44, "endColumn": 43, "suggestions": "389"}, {"ruleId": "315", "severity": 2, "message": "390", "line": 45, "column": 10, "nodeType": null, "messageId": "317", "endLine": 45, "endColumn": 22}, {"ruleId": "315", "severity": 2, "message": "391", "line": 45, "column": 24, "nodeType": null, "messageId": "317", "endLine": 45, "endColumn": 39}, {"ruleId": "315", "severity": 2, "message": "392", "line": 46, "column": 10, "nodeType": null, "messageId": "317", "endLine": 46, "endColumn": 19}, {"ruleId": "315", "severity": 2, "message": "393", "line": 46, "column": 21, "nodeType": null, "messageId": "317", "endLine": 46, "endColumn": 33}, {"ruleId": "315", "severity": 2, "message": "394", "line": 47, "column": 10, "nodeType": null, "messageId": "317", "endLine": 47, "endColumn": 18}, {"ruleId": "315", "severity": 2, "message": "395", "line": 47, "column": 20, "nodeType": null, "messageId": "317", "endLine": 47, "endColumn": 31}, {"ruleId": "315", "severity": 2, "message": "396", "line": 48, "column": 10, "nodeType": null, "messageId": "317", "endLine": 48, "endColumn": 20}, {"ruleId": "315", "severity": 2, "message": "397", "line": 48, "column": 22, "nodeType": null, "messageId": "317", "endLine": 48, "endColumn": 35}, {"ruleId": "315", "severity": 2, "message": "398", "line": 49, "column": 10, "nodeType": null, "messageId": "317", "endLine": 49, "endColumn": 17}, {"ruleId": "315", "severity": 2, "message": "399", "line": 49, "column": 19, "nodeType": null, "messageId": "317", "endLine": 49, "endColumn": 29}, {"ruleId": "315", "severity": 2, "message": "400", "line": 50, "column": 10, "nodeType": null, "messageId": "317", "endLine": 50, "endColumn": 22}, {"ruleId": "315", "severity": 2, "message": "401", "line": 50, "column": 24, "nodeType": null, "messageId": "317", "endLine": 50, "endColumn": 39}, {"ruleId": "315", "severity": 2, "message": "402", "line": 51, "column": 24, "nodeType": null, "messageId": "317", "endLine": 51, "endColumn": 39}, {"ruleId": "315", "severity": 2, "message": "403", "line": 52, "column": 29, "nodeType": null, "messageId": "317", "endLine": 52, "endColumn": 49}, {"ruleId": "315", "severity": 2, "message": "404", "line": 55, "column": 10, "nodeType": null, "messageId": "317", "endLine": 55, "endColumn": 20}, {"ruleId": "315", "severity": 2, "message": "405", "line": 55, "column": 22, "nodeType": null, "messageId": "317", "endLine": 55, "endColumn": 35}, {"ruleId": "315", "severity": 2, "message": "406", "line": 56, "column": 10, "nodeType": null, "messageId": "317", "endLine": 56, "endColumn": 23}, {"ruleId": "315", "severity": 2, "message": "407", "line": 56, "column": 25, "nodeType": null, "messageId": "317", "endLine": 56, "endColumn": 41}, {"ruleId": "315", "severity": 2, "message": "408", "line": 57, "column": 10, "nodeType": null, "messageId": "317", "endLine": 57, "endColumn": 19}, {"ruleId": "315", "severity": 2, "message": "409", "line": 57, "column": 21, "nodeType": null, "messageId": "317", "endLine": 57, "endColumn": 33}, {"ruleId": "315", "severity": 2, "message": "410", "line": 58, "column": 10, "nodeType": null, "messageId": "317", "endLine": 58, "endColumn": 17}, {"ruleId": "315", "severity": 2, "message": "411", "line": 58, "column": 19, "nodeType": null, "messageId": "317", "endLine": 58, "endColumn": 29}, {"ruleId": "345", "severity": 2, "message": "346", "line": 58, "column": 42, "nodeType": "347", "messageId": "348", "endLine": 58, "endColumn": 45, "suggestions": "412"}, {"ruleId": "315", "severity": 2, "message": "364", "line": 5, "column": 8, "nodeType": null, "messageId": "317", "endLine": 5, "endColumn": 15}, {"ruleId": "315", "severity": 2, "message": "366", "line": 8, "column": 12, "nodeType": null, "messageId": "317", "endLine": 8, "endColumn": 21}, {"ruleId": "315", "severity": 2, "message": "367", "line": 9, "column": 3, "nodeType": null, "messageId": "317", "endLine": 9, "endColumn": 10}, {"ruleId": "345", "severity": 2, "message": "346", "line": 24, "column": 13, "nodeType": "347", "messageId": "348", "endLine": 24, "endColumn": 16, "suggestions": "413"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 27, "column": 39, "nodeType": "347", "messageId": "348", "endLine": 27, "endColumn": 42, "suggestions": "414"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 27, "column": 49, "nodeType": "347", "messageId": "348", "endLine": 27, "endColumn": 52, "suggestions": "415"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 65, "column": 17, "nodeType": "347", "messageId": "348", "endLine": 65, "endColumn": 20, "suggestions": "416"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 79, "column": 28, "nodeType": "347", "messageId": "348", "endLine": 79, "endColumn": 31, "suggestions": "417"}, {"ruleId": "315", "severity": 2, "message": "385", "line": 89, "column": 3, "nodeType": null, "messageId": "317", "endLine": 89, "endColumn": 12}, {"ruleId": "345", "severity": 2, "message": "346", "line": 115, "column": 40, "nodeType": "347", "messageId": "348", "endLine": 115, "endColumn": 43, "suggestions": "418"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 119, "column": 52, "nodeType": "347", "messageId": "348", "endLine": 119, "endColumn": 55, "suggestions": "419"}, {"ruleId": "315", "severity": 2, "message": "420", "line": 126, "column": 10, "nodeType": null, "messageId": "317", "endLine": 126, "endColumn": 25}, {"ruleId": "315", "severity": 2, "message": "421", "line": 126, "column": 27, "nodeType": null, "messageId": "317", "endLine": 126, "endColumn": 45}, {"ruleId": "345", "severity": 2, "message": "346", "line": 244, "column": 19, "nodeType": "347", "messageId": "348", "endLine": 244, "endColumn": 22, "suggestions": "422"}, {"ruleId": "423", "severity": 1, "message": "424", "line": 300, "column": 6, "nodeType": "425", "endLine": 300, "endColumn": 8, "suggestions": "426"}, {"ruleId": "315", "severity": 2, "message": "427", "line": 303, "column": 9, "nodeType": null, "messageId": "317", "endLine": 303, "endColumn": 22}, {"ruleId": "345", "severity": 2, "message": "346", "line": 367, "column": 40, "nodeType": "347", "messageId": "348", "endLine": 367, "endColumn": 43, "suggestions": "428"}, {"ruleId": "345", "severity": 2, "message": "346", "line": 436, "column": 13, "nodeType": "347", "messageId": "348", "endLine": 436, "endColumn": 16, "suggestions": "429"}, {"ruleId": "315", "severity": 2, "message": "430", "line": 682, "column": 9, "nodeType": null, "messageId": "317", "endLine": 682, "endColumn": 28}, {"ruleId": "321", "severity": 1, "message": "322", "line": 1312, "column": 25, "nodeType": "323", "endLine": 1316, "endColumn": 27}, {"ruleId": "315", "severity": 2, "message": "371", "line": 4, "column": 25, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 33}, {"ruleId": "315", "severity": 2, "message": "431", "line": 4, "column": 35, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 46}, {"ruleId": "315", "severity": 2, "message": "384", "line": 20, "column": 3, "nodeType": null, "messageId": "317", "endLine": 20, "endColumn": 23}, {"ruleId": "423", "severity": 1, "message": "432", "line": 49, "column": 6, "nodeType": "425", "endLine": 49, "endColumn": 45, "suggestions": "433"}, {"ruleId": "423", "severity": 1, "message": "432", "line": 53, "column": 6, "nodeType": "425", "endLine": 53, "endColumn": 45, "suggestions": "434"}, {"ruleId": "338", "severity": 2, "message": "355", "line": 92, "column": 24, "nodeType": "340", "messageId": "341", "suggestions": "435"}, {"ruleId": "338", "severity": 2, "message": "355", "line": 92, "column": 36, "nodeType": "340", "messageId": "341", "suggestions": "436"}, {"ruleId": "321", "severity": 1, "message": "322", "line": 101, "column": 11, "nodeType": "323", "endLine": 108, "endColumn": 13}, {"ruleId": "338", "severity": 2, "message": "339", "line": 113, "column": 41, "nodeType": "340", "messageId": "341", "suggestions": "437"}, "@typescript-eslint/no-unused-vars", "'e' is defined but never used.", "unusedVar", "'Order' is defined but never used.", "'Bottle3D' is assigned a value but never used.", "'Tshirt3D' is assigned a value but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'User' is defined but never used.", "'Image' is defined but never used.", "'Doughnut' is defined but never used.", "'setCurrentPage' is assigned a value but never used.", "'tab' is assigned a value but never used.", "'setTab' is assigned a value but never used.", "'handleConfirmCollection' is assigned a value but never used.", "'handleEditProduct' is defined but never used.", "'handleDeleteProduct' is defined but never used.", "'totalPages' is assigned a value but never used.", "'paginatedItems' is assigned a value but never used.", "'doughnutData' is assigned a value but never used.", "'doughnutOptions' is assigned a value but never used.", "'error' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["438", "439", "440", "441"], ["442", "443", "444", "445"], ["446", "447", "448", "449"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["450", "451"], "'CustomizationData' is defined but never used.", "'userData' is assigned a value but never used.", "'token' is assigned a value but never used.", ["452", "453", "454", "455"], ["456", "457", "458", "459"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["460", "461", "462", "463"], ["464", "465", "466", "467"], ["468", "469"], ["470", "471"], ["472", "473"], ["474", "475"], ["476", "477"], "'useEffect' is defined but never used.", "'dynamic' is defined but never used.", "'Type' is defined but never used.", "'ImageIcon' is defined but never used.", "'Palette' is defined but never used.", "'RotateCw' is defined but never used.", "'Move' is defined but never used.", "'Trash2' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "'Undo' is defined but never used.", "'Redo' is defined but never used.", "'Save' is defined but never used.", ["478", "479"], "'fabric' is assigned a value but never used.", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", "'productImage' is defined but never used.", "'onCustomizationChange' is defined but never used.", "'initialCustomization' is defined but never used.", "'productId' is defined but never used.", "'canvasRef' is assigned a value but never used.", "'canvas' is assigned a value but never used.", "'setCanvas' is assigned a value but never used.", ["480", "481"], "'selectedTool' is assigned a value but never used.", "'setSelectedTool' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'fontSize' is assigned a value but never used.", "'setFontSize' is assigned a value but never used.", "'fontFamily' is assigned a value but never used.", "'setFontFamily' is assigned a value but never used.", "'history' is assigned a value but never used.", "'setHistory' is assigned a value but never used.", "'historyIndex' is assigned a value but never used.", "'setHistoryIndex' is assigned a value but never used.", "'setFabricLoaded' is assigned a value but never used.", "'setHasUnsavedChanges' is assigned a value but never used.", "'showLayers' is assigned a value but never used.", "'setShowLayers' is assigned a value but never used.", "'showTemplates' is assigned a value but never used.", "'setShowTemplates' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "'objects' is assigned a value but never used.", "'setObjects' is assigned a value but never used.", ["482", "483"], ["484", "485"], ["486", "487"], ["488", "489"], ["490", "491"], ["492", "493"], ["494", "495"], ["496", "497"], "'showSaveSuccess' is assigned a value but never used.", "'setShowSaveSuccess' is assigned a value but never used.", ["498", "499"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'canvas', 'handleCanvasChange', 'normalizedInitialData.canvasData', 'normalizedProductImage', 'onCanvasReady', and 'saveToHistory'. Either include them or remove the dependency array. If 'onCanvasReady' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["500"], "'isCanvasEmpty' is assigned a value but never used.", ["501", "502"], ["503", "504"], "'setActiveObjectById' is assigned a value but never used.", "'AlertCircle' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'handleChange'. Either include it or remove the dependency array.", ["505"], ["506"], ["507", "508", "509", "510"], ["511", "512", "513", "514"], ["515", "516", "517", "518"], {"messageId": "519", "data": "520", "fix": "521", "desc": "522"}, {"messageId": "519", "data": "523", "fix": "524", "desc": "525"}, {"messageId": "519", "data": "526", "fix": "527", "desc": "528"}, {"messageId": "519", "data": "529", "fix": "530", "desc": "531"}, {"messageId": "519", "data": "532", "fix": "533", "desc": "522"}, {"messageId": "519", "data": "534", "fix": "535", "desc": "525"}, {"messageId": "519", "data": "536", "fix": "537", "desc": "528"}, {"messageId": "519", "data": "538", "fix": "539", "desc": "531"}, {"messageId": "519", "data": "540", "fix": "541", "desc": "522"}, {"messageId": "519", "data": "542", "fix": "543", "desc": "525"}, {"messageId": "519", "data": "544", "fix": "545", "desc": "528"}, {"messageId": "519", "data": "546", "fix": "547", "desc": "531"}, {"messageId": "548", "fix": "549", "desc": "550"}, {"messageId": "551", "fix": "552", "desc": "553"}, {"messageId": "519", "data": "554", "fix": "555", "desc": "522"}, {"messageId": "519", "data": "556", "fix": "557", "desc": "525"}, {"messageId": "519", "data": "558", "fix": "559", "desc": "528"}, {"messageId": "519", "data": "560", "fix": "561", "desc": "531"}, {"messageId": "519", "data": "562", "fix": "563", "desc": "522"}, {"messageId": "519", "data": "564", "fix": "565", "desc": "525"}, {"messageId": "519", "data": "566", "fix": "567", "desc": "528"}, {"messageId": "519", "data": "568", "fix": "569", "desc": "531"}, {"messageId": "519", "data": "570", "fix": "571", "desc": "572"}, {"messageId": "519", "data": "573", "fix": "574", "desc": "575"}, {"messageId": "519", "data": "576", "fix": "577", "desc": "578"}, {"messageId": "519", "data": "579", "fix": "580", "desc": "581"}, {"messageId": "519", "data": "582", "fix": "583", "desc": "572"}, {"messageId": "519", "data": "584", "fix": "585", "desc": "575"}, {"messageId": "519", "data": "586", "fix": "587", "desc": "578"}, {"messageId": "519", "data": "588", "fix": "589", "desc": "581"}, {"messageId": "548", "fix": "590", "desc": "550"}, {"messageId": "551", "fix": "591", "desc": "553"}, {"messageId": "548", "fix": "592", "desc": "550"}, {"messageId": "551", "fix": "593", "desc": "553"}, {"messageId": "548", "fix": "594", "desc": "550"}, {"messageId": "551", "fix": "595", "desc": "553"}, {"messageId": "548", "fix": "596", "desc": "550"}, {"messageId": "551", "fix": "597", "desc": "553"}, {"messageId": "548", "fix": "598", "desc": "550"}, {"messageId": "551", "fix": "599", "desc": "553"}, {"messageId": "548", "fix": "600", "desc": "550"}, {"messageId": "551", "fix": "601", "desc": "553"}, {"messageId": "548", "fix": "602", "desc": "550"}, {"messageId": "551", "fix": "603", "desc": "553"}, {"messageId": "548", "fix": "604", "desc": "550"}, {"messageId": "551", "fix": "605", "desc": "553"}, {"messageId": "548", "fix": "606", "desc": "550"}, {"messageId": "551", "fix": "607", "desc": "553"}, {"messageId": "548", "fix": "608", "desc": "550"}, {"messageId": "551", "fix": "609", "desc": "553"}, {"messageId": "548", "fix": "610", "desc": "550"}, {"messageId": "551", "fix": "611", "desc": "553"}, {"messageId": "548", "fix": "612", "desc": "550"}, {"messageId": "551", "fix": "613", "desc": "553"}, {"messageId": "548", "fix": "614", "desc": "550"}, {"messageId": "551", "fix": "615", "desc": "553"}, {"messageId": "548", "fix": "616", "desc": "550"}, {"messageId": "551", "fix": "617", "desc": "553"}, {"messageId": "548", "fix": "618", "desc": "550"}, {"messageId": "551", "fix": "619", "desc": "553"}, {"messageId": "548", "fix": "620", "desc": "550"}, {"messageId": "551", "fix": "621", "desc": "553"}, {"desc": "622", "fix": "623"}, {"messageId": "548", "fix": "624", "desc": "550"}, {"messageId": "551", "fix": "625", "desc": "553"}, {"messageId": "548", "fix": "626", "desc": "550"}, {"messageId": "551", "fix": "627", "desc": "553"}, {"desc": "628", "fix": "629"}, {"desc": "628", "fix": "630"}, {"messageId": "519", "data": "631", "fix": "632", "desc": "572"}, {"messageId": "519", "data": "633", "fix": "634", "desc": "575"}, {"messageId": "519", "data": "635", "fix": "636", "desc": "578"}, {"messageId": "519", "data": "637", "fix": "638", "desc": "581"}, {"messageId": "519", "data": "639", "fix": "640", "desc": "572"}, {"messageId": "519", "data": "641", "fix": "642", "desc": "575"}, {"messageId": "519", "data": "643", "fix": "644", "desc": "578"}, {"messageId": "519", "data": "645", "fix": "646", "desc": "581"}, {"messageId": "519", "data": "647", "fix": "648", "desc": "522"}, {"messageId": "519", "data": "649", "fix": "650", "desc": "525"}, {"messageId": "519", "data": "651", "fix": "652", "desc": "528"}, {"messageId": "519", "data": "653", "fix": "654", "desc": "531"}, "replaceWithAlt", {"alt": "655"}, {"range": "656", "text": "657"}, "Replace with `&apos;`.", {"alt": "658"}, {"range": "659", "text": "660"}, "Replace with `&lsquo;`.", {"alt": "661"}, {"range": "662", "text": "663"}, "Replace with `&#39;`.", {"alt": "664"}, {"range": "665", "text": "666"}, "Replace with `&rsquo;`.", {"alt": "655"}, {"range": "667", "text": "668"}, {"alt": "658"}, {"range": "669", "text": "670"}, {"alt": "661"}, {"range": "671", "text": "672"}, {"alt": "664"}, {"range": "673", "text": "674"}, {"alt": "655"}, {"range": "675", "text": "676"}, {"alt": "658"}, {"range": "677", "text": "678"}, {"alt": "661"}, {"range": "679", "text": "680"}, {"alt": "664"}, {"range": "681", "text": "682"}, "suggestUnknown", {"range": "683", "text": "684"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "685", "text": "686"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "655"}, {"range": "687", "text": "688"}, {"alt": "658"}, {"range": "689", "text": "690"}, {"alt": "661"}, {"range": "691", "text": "692"}, {"alt": "664"}, {"range": "693", "text": "694"}, {"alt": "655"}, {"range": "695", "text": "696"}, {"alt": "658"}, {"range": "697", "text": "698"}, {"alt": "661"}, {"range": "699", "text": "700"}, {"alt": "664"}, {"range": "701", "text": "702"}, {"alt": "703"}, {"range": "704", "text": "705"}, "Replace with `&quot;`.", {"alt": "706"}, {"range": "707", "text": "708"}, "Replace with `&ldquo;`.", {"alt": "709"}, {"range": "710", "text": "711"}, "Replace with `&#34;`.", {"alt": "712"}, {"range": "713", "text": "714"}, "Replace with `&rdquo;`.", {"alt": "703"}, {"range": "715", "text": "716"}, {"alt": "706"}, {"range": "717", "text": "718"}, {"alt": "709"}, {"range": "719", "text": "720"}, {"alt": "712"}, {"range": "721", "text": "722"}, {"range": "723", "text": "684"}, {"range": "724", "text": "686"}, {"range": "725", "text": "684"}, {"range": "726", "text": "686"}, {"range": "727", "text": "684"}, {"range": "728", "text": "686"}, {"range": "729", "text": "684"}, {"range": "730", "text": "686"}, {"range": "731", "text": "684"}, {"range": "732", "text": "686"}, {"range": "733", "text": "684"}, {"range": "734", "text": "686"}, {"range": "735", "text": "684"}, {"range": "736", "text": "686"}, {"range": "737", "text": "684"}, {"range": "738", "text": "686"}, {"range": "739", "text": "684"}, {"range": "740", "text": "686"}, {"range": "741", "text": "684"}, {"range": "742", "text": "686"}, {"range": "743", "text": "684"}, {"range": "744", "text": "686"}, {"range": "745", "text": "684"}, {"range": "746", "text": "686"}, {"range": "747", "text": "684"}, {"range": "748", "text": "686"}, {"range": "749", "text": "684"}, {"range": "750", "text": "686"}, {"range": "751", "text": "684"}, {"range": "752", "text": "686"}, {"range": "753", "text": "684"}, {"range": "754", "text": "686"}, "Update the dependencies array to be: [canvas, handleCanvasChange, normalizedInitialData.canvasData, normalizedProductImage, onCanvasReady, saveToHistory]", {"range": "755", "text": "756"}, {"range": "757", "text": "684"}, {"range": "758", "text": "686"}, {"range": "759", "text": "684"}, {"range": "760", "text": "686"}, "Update the dependencies array to be: [text, textColor, fontSize, fontFamily, handleChange]", {"range": "761", "text": "762"}, {"range": "763", "text": "762"}, {"alt": "703"}, {"range": "764", "text": "765"}, {"alt": "706"}, {"range": "766", "text": "767"}, {"alt": "709"}, {"range": "768", "text": "769"}, {"alt": "712"}, {"range": "770", "text": "771"}, {"alt": "703"}, {"range": "772", "text": "773"}, {"alt": "706"}, {"range": "774", "text": "775"}, {"alt": "709"}, {"range": "776", "text": "777"}, {"alt": "712"}, {"range": "778", "text": "779"}, {"alt": "655"}, {"range": "780", "text": "781"}, {"alt": "658"}, {"range": "782", "text": "783"}, {"alt": "661"}, {"range": "784", "text": "785"}, {"alt": "664"}, {"range": "786", "text": "787"}, "&apos;", [2997, 3033], " We&apos;ve sent a password reset link to", "&lsquo;", [2997, 3033], " We&lsquo;ve sent a password reset link to", "&#39;", [2997, 3033], " We&#39;ve sent a password reset link to", "&rsquo;", [2997, 3033], " We&rsquo;ve sent a password reset link to", [4695, 4793], "\n            Enter your email address and we&apos;ll send you a link to reset your password.\n          ", [4695, 4793], "\n            Enter your email address and we&lsquo;ll send you a link to reset your password.\n          ", [4695, 4793], "\n            Enter your email address and we&#39;ll send you a link to reset your password.\n          ", [4695, 4793], "\n            Enter your email address and we&rsquo;ll send you a link to reset your password.\n          ", [7822, 7857], "\n            Don&apos;t have an account?", [7822, 7857], "\n            Don&lsquo;t have an account?", [7822, 7857], "\n            Don&#39;t have an account?", [7822, 7857], "\n            Don&rsquo;t have an account?", [3089, 3092], "unknown", [3089, 3092], "never", [3464, 3513], "\n                  &apos;Save Design'\n                ", [3464, 3513], "\n                  &lsquo;Save Design'\n                ", [3464, 3513], "\n                  &#39;Save Design'\n                ", [3464, 3513], "\n                  &rsquo;Save Design'\n                ", [3464, 3513], "\n                  'Save Design&apos;\n                ", [3464, 3513], "\n                  'Save Design&lsquo;\n                ", [3464, 3513], "\n                  'Save Design&#39;\n                ", [3464, 3513], "\n                  'Save Design&rsquo;\n                ", "&quot;", [4333, 4368], "Then click &quot;Add to <PERSON><PERSON>\" to proceed", "&ldquo;", [4333, 4368], "Then click &ldquo;Add to Cart\" to proceed", "&#34;", [4333, 4368], "Then click &#34;Add to <PERSON><PERSON>\" to proceed", "&rdquo;", [4333, 4368], "Then click &rdquo;Add to Cart\" to proceed", [4333, 4368], "Then click \"Add to Cart&quot; to proceed", [4333, 4368], "Then click \"Add to Cart&ldquo; to proceed", [4333, 4368], "Then click \"Add to Cart&#34; to proceed", [4333, 4368], "Then click \"Add to Cart&rdquo; to proceed", [1524, 1527], [1524, 1527], [3365, 3368], [3365, 3368], [7435, 7438], [7435, 7438], [7568, 7571], [7568, 7571], [7605, 7608], [7605, 7608], [322, 325], [322, 325], [987, 990], [987, 990], [1744, 1747], [1744, 1747], [401, 404], [401, 404], [481, 484], [481, 484], [491, 494], [491, 494], [1267, 1270], [1267, 1270], [1741, 1744], [1741, 1744], [2797, 2800], [2797, 2800], [2963, 2966], [2963, 2966], [7144, 7147], [7144, 7147], [8789, 8791], "[canvas, handleCanvasChange, normalizedInitialData.canvasData, normalizedProductImage, onCanvasReady, saveToHistory]", [10842, 10845], [10842, 10845], [12668, 12671], [12668, 12671], [1273, 1312], "[text, textColor, fontSize, fontFamily, handleChange]", [1367, 1406], [2833, 2923], "\n              4. <PERSON>lick &quot;Add to <PERSON><PERSON>\" when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> &ldquo;Add to <PERSON><PERSON>\" when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> &#34;Add to <PERSON><PERSON>\" when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> &rdquo;Add to <PERSON><PERSON>\" when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> \"Add to Cart&quot; when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> \"Add to Cart&ldquo; when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> \"Add to Cart&#34; when you&apos;re happy with your design\n            ", [2833, 2923], "\n              4. <PERSON><PERSON> \"Add to Cart&rdquo; when you&apos;re happy with your design\n            ", [3910, 4002], "\n              If this takes too long, we&apos;ll automatically switch to basic mode\n            ", [3910, 4002], "\n              If this takes too long, we&lsquo;ll automatically switch to basic mode\n            ", [3910, 4002], "\n              If this takes too long, we&#39;ll automatically switch to basic mode\n            ", [3910, 4002], "\n              If this takes too long, we&rsquo;ll automatically switch to basic mode\n            "]