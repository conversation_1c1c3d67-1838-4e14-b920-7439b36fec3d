[{"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\api\\auth.ts": "1", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\categories\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Bottle3D.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Tshirt3D.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Cart.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ConfirmationModal.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Header.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Hero.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\OrderForm.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\pages\\api\\imagekit-auth.ts": "22"}, {"size": 654, "mtime": 1748552470392, "results": "23", "hashOfConfig": "24"}, {"size": 9287, "mtime": 1748564045987, "results": "25", "hashOfConfig": "24"}, {"size": 36469, "mtime": 1748590426000, "results": "26", "hashOfConfig": "24"}, {"size": 12318, "mtime": 1748589097333, "results": "27", "hashOfConfig": "24"}, {"size": 7090, "mtime": 1748550887645, "results": "28", "hashOfConfig": "24"}, {"size": 7627, "mtime": 1748549346160, "results": "29", "hashOfConfig": "24"}, {"size": 3976, "mtime": 1748589127263, "results": "30", "hashOfConfig": "24"}, {"size": 721, "mtime": 1748556736317, "results": "31", "hashOfConfig": "24"}, {"size": 2977, "mtime": 1748442162680, "results": "32", "hashOfConfig": "24"}, {"size": 34060, "mtime": 1748563065069, "results": "33", "hashOfConfig": "24"}, {"size": 3573, "mtime": 1748590559740, "results": "34", "hashOfConfig": "24"}, {"size": 27127, "mtime": 1748589546152, "results": "35", "hashOfConfig": "24"}, {"size": 2430, "mtime": 1748369774207, "results": "36", "hashOfConfig": "24"}, {"size": 4709, "mtime": 1748442164870, "results": "37", "hashOfConfig": "24"}, {"size": 17199, "mtime": 1748564374090, "results": "38", "hashOfConfig": "24"}, {"size": 23911, "mtime": 1748564581359, "results": "39", "hashOfConfig": "24"}, {"size": 8397, "mtime": 1748589166729, "results": "40", "hashOfConfig": "24"}, {"size": 3782, "mtime": 1748559645908, "results": "41", "hashOfConfig": "24"}, {"size": 21129, "mtime": 1748565063268, "results": "42", "hashOfConfig": "24"}, {"size": 843, "mtime": 1748438908141, "results": "43", "hashOfConfig": "24"}, {"size": 9252, "mtime": 1748589421103, "results": "44", "hashOfConfig": "24"}, {"size": 639, "mtime": 1748512727239, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g4uj7b", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\api\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\categories\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Bottle3D.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Tshirt3D.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Cart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ConfirmationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\OrderForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\pages\\api\\imagekit-auth.ts", [], []]