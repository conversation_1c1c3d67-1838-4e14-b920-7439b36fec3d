[{"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\api\\auth.ts": "1", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\categories\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Bottle3D.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Tshirt3D.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Cart.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ConfirmationModal.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Header.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Hero.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\OrderForm.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\pages\\api\\imagekit-auth.ts": "22", "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\lib\\config.ts": "23"}, {"size": 670, "mtime": 1748607370486, "results": "24", "hashOfConfig": "25"}, {"size": 9325, "mtime": 1748607630036, "results": "26", "hashOfConfig": "25"}, {"size": 36512, "mtime": 1748607794987, "results": "27", "hashOfConfig": "25"}, {"size": 12366, "mtime": 1748608022907, "results": "28", "hashOfConfig": "25"}, {"size": 7105, "mtime": 1748607358891, "results": "29", "hashOfConfig": "25"}, {"size": 7677, "mtime": 1748607466929, "results": "30", "hashOfConfig": "25"}, {"size": 4026, "mtime": 1748607976778, "results": "31", "hashOfConfig": "25"}, {"size": 6226, "mtime": 1748590995584, "results": "32", "hashOfConfig": "25"}, {"size": 7047, "mtime": 1748609114503, "results": "33", "hashOfConfig": "25"}, {"size": 34045, "mtime": 1748607316732, "results": "34", "hashOfConfig": "25"}, {"size": 3573, "mtime": 1748590559740, "results": "35", "hashOfConfig": "25"}, {"size": 27128, "mtime": 1748607401072, "results": "36", "hashOfConfig": "25"}, {"size": 2430, "mtime": 1748369774207, "results": "37", "hashOfConfig": "25"}, {"size": 16687, "mtime": 1748609083014, "results": "38", "hashOfConfig": "25"}, {"size": 17249, "mtime": 1748607698637, "results": "39", "hashOfConfig": "25"}, {"size": 24004, "mtime": 1748607580473, "results": "40", "hashOfConfig": "25"}, {"size": 8397, "mtime": 1748589166729, "results": "41", "hashOfConfig": "25"}, {"size": 3782, "mtime": 1748559645908, "results": "42", "hashOfConfig": "25"}, {"size": 21167, "mtime": 1748607657622, "results": "43", "hashOfConfig": "25"}, {"size": 843, "mtime": 1748438908141, "results": "44", "hashOfConfig": "25"}, {"size": 9267, "mtime": 1748609346258, "results": "45", "hashOfConfig": "25"}, {"size": 639, "mtime": 1748512727239, "results": "46", "hashOfConfig": "25"}, {"size": 3875, "mtime": 1748608073685, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g4uj7b", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\api\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\categories\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\products\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx", ["117"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Bottle3D.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\Tshirt3D.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx", ["118"], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Cart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\ConfirmationModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\components\\OrderForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\pages\\api\\imagekit-auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\lib\\config.ts", [], [], {"ruleId": "119", "severity": 2, "message": "120", "line": 66, "column": 14, "nodeType": null, "messageId": "121", "endLine": 66, "endColumn": 17}, {"ruleId": "119", "severity": 2, "message": "120", "line": 139, "column": 14, "nodeType": null, "messageId": "121", "endLine": 139, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'err' is defined but never used.", "unusedVar"]