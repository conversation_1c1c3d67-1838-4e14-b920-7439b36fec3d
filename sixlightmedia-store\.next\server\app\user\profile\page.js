(()=>{var e={};e.id=614,e.ids=[614],e.modules={318:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=t(5239),a=t(8088),o=t(8170),i=t.n(o),l=t(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let d={children:["",{children:["user",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9538)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/user/profile/page",pathname:"/user/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1860:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2688:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=o(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),n=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:o="",children:i,iconNode:c,...u},h)=>(0,s.createElement)("svg",{ref:h,...d,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:l("lucide",o),...!i&&!n(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(i)?i:[i]])),u=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...o},n)=>(0,s.createElement)(c,{ref:n,iconNode:r,className:l(`lucide-${a(i(e))}`,`lucide-${e}`,t),...o}));return t.displayName=i(e),t}},2941:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5098:(e,r,t)=>{Promise.resolve().then(t.bind(t,8823))},5186:(e,r,t)=>{Promise.resolve().then(t.bind(t,9538))},8823:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(687),a=t(3210),o=t(474),i=t(5814),l=t.n(i),n=t(1891),d=t(5356),c=t(5475);function u(){let[e,r]=(0,a.useState)(null),[t,i]=(0,a.useState)(""),[u,h]=(0,a.useState)(""),[x,m]=(0,a.useState)(!1),[p,g]=(0,a.useState)(0),[f,b]=(0,a.useState)(!1),[v,w]=(0,a.useState)(!1),[j,y]=(0,a.useState)({oldPassword:"",newPassword:""}),N=(0,a.useRef)(null),[k,C]=(0,a.useState)(""),[P,D]=(0,a.useState)(""),[E,S]=(0,a.useState)(0),[A,L]=(0,a.useState)(!1),[_,I]=(0,a.useState)(!0),M=()=>{S(JSON.parse(localStorage.getItem("cart")||"[]").length)},U=()=>{L(!0)};async function T(s){s.preventDefault();let a=localStorage.getItem("token");if(a&&e){b(!0),C(""),D("");try{(await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.PROFILE),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({name:t,profileImage:u})})).ok?(r(e=>e?{...e,name:t,profileImage:u}:e),D("Profile updated successfully"),window.dispatchEvent(new Event("profileImageUpdated"))):C("Failed to update profile")}catch{C("Failed to update profile")}finally{b(!1)}}}async function O(e){let r=e.target.files?.[0];if(r){m(!0),g(0),C(""),D("");try{g(10);let e=await fetch("/api/imagekit-auth"),t=await e.json();g(20);let s=new FormData;s.append("file",r),s.append("fileName",r.name),s.append("publicKey","public_kFR0vTVL7DIDI8YW9eF6/luGjB4="),s.append("signature",t.signature),s.append("expire",t.expire),s.append("token",t.token),s.append("folder","/profile-images"),g(30);let a=new Promise((e,r)=>{let t=new XMLHttpRequest;t.upload.addEventListener("progress",e=>{if(e.lengthComputable){let r=Math.round(e.loaded/e.total*70)+30;g(r)}}),t.addEventListener("load",()=>{if(200===t.status)try{let r=JSON.parse(t.responseText);e(r)}catch{r(Error("Invalid response format"))}else r(Error(`Upload failed with status ${t.status}`))}),t.addEventListener("error",()=>{r(Error("Network error during upload"))}),t.open("POST","https://upload.imagekit.io/api/v1/files/upload"),t.send(s)}),o=await a;o.url?(h(o.url),g(100),D("Profile image uploaded successfully!")):C("Image upload failed: No URL returned")}catch(e){console.error("ImageKit error:",e),C("Image upload failed: "+(e instanceof Error?e.message:"Unknown error"))}finally{m(!1),setTimeout(()=>g(0),1e3)}}}async function R(e){e.preventDefault();let r=localStorage.getItem("token");if(r){w(!0),C(""),D("");try{(await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.CHANGE_PASSWORD),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(j)})).ok?(D("Password changed successfully"),y({oldPassword:"",newPassword:""})):C("Failed to change password")}catch{C("Failed to change password")}finally{w(!1)}}}async function B(){let e=localStorage.getItem("token");e&&confirm("Are you sure you want to delete your account? This cannot be undone.")&&((await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.DELETE),{method:"DELETE",headers:{Authorization:`Bearer ${e}`}})).ok?(alert("Account deleted"),localStorage.removeItem("token"),window.location.href="/"):C("Failed to delete account"))}return _?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:E,onCartClick:U}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading profile..."})]})})]}):k?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:E,onCartClick:U}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Error"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:k}),(0,s.jsxs)(l(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Home"]})]})})]}):e?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:E,onCartClick:U}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Profile"}),(0,s.jsx)("span",{className:"text-white",children:" Settings"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto",children:"Manage your account settings and personalize your experience"}),(0,s.jsxs)(l(),{href:"/user/dashboard",className:"inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-lg text-white font-semibold rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Dashboard"]})]})})]}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[P&&(0,s.jsxs)("div",{className:"mb-8 p-4 bg-green-50 border border-green-200 text-green-700 rounded-2xl flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),P]}),k&&(0,s.jsxs)("div",{className:"mb-8 p-4 bg-red-50 border border-red-200 text-red-700 rounded-2xl flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01"})}),k]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"\uD83D\uDCF8 Profile Picture"}),(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.default,{src:u||"/usericon.png",alt:"Profile",width:120,height:120,className:`w-30 h-30 rounded-full object-cover border-4 border-gray-200 shadow-lg transition-opacity ${x?"opacity-50":"opacity-100"}`}),x&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"bg-white/90 rounded-full p-3",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})})})]}),(0,s.jsx)("button",{className:`absolute bottom-2 right-2 w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 ${x?"opacity-50 cursor-not-allowed":"hover:scale-110"}`,onClick:()=>!x&&N.current?.click(),title:x?"Uploading...":"Change profile picture",disabled:x,children:(0,s.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,s.jsx)("input",{type:"file",accept:"image/*",className:"hidden",ref:N,onChange:O,disabled:x})]}),x&&p>0&&(0,s.jsxs)("div",{className:"w-full max-w-md mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,s.jsx)("span",{children:"Uploading image..."}),(0,s.jsxs)("span",{children:[p,"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out",style:{width:`${p}%`}})})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:e.name||e.email}),(0,s.jsx)("p",{className:"text-gray-600",children:e.email}),(0,s.jsx)("span",{className:"inline-block mt-2 px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full",children:e.role})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"✏️ Edit Profile"}),(0,s.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"Display Name"}),(0,s.jsx)("input",{type:"text",value:t,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",placeholder:"Enter your display name",maxLength:40,required:!0})]}),(0,s.jsx)("button",{type:"submit",className:`w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg ${f||x?"opacity-50 cursor-not-allowed":"hover:shadow-xl transform hover:-translate-y-1"}`,disabled:f||x,children:f?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Saving Profile..."})]}):"\uD83D\uDCBE Save Profile"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"\uD83D\uDD12 Change Password"}),(0,s.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"Current Password"}),(0,s.jsx)("input",{type:"password",placeholder:"Enter your current password",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",value:j.oldPassword,onChange:e=>y(r=>({...r,oldPassword:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"New Password"}),(0,s.jsx)("input",{type:"password",placeholder:"Enter your new password",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",value:j.newPassword,onChange:e=>y(r=>({...r,newPassword:e.target.value})),required:!0})]}),(0,s.jsx)("button",{type:"submit",className:`w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg ${v?"opacity-50 cursor-not-allowed":"hover:shadow-xl transform hover:-translate-y-1"}`,disabled:v,children:v?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Changing Password..."})]}):"\uD83D\uDD10 Change Password"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-red-200",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-red-900 mb-6 flex items-center",children:"⚠️ Danger Zone"}),(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-red-900 mb-2",children:"Delete Account"}),(0,s.jsx)("p",{className:"text-red-700 mb-4",children:"Once you delete your account, there is no going back. Please be certain."}),(0,s.jsx)("button",{onClick:B,className:"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold rounded-2xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"\uD83D\uDDD1️ Delete Account"})]})]})]}),(0,s.jsx)(d.A,{isOpen:A,onClose:()=>{L(!1),M()}})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:E,onCartClick:U}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading user data..."})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9538:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\user\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[728,474,814,834],()=>t(318));module.exports=s})();