(()=>{var e={};e.id=614,e.ids=[614],e.modules={318:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),a=r(8088),l=r(8170),i=r.n(l),o=r(893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["user",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9538)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/user/profile/page",pathname:"/user/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1005:(e,t,r)=>{Promise.resolve().then(r.bind(r,5746))},1860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",l),...!i&&!n(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...l},n)=>(0,s.createElement)(c,{ref:n,iconNode:t,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,r),...l}));return r.displayName=i(e),r}},2909:(e,t,r)=>{"use strict";r.d(t,{ZT:()=>o,oC:()=>i});var s=r(4999),a=r(9916);async function l(){try{let e=await (0,s.UL)(),t=e.get("jwt")?.value;if(!t)return console.log("No JWT token found in cookies"),{isAuthenticated:!1};let r="https://backendapi-sixlight.onrender.com";console.log(`Verifying auth with: ${r}/auth/verify`);let a=await fetch(`${r}/auth/verify`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},cache:"no-store"});if(console.log(`Auth verification response: ${a.status}`),!a.ok)return console.log(`Auth verification failed: ${a.status} ${a.statusText}`),{isAuthenticated:!1};let l=await a.json();return console.log(`Auth verification successful for user: ${l.email}`),{isAuthenticated:!0,user:{id:l.id,email:l.email,role:l.role,name:l.name,profileImage:l.profileImage}}}catch(e){return console.error("Auth verification failed:",e),{isAuthenticated:!1}}}async function i(e="/login"){let t=await l();return t.isAuthenticated||(0,a.redirect)(e),t}async function o(e="/"){let t=await i("/login");return t.user?.role!=="ADMIN"&&(0,a.redirect)(e),t}},2941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4048:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(687),a=r(3210),l=r(474),i=r(5814),o=r.n(i),n=r(1891),d=r(5356),c=r(5475);function u({user:e}){let[t,r]=(0,a.useState)(null),[i,u]=(0,a.useState)(""),[m,p]=(0,a.useState)(""),[h,x]=(0,a.useState)(!1),[g,f]=(0,a.useState)(0),[b,w]=(0,a.useState)(!1),[v,y]=(0,a.useState)(!1),[j,N]=(0,a.useState)({oldPassword:"",newPassword:"",confirmPassword:""}),[P,C]=(0,a.useState)(""),[k,A]=(0,a.useState)(""),[S,D]=(0,a.useState)(0),[I,E]=(0,a.useState)(!1),[_,U]=(0,a.useState)(!0),M=(0,a.useRef)(null),T=()=>{D(JSON.parse(localStorage.getItem("cart")||"[]").length)},$=async e=>{let t=e.target.files?.[0];if(t){if(t.size>5242880)return void C("Image size must be less than 5MB");x(!0),f(0),C(""),new FormData().append("profileImage",t);try{localStorage.getItem("token");let e=new XMLHttpRequest;e.upload.addEventListener("progress",e=>{if(e.lengthComputable){let t=e.loaded/e.total*100;f(t)}}),e.onload=()=>{if(200===e.status){let t=JSON.parse(e.responseText);p(t.profileImage),r(e=>e?{...e,profileImage:t.profileImage}:null),A("Profile image updated successfully!")}else C("Failed to upload image");x(!1),f(0)},e.onerror=()=>{C("Failed to upload image"),x(!1),f(0)},C("Image upload feature is temporarily disabled. Please update your name instead."),x(!1),f(0);return}catch{C("Failed to upload image"),x(!1),f(0)}}},O=async()=>{w(!0),C(""),A("");try{let e=localStorage.getItem("token"),t=await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.UPDATE_PROFILE),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},credentials:"include",body:JSON.stringify({name:i})});if(t.ok){let e=await t.json();r(e),A("Profile updated successfully!")}else C("Failed to update profile")}catch{C("Failed to update profile")}finally{w(!1)}},R=async()=>{if(j.newPassword!==j.confirmPassword)return void C("New passwords don't match");if(j.newPassword.length<6)return void C("New password must be at least 6 characters");y(!0),C(""),A("");try{let e=localStorage.getItem("token"),t=await fetch((0,c.e9)(c.i3.ENDPOINTS.USER.CHANGE_PASSWORD),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},credentials:"include",body:JSON.stringify({oldPassword:j.oldPassword,newPassword:j.newPassword})});if(t.ok)N({oldPassword:"",newPassword:"",confirmPassword:""}),A("Password changed successfully!");else{let e=await t.json();C(e.message||"Failed to change password")}}catch{C("Failed to change password")}finally{y(!1)}};return _?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{cartCount:S,onCartClick:()=>{E(!0)}}),(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Profile"}),(0,s.jsx)("span",{className:"text-white",children:" Management"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-200 mb-4 max-w-2xl mx-auto",children:"Manage your account settings and personal information"}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsxs)("span",{className:"text-white text-sm",children:["\uD83D\uDD12 Secure profile access for ",e?.name||e?.email]})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"✅ Server-side authentication active"})]})}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(o(),{href:"/user/dashboard",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Dashboard"})}),P&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:P}),k&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg",children:k}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Profile Information"}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsxs)("div",{className:"relative inline-block",children:[(0,s.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 mx-auto mb-4",children:m?(0,s.jsx)(l.default,{src:m,alt:"Profile",width:128,height:128,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400 text-4xl",children:"\uD83D\uDC64"})}),h&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50 rounded-full",children:(0,s.jsxs)("div",{className:"text-white text-sm",children:[Math.round(g),"%"]})})]}),(0,s.jsx)("input",{id:"profileImage",name:"profileImage",ref:M,type:"file",accept:"image/*",onChange:$,className:"hidden"}),(0,s.jsx)("button",{onClick:()=>M.current?.click(),disabled:h,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:h?"Uploading...":"Change Photo"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,s.jsx)("input",{id:"editName",name:"name",type:"text",value:i,onChange:e=>u(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your name",autoComplete:"name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",value:t?.email||"",disabled:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",autoComplete:"email"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email cannot be changed"})]}),(0,s.jsx)("button",{onClick:O,disabled:b,className:"w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:b?"Saving...":"Save Profile"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Change Password"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),(0,s.jsx)("input",{id:"oldPassword",name:"oldPassword",type:"password",value:j.oldPassword,onChange:e=>N(t=>({...t,oldPassword:e.target.value})),autoComplete:"current-password",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,s.jsx)("input",{id:"newPassword",name:"newPassword",type:"password",value:j.newPassword,onChange:e=>N(t=>({...t,newPassword:e.target.value})),autoComplete:"new-password",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",value:j.confirmPassword,onChange:e=>N(t=>({...t,confirmPassword:e.target.value})),autoComplete:"new-password",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)("button",{onClick:R,disabled:v||!j.oldPassword||!j.newPassword||!j.confirmPassword,className:"w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:v?"Changing...":"Change Password"})]})]})]})]})]}),(0,s.jsx)(d.A,{isOpen:I,onClose:()=>{E(!1),T()}})]})}},4973:(e,t,r)=>{Promise.resolve().then(r.bind(r,4048))},5746:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\user\\\\profile\\\\UserProfileClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\user\\profile\\UserProfileClient.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>i});var s=r(7413),a=r(2909),l=r(5746);let i="force-dynamic";async function o(){let e=await (0,a.oC)();return(0,s.jsx)(l.default,{user:e.user})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[728,474,814,994,834],()=>r(318));module.exports=s})();