import { ProductService } from './product.service';
import { CreateProductDto, UpdateProductDto } from '../product/product.dto';
export declare class ProductController {
    private readonly productService;
    constructor(productService: ProductService);
    findAll(): Promise<({
        category: {
            id: number;
            name: string;
        };
    } & {
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    })[]>;
    findOne(slug: string): Promise<({
        category: {
            id: number;
            name: string;
        };
    } & {
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }) | null>;
    create(data: CreateProductDto): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    update(slug: string, data: UpdateProductDto): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    delete(slug: string): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    getProductBySlug(slug: string): Promise<{
        category: {
            id: number;
            name: string;
        };
    } & {
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
}
