(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[575],{6589:(e,a,t)=>{Promise.resolve().then(t.bind(t,9589))},9589:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(5155),r=t(2115),o=t(5089),l=t(6766),n=t(6874),i=t.n(n);let c="http://localhost:3001";function d(){let[e,a]=(0,r.useState)({name:"",image:"",description:"",customizable:!1,slug:"",categoryId:""}),[t,n]=(0,r.useState)(""),[d,u]=(0,r.useState)(""),[m,h]=(0,r.useState)(0),[p,g]=(0,r.useState)([]),[x,b]=(0,r.useState)(!0),[f,j]=(0,r.useState)("");function y(e){let{name:t,value:s,type:r}=e.target;a(a=>({...a,[t]:"checkbox"===r?e.target.checked:s}))}async function N(t){t.preventDefault(),n(""),u("");let s=localStorage.getItem("token");if(!s)return void u("Not authenticated");let r=await fetch("".concat(c,"/product"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({...e,price:m,categoryId:Number(e.categoryId)})}),o=await r.json();r.ok?(n("Product created!"),a({name:"",image:"",description:"",customizable:!1,slug:"",categoryId:""}),h(0)):u(o.error||"Failed to create product")}return(0,r.useEffect)(()=>{b(!0),fetch("".concat(c,"/categories")).then(e=>{if(!e.ok)throw Error("Failed to fetch categories");return e.json()}).then(e=>{g(e),j("")}).catch(()=>{g([]),j("Failed to load categories")}).finally(()=>b(!1))},[]),(0,s.jsxs)("div",{className:"max-w-xl mx-auto mt-16",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold",children:"Add New Product"}),(0,s.jsx)(i(),{href:"/admin/categories",className:"bg-gray-200 text-gray-800 px-4 py-2 rounded-full font-semibold shadow hover:bg-gray-300 transition text-sm",children:"Manage Categories"})]}),(0,s.jsx)(o.IKContext,{publicKey:"public_kFR0vTVL7DIDI8YW9eF6/luGjB4=".replace(/"/g,""),urlEndpoint:"https://ik.imagekit.io/fwbvmq9re".replace(/"/g,""),authenticator:async()=>(await fetch("/api/imagekit-auth")).json(),children:(0,s.jsxs)("form",{onSubmit:N,className:"flex flex-col gap-4",children:[(0,s.jsx)("input",{name:"name",value:e.name,onChange:y,placeholder:"Name",className:"border rounded px-3 py-2",required:!0}),(0,s.jsx)("input",{name:"slug",value:e.slug,onChange:y,placeholder:"Slug (unique)",className:"border rounded px-3 py-2",required:!0}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block mb-1 font-semibold",children:"Product Image"}),(0,s.jsx)(o.IKUpload,{fileName:e.slug?"".concat(e.slug,".jpg"):"product.jpg",onSuccess:e=>a(a=>({...a,image:e.url})),onError:()=>u("Image upload failed"),className:"border rounded px-3 py-2 w-full"}),e.image&&(0,s.jsx)(l.default,{src:e.image,alt:"Preview",width:96,height:96,className:"mt-2 h-24 w-auto rounded shadow"})]}),(0,s.jsx)("textarea",{name:"description",value:e.description,onChange:y,placeholder:"Description",className:"border rounded px-3 py-2",required:!0}),(0,s.jsxs)("select",{name:"categoryId",value:e.categoryId||"",onChange:y,className:"border rounded px-3 py-2",required:!0,disabled:x||!!f,children:[(0,s.jsx)("option",{value:"",disabled:!0,children:x?"Loading categories...":"Select category"}),p.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]}),f&&(0,s.jsx)("div",{className:"text-red-600 text-sm",children:f}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",name:"customizable",checked:e.customizable,onChange:y}),"Customizable"]}),(0,s.jsxs)("label",{className:"font-semibold",children:["Price",(0,s.jsxs)("div",{className:"flex items-center mt-1",children:[(0,s.jsx)("span",{className:"px-2 py-2 bg-gray-100 border border-r-0 rounded-l",children:"K"}),(0,s.jsx)("input",{type:"number",min:"0",step:"0.01",className:"w-full border rounded-r px-3 py-2 focus:outline-none",value:m,onChange:e=>h(Number(e.target.value)),required:!0})]})]}),(0,s.jsx)("button",{type:"submit",className:"bg-[#1a237e] text-white font-semibold px-6 py-2 rounded-full shadow hover:bg-[#283593] transition",children:"Add Product"}),t&&(0,s.jsx)("div",{className:"text-green-600",children:t}),d&&(0,s.jsx)("div",{className:"text-red-600",children:d})]})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[766,874,89,441,684,358],()=>a(6589)),_N_E=e.O()}]);