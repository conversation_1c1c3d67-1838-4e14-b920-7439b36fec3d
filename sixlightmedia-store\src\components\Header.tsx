"use client";

import Link from "next/link";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import { getApiUrl, API_CONFIG } from "@/lib/config";

export default function Header({
  cartCount = 0,
  onCartClick,
}: {
  cartCount?: number;
  onCartClick?: () => void;
}) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    async function handleProfileImageUpdated() {
      const token = localStorage.getItem("token");
      if (token) {
        try {
          // Fetch latest user data from API instead of relying on JWT token
          const response = await fetch(
            getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD),
            {
              headers: { Authorization: `Bearer ${token}` },
            }
          );

          if (response.ok) {
            const data = await response.json();
            if (data && data.user) {
              setProfileImage(data.user.profileImage || null);
            }
          }
        } catch (error) {
          console.error("Failed to fetch updated profile image:", error);
          // Fallback to JWT token method
          try {
            const payload = JSON.parse(atob(token.split(".")[1]));
            setProfileImage(payload.profileImage || null);
          } catch {
            setProfileImage(null);
          }
        }
      }
    }
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("token");
      setIsLoggedIn(!!token);
      if (token) {
        // Decode JWT to get role (simple base64 decode, not secure for prod, but fine for client display)
        try {
          const payload = JSON.parse(atob(token.split(".")[1]));
          setUserRole(payload.role || null);
          setProfileImage(payload.profileImage || null);
        } catch {
          setUserRole(null);
          setProfileImage(null);
        }
      } else {
        setUserRole(null);
        setProfileImage(null);
      }
      // Listen for profile image update event
      window.addEventListener("profileImageUpdated", handleProfileImageUpdated);
    }
    // Cleanup event listener
    return () => {
      window.removeEventListener(
        "profileImageUpdated",
        handleProfileImageUpdated
      );
    };
  }, []);

  // Prevent background scroll and horizontal scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = "hidden";
      document.documentElement.style.overflowX = "hidden";
    } else {
      document.body.style.overflow = "";
      document.documentElement.style.overflowX = "";
    }
    return () => {
      document.body.style.overflow = "";
      document.documentElement.style.overflowX = "";
    };
  }, [mobileMenuOpen]);

  // Close menus on Escape key
  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (e.key === "Escape") {
        setShowDropdown(false);
        setMobileMenuOpen(false);
      }
    }
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Ensure dropdown closes when mobile menu opens
  useEffect(() => {
    if (mobileMenuOpen) setShowDropdown(false);
  }, [mobileMenuOpen]);

  function handleLogout() {
    localStorage.removeItem("token");
    setIsLoggedIn(false);
    window.location.href = "/login";
  }

  function handleDropdownToggle() {
    setShowDropdown((prev) => !prev);
  }

  function handleDropdownClose() {
    setShowDropdown(false);
  }

  return (
    <header className="w-full bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-30">
      <nav className="max-w-7xl mx-auto flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 md:py-4">
        {/* Brand Logo */}
        <Link href="/" className="flex items-center gap-2 min-w-0 group">
          <div className="relative">
            <Image
              src="/6 Light Logo.png"
              alt="Six Light Media Logo"
              width={40}
              height={40}
              className="h-8 w-auto sm:h-10 md:h-12 transition-transform duration-300 group-hover:scale-110"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"></div>
          </div>
          <span className="truncate text-base xs:text-lg sm:text-2xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-indigo-500 group-hover:via-purple-500 group-hover:to-pink-500 transition-all duration-300">
            Store
          </span>
        </Link>
        {/* Desktop Nav */}
        <div className="hidden sm:flex gap-2 md:gap-4 lg:gap-6 items-center min-w-0">
          <Link
            href="/"
            className="px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5"
          >
            🏠 Home
          </Link>
          {isLoggedIn && (
            <Link
              href="/user/dashboard"
              className="px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5"
            >
              📊 Dashboard
            </Link>
          )}
          {isLoggedIn && userRole === "ADMIN" && (
            <Link
              href="/admin/dashboard"
              className="px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              ⚡ Admin
            </Link>
          )}
          {!isLoggedIn ? (
            <>
              <Link
                href="/login"
                className="px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                🔐 Login
              </Link>
              <Link
                href="/register"
                className="px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                ✨ Register
              </Link>
            </>
          ) : (
            <div className="relative min-w-0">
              <button
                className="flex items-center gap-2 focus:outline-none min-w-0 px-2 py-1 rounded-2xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 transform hover:-translate-y-0.5"
                onClick={handleDropdownToggle}
                aria-label="Open user menu"
              >
                <div className="relative">
                  <Image
                    src={profileImage || "/usericon.png"}
                    alt="Profile"
                    width={32}
                    height={32}
                    className="rounded-full border-2 border-gradient-to-r from-indigo-200 to-purple-200 object-cover bg-white h-8 w-8 md:h-9 md:w-9 shadow-md"
                  />
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <span className="hidden md:inline font-semibold text-gray-700 truncate max-w-[80px]">
                  Account
                </span>
                <svg
                  className={`w-4 h-4 ml-1 text-gray-600 transition-transform duration-300 ${
                    showDropdown ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              {showDropdown && (
                <div
                  className="absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-lg border border-gray-200 rounded-2xl shadow-2xl z-50 overflow-hidden"
                  onMouseLeave={handleDropdownClose}
                >
                  <div className="p-2">
                    <Link
                      href="/user/dashboard"
                      className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5"
                      onClick={handleDropdownClose}
                    >
                      <span className="text-lg">📊</span>
                      Dashboard
                    </Link>
                    <Link
                      href="/user/profile"
                      className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5"
                      onClick={handleDropdownClose}
                    >
                      <span className="text-lg">⚙️</span>
                      Profile Settings
                    </Link>
                    <div className="border-t border-gray-100 my-2"></div>
                    <button
                      onClick={() => {
                        handleLogout();
                        handleDropdownClose();
                      }}
                      className="flex items-center gap-3 w-full text-left px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5"
                    >
                      <span className="text-lg">🚪</span>
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        {/* Responsive Mobile Menu Button */}
        <button
          type="button"
          className="flex items-center justify-center p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 sm:hidden hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300"
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
          onClick={() => setMobileMenuOpen((open) => !open)}
        >
          {mobileMenuOpen ? (
            <X
              className="w-7 h-7 text-gray-700"
              aria-hidden={!mobileMenuOpen}
            />
          ) : (
            <Menu
              className="w-7 h-7 text-gray-700"
              aria-hidden={mobileMenuOpen}
            />
          )}
        </button>
        {/* Cart Button */}
        <button
          className="relative px-3 py-1 md:px-4 md:py-2 rounded-2xl font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg hover:from-red-600 hover:to-pink-700 hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 ml-1 md:ml-2 text-sm md:text-base transform hover:-translate-y-0.5"
          onClick={onCartClick}
          aria-label="Open cart"
        >
          <span className="hidden sm:inline">🛒 Cart</span>
          <span className="sm:hidden">🛒</span>
          <span className="sm:ml-1">({cartCount})</span>
          {cartCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs rounded-full px-1.5 py-0.5 animate-bounce font-bold shadow-lg">
              {cartCount}
            </span>
          )}
        </button>
      </nav>
      {/* Mobile Nav Drawer Overlay & Drawer: Only render when open */}
      {mobileMenuOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300 block"
            style={{ left: 0, right: 0 }}
            onClick={() => setMobileMenuOpen(false)}
            aria-hidden={!mobileMenuOpen}
          />
          {/* Drawer */}
          <div
            className="fixed top-0 right-0 z-50 w-full max-w-xs h-screen bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs min-w-0 sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden translate-x-0 border-l border-gray-200"
            role="dialog"
            aria-modal="true"
            tabIndex={-1}
            style={{ right: 0, left: "auto" }}
            onKeyDown={(e) => {
              if (e.key === "Tab") {
                // Basic focus trap: keep focus inside drawer
                const focusable = Array.from(
                  (e.currentTarget as HTMLElement).querySelectorAll(
                    'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
                  )
                ) as HTMLElement[];
                if (focusable.length === 0) return;
                const first = focusable[0];
                const last = focusable[focusable.length - 1];
                if (!e.shiftKey && document.activeElement === last) {
                  e.preventDefault();
                  first.focus();
                } else if (e.shiftKey && document.activeElement === first) {
                  e.preventDefault();
                  last.focus();
                }
              }
            }}
          >
            <div className="flex items-center justify-between px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
              <span className="font-black text-lg bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                📱 Menu
              </span>
              <button
                className="p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 hover:bg-white/50 transition-all duration-300"
                aria-label="Close menu"
                onClick={() => setMobileMenuOpen(false)}
                tabIndex={mobileMenuOpen ? 0 : -1}
              >
                <X className="w-6 h-6 text-gray-700" />
              </button>
            </div>
            <nav className="flex flex-col gap-2 px-4 py-6 flex-1 overflow-y-auto max-h-[calc(100vh-80px)]">
              <Link
                href="/"
                className="flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base transform hover:-translate-y-0.5"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="text-lg">🏠</span>
                Home
              </Link>
              {isLoggedIn && (
                <Link
                  href="/user/dashboard"
                  className="flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base transform hover:-translate-y-0.5"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <span className="text-lg">📊</span>
                  Dashboard
                </Link>
              )}
              {isLoggedIn && userRole === "ADMIN" && (
                <Link
                  href="/admin/dashboard"
                  className="flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <span className="text-lg">⚡</span>
                  Admin Panel
                </Link>
              )}
              {!isLoggedIn ? (
                <>
                  <Link
                    href="/login"
                    className="flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-lg">🔐</span>
                    Login
                  </Link>
                  <Link
                    href="/register"
                    className="flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-lg">✨</span>
                    Register
                  </Link>
                </>
              ) : (
                <div className="flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200">
                  <Link
                    href="/user/profile"
                    className="flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-300 text-base transform hover:-translate-y-0.5"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-lg">⚙️</span>
                    Profile Settings
                  </Link>
                  <button
                    onClick={() => {
                      handleLogout();
                      setMobileMenuOpen(false);
                    }}
                    className="flex items-center gap-3 w-full text-left px-4 py-3 rounded-2xl font-semibold text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-300 text-base transform hover:-translate-y-0.5"
                  >
                    <span className="text-lg">🚪</span>
                    Logout
                  </button>
                </div>
              )}
            </nav>
            {isLoggedIn && (
              <div className="flex items-center gap-3 px-4 py-4 border-t border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                <div className="relative">
                  <Image
                    src={profileImage || "/usericon.png"}
                    alt="Profile"
                    width={40}
                    height={40}
                    className="rounded-full border-2 border-indigo-200 object-cover bg-white shadow-md w-10 h-10"
                  />
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-bold text-gray-900 truncate text-sm">
                    Account
                  </p>
                  <p className="text-xs text-gray-600 truncate">Logged in</p>
                  <span className="inline-block mt-1 px-2 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full">
                    {userRole}
                  </span>
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </header>
  );
}
