(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1264:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3843:(e,t,s)=>{"use strict";s.d(t,{e9:()=>i,i3:()=>r});var a=s(9509);let r={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh",VERIFY_EMAIL:"/auth/verify-email",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",RESEND_VERIFICATION:"/auth/resend-verification"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",UPDATE_PROFILE:"/user/profile",UPLOAD_PROFILE_IMAGE:"/user/upload-profile-image",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};a.env.IMAGEKIT_PRIVATE_KEY,a.env.NEXT_PUBLIC_GA_ID,a.env.NEXT_PUBLIC_GA_ID;let i=e=>{let t=r.BASE_URL.replace(/\/$/,""),s=e.startsWith("/")?e:"/".concat(e);return"".concat(t).concat(s)}},4070:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(5155),r=s(2115),i=s(9958),l=s(6874),n=s.n(l),o=s(6766),d=s(1153),c=s(646),m=s(1264),h=s(5339);let u=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),x=d.z.object({email:d.z.string().email("Please enter a valid email address")});function p(){let[e,t]=(0,r.useState)({email:""}),[s,l]=(0,r.useState)({}),[d,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(!1),[b,y]=(0,r.useState)(""),N=(e,a)=>{t(t=>({...t,[e]:a})),s[e]&&l(t=>({...t,[e]:void 0}))},E=async t=>{t.preventDefault(),p(!0),l({}),y("");let s=x.safeParse(e);if(!s.success){p(!1);let e={};s.error.errors.forEach(t=>{t.path[0]&&(e[t.path[0]]=t.message)}),l(e);return}try{let t=await (0,i.BD)(e.email);p(!1),f(!0),y(t.message||"Password reset link sent to your email!")}catch(e){p(!1),y("Failed to send reset email. Please try again.")}};return g?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,a.jsx)(o.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,a.jsx)(c.A,{className:"h-16 w-16 text-green-500 mb-4"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-green-700",children:"Email Sent!"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:b}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"\uD83D\uDCE7 Check your email:"})," We've sent a password reset link to"," ",(0,a.jsx)("span",{className:"font-mono",children:e.email})]})}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("strong",{children:"⏰ Link expires in 1 hour"})," for your security."]})})]}),(0,a.jsxs)("div",{className:"space-y-3 mt-6",children:[(0,a.jsx)(n(),{href:"/login",className:"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Login"}),(0,a.jsx)("button",{onClick:()=>{f(!1),t({email:""}),y("")},className:"w-full text-gray-600 hover:text-gray-800 transition-colors",children:"Send to different email"})]})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,a.jsx)(o.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Forgot Password"}),(0,a.jsx)("p",{className:"text-gray-600 text-center mt-2",children:"Enter your email address and we'll send you a link to reset your password."})]}),(0,a.jsxs)("form",{onSubmit:E,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",value:e.email,onChange:e=>N("email",e.target.value),placeholder:"Enter your email address",autoComplete:"email",className:"w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ".concat(s.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),"aria-invalid":!!s.email,"aria-describedby":s.email?"email-error":void 0})]}),s.email&&(0,a.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,a.jsx)(h.A,{size:16}),s.email]})]}),(0,a.jsx)("button",{type:"submit",className:"w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ".concat(d?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"),disabled:d,children:d?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,a.jsx)("span",{children:"Sending Reset Link..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Send Reset Link"})]})}),b&&!g&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,a.jsx)(h.A,{size:16}),b]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 text-center",children:(0,a.jsxs)(n(),{href:"/login",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,a.jsx)(u,{className:"h-4 w-4"}),"Back to Login"]})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Don't have an account?"," ",(0,a.jsx)(n(),{href:"/register",className:"text-blue-600 hover:text-blue-800 font-medium",children:"Sign up"})]})})]})})}},5583:(e,t,s)=>{Promise.resolve().then(s.bind(s,4070))},9958:(e,t,s)=>{"use strict";s.d(t,{A$:()=>l,BD:()=>n,RS:()=>d,iD:()=>r,kz:()=>i,xw:()=>o});var a=s(3843);async function r(e,t){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})})).json()}async function i(e,t,s){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t,name:s})})).json()}async function l(e){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.VERIFY_EMAIL),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e})})).json()}async function n(e){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.FORGOT_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}async function o(e,t){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.RESET_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e,password:t})})).json()}async function d(e){return(await fetch((0,a.e9)(a.i3.ENDPOINTS.AUTH.RESEND_VERIFICATION),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,619,441,684,358],()=>t(5583)),_N_E=e.O()}]);