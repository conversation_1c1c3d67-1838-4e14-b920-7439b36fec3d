// Example extensions for the ProductCustomizer component
// These can be integrated into the main component as needed

import { fabric } from "fabric";

// Template system for pre-made designs
export const designTemplates = {
  birthday: {
    name: "Birthday Design",
    objects: [
      {
        type: "text",
        text: "Happy Birthday!",
        left: 100,
        top: 100,
        fontSize: 30,
        fill: "#ff6b6b",
        fontFamily: "Arial",
      },
      {
        type: "circle",
        radius: 50,
        left: 200,
        top: 200,
        fill: "#4ecdc4",
      },
    ],
  },
  wedding: {
    name: "Wedding Design",
    objects: [
      {
        type: "text",
        text: "Mr. & Mrs.",
        left: 150,
        top: 150,
        fontSize: 25,
        fill: "#d4af37",
        fontFamily: "Georgia",
      },
    ],
  },
};

// Load template function
export const loadTemplate = (
  canvas: fabric.Canvas,
  templateKey: keyof typeof designTemplates
) => {
  const template = designTemplates[templateKey];
  if (!template) return;

  canvas.clear();

  template.objects.forEach((objData) => {
    let obj;
    switch (objData.type) {
      case "text":
        obj = new fabric.IText(objData.text || "Text", {
          left: objData.left,
          top: objData.top,
          fontSize: objData.fontSize,
          fill: objData.fill,
          fontFamily: objData.fontFamily,
        });
        break;
      case "circle":
        obj = new fabric.Circle({
          radius: (objData as any).radius || 50,
          left: objData.left,
          top: objData.top,
          fill: objData.fill,
        });
        break;
      // Add more shape types as needed
    }

    if (obj) {
      canvas.add(obj);
    }
  });

  canvas.renderAll();
};

// Advanced text styling options
export const textStyleOptions = {
  fontFamilies: [
    "Arial",
    "Times New Roman",
    "Helvetica",
    "Georgia",
    "Verdana",
    "Comic Sans MS",
    "Impact",
    "Trebuchet MS",
    "Courier New",
    "Lucida Console",
  ],
  textEffects: {
    shadow: (text: fabric.IText) => {
      text.set({
        shadow: new fabric.Shadow({
          color: "rgba(0,0,0,0.3)",
          blur: 5,
          offsetX: 3,
          offsetY: 3,
        }),
      });
    },
    outline: (text: fabric.IText, color: string = "#000000") => {
      text.set({
        stroke: color,
        strokeWidth: 2,
      });
    },
    gradient: (text: fabric.IText) => {
      text.set({
        fill: new fabric.Gradient({
          type: "linear",
          coords: { x1: 0, y1: 0, x2: 0, y2: text.height || 50 },
          colorStops: [
            { offset: 0, color: "#ff6b6b" },
            { offset: 1, color: "#4ecdc4" },
          ],
        }),
      });
    },
  },
};

// Image filters and effects
export const imageFilters = {
  grayscale: () => new fabric.Image.filters.Grayscale(),
  sepia: () => new fabric.Image.filters.Sepia(),
  brightness: (brightness: number = 0.1) =>
    new fabric.Image.filters.Brightness({ brightness }),
  contrast: (contrast: number = 0.1) =>
    new fabric.Image.filters.Contrast({ contrast }),
  blur: (blur: number = 0.1) => new fabric.Image.filters.Blur({ blur }),
};

// Apply filter to selected image
export const applyImageFilter = (
  canvas: fabric.Canvas,
  filterName: keyof typeof imageFilters,
  ...args: any[]
) => {
  const activeObject = canvas.getActiveObject();
  if (activeObject && activeObject.type === "image") {
    const image = activeObject as fabric.Image;
    const filter = imageFilters[filterName](...args);

    image.filters = image.filters || [];
    image.filters.push(filter);
    image.applyFilters();
    canvas.renderAll();
  }
};

// Grid and alignment helpers
export const alignmentHelpers = {
  alignLeft: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      activeObject.set({ left: 0 });
      canvas.renderAll();
    }
  },
  alignCenter: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject && canvas.width) {
      activeObject.set({
        left: (canvas.width - (activeObject.width || 0)) / 2,
      });
      canvas.renderAll();
    }
  },
  alignRight: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject && canvas.width) {
      activeObject.set({ left: canvas.width - (activeObject.width || 0) });
      canvas.renderAll();
    }
  },
  distributeHorizontally: (canvas: fabric.Canvas) => {
    const objects = canvas.getActiveObjects();
    if (objects.length > 1 && canvas.width) {
      const totalWidth = canvas.width;
      const spacing = totalWidth / (objects.length + 1);

      objects.forEach((obj, index) => {
        obj.set({ left: spacing * (index + 1) - (obj.width || 0) / 2 });
      });
      canvas.renderAll();
    }
  },
};

// Layer management
export const layerManagement = {
  bringToFront: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      canvas.bringToFront(activeObject);
      canvas.renderAll();
    }
  },
  sendToBack: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      canvas.sendToBack(activeObject);
      canvas.renderAll();
    }
  },
  bringForward: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      canvas.bringForward(activeObject);
      canvas.renderAll();
    }
  },
  sendBackwards: (canvas: fabric.Canvas) => {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      canvas.sendBackwards(activeObject);
      canvas.renderAll();
    }
  },
};

// Export options
export const exportOptions = {
  exportAsPNG: (canvas: fabric.Canvas, filename: string = "design.png") => {
    const dataURL = canvas.toDataURL({
      format: "png",
      quality: 1,
      multiplier: 2,
    });
    downloadImage(dataURL, filename);
  },
  exportAsJPEG: (canvas: fabric.Canvas, filename: string = "design.jpg") => {
    const dataURL = canvas.toDataURL({
      format: "jpeg",
      quality: 0.9,
      multiplier: 2,
    });
    downloadImage(dataURL, filename);
  },
  exportAsSVG: (canvas: fabric.Canvas, filename: string = "design.svg") => {
    const svg = canvas.toSVG();
    const blob = new Blob([svg], { type: "image/svg+xml" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.download = filename;
    link.href = url;
    link.click();

    URL.revokeObjectURL(url);
  },
};

// Helper function for downloading images
const downloadImage = (dataURL: string, filename: string) => {
  const link = document.createElement("a");
  link.download = filename;
  link.href = dataURL;
  link.click();
};

// Keyboard shortcuts
export const setupKeyboardShortcuts = (canvas: fabric.Canvas) => {
  const handleKeyDown = (e: KeyboardEvent) => {
    // Delete selected object
    if (e.key === "Delete" || e.key === "Backspace") {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        canvas.remove(activeObject);
        canvas.renderAll();
      }
    }

    // Copy (Ctrl+C)
    if (e.ctrlKey && e.key === "c") {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        activeObject.clone((cloned: fabric.Object) => {
          (window as any).clipboard = cloned;
        });
      }
    }

    // Paste (Ctrl+V)
    if (e.ctrlKey && e.key === "v") {
      if ((window as any).clipboard) {
        (window as any).clipboard.clone((cloned: fabric.Object) => {
          cloned.set({
            left: cloned.left! + 10,
            top: cloned.top! + 10,
          });
          canvas.add(cloned);
          canvas.setActiveObject(cloned);
          canvas.renderAll();
        });
      }
    }

    // Undo (Ctrl+Z)
    if (e.ctrlKey && e.key === "z" && !e.shiftKey) {
      e.preventDefault();
      // Implement undo functionality
    }

    // Redo (Ctrl+Shift+Z)
    if (e.ctrlKey && e.shiftKey && e.key === "Z") {
      e.preventDefault();
      // Implement redo functionality
    }
  };

  document.addEventListener("keydown", handleKeyDown);

  // Return cleanup function
  return () => {
    document.removeEventListener("keydown", handleKeyDown);
  };
};

// Usage example:
/*
import { 
  loadTemplate, 
  textStyleOptions, 
  applyImageFilter, 
  alignmentHelpers,
  setupKeyboardShortcuts 
} from './CustomizerExtensions';

// In your component:
useEffect(() => {
  if (canvas) {
    const cleanup = setupKeyboardShortcuts(canvas);
    return cleanup;
  }
}, [canvas]);

// Load a template
const handleLoadTemplate = (templateKey) => {
  loadTemplate(canvas, templateKey);
};

// Apply text effect
const handleTextEffect = (effectName) => {
  const activeObject = canvas.getActiveObject();
  if (activeObject && activeObject.type === 'i-text') {
    textStyleOptions.textEffects[effectName](activeObject);
    canvas.renderAll();
  }
};
*/
