const nodemailer = require('nodemailer');
require('dotenv').config();

async function testEmail() {
  console.log('🔧 Testing Email Configuration...');
  console.log('📧 SMTP Configuration:');
  console.log(`   Host: ${process.env.SMTP_HOST}`);
  console.log(`   Port: ${process.env.SMTP_PORT}`);
  console.log(`   User: ${process.env.SMTP_USER}`);
  console.log(`   Pass: ${process.env.SMTP_PASS ? '***configured***' : 'NOT SET'}`);

  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  try {
    console.log('🔍 Testing SMTP connection...');
    await transporter.verify();
    console.log('✅ SMTP connection verified successfully');

    console.log('📤 Sending test email...');
    const testEmail = {
      from: `"Six Light Media Store" <${process.env.SMTP_USER}>`,
      to: process.env.SMTP_USER, // Send to yourself for testing
      subject: '🧪 Test Email - Six Light Media Store',
      html: `
        <h1>Test Email</h1>
        <p>This is a test email to verify your SMTP configuration.</p>
        <p>If you receive this email, your email service is working correctly!</p>
        <p>Time: ${new Date().toISOString()}</p>
      `,
    };

    const result = await transporter.sendMail(testEmail);
    console.log('✅ Test email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    console.log(`📬 Check your inbox: ${process.env.SMTP_USER}`);

  } catch (error) {
    console.error('❌ Email test failed:', error);
    
    if (error.code === 'EAUTH') {
      console.error('🔐 Authentication failed. Please check:');
      console.error('   1. Your email address is correct');
      console.error('   2. You are using an App Password (not your regular Gmail password)');
      console.error('   3. 2-Factor Authentication is enabled on your Gmail account');
    } else if (error.code === 'ECONNECTION') {
      console.error('🌐 Connection failed. Please check:');
      console.error('   1. Your internet connection');
      console.error('   2. SMTP host and port settings');
      console.error('   3. Firewall settings');
    }
  }
}

testEmail();
