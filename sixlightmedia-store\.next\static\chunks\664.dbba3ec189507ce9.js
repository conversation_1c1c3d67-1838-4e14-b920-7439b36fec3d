"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283,664],{2283:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var s=t(5155),a=t(2115),l=t(9946);let o=(0,l.A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]]),i=(0,l.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),c=e=>{let{productImage:r,onCustomizationChange:t,initialCustomization:l}=e,[c,n]=(0,a.useState)(""),[d,x]=(0,a.useState)("#000000"),[u,m]=(0,a.useState)(20),[h,p]=(0,a.useState)("Arial"),b=()=>{t({canvasData:JSON.stringify({text:c,textColor:d,fontSize:u,fontFamily:h,type:"basic"}),preview:c?"data:text/plain;base64,".concat(btoa("".concat(c," (").concat(d,")"))):void 0})};return a.useEffect(()=>{c.trim()&&b()},[c,d,u,h]),a.useEffect(()=>{b()},[c,d,u,h]),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"\uD83C\uDFA8 Product Customizer"}),(0,s.jsx)("p",{className:"text-gray-600 mb-3",children:"Customize your product with text and colors"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 text-xs",children:[(0,s.jsx)("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded-full",children:"✓ Auto-save enabled"}),(0,s.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-1 rounded-full",children:"✓ Real-time preview"}),(0,s.jsx)("span",{className:"bg-purple-100 text-purple-700 px-2 py-1 rounded-full",children:"✓ Ready to use"})]})]}),(0,s.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"text-blue-600 mt-1",children:"\uD83D\uDCA1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-800 mb-1",children:"Quick Start Guide"}),(0,s.jsxs)("p",{className:"text-sm text-blue-700 mb-2",children:["1. Enter your custom text below",(0,s.jsx)("br",{}),"2. Choose your preferred color and font",(0,s.jsx)("br",{}),"3. Your design will auto-save as you type",(0,s.jsx)("br",{}),'4. Click "Add to Cart" when you\'re happy with your design']})]})]})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"relative bg-gray-100 rounded-xl overflow-hidden",children:[(0,s.jsx)("img",{src:r,alt:"Product",className:"w-full h-64 object-contain",onError:e=>{e.target.src="/bottle-dummy.jpg"}}),c&&(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none",style:{color:d,fontSize:"".concat(u,"px"),fontFamily:h,textShadow:"1px 1px 2px rgba(0,0,0,0.5)"},children:c})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:[(0,s.jsx)(o,{className:"inline mr-2",size:16}),"Custom Text"]}),(0,s.jsx)("input",{type:"text",value:c,onChange:e=>n(e.target.value),placeholder:"Enter your custom text",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200",maxLength:30}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[c.length,"/30 characters"]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:[(0,s.jsx)(i,{className:"inline mr-2",size:16}),"Text Color"]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("input",{type:"color",value:d,onChange:e=>x(e.target.value),className:"w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("div",{className:"text-sm font-mono text-gray-600 bg-gray-100 px-3 py-2 rounded-lg",children:d.toUpperCase()})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:["Font Size: ",u,"px"]}),(0,s.jsx)("input",{type:"range",min:"12",max:"48",value:u,onChange:e=>m(Number(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block text-sm font-semibold text-gray-700",children:"Font Family"}),(0,s.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100",children:[(0,s.jsx)("option",{value:"Arial",children:"Arial"}),(0,s.jsx)("option",{value:"Times New Roman",children:"Times New Roman"}),(0,s.jsx)("option",{value:"Helvetica",children:"Helvetica"}),(0,s.jsx)("option",{value:"Georgia",children:"Georgia"}),(0,s.jsx)("option",{value:"Verdana",children:"Verdana"}),(0,s.jsx)("option",{value:"Comic Sans MS",children:"Comic Sans MS"}),(0,s.jsx)("option",{value:"Impact",children:"Impact"})]})]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-xl",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Customization Preview"}),(0,s.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Text:"})," ",c||"No text added"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Color:"})," ",d]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Font:"})," ",h," (",u,"px)"]})]})]})]}),(0,s.jsx)("div",{className:"mt-6 text-sm text-gray-500 text-center",children:"\uD83D\uDCA1 Tip: Your customization will be saved when you add the product to cart"})]})}},9946:(e,r,t)=>{t.d(r,{A:()=>x});var s=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),o=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:x,iconNode:u,...m}=e;return(0,s.createElement)("svg",{ref:r,...n,width:a,height:a,stroke:t,strokeWidth:o?24*Number(l)/Number(a):l,className:i("lucide",d),...!x&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(x)?x:[x]])}),x=(e,r)=>{let t=(0,s.forwardRef)((t,l)=>{let{className:c,...n}=t;return(0,s.createElement)(d,{ref:l,iconNode:r,className:i("lucide-".concat(a(o(e))),"lucide-".concat(e),c),...n})});return t.displayName=o(e),t}}}]);