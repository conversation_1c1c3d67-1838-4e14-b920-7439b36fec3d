"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[632],{6632:(e,t,s)=>{s.r(t),s.d(t,{default:()=>h});var i=s(5155),o=s(7558),n=s(8842),r=s(4688),a=s(2115),l=s(6670);function h(e){let{color:t="white",text:s="",onColorChange:h}=e,[d,c]=(0,a.useState)(!1);return(0,i.jsxs)("div",{style:{width:"100%"},children:[(0,i.jsxs)(o.Hl,{style:{height:320,width:"100%",background:"#f9f9f9"},camera:{position:[0,0,5],fov:50},children:[(0,i.jsx)("ambientLight",{intensity:.7}),(0,i.jsx)("directionalLight",{position:[5,5,5],intensity:.7}),(0,i.jsxs)("mesh",{position:[0,0,0],children:[(0,i.jsx)("boxGeometry",{args:[2,2.2,.5]}),(0,i.jsx)("meshStandardMaterial",{color:t})]}),(0,i.jsxs)("mesh",{position:[-1.2,.5,0],rotation:[0,0,Math.PI/2],children:[(0,i.jsx)("cylinderGeometry",{args:[.25,.25,1,32]}),(0,i.jsx)("meshStandardMaterial",{color:t})]}),(0,i.jsxs)("mesh",{position:[1.2,.5,0],rotation:[0,0,Math.PI/2],children:[(0,i.jsx)("cylinderGeometry",{args:[.25,.25,1,32]}),(0,i.jsx)("meshStandardMaterial",{color:t})]}),s&&""!==s.trim()&&(0,i.jsx)(n.E,{position:[0,.3,.28],fontSize:.3,color:"#222",anchorX:"center",anchorY:"middle",maxWidth:1.5,children:s}),(0,i.jsx)(r.N,{enablePan:!0,enableZoom:!0,enableRotate:!0})]}),(0,i.jsxs)("div",{className:"flex flex-col items-center mt-2",children:[(0,i.jsx)("button",{className:"bg-[#ffd600] text-[#171717] font-semibold px-4 py-1 rounded-full shadow hover:bg-[#ffe066] transition mb-2",onClick:()=>c(e=>!e),children:d?"Close Color Picker":"Pick Custom Color"}),d&&(0,i.jsx)(l.jI,{color:t,onChange:h,style:{width:180,height:120}})]})]})}}}]);