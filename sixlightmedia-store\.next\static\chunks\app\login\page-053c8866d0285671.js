(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{2657:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3843:(e,r,t)=>{"use strict";t.d(r,{e9:()=>i,i3:()=>a});var s=t(9509);let a={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh",VERIFY_EMAIL:"/auth/verify-email",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",RESEND_VERIFICATION:"/auth/resend-verification"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",UPDATE_PROFILE:"/user/profile",UPLOAD_PROFILE_IMAGE:"/user/upload-profile-image",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};s.env.IMAGEKIT_PRIVATE_KEY,s.env.NEXT_PUBLIC_GA_ID,s.env.NEXT_PUBLIC_GA_ID;let i=e=>{let r=a.BASE_URL.replace(/\/$/,""),t=e.startsWith("/")?e:"/".concat(e);return"".concat(r).concat(t)}},5905:(e,r,t)=>{Promise.resolve().then(t.bind(t,9690))},8749:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(5155),a=t(2115),i=t(9958),o=t(6874),n=t.n(o),l=t(6766),d=t(1153),c=t(5339),u=t(8749),p=t(2657);let h=d.z.object({email:d.z.string().email("Please enter a valid email address"),password:d.z.string().min(1,"Password is required")});function m(){let[e,r]=(0,a.useState)({email:"",password:""});useEffect(()=>{console.log("\uD83D\uDD0D Debug Info:"),console.log("API URL:","https://backendapi-sixlight.onrender.com"),console.log("Site URL:","https://sixlightmediastorebeta.netlify.app"),console.log("Environment:","production"),console.log("Current location:",window.location.href)},[]);let[t,o]=(0,a.useState)({}),[d,m]=(0,a.useState)(""),[f,g]=(0,a.useState)(!1),[x,S]=(0,a.useState)(!1),E=(e,s)=>{r(r=>({...r,[e]:s})),t[e]&&o(r=>({...r,[e]:void 0}))};async function y(r){r.preventDefault(),g(!0),m(""),o({});let t=h.safeParse(e);if(!t.success){g(!1);let e={};t.error.errors.forEach(r=>{r.path[0]&&(e[r.path[0]]=r.message)}),o(e);return}try{let r=await (0,i.iD)(e.email,e.password);if(g(!1),r.access_token){var s;localStorage.setItem("token",r.access_token),r.user&&localStorage.setItem("user",JSON.stringify(r.user));let e=new URLSearchParams(window.location.search).get("redirect"),t=(null==(s=r.user)?void 0:s.role)==="ADMIN";if(e&&e.startsWith("/admin/")&&!t)return void m("You don't have permission to access the admin area");let a=e||"/user/dashboard";console.log("Redirecting to: ".concat(a)),setTimeout(()=>{window.location.href=a},100)}else m(r.error||"Login failed")}catch(e){g(!1),m("Network error. Please try again.")}}return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100",children:[(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,s.jsx)("h2",{className:"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Sign In"}),(0,s.jsxs)("form",{onSubmit:y,className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("input",{id:"email",name:"email",value:e.email,onChange:e=>E("email",e.target.value),placeholder:"Email",type:"email",autoComplete:"email",className:"w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition ".concat(t.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),"aria-invalid":!!t.email,"aria-describedby":t.email?"email-error":void 0}),t.email&&(0,s.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,s.jsx)(c.A,{size:16}),t.email]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",value:e.password,onChange:e=>E("password",e.target.value),type:x?"text":"password",placeholder:"Password",autoComplete:"current-password",className:"w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition ".concat(t.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),"aria-invalid":!!t.password,"aria-describedby":t.password?"password-error":void 0}),(0,s.jsx)("button",{type:"button",onClick:()=>S(!x),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":x?"Hide password":"Show password",children:x?(0,s.jsx)(u.A,{size:20}):(0,s.jsx)(p.A,{size:20})})]}),t.password&&(0,s.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,s.jsx)(c.A,{size:16}),t.password]})]}),(0,s.jsx)("button",{type:"submit",className:"w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ".concat(f?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"),disabled:f,children:f?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Signing in..."})]}):"Sign In"}),d&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,s.jsx)(c.A,{size:16}),d]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-600 space-y-2",children:[(0,s.jsx)("div",{children:(0,s.jsx)(n(),{href:"/forgot-password",className:"text-blue-600 hover:text-blue-800 font-medium",children:"Forgot your password?"})}),(0,s.jsxs)("div",{children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/register",className:"text-red-700 font-semibold hover:underline",children:"Register"})]})]})]})})}},9958:(e,r,t)=>{"use strict";t.d(r,{A$:()=>o,BD:()=>n,RS:()=>d,iD:()=>a,kz:()=>i,xw:()=>l});var s=t(3843);async function a(e,r){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:r})})).json()}async function i(e,r,t){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:r,name:t})})).json()}async function o(e){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.VERIFY_EMAIL),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e})})).json()}async function n(e){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.FORGOT_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}async function l(e,r){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.RESET_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e,password:r})})).json()}async function d(e){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.RESEND_VERIFICATION),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}}},e=>{var r=r=>e(e.s=r);e.O(0,[766,874,619,441,684,358],()=>r(5905)),_N_E=e.O()}]);