const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

async function testDatabase() {
  console.log('🔍 Testing Database Connection...');
  console.log(`📊 Database URL: ${process.env.DATABASE_URL?.substring(0, 50)}...`);

  const prisma = new PrismaClient();

  try {
    console.log('🔌 Attempting to connect to database...');
    await prisma.$connect();
    console.log('✅ Database connection successful!');

    console.log('📊 Testing query...');
    const userCount = await prisma.user.count();
    console.log(`👥 Total users in database: ${userCount}`);

    console.log('🔍 Testing recent users...');
    const recentUsers = await prisma.user.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        createdAt: true,
      },
    });
    
    console.log('📋 Recent users:');
    recentUsers.forEach(user => {
      console.log(`   - ${user.email} (verified: ${user.emailVerified}) - ${user.createdAt}`);
    });

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    
    if (error.code === 'P1001') {
      console.error('🌐 Network connectivity issue. Possible causes:');
      console.error('   1. Database is sleeping (Neon auto-sleeps after inactivity)');
      console.error('   2. Internet connection issue');
      console.error('   3. Database server is down');
      console.error('   4. Connection string is incorrect');
      console.error('');
      console.error('💡 Solutions:');
      console.error('   1. Visit your Neon dashboard to wake up the database');
      console.error('   2. Check your internet connection');
      console.error('   3. Verify your DATABASE_URL in .env file');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testDatabase();
