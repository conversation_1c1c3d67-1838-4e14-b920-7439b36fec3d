"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("./auth/jwt-auth.guard");
const prisma_service_1 = require("./prisma.service");
const bcrypt = require("bcryptjs");
let UserController = class UserController {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getDashboard(req) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId) {
            throw new Error('User ID not found in request');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: Number(userId) },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                profileImage: true,
            },
        });
        const orders = await this.prisma.order.findMany({
            where: { userId: Number(userId) },
            include: {
                product: { select: { id: true, name: true, price: true, image: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
        return {
            user,
            orders,
        };
    }
    async changePassword(req, body) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId)
            throw new Error('User ID not found in request');
        if (!body.oldPassword || !body.newPassword) {
            return { success: false, message: 'Old and new password required.' };
        }
        const user = await this.prisma.user.findUnique({
            where: { id: Number(userId) },
            select: { password: true },
        });
        if (!user)
            return { success: false, message: 'User not found.' };
        const isMatch = await bcrypt.compare(body.oldPassword, user.password);
        if (!isMatch) {
            return { success: false, message: 'Old password is incorrect.' };
        }
        const hashed = await bcrypt.hash(body.newPassword, 10);
        await this.prisma.user.update({
            where: { id: Number(userId) },
            data: { password: hashed },
        });
        return { success: true };
    }
    async updateProfile(req, body) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId)
            throw new Error('User ID not found in request');
        const data = {};
        if (body.name !== undefined)
            data.name = body.name;
        if (body.profileImage !== undefined)
            data.profileImage = body.profileImage;
        return this.prisma.user.update({
            where: { id: Number(userId) },
            data,
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                profileImage: true,
            },
        });
    }
    async deleteAccount(req) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId)
            throw new Error('User ID not found in request');
        await this.prisma.user.delete({ where: { id: Number(userId) } });
        return { success: true };
    }
    async createOrder(req, body) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId) {
            throw new Error('User ID not found in request');
        }
        const { productId, customerName, customerPhone, customerAddress, customColor, customText, isCustomized, customizationData, customizationPreview, quantity = 1, } = body;
        const product = await this.prisma.product.findUnique({
            where: { id: Number(productId) },
        });
        if (!product) {
            throw new Error('Product not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: Number(userId) },
            select: { email: true },
        });
        const unitPrice = product.price;
        const totalPrice = unitPrice * quantity;
        const order = await this.prisma.order.create({
            data: {
                userId: Number(userId),
                productId: Number(productId),
                customerName,
                customerPhone,
                customerEmail: user?.email || '',
                customerAddress,
                customColor: isCustomized ? customColor : null,
                customText: isCustomized ? customText : null,
                isCustomized: Boolean(isCustomized),
                customizationData: isCustomized ? customizationData : null,
                customizationPreview: isCustomized ? customizationPreview : null,
                quantity,
                unitPrice,
                totalPrice,
            },
            include: {
                product: { select: { id: true, name: true, price: true, image: true } },
                user: { select: { id: true, name: true, email: true } },
            },
        });
        return order;
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)('dashboard'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Patch)('change-password'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Patch)('profile'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.Delete)('delete'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "deleteAccount", null);
__decorate([
    (0, common_1.Post)('orders'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "createOrder", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)('user'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserController);
//# sourceMappingURL=user.controller.js.map