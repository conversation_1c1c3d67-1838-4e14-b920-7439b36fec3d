import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import <PERSON>rip<PERSON> from "next/script";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default:
      "Six Light Media Store - Premium Custom Products & Personalized Gifts",
    template: "%s | Six Light Media Store",
  },
  description:
    "Discover premium custom products and personalized gifts at Six Light Media Store. From engraved bottles to custom t-shirts, we create unique items tailored just for you. Fast delivery, premium quality, 100% satisfaction guaranteed.",
  keywords: [
    "custom products",
    "personalized gifts",
    "engraved bottles",
    "custom t-shirts",
    "Six Light Media",
    "premium quality",
    "custom printing",
    "personalized items",
    "unique gifts",
    "custom design",
    "Zambia",
    "e-commerce",
  ],
  authors: [{ name: "Six Light Media" }],
  creator: "Six Light Media",
  publisher: "Six Light Media",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
  ),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title:
      "Six Light Media Store - Premium Custom Products & Personalized Gifts",
    description:
      "Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.",
    siteName: "Six Light Media Store",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Six Light Media Store - Premium Custom Products",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title:
      "Six Light Media Store - Premium Custom Products & Personalized Gifts",
    description:
      "Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.",
    images: ["/og-image.jpg"],
    creator: "@sixlightmedia",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code", // Replace with actual verification code
    // yandex: "your-yandex-verification-code",
    // yahoo: "your-yahoo-verification-code",
  },
  category: "e-commerce",

  // PWA Configuration
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Six Light Media Store",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        {/* Additional SEO and Performance Meta Tags */}
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=5"
        />
        <meta name="theme-color" content="#1a237e" />
        <meta name="msapplication-TileColor" content="#1a237e" />

        {/* Modern PWA Meta Tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta
          name="apple-mobile-web-app-title"
          content="Six Light Media Store"
        />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* Favicon and App Icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* DNS Prefetch for better performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

        {/* Structured Data for Rich Snippets */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "Six Light Media Store",
              description: "Premium custom products and personalized gifts",
              url: process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
              logo: `${
                process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
              }/6 Light Logo.png`,
              contactPoint: {
                "@type": "ContactPoint",
                contactType: "customer service",
                availableLanguage: "English",
              },
              sameAs: [
                "https://facebook.com/sixlightmedia",
                "https://twitter.com/sixlightmedia",
                "https://instagram.com/sixlightmedia",
              ],
            }),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}

        {/* Analytics and Performance Monitoring */}
        {process.env.NODE_ENV === "production" &&
          process.env.NEXT_PUBLIC_GA_ID && (
            <>
              {/* Google Analytics - Replace NEXT_PUBLIC_GA_ID with your actual GA4 ID */}
              <Script
                src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
                strategy="afterInteractive"
              />
              <Script id="google-analytics" strategy="afterInteractive">
                {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
              `}
              </Script>
            </>
          )}
      </body>
    </html>
  );
}
