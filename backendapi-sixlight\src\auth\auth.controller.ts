import {
  Controller,
  Post,
  Body,
  Request,
  UseGuards,
  Get,
  Response,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './jwt-auth.guard';
import { Response as ExpressResponse } from 'express';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(
    @Body() body: { email: string; password: string },
    @Response() res: ExpressResponse,
  ) {
    const user = await this.authService.validateUser(body.email, body.password);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const loginResult = await this.authService.login(user);

    // Set JWT in httpOnly cookie for security
    res.cookie('jwt', loginResult.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    });

    // Return user data (without token for security)
    return res.json({
      user: loginResult.user,
      message: 'Login successful',
    });
  }

  @Post('register')
  async register(
    @Body()
    body: {
      email: string;
      password: string;
      name?: string;
      role?: string;
    },
  ) {
    return this.authService.register(body);
  }

  @Post('me')
  @UseGuards(JwtAuthGuard)
  async me(@Request() req) {
    return req.user;
  }

  @Get('verify')
  @UseGuards(JwtAuthGuard)
  async verify(@Request() req) {
    // Return user data for frontend auth verification
    return {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role,
      name: req.user.name,
      profileImage: req.user.profileImage,
    };
  }

  @Post('logout')
  async logout(@Response() res: ExpressResponse) {
    // Clear the JWT cookie
    res.clearCookie('jwt', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    return res.json({ message: 'Logged out successfully' });
  }
}
