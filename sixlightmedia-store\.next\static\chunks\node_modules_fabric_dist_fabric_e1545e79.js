(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/fabric/dist/fabric.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fabric_dist_fabric_f47627fe.js",
  "static/chunks/node_modules_next_dist_compiled_buffer_index_feebad72.js",
  "static/chunks/node_modules_fabric_dist_fabric_9e1dc158.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fabric/dist/fabric.js [app-client] (ecmascript)");
    });
});
}}),
}]);