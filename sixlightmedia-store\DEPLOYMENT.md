# Deployment Guide - Six Light Media Store

This guide explains how to deploy your Six Light Media Store to production with the correct environment configuration.

## Environment Configuration

### Development vs Production

The application automatically detects the environment and uses the appropriate backend URL:

- **Development**: `http://localhost:3001`
- **Production**: `https://backendapi-sixlight.onrender.com`

### Environment Files

1. **`.env.development`** - Used in development mode
2. **`.env.production`** - Used in production builds
3. **`.env.local`** - Local overrides (not committed to git)

## Deployment Platforms

### Vercel (Recommended for Next.js)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard:

```bash
NEXT_PUBLIC_API_URL=https://backendapi-sixlight.onrender.com
NEXT_PUBLIC_SITE_URL=https://your-vercel-app.vercel.app
NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/fwbvmq9re
NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY=public_kFR0vTVL7DIDI8YW9eF6/luGjB4=
IMAGEKIT_PRIVATE_KEY=private_jtsBIKXyuTkkHzHJOBSHLqbyK74=
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

3. **Deploy** - Vercel will automatically build and deploy

### Netlify

1. **Connect your repository** to Netlify
2. **Set build command**: `npm run build`
3. **Set publish directory**: `.next`
4. **Set environment variables** in Netlify dashboard (same as above)

### Other Platforms

For other platforms, ensure you set the environment variables in their respective dashboards or configuration files.

## Environment Variables Reference

### Required Variables

| Variable | Development | Production | Description |
|----------|-------------|------------|-------------|
| `NEXT_PUBLIC_API_URL` | `http://localhost:3001` | `https://backendapi-sixlight.onrender.com` | Backend API URL |
| `NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT` | Same for both | Same for both | ImageKit endpoint |
| `NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY` | Same for both | Same for both | ImageKit public key |
| `IMAGEKIT_PRIVATE_KEY` | Same for both | Same for both | ImageKit private key |

### Optional Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_SITE_URL` | Your site's URL | `https://yourdomain.com` |
| `NEXT_PUBLIC_GA_ID` | Google Analytics ID | `G-XXXXXXXXXX` |
| `GOOGLE_VERIFICATION_CODE` | Google Search Console verification | `your-verification-code` |

## Backend Configuration

Ensure your backend (https://backendapi-sixlight.onrender.com) has the correct CORS configuration:

```typescript
// In your backend main.ts or app configuration
app.enableCors({
  origin: [
    'https://your-frontend-domain.com',
    'https://your-vercel-app.vercel.app',
    // Add your actual frontend URLs
  ],
  credentials: true,
});
```

## Testing Production Build Locally

To test the production build locally:

```bash
# Build the application
npm run build

# Start the production server
npm start
```

## Troubleshooting

### Common Issues

1. **API calls failing in production**
   - Check that `NEXT_PUBLIC_API_URL` is set correctly
   - Verify backend CORS configuration includes your frontend domain

2. **Images not loading**
   - Verify ImageKit environment variables are set
   - Check ImageKit dashboard for usage limits

3. **Analytics not working**
   - Ensure `NEXT_PUBLIC_GA_ID` is set in production
   - Verify Google Analytics configuration

### Debug Information

In development mode, you can check the configuration by adding this to any component:

```typescript
import { getDebugInfo } from '@/lib/config';

console.log('Debug Info:', getDebugInfo());
```

## Security Notes

- Never commit `.env.local` or files containing secrets
- Use environment variables for all sensitive data
- Regularly rotate API keys and secrets
- Use HTTPS in production for all external API calls

## Performance Optimization

- Enable analytics only in production
- Use appropriate timeout settings for API calls
- Implement proper error handling for network requests
- Consider implementing request caching for frequently accessed data
