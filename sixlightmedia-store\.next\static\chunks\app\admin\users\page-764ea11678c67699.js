(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{657:(e,t,a)=>{Promise.resolve().then(a.bind(a,2899))},2899:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(5155),r=a(2115),n=a(3843);function i(){let[e,t]=(0,r.useState)([]),[a,i]=(0,r.useState)(!0),[o,l]=(0,r.useState)(""),[d,c]=(0,r.useState)(null);async function u(e,a){c(e);let s=localStorage.getItem("token"),r=await fetch((0,n.e9)("".concat(n.i3.ENDPOINTS.ADMIN.USERS,"/").concat(e,"/role")),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({role:a})});c(null),r.ok?t(t=>t.map(t=>t.id===e?{...t,role:a}:t)):alert("Failed to update user role")}async function h(e){if(!confirm("Are you sure you want to delete this user?"))return;let a=localStorage.getItem("token");(await fetch((0,n.e9)("".concat(n.i3.ENDPOINTS.ADMIN.USERS,"/").concat(e)),{method:"DELETE",headers:{Authorization:"Bearer ".concat(a)}})).ok?t(t=>t.filter(t=>t.id!==e)):alert("Failed to delete user")}return((0,r.useEffect)(()=>{let e=localStorage.getItem("token");fetch((0,n.e9)(n.i3.ENDPOINTS.ADMIN.USERS),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{Array.isArray(e)?t(e):Array.isArray(e.users)?t(e.users):t([])}).catch(()=>l("Failed to load users")).finally(()=>i(!1))},[]),a)?(0,s.jsx)("div",{className:"text-center mt-16",children:"Loading..."}):o?(0,s.jsx)("div",{className:"text-red-600 text-center mt-16",children:o}):(0,s.jsxs)("div",{className:"max-w-2xl mx-auto mt-10 bg-white rounded-2xl shadow p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center text-[#1a237e]",children:"Manage Users"}),(0,s.jsx)("ul",{className:"divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("li",{className:"py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-[#171717]",children:e.name}),(0,s.jsx)("span",{className:"block text-gray-500 text-sm",children:e.email})]}),(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsxs)("select",{className:"border rounded px-2 py-1 text-sm",value:e.role||"USER",onChange:t=>u(e.id,t.target.value),disabled:d===e.id,children:[(0,s.jsx)("option",{value:"USER",children:"User"}),(0,s.jsx)("option",{value:"ADMIN",children:"Admin"})]}),(0,s.jsx)("button",{className:"px-3 py-1 rounded bg-red-600 text-white text-xs font-semibold hover:bg-red-700 transition",onClick:()=>h(e.id),disabled:d===e.id,children:"Delete"})]})]},e.id))})]})}},3843:(e,t,a)=>{"use strict";a.d(t,{e9:()=>n,i3:()=>r});var s=a(9509);let r={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};s.env.IMAGEKIT_PRIVATE_KEY,s.env.NEXT_PUBLIC_GA_ID,s.env.NEXT_PUBLIC_GA_ID;let n=e=>{let t=r.BASE_URL.replace(/\/$/,""),a=e.startsWith("/")?e:"/".concat(e);return"".concat(t).concat(a)}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(657)),_N_E=e.O()}]);