import { getApiUrl, API_CONFIG } from "@/lib/config";

export async function login(email: string, password: string) {
  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    credentials: "include", // Important: This allows cookies to be sent/received
    body: JSON.stringify({ email, password }),
  });
  return res.json();
}

export async function register(email: string, password: string, name?: string) {
  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    credentials: "include", // Important: This allows cookies to be sent/received
    body: JSON.stringify({ email, password, name }),
  });
  return res.json();
}

export async function verifyEmail(token: string) {
  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.VERIFY_EMAIL), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    credentials: "include",
    body: JSON.stringify({ token }),
  });
  return res.json();
}

export async function forgotPassword(email: string) {
  const res = await fetch(
    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.FORGOT_PASSWORD),
    {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
      body: JSON.stringify({ email }),
    }
  );
  return res.json();
}

export async function resetPassword(token: string, password: string) {
  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    credentials: "include",
    body: JSON.stringify({ token, password }),
  });
  return res.json();
}

export async function resendVerification(email: string) {
  const res = await fetch(
    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESEND_VERIFICATION),
    {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
      body: JSON.stringify({ email }),
    }
  );
  return res.json();
}
