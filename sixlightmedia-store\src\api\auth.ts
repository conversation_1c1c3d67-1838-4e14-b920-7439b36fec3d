import { getApiUrl, API_CONFIG } from "@/lib/config";

export async function login(email: string, password: string) {
  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password }),
  });
  return res.json();
}

export async function register(email: string, password: string, name?: string) {
  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password, name }),
  });
  return res.json();
}
