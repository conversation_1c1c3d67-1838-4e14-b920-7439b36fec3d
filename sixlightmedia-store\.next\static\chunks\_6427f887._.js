(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ProductCustomizerFallback.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_860f4e17._.js",
  "static/chunks/src_components_ProductCustomizerFallback_tsx_36108e43._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ProductCustomizerFallback.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/fabric/dist/fabric.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fabric_dist_fabric_f47627fe.js",
  "static/chunks/node_modules_next_dist_compiled_buffer_index_feebad72.js",
  "static/chunks/node_modules_fabric_dist_fabric_367793aa.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fabric/dist/fabric.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/ProductCustomizer.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fabric_dist_fabric_f0727140.js",
  "static/chunks/src_components_ProductCustomizer_tsx_5b9e524f._.js",
  "static/chunks/node_modules_fabric_dist_fabric_f47627fe.js",
  "static/chunks/node_modules_8598f05f._.js",
  "static/chunks/src_components_ProductCustomizer_tsx_367793aa._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ProductCustomizer.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);