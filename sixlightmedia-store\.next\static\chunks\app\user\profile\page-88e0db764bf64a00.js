(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[614],{4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},6736:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(5155),s=r(2115),o=r(6766),l=r(6874),n=r.n(l),i=r(4615),d=r(3389);let c="http://localhost:3001";function u(){let[e,t]=(0,s.useState)(null),[r,l]=(0,s.useState)(""),[u,h]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),[p,f]=(0,s.useState)(0),[g,b]=(0,s.useState)(!1),[w,j]=(0,s.useState)(!1),[v,y]=(0,s.useState)({oldPassword:"",newPassword:""}),N=(0,s.useRef)(null),[k,C]=(0,s.useState)(""),[L,A]=(0,s.useState)(""),[P,S]=(0,s.useState)(0),[E,D]=(0,s.useState)(!1),[B,I]=(0,s.useState)(!0),M=()=>{S(JSON.parse(localStorage.getItem("cart")||"[]").length)},F=()=>{D(!0)};async function W(a){a.preventDefault();let s=localStorage.getItem("token");if(s&&e){b(!0),C(""),A("");try{(await fetch("".concat(c,"/user/profile"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({name:r,profileImage:u})})).ok?(t(e=>e?{...e,name:r,profileImage:u}:e),A("Profile updated successfully"),window.dispatchEvent(new Event("profileImageUpdated"))):C("Failed to update profile")}catch(e){C("Failed to update profile")}finally{b(!1)}}}async function z(e){var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){x(!0),f(0),C(""),A("");try{f(10);let e=await fetch("/api/imagekit-auth"),t=await e.json();f(20);let a=new FormData;a.append("file",r),a.append("fileName",r.name),a.append("publicKey","public_kFR0vTVL7DIDI8YW9eF6/luGjB4="),a.append("signature",t.signature),a.append("expire",t.expire),a.append("token",t.token),a.append("folder","/profile-images"),f(30);let s=new Promise((e,t)=>{let r=new XMLHttpRequest;r.upload.addEventListener("progress",e=>{if(e.lengthComputable){let t=Math.round(e.loaded/e.total*70)+30;f(t)}}),r.addEventListener("load",()=>{if(200===r.status)try{let t=JSON.parse(r.responseText);e(t)}catch(e){t(Error("Invalid response format"))}else t(Error("Upload failed with status ".concat(r.status)))}),r.addEventListener("error",()=>{t(Error("Network error during upload"))}),r.open("POST","https://upload.imagekit.io/api/v1/files/upload"),r.send(a)}),o=await s;o.url?(h(o.url),f(100),A("Profile image uploaded successfully!")):C("Image upload failed: No URL returned")}catch(e){console.error("ImageKit error:",e),C("Image upload failed: "+(e instanceof Error?e.message:"Unknown error"))}finally{x(!1),setTimeout(()=>f(0),1e3)}}}async function O(e){e.preventDefault();let t=localStorage.getItem("token");if(t){j(!0),C(""),A("");try{(await fetch("".concat(c,"/user/change-password"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(v)})).ok?(A("Password changed successfully"),y({oldPassword:"",newPassword:""})):C("Failed to change password")}catch(e){C("Failed to change password")}finally{j(!1)}}}async function T(){let e=localStorage.getItem("token");e&&confirm("Are you sure you want to delete your account? This cannot be undone.")&&((await fetch("".concat(c,"/user/delete"),{method:"DELETE",headers:{Authorization:"Bearer ".concat(e)}})).ok?(alert("Account deleted"),localStorage.removeItem("token"),window.location.href="/"):C("Failed to delete account"))}return((0,s.useEffect)(()=>{M()},[]),(0,s.useEffect)(()=>{let e=localStorage.getItem("token");if(!e){C("Not authenticated"),I(!1);return}fetch("".concat(c,"/user/dashboard"),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{e&&e.user?(t(e.user),l(e.user.name||""),h(e.user.profileImage||"")):C(e.error||"Failed to load user info"),I(!1)}).catch(()=>{C("Failed to load user info"),I(!1)})},[]),B)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(i.A,{cartCount:P,onCartClick:F}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading profile..."})]})})]}):k?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(i.A,{cartCount:P,onCartClick:F}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Error"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:k}),(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Home"]})]})})]}):e?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(i.A,{cartCount:P,onCartClick:F}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,a.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Profile"}),(0,a.jsx)("span",{className:"text-white",children:" Settings"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto",children:"Manage your account settings and personalize your experience"}),(0,a.jsxs)(n(),{href:"/user/dashboard",className:"inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-lg text-white font-semibold rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Dashboard"]})]})})]}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[L&&(0,a.jsxs)("div",{className:"mb-8 p-4 bg-green-50 border border-green-200 text-green-700 rounded-2xl flex items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),L]}),k&&(0,a.jsxs)("div",{className:"mb-8 p-4 bg-red-50 border border-red-200 text-red-700 rounded-2xl flex items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01"})}),k]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"\uD83D\uDCF8 Profile Picture"}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.default,{src:u||"/usericon.png",alt:"Profile",width:120,height:120,className:"w-30 h-30 rounded-full object-cover border-4 border-gray-200 shadow-lg transition-opacity ".concat(m?"opacity-50":"opacity-100")}),m&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"bg-white/90 rounded-full p-3",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})})})]}),(0,a.jsx)("button",{className:"absolute bottom-2 right-2 w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 ".concat(m?"opacity-50 cursor-not-allowed":"hover:scale-110"),onClick:()=>{var e;return!m&&(null==(e=N.current)?void 0:e.click())},title:m?"Uploading...":"Change profile picture",disabled:m,children:(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsx)("input",{type:"file",accept:"image/*",className:"hidden",ref:N,onChange:z,disabled:m})]}),m&&p>0&&(0,a.jsxs)("div",{className:"w-full max-w-md mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,a.jsx)("span",{children:"Uploading image..."}),(0,a.jsxs)("span",{children:[p,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out",style:{width:"".concat(p,"%")}})})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:e.name||e.email}),(0,a.jsx)("p",{className:"text-gray-600",children:e.email}),(0,a.jsx)("span",{className:"inline-block mt-2 px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full",children:e.role})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"✏️ Edit Profile"}),(0,a.jsxs)("form",{onSubmit:W,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"Display Name"}),(0,a.jsx)("input",{type:"text",value:r,onChange:e=>l(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",placeholder:"Enter your display name",maxLength:40,required:!0})]}),(0,a.jsx)("button",{type:"submit",className:"w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg ".concat(g||m?"opacity-50 cursor-not-allowed":"hover:shadow-xl transform hover:-translate-y-1"),disabled:g||m,children:g?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,a.jsx)("span",{children:"Saving Profile..."})]}):"\uD83D\uDCBE Save Profile"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-8 border border-gray-100",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"\uD83D\uDD12 Change Password"}),(0,a.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"Current Password"}),(0,a.jsx)("input",{type:"password",placeholder:"Enter your current password",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",value:v.oldPassword,onChange:e=>y(t=>({...t,oldPassword:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-2",children:"New Password"}),(0,a.jsx)("input",{type:"password",placeholder:"Enter your new password",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",value:v.newPassword,onChange:e=>y(t=>({...t,newPassword:e.target.value})),required:!0})]}),(0,a.jsx)("button",{type:"submit",className:"w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg ".concat(w?"opacity-50 cursor-not-allowed":"hover:shadow-xl transform hover:-translate-y-1"),disabled:w,children:w?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,a.jsx)("span",{children:"Changing Password..."})]}):"\uD83D\uDD10 Change Password"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-red-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-red-900 mb-6 flex items-center",children:"⚠️ Danger Zone"}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-900 mb-2",children:"Delete Account"}),(0,a.jsx)("p",{className:"text-red-700 mb-4",children:"Once you delete your account, there is no going back. Please be certain."}),(0,a.jsx)("button",{onClick:T,className:"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold rounded-2xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"\uD83D\uDDD1️ Delete Account"})]})]})]}),(0,a.jsx)(d.A,{isOpen:E,onClose:()=>{D(!1),M()}})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(i.A,{cartCount:P,onCartClick:F}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading user data..."})]})})]})}},8086:(e,t,r)=>{Promise.resolve().then(r.bind(r,6736))},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:c="",children:u,iconNode:h,...m}=e;return(0,a.createElement)("svg",{ref:t,...d,width:s,height:s,stroke:r,strokeWidth:l?24*Number(o)/Number(s):o,className:n("lucide",c),...!u&&!i(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,o)=>{let{className:i,...d}=r;return(0,a.createElement)(c,{ref:o,iconNode:t,className:n("lucide-".concat(s(l(e))),"lucide-".concat(e),i),...d})});return r.displayName=l(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,211,441,684,358],()=>t(8086)),_N_E=e.O()}]);