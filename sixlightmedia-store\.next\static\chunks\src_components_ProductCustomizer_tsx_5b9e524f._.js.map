{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/ProductCustomizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { useEffect, useRef, useState, useCallback, useMemo } from \"react\";\nimport dynamic from \"next/dynamic\";\nimport {\n  Type,\n  Image as ImageIcon,\n  Palette,\n  RotateCw,\n  Move,\n  Trash2,\n  Download,\n  Upload,\n  Undo,\n  Redo,\n  Save,\n  Square,\n  Circle,\n  Triangle,\n} from \"lucide-react\";\n\n// Dynamic import for fabric to avoid SSR issues\nlet fabric: any = null;\nif (typeof window !== \"undefined\") {\n  fabric = require(\"fabric\").fabric;\n}\n\n// Debounce utility function\nfunction debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\ninterface CustomizationData {\n  canvasData?: string;\n  preview?: string;\n}\n\n// Fabric.js object interface\ninterface FabricObject {\n  selectable?: boolean;\n  evented?: boolean;\n  id?: string;\n  type?: string;\n  fill?: string;\n  stroke?: string;\n  strokeWidth?: number;\n  fontSize?: number;\n  fontFamily?: string;\n  textAlign?: string;\n  fontWeight?: string;\n  fontStyle?: string;\n  textDecoration?: string;\n  angle?: number;\n  left?: number;\n  top?: number;\n  width?: number;\n  height?: number;\n  scaleX?: number;\n  scaleY?: number;\n  set?: (props: any) => void;\n  scale?: (value: number) => void;\n  center?: () => void;\n  rotate?: (angle: number) => void;\n}\n\ninterface ProductCustomizerProps {\n  productImage: string;\n  onCustomizationChange: (customization: CustomizationData) => void;\n  initialCustomization?: CustomizationData | null;\n  productId?: string | number;\n  // Legacy props for backward compatibility\n  initialData?: CustomizationData;\n  onSave?: (data: CustomizationData) => void;\n  onCanvasReady?: (canvas: any) => void;\n  productImageUrl?: string;\n  productName?: string;\n}\n\nexport default function ProductCustomizer({\n  // New interface props\n  productImage,\n  onCustomizationChange,\n  initialCustomization,\n  productId,\n  // Legacy props for backward compatibility\n  initialData,\n  onSave,\n  onCanvasReady,\n  productImageUrl,\n  productName = \"Custom Product\",\n}: ProductCustomizerProps) {\n  // Normalize props to handle both interfaces\n  const normalizedProductImage = productImage || productImageUrl || \"\";\n  const normalizedInitialData = initialCustomization || initialData;\n  const normalizedOnSave = onCustomizationChange || onSave;\n\n  // Debounced auto-save function\n  const debouncedAutoSave = useMemo(\n    () =>\n      debounce((data: CustomizationData) => {\n        if (normalizedOnSave) {\n          normalizedOnSave(data);\n        }\n      }, 500),\n    [normalizedOnSave]\n  );\n  // Canvas and editor state\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [canvas, setCanvas] = useState<any>(null);\n  const [activeTab, setActiveTab] = useState<\n    \"text\" | \"shapes\" | \"images\" | \"colors\"\n  >(\"text\");\n  const [activeObject, setActiveObject] = useState<any>(null);\n  const [canvasHistory, setCanvasHistory] = useState<string[]>([]);\n  const [historyIndex, setHistoryIndex] = useState<number>(-1);\n  const [fabricLoaded, setFabricLoaded] = useState<boolean>(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);\n  const [showUnsavedWarning, setShowUnsavedWarning] = useState<boolean>(false);\n  const [isExporting, setIsExporting] = useState<boolean>(false);\n  const [showSaveSuccess, setShowSaveSuccess] = useState<boolean>(false);\n\n  // Text properties\n  const [textColor, setTextColor] = useState<string>(\"#000000\");\n  const [fontSize, setFontSize] = useState<number>(24);\n  const [fontFamily, setFontFamily] = useState<string>(\"Arial\");\n  const [textAlign, setTextAlign] = useState<string>(\"left\");\n  const [fontWeight, setFontWeight] = useState<string>(\"normal\");\n  const [fontStyle, setFontStyle] = useState<string>(\"normal\");\n  const [textDecoration, setTextDecoration] = useState<string>(\"none\");\n\n  // Available fonts\n  const fonts = [\n    \"Arial\",\n    \"Helvetica\",\n    \"Times New Roman\",\n    \"Courier New\",\n    \"Verdana\",\n    \"Georgia\",\n    \"Palatino\",\n    \"Garamond\",\n    \"Bookman\",\n    \"Comic Sans MS\",\n    \"Trebuchet MS\",\n    \"Impact\",\n  ];\n\n  // Available colors\n  const colors = [\n    \"#000000\", // Black\n    \"#FFFFFF\", // White\n    \"#FF0000\", // Red\n    \"#00FF00\", // Green\n    \"#0000FF\", // Blue\n    \"#FFFF00\", // Yellow\n    \"#FF00FF\", // Magenta\n    \"#00FFFF\", // Cyan\n    \"#FFA500\", // Orange\n    \"#800080\", // Purple\n    \"#008000\", // Dark Green\n    \"#800000\", // Maroon\n    \"#008080\", // Teal\n    \"#000080\", // Navy\n    \"#FFC0CB\", // Pink\n    \"#A52A2A\", // Brown\n    \"#808080\", // Gray\n    \"#C0C0C0\", // Silver\n  ];\n\n  // Sample images for quick insertion\n  const sampleImages = [\n    \"/sample-images/logo1.png\",\n    \"/sample-images/logo2.png\",\n    \"/sample-images/shape1.svg\",\n    \"/sample-images/shape2.svg\",\n    \"/sample-images/pattern1.png\",\n    \"/sample-images/pattern2.png\",\n  ];\n\n  // Initialize Fabric canvas\n  useEffect(() => {\n    if (!canvasRef.current || typeof window === \"undefined\") return;\n\n    // Load Fabric.js dynamically\n    const loadFabric = async () => {\n      if (!fabric) {\n        const fabricModule = await import(\"fabric\");\n        fabric = fabricModule.fabric;\n      }\n\n      // Create canvas\n      const fabricCanvas = new fabric.Canvas(canvasRef.current, {\n        width: 500,\n        height: 500,\n        backgroundColor: \"#FFFFFF\",\n        preserveObjectStacking: true,\n      });\n\n      // Set up event listeners\n      fabricCanvas.on(\"object:modified\", handleCanvasChange);\n      fabricCanvas.on(\"object:added\", handleCanvasChange);\n      fabricCanvas.on(\"object:removed\", handleCanvasChange);\n      fabricCanvas.on(\"selection:created\", handleSelectionChange);\n      fabricCanvas.on(\"selection:updated\", handleSelectionChange);\n      fabricCanvas.on(\"selection:cleared\", () => setActiveObject(null));\n\n      // Load initial data if provided\n      if (normalizedInitialData?.canvasData) {\n        fabricCanvas.loadFromJSON(normalizedInitialData.canvasData, () => {\n          fabricCanvas.renderAll();\n          saveToHistory(fabricCanvas);\n        });\n      } else if (normalizedProductImage) {\n        // Load product image as background\n        fabric.Image.fromURL(\n          normalizedProductImage,\n          (img: any) => {\n            // Scale image to fit canvas while maintaining aspect ratio\n            const scale = Math.min(\n              fabricCanvas.width / img.width,\n              fabricCanvas.height / img.height\n            );\n            img.scale(scale * 0.8);\n\n            // Center the image\n            img.set({\n              left: fabricCanvas.width / 2,\n              top: fabricCanvas.height / 2,\n              originX: \"center\",\n              originY: \"center\",\n              selectable: false, // Make background non-selectable\n              evented: false, // Disable events on background\n            });\n\n            fabricCanvas.add(img);\n            fabricCanvas.sendToBack(img);\n            fabricCanvas.renderAll();\n            saveToHistory(fabricCanvas);\n          },\n          { crossOrigin: \"anonymous\" }\n        );\n      }\n\n      setCanvas(fabricCanvas);\n      setFabricLoaded(true);\n\n      // Notify parent component that canvas is ready\n      if (onCanvasReady) {\n        onCanvasReady(fabricCanvas);\n      }\n\n      // Save initial state to history\n      saveToHistory(fabricCanvas);\n    };\n\n    loadFabric();\n\n    // Cleanup function\n    return () => {\n      if (canvas) {\n        canvas.dispose();\n      }\n    };\n  }, []);\n\n  // Check if canvas is empty (only has background image)\n  const isCanvasEmpty = (): boolean => {\n    if (!canvas) return true;\n    const objects = canvas.getObjects();\n    // Consider canvas empty if it only has the background image (non-selectable)\n    return (\n      objects.filter((obj: FabricObject) => obj.selectable !== false).length ===\n      0\n    );\n  };\n\n  // Handle canvas changes\n  const handleCanvasChange = () => {\n    if (!canvas) return;\n    saveToHistory(canvas);\n    setHasUnsavedChanges(true);\n\n    // Auto-save to parent component (debounced)\n    if (debouncedAutoSave) {\n      // Temporarily disable selection borders for clean preview\n      const activeObj = canvas.getActiveObject();\n      canvas.discardActiveObject();\n      canvas.renderAll();\n\n      // Get data URL for preview\n      const previewUrl = canvas.toDataURL({\n        format: \"png\",\n        quality: 0.8,\n      });\n\n      // Get JSON data\n      const canvasData = JSON.stringify(canvas.toJSON());\n\n      // Re-enable selection\n      if (activeObj) {\n        canvas.setActiveObject(activeObj);\n        canvas.renderAll();\n      }\n\n      // Call debounced callback with updated data\n      debouncedAutoSave({\n        canvasData,\n        preview: previewUrl,\n      });\n    }\n  };\n\n  // Handle selection changes\n  const handleSelectionChange = (e: { selected: FabricObject[] }) => {\n    const selectedObject = e.selected[0];\n    setActiveObject(selectedObject);\n\n    // Update text properties if text is selected\n    if (selectedObject && selectedObject.type === \"text\") {\n      setTextColor(selectedObject.fill || \"#000000\");\n      setFontSize(selectedObject.fontSize || 24);\n      setFontFamily(selectedObject.fontFamily || \"Arial\");\n      setTextAlign(selectedObject.textAlign || \"left\");\n      setFontWeight(selectedObject.fontWeight || \"normal\");\n      setFontStyle(selectedObject.fontStyle || \"normal\");\n      setTextDecoration(selectedObject.textDecoration || \"none\");\n    }\n  };\n\n  // Save canvas state to history\n  const saveToHistory = (fabricCanvas: any) => {\n    if (!fabricCanvas) return;\n\n    const json = JSON.stringify(fabricCanvas.toJSON());\n\n    // If we're not at the end of the history, truncate\n    if (historyIndex < canvasHistory.length - 1) {\n      setCanvasHistory((prev) => prev.slice(0, historyIndex + 1));\n    }\n\n    setCanvasHistory((prev) => [...prev, json]);\n    setHistoryIndex((prev) => prev + 1);\n  };\n\n  // Undo action\n  const handleUndo = useCallback(() => {\n    if (historyIndex <= 0 || !canvas) return;\n\n    const newIndex = historyIndex - 1;\n    setHistoryIndex(newIndex);\n\n    canvas.loadFromJSON(canvasHistory[newIndex], () => {\n      canvas.renderAll();\n      setHasUnsavedChanges(true);\n    });\n  }, [historyIndex, canvas, canvasHistory]);\n\n  // Redo action\n  const handleRedo = useCallback(() => {\n    if (historyIndex >= canvasHistory.length - 1 || !canvas) return;\n\n    const newIndex = historyIndex + 1;\n    setHistoryIndex(newIndex);\n\n    canvas.loadFromJSON(canvasHistory[newIndex], () => {\n      canvas.renderAll();\n      setHasUnsavedChanges(true);\n    });\n  }, [historyIndex, canvasHistory, canvas]);\n\n  // Add text to canvas\n  const addText = (text = \"Double click to edit\") => {\n    if (!canvas || !fabric) return;\n\n    const textObject = new fabric.IText(text, {\n      left: canvas.width / 2,\n      top: canvas.height / 2,\n      originX: \"center\",\n      originY: \"center\",\n      fontFamily: fontFamily,\n      fontSize: fontSize,\n      fill: textColor,\n      textAlign: textAlign,\n      fontWeight: fontWeight,\n      fontStyle: fontStyle,\n      textDecoration: textDecoration,\n    });\n\n    canvas.add(textObject);\n    canvas.setActiveObject(textObject);\n    canvas.renderAll();\n  };\n\n  // Add image to canvas\n  const addImage = (url: string) => {\n    if (!canvas || !fabric) return;\n\n    fabric.Image.fromURL(\n      url,\n      (img: any) => {\n        // Scale image to fit canvas while maintaining aspect ratio\n        const scale = Math.min(\n          canvas.width / 3 / img.width,\n          canvas.height / 3 / img.height\n        );\n        img.scale(scale);\n\n        // Center the image\n        img.set({\n          left: canvas.width / 2,\n          top: canvas.height / 2,\n          originX: \"center\",\n          originY: \"center\",\n        });\n\n        canvas.add(img);\n        canvas.setActiveObject(img);\n        canvas.renderAll();\n      },\n      { crossOrigin: \"anonymous\" }\n    );\n  };\n\n  // Add rectangle to canvas\n  const addRectangle = () => {\n    if (!canvas || !fabric) return;\n\n    const rect = new fabric.Rect({\n      left: canvas.width / 2,\n      top: canvas.height / 2,\n      originX: \"center\",\n      originY: \"center\",\n      width: 100,\n      height: 60,\n      fill: \"#3b82f6\",\n      stroke: \"#1e40af\",\n      strokeWidth: 2,\n    });\n\n    canvas.add(rect);\n    canvas.setActiveObject(rect);\n    canvas.renderAll();\n  };\n\n  // Add circle to canvas\n  const addCircle = () => {\n    if (!canvas || !fabric) return;\n\n    const circle = new fabric.Circle({\n      left: canvas.width / 2,\n      top: canvas.height / 2,\n      originX: \"center\",\n      originY: \"center\",\n      radius: 50,\n      fill: \"#10b981\",\n      stroke: \"#047857\",\n      strokeWidth: 2,\n    });\n\n    canvas.add(circle);\n    canvas.setActiveObject(circle);\n    canvas.renderAll();\n  };\n\n  // Add triangle to canvas\n  const addTriangle = () => {\n    if (!canvas || !fabric) return;\n\n    const triangle = new fabric.Triangle({\n      left: canvas.width / 2,\n      top: canvas.height / 2,\n      originX: \"center\",\n      originY: \"center\",\n      width: 100,\n      height: 100,\n      fill: \"#f59e0b\",\n      stroke: \"#d97706\",\n      strokeWidth: 2,\n    });\n\n    canvas.add(triangle);\n    canvas.setActiveObject(triangle);\n    canvas.renderAll();\n  };\n\n  // Upload image\n  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (!e.target.files || !e.target.files[0]) return;\n\n    const file = e.target.files[0];\n    const reader = new FileReader();\n\n    reader.onload = (event) => {\n      if (!event.target?.result) return;\n      addImage(event.target.result as string);\n    };\n\n    reader.readAsDataURL(file);\n    e.target.value = \"\"; // Reset input\n  };\n\n  // Delete selected object\n  const deleteSelected = useCallback(() => {\n    if (!canvas || !canvas.getActiveObject()) return;\n\n    canvas.remove(canvas.getActiveObject());\n    canvas.renderAll();\n    setActiveObject(null);\n  }, [canvas]);\n\n  // Update text properties\n  const updateTextProperty = (\n    property: string,\n    value: string | number | boolean\n  ) => {\n    if (!canvas || !activeObject || activeObject.type !== \"text\") return;\n\n    activeObject.set({ [property]: value });\n    canvas.renderAll();\n\n    // Update local state\n    switch (property) {\n      case \"fill\":\n        setTextColor(value as string);\n        break;\n      case \"fontSize\":\n        setFontSize(value as number);\n        break;\n      case \"fontFamily\":\n        setFontFamily(value as string);\n        break;\n      case \"textAlign\":\n        setTextAlign(value as string);\n        break;\n      case \"fontWeight\":\n        setFontWeight(value as string);\n        break;\n      case \"fontStyle\":\n        setFontStyle(value as string);\n        break;\n      case \"textDecoration\":\n        setTextDecoration(value as string);\n        break;\n    }\n  };\n\n  // Export canvas as image\n  const exportImage = () => {\n    if (!canvas) return;\n\n    setIsExporting(true);\n\n    // Temporarily disable selection borders\n    const activeObj = canvas.getActiveObject();\n    canvas.discardActiveObject();\n    canvas.renderAll();\n\n    // Get data URL\n    const dataUrl = canvas.toDataURL({\n      format: \"png\",\n      quality: 1,\n      multiplier: 2, // Higher resolution\n    });\n\n    // Re-enable selection\n    if (activeObj) {\n      canvas.setActiveObject(activeObj);\n      canvas.renderAll();\n    }\n\n    // Create download link\n    const link = document.createElement(\"a\");\n    link.download = `${productName\n      .replace(/\\s+/g, \"-\")\n      .toLowerCase()}-design.png`;\n    link.href = dataUrl;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    setIsExporting(false);\n  };\n\n  // Save design\n  const saveDesign = useCallback(() => {\n    if (!canvas || !normalizedOnSave) return;\n\n    // Temporarily disable selection borders\n    const activeObj = canvas.getActiveObject();\n    canvas.discardActiveObject();\n    canvas.renderAll();\n\n    // Get data URL for preview\n    const previewUrl = canvas.toDataURL({\n      format: \"png\",\n      quality: 0.8,\n    });\n\n    // Get JSON data\n    const canvasData = JSON.stringify(canvas.toJSON());\n\n    // Re-enable selection\n    if (activeObj) {\n      canvas.setActiveObject(activeObj);\n      canvas.renderAll();\n    }\n\n    // Call onSave callback\n    normalizedOnSave({\n      canvasData,\n      preview: previewUrl,\n    });\n\n    setHasUnsavedChanges(false);\n  }, [canvas, normalizedOnSave]);\n\n  // Bring object forward\n  const bringForward = () => {\n    if (!canvas || !activeObject) return;\n    canvas.bringForward(activeObject);\n    canvas.renderAll();\n  };\n\n  // Send object backward\n  const sendBackward = () => {\n    if (!canvas || !activeObject) return;\n    canvas.sendBackward(activeObject);\n    canvas.renderAll();\n  };\n\n  // Rotate object\n  const rotateObject = (angle: number = 90) => {\n    if (!canvas || !activeObject) return;\n    activeObject.rotate((activeObject.angle || 0) + angle);\n    canvas.renderAll();\n  };\n\n  // Center object\n  const centerObject = () => {\n    if (!canvas || !activeObject) return;\n    activeObject.center();\n    canvas.renderAll();\n  };\n\n  // Set active object by ID\n  const setActiveObjectById = (id: string) => {\n    if (!canvas) return;\n\n    const objects = canvas.getObjects();\n    const obj = objects.find((o: FabricObject) => o.id === id);\n    if (obj) {\n      canvas.setActiveObject(obj);\n      canvas.renderAll();\n    }\n  };\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // Only handle shortcuts when canvas is focused or no input is focused\n      const activeElement = document.activeElement;\n      const isInputFocused =\n        activeElement?.tagName === \"INPUT\" ||\n        activeElement?.tagName === \"TEXTAREA\" ||\n        (activeElement as HTMLElement)?.contentEditable === \"true\";\n\n      if (isInputFocused) return;\n\n      // Prevent default for our shortcuts\n      if (\n        (e.ctrlKey || e.metaKey) &&\n        [\"z\", \"y\", \"s\"].includes(e.key.toLowerCase())\n      ) {\n        e.preventDefault();\n      }\n\n      if (e.key === \"Delete\" || e.key === \"Backspace\") {\n        e.preventDefault();\n      }\n\n      // Handle shortcuts\n      if (\n        (e.ctrlKey || e.metaKey) &&\n        e.key.toLowerCase() === \"z\" &&\n        !e.shiftKey\n      ) {\n        handleUndo();\n      } else if (\n        (e.ctrlKey || e.metaKey) &&\n        (e.key.toLowerCase() === \"y\" ||\n          (e.key.toLowerCase() === \"z\" && e.shiftKey))\n      ) {\n        handleRedo();\n      } else if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === \"s\") {\n        saveDesign();\n      } else if (e.key === \"Delete\" || e.key === \"Backspace\") {\n        deleteSelected();\n      } else if (e.key === \"Escape\") {\n        if (canvas) {\n          canvas.discardActiveObject();\n          canvas.renderAll();\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [\n    canvas,\n    historyIndex,\n    canvasHistory,\n    handleUndo,\n    handleRedo,\n    saveDesign,\n    deleteSelected,\n  ]);\n\n  // Show loading state while Fabric.js is loading\n  if (!fabricLoaded) {\n    return (\n      <div\n        className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100 space-y-6\"\n        data-has-unsaved-changes={hasUnsavedChanges ? \"true\" : \"false\"}\n      >\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 font-medium\">\n              Loading Professional Designer...\n            </p>\n            <p className=\"text-sm text-gray-500 mt-2\">\n              Preparing your advanced customization tools\n            </p>\n            <div className=\"mt-4 bg-blue-50 rounded-lg p-3 text-sm text-blue-700\">\n              💡 <strong>Tip:</strong> You&apos;ll be able to add text, shapes,\n              images, and more!\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <React.Fragment>\n      <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n        <div className=\"mb-6\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"text-2xl font-bold text-gray-900 flex items-center gap-2\">\n              🎨 Professional Designer\n            </h3>\n            {hasUnsavedChanges && (\n              <div className=\"flex items-center gap-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-full text-sm\">\n                <div className=\"w-2 h-2 bg-amber-500 rounded-full animate-pulse\"></div>\n                Auto-saving...\n              </div>\n            )}\n          </div>\n          <p className=\"text-gray-600 mb-3\">\n            Create stunning designs with our professional-grade editor. Add\n            text, shapes, images, and customize colors.\n          </p>\n          <div className=\"flex flex-wrap gap-2 text-xs\">\n            <span className=\"bg-green-100 text-green-700 px-2 py-1 rounded-full\">\n              ✓ Real-time preview\n            </span>\n            <span className=\"bg-blue-100 text-blue-700 px-2 py-1 rounded-full\">\n              ✓ Undo/Redo\n            </span>\n            <span className=\"bg-purple-100 text-purple-700 px-2 py-1 rounded-full\">\n              ✓ High-quality export\n            </span>\n            <span className=\"bg-orange-100 text-orange-700 px-2 py-1 rounded-full\">\n              ✓ Auto-save\n            </span>\n            <span className=\"bg-indigo-100 text-indigo-700 px-2 py-1 rounded-full\">\n              ✓ Keyboard shortcuts\n            </span>\n          </div>\n\n          {/* Keyboard shortcuts info */}\n          <div className=\"mt-3 p-3 bg-gray-50 rounded-lg\">\n            <div className=\"text-xs text-gray-600\">\n              <strong>Keyboard Shortcuts:</strong>\n              <span className=\"ml-2\">Ctrl+Z (Undo)</span>\n              <span className=\"ml-2\">Ctrl+Y (Redo)</span>\n              <span className=\"ml-2\">Ctrl+S (Save)</span>\n              <span className=\"ml-2\">Delete (Remove)</span>\n              <span className=\"ml-2\">Esc (Deselect)</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Main editor layout with sidebar */}\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          {/* Left sidebar with tabs */}\n          <div className=\"w-full lg:w-64 bg-gray-50 rounded-xl overflow-hidden border border-gray-200 flex flex-col order-2 lg:order-1\">\n            {/* Sidebar tabs */}\n            <div className=\"flex border-b border-gray-200\">\n              <button\n                className={`flex-1 py-3 text-sm font-medium ${\n                  activeTab === \"text\"\n                    ? \"bg-white text-blue-600\"\n                    : \"text-gray-600 hover:bg-gray-100\"\n                }`}\n                onClick={() => setActiveTab(\"text\")}\n              >\n                Text\n              </button>\n              <button\n                className={`flex-1 py-3 text-sm font-medium ${\n                  activeTab === \"shapes\"\n                    ? \"bg-white text-blue-600\"\n                    : \"text-gray-600 hover:bg-gray-100\"\n                }`}\n                onClick={() => setActiveTab(\"shapes\")}\n              >\n                Shapes\n              </button>\n              <button\n                className={`flex-1 py-3 text-sm font-medium ${\n                  activeTab === \"images\"\n                    ? \"bg-white text-blue-600\"\n                    : \"text-gray-600 hover:bg-gray-100\"\n                }`}\n                onClick={() => setActiveTab(\"images\")}\n              >\n                Images\n              </button>\n              <button\n                className={`flex-1 py-3 text-sm font-medium ${\n                  activeTab === \"colors\"\n                    ? \"bg-white text-blue-600\"\n                    : \"text-gray-600 hover:bg-gray-100\"\n                }`}\n                onClick={() => setActiveTab(\"colors\")}\n              >\n                Colors\n              </button>\n            </div>\n\n            {/* Tab content */}\n            <div className=\"flex-1 overflow-y-auto p-4\">\n              {/* Text tab */}\n              {activeTab === \"text\" && (\n                <div className=\"space-y-4\">\n                  <button\n                    className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2\"\n                    onClick={() => addText()}\n                  >\n                    <Type size={16} /> Add Text\n                  </button>\n\n                  {activeObject && activeObject.type === \"text\" && (\n                    <div className=\"space-y-4 pt-4 border-t border-gray-200\">\n                      <h4 className=\"font-medium text-gray-900\">\n                        Text Properties\n                      </h4>\n\n                      {/* Font family */}\n                      <div>\n                        <label className=\"block text-sm text-gray-600 mb-1\">\n                          Font\n                        </label>\n                        <select\n                          className=\"w-full border border-gray-300 rounded-lg p-2 text-sm\"\n                          value={fontFamily}\n                          onChange={(e) =>\n                            updateTextProperty(\"fontFamily\", e.target.value)\n                          }\n                        >\n                          {fonts.map((font) => (\n                            <option key={font} value={font}>\n                              {font}\n                            </option>\n                          ))}\n                        </select>\n                      </div>\n\n                      {/* Font size */}\n                      <div>\n                        <label className=\"block text-sm text-gray-600 mb-1\">\n                          Size\n                        </label>\n                        <input\n                          type=\"range\"\n                          min=\"8\"\n                          max=\"80\"\n                          value={fontSize}\n                          onChange={(e) =>\n                            updateTextProperty(\n                              \"fontSize\",\n                              parseInt(e.target.value)\n                            )\n                          }\n                          className=\"w-full\"\n                        />\n                        <div className=\"text-xs text-gray-500 text-right\">\n                          {fontSize}px\n                        </div>\n                      </div>\n\n                      {/* Text color */}\n                      <div>\n                        <label className=\"block text-sm text-gray-600 mb-1\">\n                          Color\n                        </label>\n                        <div className=\"grid grid-cols-6 gap-2\">\n                          {colors.slice(0, 12).map((color) => (\n                            <button\n                              key={color}\n                              className={`w-full aspect-square rounded-full border ${\n                                textColor === color\n                                  ? \"border-blue-500 ring-2 ring-blue-300\"\n                                  : \"border-gray-300\"\n                              }`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => updateTextProperty(\"fill\", color)}\n                              aria-label={`Color ${color}`}\n                            />\n                          ))}\n                        </div>\n                      </div>\n\n                      {/* Text alignment */}\n                      <div>\n                        <label className=\"block text-sm text-gray-600 mb-1\">\n                          Alignment\n                        </label>\n                        <div className=\"flex border border-gray-300 rounded-lg overflow-hidden\">\n                          <button\n                            className={`flex-1 py-1 ${\n                              textAlign === \"left\"\n                                ? \"bg-blue-100 text-blue-600\"\n                                : \"hover:bg-gray-100\"\n                            }`}\n                            onClick={() =>\n                              updateTextProperty(\"textAlign\", \"left\")\n                            }\n                          >\n                            Left\n                          </button>\n                          <button\n                            className={`flex-1 py-1 ${\n                              textAlign === \"center\"\n                                ? \"bg-blue-100 text-blue-600\"\n                                : \"hover:bg-gray-100\"\n                            }`}\n                            onClick={() =>\n                              updateTextProperty(\"textAlign\", \"center\")\n                            }\n                          >\n                            Center\n                          </button>\n                          <button\n                            className={`flex-1 py-1 ${\n                              textAlign === \"right\"\n                                ? \"bg-blue-100 text-blue-600\"\n                                : \"hover:bg-gray-100\"\n                            }`}\n                            onClick={() =>\n                              updateTextProperty(\"textAlign\", \"right\")\n                            }\n                          >\n                            Right\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Text style */}\n                      <div>\n                        <label className=\"block text-sm text-gray-600 mb-1\">\n                          Style\n                        </label>\n                        <div className=\"flex border border-gray-300 rounded-lg overflow-hidden\">\n                          <button\n                            className={`flex-1 py-1 font-bold ${\n                              fontWeight === \"bold\"\n                                ? \"bg-blue-100 text-blue-600\"\n                                : \"hover:bg-gray-100\"\n                            }`}\n                            onClick={() =>\n                              updateTextProperty(\n                                \"fontWeight\",\n                                fontWeight === \"bold\" ? \"normal\" : \"bold\"\n                              )\n                            }\n                          >\n                            B\n                          </button>\n                          <button\n                            className={`flex-1 py-1 italic ${\n                              fontStyle === \"italic\"\n                                ? \"bg-blue-100 text-blue-600\"\n                                : \"hover:bg-gray-100\"\n                            }`}\n                            onClick={() =>\n                              updateTextProperty(\n                                \"fontStyle\",\n                                fontStyle === \"italic\" ? \"normal\" : \"italic\"\n                              )\n                            }\n                          >\n                            I\n                          </button>\n                          <button\n                            className={`flex-1 py-1 underline ${\n                              textDecoration === \"underline\"\n                                ? \"bg-blue-100 text-blue-600\"\n                                : \"hover:bg-gray-100\"\n                            }`}\n                            onClick={() =>\n                              updateTextProperty(\n                                \"textDecoration\",\n                                textDecoration === \"underline\"\n                                  ? \"\"\n                                  : \"underline\"\n                              )\n                            }\n                          >\n                            U\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Shapes tab */}\n              {activeTab === \"shapes\" && (\n                <div className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 gap-3\">\n                    <button\n                      className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2\"\n                      onClick={addRectangle}\n                    >\n                      <Square size={16} /> Rectangle\n                    </button>\n                    <button\n                      className=\"w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2\"\n                      onClick={addCircle}\n                    >\n                      <Circle size={16} /> Circle\n                    </button>\n                    <button\n                      className=\"w-full bg-yellow-600 text-white py-3 px-4 rounded-lg hover:bg-yellow-700 transition-colors flex items-center justify-center gap-2\"\n                      onClick={addTriangle}\n                    >\n                      <Triangle size={16} /> Triangle\n                    </button>\n                  </div>\n\n                  {activeObject &&\n                    activeObject.type !== \"text\" &&\n                    activeObject.type !== \"image\" && (\n                      <div className=\"space-y-4 pt-4 border-t border-gray-200\">\n                        <h4 className=\"font-medium text-gray-900\">\n                          Shape Properties\n                        </h4>\n\n                        {/* Shape color */}\n                        <div>\n                          <label className=\"block text-sm text-gray-600 mb-1\">\n                            Fill Color\n                          </label>\n                          <div className=\"grid grid-cols-6 gap-2\">\n                            {colors.slice(0, 12).map((color) => (\n                              <button\n                                key={color}\n                                className={`w-full aspect-square rounded-full border ${\n                                  activeObject?.fill === color\n                                    ? \"border-blue-500 ring-2 ring-blue-300\"\n                                    : \"border-gray-300\"\n                                }`}\n                                style={{ backgroundColor: color }}\n                                onClick={() => {\n                                  if (activeObject) {\n                                    activeObject.set({ fill: color });\n                                    canvas.renderAll();\n                                  }\n                                }}\n                                aria-label={`Color ${color}`}\n                              />\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Stroke color */}\n                        <div>\n                          <label className=\"block text-sm text-gray-600 mb-1\">\n                            Border Color\n                          </label>\n                          <div className=\"grid grid-cols-6 gap-2\">\n                            {colors.slice(0, 12).map((color) => (\n                              <button\n                                key={color}\n                                className={`w-full aspect-square rounded-full border ${\n                                  activeObject?.stroke === color\n                                    ? \"border-blue-500 ring-2 ring-blue-300\"\n                                    : \"border-gray-300\"\n                                }`}\n                                style={{ backgroundColor: color }}\n                                onClick={() => {\n                                  if (activeObject) {\n                                    activeObject.set({ stroke: color });\n                                    canvas.renderAll();\n                                  }\n                                }}\n                                aria-label={`Border color ${color}`}\n                              />\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Stroke width */}\n                        <div>\n                          <label className=\"block text-sm text-gray-600 mb-1\">\n                            Border Width\n                          </label>\n                          <input\n                            type=\"range\"\n                            min=\"0\"\n                            max=\"10\"\n                            value={activeObject?.strokeWidth || 0}\n                            onChange={(e) => {\n                              if (activeObject) {\n                                activeObject.set({\n                                  strokeWidth: parseInt(e.target.value),\n                                });\n                                canvas.renderAll();\n                              }\n                            }}\n                            className=\"w-full\"\n                          />\n                          <div className=\"text-xs text-gray-500 text-right\">\n                            {activeObject?.strokeWidth || 0}px\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                </div>\n              )}\n\n              {/* Images tab */}\n              {activeTab === \"images\" && (\n                <div className=\"space-y-4\">\n                  <button\n                    className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2\"\n                    onClick={() => fileInputRef.current?.click()}\n                  >\n                    <Upload size={16} /> Upload Image\n                  </button>\n                  <input\n                    ref={fileInputRef}\n                    type=\"file\"\n                    accept=\"image/*\"\n                    className=\"hidden\"\n                    onChange={handleImageUpload}\n                  />\n\n                  <h4 className=\"font-medium text-gray-900 pt-4 border-t border-gray-200\">\n                    Sample Images\n                  </h4>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {sampleImages.map((img, index) => (\n                      <button\n                        key={index}\n                        className=\"border border-gray-200 rounded-lg p-2 hover:border-blue-500 transition-colors\"\n                        onClick={() => addImage(img)}\n                      >\n                        <img\n                          src={img}\n                          alt={`Sample ${index + 1}`}\n                          className=\"w-full aspect-square object-contain\"\n                        />\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Colors tab */}\n              {activeTab === \"colors\" && (\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-medium text-gray-900\">\n                    Background Color\n                  </h4>\n                  <div className=\"grid grid-cols-4 gap-2\">\n                    {colors.map((color) => (\n                      <button\n                        key={color}\n                        className={`w-full aspect-square rounded-lg border ${\n                          canvas?.backgroundColor === color\n                            ? \"border-blue-500 ring-2 ring-blue-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        style={{ backgroundColor: color }}\n                        onClick={() => {\n                          if (canvas) {\n                            canvas.setBackgroundColor(color, () =>\n                              canvas.renderAll()\n                            );\n                            handleCanvasChange();\n                          }\n                        }}\n                        aria-label={`Background color ${color}`}\n                      />\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Main canvas area */}\n          <div className=\"flex-1 order-1 lg:order-2\">\n            <div className=\"relative\">\n              {/* Mobile-friendly canvas info */}\n              <div className=\"lg:hidden mb-3 p-3 bg-blue-50 rounded-lg text-sm text-blue-700\">\n                💡 <strong>Tip:</strong> Use the tools below to customize your\n                design. Tap objects to select and edit them.\n              </div>\n              {/* Canvas container */}\n              <div className=\"border border-gray-300 rounded-lg overflow-hidden bg-white shadow-inner relative\">\n                <canvas ref={canvasRef} />\n\n                {/* Empty canvas guide */}\n                {canvas &&\n                  canvas\n                    .getObjects()\n                    .filter((obj: FabricObject) => obj.selectable !== false)\n                    .length === 0 && (\n                    <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none\">\n                      <div className=\"text-center bg-white bg-opacity-90 p-6 rounded-xl shadow-lg max-w-sm\">\n                        <div className=\"text-4xl mb-3\">🎨</div>\n                        <h4 className=\"font-semibold text-gray-900 mb-2\">\n                          Start Creating!\n                        </h4>\n                        <p className=\"text-sm text-gray-600 mb-4\">\n                          Use the tools on the left to add text, shapes, or\n                          images to your design.\n                        </p>\n                        <div className=\"flex flex-wrap gap-2 justify-center\">\n                          <button\n                            className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700 transition-colors pointer-events-auto\"\n                            onClick={() => addText(\"Your Text Here\")}\n                          >\n                            + Add Text\n                          </button>\n                          <button\n                            className=\"bg-green-600 text-white px-3 py-1 rounded-full text-xs hover:bg-green-700 transition-colors pointer-events-auto\"\n                            onClick={addRectangle}\n                          >\n                            + Add Shape\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n              </div>\n\n              {/* Toolbar */}\n              <div className=\"absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-1 flex flex-col gap-1\">\n                <button\n                  className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                  onClick={() => rotateObject()}\n                  title=\"Rotate 90°\"\n                  disabled={!activeObject}\n                >\n                  <RotateCw size={16} />\n                </button>\n                <button\n                  className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                  onClick={centerObject}\n                  title=\"Center Object\"\n                  disabled={!activeObject}\n                >\n                  <Move size={16} />\n                </button>\n                <button\n                  className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                  onClick={bringForward}\n                  title=\"Bring Forward\"\n                  disabled={!activeObject}\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"16\"\n                    height=\"16\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                  >\n                    <rect x=\"7\" y=\"7\" width=\"10\" height=\"10\" rx=\"1\" />\n                    <rect x=\"4\" y=\"4\" width=\"10\" height=\"10\" rx=\"1\" />\n                  </svg>\n                </button>\n                <button\n                  className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n                  onClick={sendBackward}\n                  title=\"Send Backward\"\n                  disabled={!activeObject}\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    width=\"16\"\n                    height=\"16\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                  >\n                    <rect x=\"7\" y=\"7\" width=\"10\" height=\"10\" rx=\"1\" />\n                    <rect x=\"10\" y=\"10\" width=\"10\" height=\"10\" rx=\"1\" />\n                  </svg>\n                </button>\n                <button\n                  className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors text-red-500\"\n                  onClick={deleteSelected}\n                  title=\"Delete Object\"\n                  disabled={!activeObject}\n                >\n                  <Trash2 size={16} />\n                </button>\n              </div>\n            </div>\n\n            {/* Bottom toolbar */}\n            <div className=\"mt-4 flex justify-between items-center\">\n              <div className=\"flex gap-2\">\n                <button\n                  className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm\"\n                  onClick={handleUndo}\n                  disabled={historyIndex <= 0}\n                >\n                  <Undo size={16} /> Undo\n                </button>\n                <button\n                  className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm\"\n                  onClick={handleRedo}\n                  disabled={historyIndex >= canvasHistory.length - 1}\n                >\n                  <Redo size={16} /> Redo\n                </button>\n              </div>\n\n              <div className=\"flex gap-2\">\n                <button\n                  className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-1 text-sm\"\n                  onClick={exportImage}\n                  disabled={isExporting}\n                >\n                  <Download size={16} />\n                  {isExporting ? \"Exporting...\" : \"Export\"}\n                </button>\n                {onSave && (\n                  <button\n                    className=\"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-1 text-sm\"\n                    onClick={saveDesign}\n                  >\n                    <Save size={16} /> Save Design\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Warning about unsaved changes */}\n      {showUnsavedWarning && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-xl p-6 max-w-md w-full\">\n            <h3 className=\"text-xl font-bold mb-4\">Unsaved Changes</h3>\n            <p className=\"mb-6\">\n              You have unsaved changes to your design. Are you sure you want to\n              leave without saving?\n            </p>\n            <div className=\"flex justify-end gap-3\">\n              <button\n                className=\"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100\"\n                onClick={() => setShowUnsavedWarning(false)}\n              >\n                Cancel\n              </button>\n              <button\n                className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                onClick={() => {\n                  setShowUnsavedWarning(false);\n                  // Handle discard action\n                }}\n              >\n                Discard Changes\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </React.Fragment>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;AAsBA,gDAAgD;AAChD,IAAI,SAAc;AAClB,wCAAmC;IACjC,SAAS,kGAAkB,MAAM;AACnC;AAEA,4BAA4B;AAC5B,SAAS,SACP,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAgDe,SAAS,kBAAkB,EACxC,sBAAsB;AACtB,YAAY,EACZ,qBAAqB,EACrB,oBAAoB,EACpB,SAAS,EACT,0CAA0C;AAC1C,WAAW,EACX,MAAM,EACN,aAAa,EACb,eAAe,EACf,cAAc,gBAAgB,EACP;;IACvB,4CAA4C;IAC5C,MAAM,yBAAyB,gBAAgB,mBAAmB;IAClE,MAAM,wBAAwB,wBAAwB;IACtD,MAAM,mBAAmB,yBAAyB;IAElD,+BAA+B;IAC/B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAC9B,IACE;gEAAS,CAAC;oBACR,IAAI,kBAAkB;wBACpB,iBAAiB;oBACnB;gBACF;+DAAG;uDACL;QAAC;KAAiB;IAEpB,0BAA0B;IAC1B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEvC;IACF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEhE,kBAAkB;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,kBAAkB;IAClB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,mBAAmB;IACnB,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,oCAAoC;IACpC,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,UAAU,OAAO,IAAI,aAAkB,aAAa;YAEzD,6BAA6B;YAC7B,MAAM;0DAAa;oBACjB,IAAI,CAAC,QAAQ;wBACX,MAAM,eAAe;wBACrB,SAAS,aAAa,MAAM;oBAC9B;oBAEA,gBAAgB;oBAChB,MAAM,eAAe,IAAI,OAAO,MAAM,CAAC,UAAU,OAAO,EAAE;wBACxD,OAAO;wBACP,QAAQ;wBACR,iBAAiB;wBACjB,wBAAwB;oBAC1B;oBAEA,yBAAyB;oBACzB,aAAa,EAAE,CAAC,mBAAmB;oBACnC,aAAa,EAAE,CAAC,gBAAgB;oBAChC,aAAa,EAAE,CAAC,kBAAkB;oBAClC,aAAa,EAAE,CAAC,qBAAqB;oBACrC,aAAa,EAAE,CAAC,qBAAqB;oBACrC,aAAa,EAAE,CAAC;kEAAqB,IAAM,gBAAgB;;oBAE3D,gCAAgC;oBAChC,IAAI,uBAAuB,YAAY;wBACrC,aAAa,YAAY,CAAC,sBAAsB,UAAU;sEAAE;gCAC1D,aAAa,SAAS;gCACtB,cAAc;4BAChB;;oBACF,OAAO,IAAI,wBAAwB;wBACjC,mCAAmC;wBACnC,OAAO,KAAK,CAAC,OAAO,CAClB;sEACA,CAAC;gCACC,2DAA2D;gCAC3D,MAAM,QAAQ,KAAK,GAAG,CACpB,aAAa,KAAK,GAAG,IAAI,KAAK,EAC9B,aAAa,MAAM,GAAG,IAAI,MAAM;gCAElC,IAAI,KAAK,CAAC,QAAQ;gCAElB,mBAAmB;gCACnB,IAAI,GAAG,CAAC;oCACN,MAAM,aAAa,KAAK,GAAG;oCAC3B,KAAK,aAAa,MAAM,GAAG;oCAC3B,SAAS;oCACT,SAAS;oCACT,YAAY;oCACZ,SAAS;gCACX;gCAEA,aAAa,GAAG,CAAC;gCACjB,aAAa,UAAU,CAAC;gCACxB,aAAa,SAAS;gCACtB,cAAc;4BAChB;qEACA;4BAAE,aAAa;wBAAY;oBAE/B;oBAEA,UAAU;oBACV,gBAAgB;oBAEhB,+CAA+C;oBAC/C,IAAI,eAAe;wBACjB,cAAc;oBAChB;oBAEA,gCAAgC;oBAChC,cAAc;gBAChB;;YAEA;YAEA,mBAAmB;YACnB;+CAAO;oBACL,IAAI,QAAQ;wBACV,OAAO,OAAO;oBAChB;gBACF;;QACF;sCAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,OAAO;QACpB,MAAM,UAAU,OAAO,UAAU;QACjC,6EAA6E;QAC7E,OACE,QAAQ,MAAM,CAAC,CAAC,MAAsB,IAAI,UAAU,KAAK,OAAO,MAAM,KACtE;IAEJ;IAEA,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ;QACb,cAAc;QACd,qBAAqB;QAErB,4CAA4C;QAC5C,IAAI,mBAAmB;YACrB,0DAA0D;YAC1D,MAAM,YAAY,OAAO,eAAe;YACxC,OAAO,mBAAmB;YAC1B,OAAO,SAAS;YAEhB,2BAA2B;YAC3B,MAAM,aAAa,OAAO,SAAS,CAAC;gBAClC,QAAQ;gBACR,SAAS;YACX;YAEA,gBAAgB;YAChB,MAAM,aAAa,KAAK,SAAS,CAAC,OAAO,MAAM;YAE/C,sBAAsB;YACtB,IAAI,WAAW;gBACb,OAAO,eAAe,CAAC;gBACvB,OAAO,SAAS;YAClB;YAEA,4CAA4C;YAC5C,kBAAkB;gBAChB;gBACA,SAAS;YACX;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB,CAAC;QAC7B,MAAM,iBAAiB,EAAE,QAAQ,CAAC,EAAE;QACpC,gBAAgB;QAEhB,6CAA6C;QAC7C,IAAI,kBAAkB,eAAe,IAAI,KAAK,QAAQ;YACpD,aAAa,eAAe,IAAI,IAAI;YACpC,YAAY,eAAe,QAAQ,IAAI;YACvC,cAAc,eAAe,UAAU,IAAI;YAC3C,aAAa,eAAe,SAAS,IAAI;YACzC,cAAc,eAAe,UAAU,IAAI;YAC3C,aAAa,eAAe,SAAS,IAAI;YACzC,kBAAkB,eAAe,cAAc,IAAI;QACrD;IACF;IAEA,+BAA+B;IAC/B,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,cAAc;QAEnB,MAAM,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;QAE/C,mDAAmD;QACnD,IAAI,eAAe,cAAc,MAAM,GAAG,GAAG;YAC3C,iBAAiB,CAAC,OAAS,KAAK,KAAK,CAAC,GAAG,eAAe;QAC1D;QAEA,iBAAiB,CAAC,OAAS;mBAAI;gBAAM;aAAK;QAC1C,gBAAgB,CAAC,OAAS,OAAO;IACnC;IAEA,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,IAAI,gBAAgB,KAAK,CAAC,QAAQ;YAElC,MAAM,WAAW,eAAe;YAChC,gBAAgB;YAEhB,OAAO,YAAY,CAAC,aAAa,CAAC,SAAS;6DAAE;oBAC3C,OAAO,SAAS;oBAChB,qBAAqB;gBACvB;;QACF;oDAAG;QAAC;QAAc;QAAQ;KAAc;IAExC,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,IAAI,gBAAgB,cAAc,MAAM,GAAG,KAAK,CAAC,QAAQ;YAEzD,MAAM,WAAW,eAAe;YAChC,gBAAgB;YAEhB,OAAO,YAAY,CAAC,aAAa,CAAC,SAAS;6DAAE;oBAC3C,OAAO,SAAS;oBAChB,qBAAqB;gBACvB;;QACF;oDAAG;QAAC;QAAc;QAAe;KAAO;IAExC,qBAAqB;IACrB,MAAM,UAAU,CAAC,OAAO,sBAAsB;QAC5C,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,MAAM,aAAa,IAAI,OAAO,KAAK,CAAC,MAAM;YACxC,MAAM,OAAO,KAAK,GAAG;YACrB,KAAK,OAAO,MAAM,GAAG;YACrB,SAAS;YACT,SAAS;YACT,YAAY;YACZ,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;YACZ,WAAW;YACX,gBAAgB;QAClB;QAEA,OAAO,GAAG,CAAC;QACX,OAAO,eAAe,CAAC;QACvB,OAAO,SAAS;IAClB;IAEA,sBAAsB;IACtB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,OAAO,KAAK,CAAC,OAAO,CAClB,KACA,CAAC;YACC,2DAA2D;YAC3D,MAAM,QAAQ,KAAK,GAAG,CACpB,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,EAC5B,OAAO,MAAM,GAAG,IAAI,IAAI,MAAM;YAEhC,IAAI,KAAK,CAAC;YAEV,mBAAmB;YACnB,IAAI,GAAG,CAAC;gBACN,MAAM,OAAO,KAAK,GAAG;gBACrB,KAAK,OAAO,MAAM,GAAG;gBACrB,SAAS;gBACT,SAAS;YACX;YAEA,OAAO,GAAG,CAAC;YACX,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB,GACA;YAAE,aAAa;QAAY;IAE/B;IAEA,0BAA0B;IAC1B,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC;YAC3B,MAAM,OAAO,KAAK,GAAG;YACrB,KAAK,OAAO,MAAM,GAAG;YACrB,SAAS;YACT,SAAS;YACT,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,aAAa;QACf;QAEA,OAAO,GAAG,CAAC;QACX,OAAO,eAAe,CAAC;QACvB,OAAO,SAAS;IAClB;IAEA,uBAAuB;IACvB,MAAM,YAAY;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,MAAM,SAAS,IAAI,OAAO,MAAM,CAAC;YAC/B,MAAM,OAAO,KAAK,GAAG;YACrB,KAAK,OAAO,MAAM,GAAG;YACrB,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,aAAa;QACf;QAEA,OAAO,GAAG,CAAC;QACX,OAAO,eAAe,CAAC;QACvB,OAAO,SAAS;IAClB;IAEA,yBAAyB;IACzB,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,MAAM,WAAW,IAAI,OAAO,QAAQ,CAAC;YACnC,MAAM,OAAO,KAAK,GAAG;YACrB,KAAK,OAAO,MAAM,GAAG;YACrB,SAAS;YACT,SAAS;YACT,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,aAAa;QACf;QAEA,OAAO,GAAG,CAAC;QACX,OAAO,eAAe,CAAC;QACvB,OAAO,SAAS;IAClB;IAEA,eAAe;IACf,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;QAE3C,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,SAAS,IAAI;QAEnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI,CAAC,MAAM,MAAM,EAAE,QAAQ;YAC3B,SAAS,MAAM,MAAM,CAAC,MAAM;QAC9B;QAEA,OAAO,aAAa,CAAC;QACrB,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,cAAc;IACrC;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACjC,IAAI,CAAC,UAAU,CAAC,OAAO,eAAe,IAAI;YAE1C,OAAO,MAAM,CAAC,OAAO,eAAe;YACpC,OAAO,SAAS;YAChB,gBAAgB;QAClB;wDAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,MAAM,qBAAqB,CACzB,UACA;QAEA,IAAI,CAAC,UAAU,CAAC,gBAAgB,aAAa,IAAI,KAAK,QAAQ;QAE9D,aAAa,GAAG,CAAC;YAAE,CAAC,SAAS,EAAE;QAAM;QACrC,OAAO,SAAS;QAEhB,qBAAqB;QACrB,OAAQ;YACN,KAAK;gBACH,aAAa;gBACb;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,cAAc;gBACd;YACF,KAAK;gBACH,aAAa;gBACb;YACF,KAAK;gBACH,cAAc;gBACd;YACF,KAAK;gBACH,aAAa;gBACb;YACF,KAAK;gBACH,kBAAkB;gBAClB;QACJ;IACF;IAEA,yBAAyB;IACzB,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;QAEb,eAAe;QAEf,wCAAwC;QACxC,MAAM,YAAY,OAAO,eAAe;QACxC,OAAO,mBAAmB;QAC1B,OAAO,SAAS;QAEhB,eAAe;QACf,MAAM,UAAU,OAAO,SAAS,CAAC;YAC/B,QAAQ;YACR,SAAS;YACT,YAAY;QACd;QAEA,sBAAsB;QACtB,IAAI,WAAW;YACb,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;QAEA,uBAAuB;QACvB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG,GAAG,YAChB,OAAO,CAAC,QAAQ,KAChB,WAAW,GAAG,WAAW,CAAC;QAC7B,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,eAAe;IACjB;IAEA,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,kBAAkB;YAElC,wCAAwC;YACxC,MAAM,YAAY,OAAO,eAAe;YACxC,OAAO,mBAAmB;YAC1B,OAAO,SAAS;YAEhB,2BAA2B;YAC3B,MAAM,aAAa,OAAO,SAAS,CAAC;gBAClC,QAAQ;gBACR,SAAS;YACX;YAEA,gBAAgB;YAChB,MAAM,aAAa,KAAK,SAAS,CAAC,OAAO,MAAM;YAE/C,sBAAsB;YACtB,IAAI,WAAW;gBACb,OAAO,eAAe,CAAC;gBACvB,OAAO,SAAS;YAClB;YAEA,uBAAuB;YACvB,iBAAiB;gBACf;gBACA,SAAS;YACX;YAEA,qBAAqB;QACvB;oDAAG;QAAC;QAAQ;KAAiB;IAE7B,uBAAuB;IACvB,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU,CAAC,cAAc;QAC9B,OAAO,YAAY,CAAC;QACpB,OAAO,SAAS;IAClB;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU,CAAC,cAAc;QAC9B,OAAO,YAAY,CAAC;QACpB,OAAO,SAAS;IAClB;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC,QAAgB,EAAE;QACtC,IAAI,CAAC,UAAU,CAAC,cAAc;QAC9B,aAAa,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,IAAI;QAChD,OAAO,SAAS;IAClB;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU,CAAC,cAAc;QAC9B,aAAa,MAAM;QACnB,OAAO,SAAS;IAClB;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,QAAQ;QAEb,MAAM,UAAU,OAAO,UAAU;QACjC,MAAM,MAAM,QAAQ,IAAI,CAAC,CAAC,IAAoB,EAAE,EAAE,KAAK;QACvD,IAAI,KAAK;YACP,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB,CAAC;oBACrB,sEAAsE;oBACtE,MAAM,gBAAgB,SAAS,aAAa;oBAC5C,MAAM,iBACJ,eAAe,YAAY,WAC3B,eAAe,YAAY,cAC3B,AAAC,eAA+B,oBAAoB;oBAEtD,IAAI,gBAAgB;oBAEpB,oCAAoC;oBACpC,IACE,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KACvB;wBAAC;wBAAK;wBAAK;qBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAC1C;wBACA,EAAE,cAAc;oBAClB;oBAEA,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,KAAK,aAAa;wBAC/C,EAAE,cAAc;oBAClB;oBAEA,mBAAmB;oBACnB,IACE,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KACvB,EAAE,GAAG,CAAC,WAAW,OAAO,OACxB,CAAC,EAAE,QAAQ,EACX;wBACA;oBACF,OAAO,IACL,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KACvB,CAAC,EAAE,GAAG,CAAC,WAAW,OAAO,OACtB,EAAE,GAAG,CAAC,WAAW,OAAO,OAAO,EAAE,QAAQ,AAAC,GAC7C;wBACA;oBACF,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,CAAC,WAAW,OAAO,KAAK;wBAClE;oBACF,OAAO,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,KAAK,aAAa;wBACtD;oBACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;wBAC7B,IAAI,QAAQ;4BACV,OAAO,mBAAmB;4BAC1B,OAAO,SAAS;wBAClB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;+CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;sCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gDAAgD;IAChD,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YACC,WAAU;YACV,4BAA0B,oBAAoB,SAAS;sBAEvD,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;sCAGzC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;gCAAuD;8CACjE,6LAAC;8CAAO;;;;;;gCAAa;;;;;;;;;;;;;;;;;;;;;;;IAOpC;IAEA,qBACE,6LAAC,6JAAA,CAAA,WAAc;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;oCAGxE,mCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAwD;;;;;;;;;;;;;0CAK7E,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAqD;;;;;;kDAGrE,6LAAC;wCAAK,WAAU;kDAAmD;;;;;;kDAGnE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAMzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAO;;;;;;sDACR,6LAAC;4CAAK,WAAU;sDAAO;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAO;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAO;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAO;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;kCAM7B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,gCAAgC,EAC1C,cAAc,SACV,2BACA,mCACJ;gDACF,SAAS,IAAM,aAAa;0DAC7B;;;;;;0DAGD,6LAAC;gDACC,WAAW,CAAC,gCAAgC,EAC1C,cAAc,WACV,2BACA,mCACJ;gDACF,SAAS,IAAM,aAAa;0DAC7B;;;;;;0DAGD,6LAAC;gDACC,WAAW,CAAC,gCAAgC,EAC1C,cAAc,WACV,2BACA,mCACJ;gDACF,SAAS,IAAM,aAAa;0DAC7B;;;;;;0DAGD,6LAAC;gDACC,WAAW,CAAC,gCAAgC,EAC1C,cAAc,WACV,2BACA,mCACJ;gDACF,SAAS,IAAM,aAAa;0DAC7B;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;;4CAEZ,cAAc,wBACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM;;0EAEf,6LAAC,qMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;4DAAM;;;;;;;oDAGnB,gBAAgB,aAAa,IAAI,KAAK,wBACrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAK1C,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEACC,WAAU;wEACV,OAAO;wEACP,UAAU,CAAC,IACT,mBAAmB,cAAc,EAAE,MAAM,CAAC,KAAK;kFAGhD,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gFAAkB,OAAO;0FACvB;+EADU;;;;;;;;;;;;;;;;0EAQnB,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,UAAU,CAAC,IACT,mBACE,YACA,SAAS,EAAE,MAAM,CAAC,KAAK;wEAG3B,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;;4EACZ;4EAAS;;;;;;;;;;;;;0EAKd,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,sBACxB,6LAAC;gFAEC,WAAW,CAAC,yCAAyC,EACnD,cAAc,QACV,yCACA,mBACJ;gFACF,OAAO;oFAAE,iBAAiB;gFAAM;gFAChC,SAAS,IAAM,mBAAmB,QAAQ;gFAC1C,cAAY,CAAC,MAAM,EAAE,OAAO;+EARvB;;;;;;;;;;;;;;;;0EAeb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,WAAW,CAAC,YAAY,EACtB,cAAc,SACV,8BACA,qBACJ;gFACF,SAAS,IACP,mBAAmB,aAAa;0FAEnC;;;;;;0FAGD,6LAAC;gFACC,WAAW,CAAC,YAAY,EACtB,cAAc,WACV,8BACA,qBACJ;gFACF,SAAS,IACP,mBAAmB,aAAa;0FAEnC;;;;;;0FAGD,6LAAC;gFACC,WAAW,CAAC,YAAY,EACtB,cAAc,UACV,8BACA,qBACJ;gFACF,SAAS,IACP,mBAAmB,aAAa;0FAEnC;;;;;;;;;;;;;;;;;;0EAOL,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,WAAW,CAAC,sBAAsB,EAChC,eAAe,SACX,8BACA,qBACJ;gFACF,SAAS,IACP,mBACE,cACA,eAAe,SAAS,WAAW;0FAGxC;;;;;;0FAGD,6LAAC;gFACC,WAAW,CAAC,mBAAmB,EAC7B,cAAc,WACV,8BACA,qBACJ;gFACF,SAAS,IACP,mBACE,aACA,cAAc,WAAW,WAAW;0FAGzC;;;;;;0FAGD,6LAAC;gFACC,WAAW,CAAC,sBAAsB,EAChC,mBAAmB,cACf,8BACA,qBACJ;gFACF,SAAS,IACP,mBACE,kBACA,mBAAmB,cACf,KACA;0FAGT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAWZ,cAAc,0BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,SAAS;;kFAET,6LAAC,yMAAA,CAAA,SAAM;wEAAC,MAAM;;;;;;oEAAM;;;;;;;0EAEtB,6LAAC;gEACC,WAAU;gEACV,SAAS;;kFAET,6LAAC,yMAAA,CAAA,SAAM;wEAAC,MAAM;;;;;;oEAAM;;;;;;;0EAEtB,6LAAC;gEACC,WAAU;gEACV,SAAS;;kFAET,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,MAAM;;;;;;oEAAM;;;;;;;;;;;;;oDAIzB,gBACC,aAAa,IAAI,KAAK,UACtB,aAAa,IAAI,KAAK,yBACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAK1C,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,sBACxB,6LAAC;gFAEC,WAAW,CAAC,yCAAyC,EACnD,cAAc,SAAS,QACnB,yCACA,mBACJ;gFACF,OAAO;oFAAE,iBAAiB;gFAAM;gFAChC,SAAS;oFACP,IAAI,cAAc;wFAChB,aAAa,GAAG,CAAC;4FAAE,MAAM;wFAAM;wFAC/B,OAAO,SAAS;oFAClB;gFACF;gFACA,cAAY,CAAC,MAAM,EAAE,OAAO;+EAbvB;;;;;;;;;;;;;;;;0EAoBb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,sBACxB,6LAAC;gFAEC,WAAW,CAAC,yCAAyC,EACnD,cAAc,WAAW,QACrB,yCACA,mBACJ;gFACF,OAAO;oFAAE,iBAAiB;gFAAM;gFAChC,SAAS;oFACP,IAAI,cAAc;wFAChB,aAAa,GAAG,CAAC;4FAAE,QAAQ;wFAAM;wFACjC,OAAO,SAAS;oFAClB;gFACF;gFACA,cAAY,CAAC,aAAa,EAAE,OAAO;+EAb9B;;;;;;;;;;;;;;;;0EAoBb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAmC;;;;;;kFAGpD,6LAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,OAAO,cAAc,eAAe;wEACpC,UAAU,CAAC;4EACT,IAAI,cAAc;gFAChB,aAAa,GAAG,CAAC;oFACf,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;gFACtC;gFACA,OAAO,SAAS;4EAClB;wEACF;wEACA,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;;4EACZ,cAAc,eAAe;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;4CAS7C,cAAc,0BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,aAAa,OAAO,EAAE;;0EAErC,6LAAC,yMAAA,CAAA,SAAM;gEAAC,MAAM;;;;;;4DAAM;;;;;;;kEAEtB,6LAAC;wDACC,KAAK;wDACL,MAAK;wDACL,QAAO;wDACP,WAAU;wDACV,UAAU;;;;;;kEAGZ,6LAAC;wDAAG,WAAU;kEAA0D;;;;;;kEAGxE,6LAAC;wDAAI,WAAU;kEACZ,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;gEAEC,WAAU;gEACV,SAAS,IAAM,SAAS;0EAExB,cAAA,6LAAC;oEACC,KAAK;oEACL,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG;oEAC1B,WAAU;;;;;;+DAPP;;;;;;;;;;;;;;;;4CAgBd,cAAc,0BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;kEACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gEAEC,WAAW,CAAC,uCAAuC,EACjD,QAAQ,oBAAoB,QACxB,yCACA,mBACJ;gEACF,OAAO;oEAAE,iBAAiB;gEAAM;gEAChC,SAAS;oEACP,IAAI,QAAQ;wEACV,OAAO,kBAAkB,CAAC,OAAO,IAC/B,OAAO,SAAS;wEAElB;oEACF;gEACF;gEACA,cAAY,CAAC,iBAAiB,EAAE,OAAO;+DAflC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAyBnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;oDAAiE;kEAC3E,6LAAC;kEAAO;;;;;;oDAAa;;;;;;;0DAI1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,KAAK;;;;;;oDAGZ,UACC,OACG,UAAU,GACV,MAAM,CAAC,CAAC,MAAsB,IAAI,UAAU,KAAK,OACjD,MAAM,KAAK,mBACZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,6LAAC;oEAAG,WAAU;8EAAmC;;;;;;8EAGjD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAI1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,SAAS,IAAM,QAAQ;sFACxB;;;;;;sFAGD,6LAAC;4EACC,WAAU;4EACV,SAAS;sFACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAUb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM;wDACf,OAAM;wDACN,UAAU,CAAC;kEAEX,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,MAAM;;;;;;;;;;;kEAElB,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,OAAM;wDACN,UAAU,CAAC;kEAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;;;;;;kEAEd,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,OAAM;wDACN,UAAU,CAAC;kEAEX,cAAA,6LAAC;4DACC,OAAM;4DACN,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,QAAO;4DACP,aAAY;4DACZ,eAAc;4DACd,gBAAe;;8EAEf,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAK,QAAO;oEAAK,IAAG;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAK,QAAO;oEAAK,IAAG;;;;;;;;;;;;;;;;;kEAGhD,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,OAAM;wDACN,UAAU,CAAC;kEAEX,cAAA,6LAAC;4DACC,OAAM;4DACN,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,QAAO;4DACP,aAAY;4DACZ,eAAc;4DACd,gBAAe;;8EAEf,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAK,QAAO;oEAAK,IAAG;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAK,QAAO;oEAAK,IAAG;;;;;;;;;;;;;;;;;kEAGlD,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,OAAM;wDACN,UAAU,CAAC;kEAEX,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,UAAU,gBAAgB;;0EAE1B,6LAAC,qMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;4DAAM;;;;;;;kEAEpB,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,UAAU,gBAAgB,cAAc,MAAM,GAAG;;0EAEjD,6LAAC,qMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;4DAAM;;;;;;;;;;;;;0DAItB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS;wDACT,UAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,MAAM;;;;;;4DACf,cAAc,iBAAiB;;;;;;;oDAEjC,wBACC,6LAAC;wDACC,WAAU;wDACV,SAAS;;0EAET,6LAAC,qMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU/B,oCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BAAE,WAAU;sCAAO;;;;;;sCAIpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,sBAAsB;8CACtC;;;;;;8CAGD,6LAAC;oCACC,WAAU;oCACV,SAAS;wCACP,sBAAsB;oCACtB,wBAAwB;oCAC1B;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhzCwB;KAAA", "debugId": null}}]}