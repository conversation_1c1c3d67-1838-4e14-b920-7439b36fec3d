{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/ProductCustomizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef, useState } from \"react\";\nimport dynamic from \"next/dynamic\";\nimport {\n  Type,\n  Image as ImageIcon,\n  Palette,\n  RotateCw,\n  Move,\n  Trash2,\n  Download,\n  Upload,\n  Undo,\n  Redo,\n  Save,\n} from \"lucide-react\";\n\n// Dynamic import for fabric to avoid SSR issues\nlet fabric: any = null;\nif (typeof window !== \"undefined\") {\n  fabric = require(\"fabric\").fabric;\n}\n\ninterface CustomizationData {\n  canvasData?: string;\n  preview?: string;\n}\n\ninterface ProductCustomizerProps {\n  productImage: string;\n  onCustomizationChange: (customization: CustomizationData) => void;\n  initialCustomization?: CustomizationData | null;\n}\n\nconst ProductCustomizer: React.FC<ProductCustomizerProps> = ({\n  productImage,\n  onCustomizationChange,\n  initialCustomization,\n}) => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [canvas, setCanvas] = useState<any>(null);\n  const [selectedTool, setSelectedTool] = useState<string>(\"select\");\n  const [textColor, setTextColor] = useState(\"#000000\");\n  const [fontSize, setFontSize] = useState(20);\n  const [fontFamily, setFontFamily] = useState(\"Arial\");\n  const [history, setHistory] = useState<string[]>([]);\n  const [historyIndex, setHistoryIndex] = useState(-1);\n  const [fabricLoaded, setFabricLoaded] = useState(false);\n\n  // Load Fabric.js dynamically\n  useEffect(() => {\n    const loadFabric = async () => {\n      if (typeof window !== \"undefined\" && !fabric) {\n        try {\n          const fabricModule = await import(\"fabric\");\n          fabric = fabricModule.fabric;\n          setFabricLoaded(true);\n        } catch (error) {\n          console.error(\"Failed to load Fabric.js:\", error);\n        }\n      } else if (fabric) {\n        setFabricLoaded(true);\n      }\n    };\n\n    loadFabric();\n  }, []);\n\n  // Initialize Fabric.js canvas\n  useEffect(() => {\n    if (!canvasRef.current || !fabricLoaded || !fabric) return;\n\n    const fabricCanvas = new fabric.Canvas(canvasRef.current, {\n      width: 500,\n      height: 500,\n      backgroundColor: \"#ffffff\",\n    });\n\n    // Load product image as background\n    if (productImage) {\n      fabric.Image.fromURL(\n        productImage,\n        (img: any) => {\n          if (img) {\n            img.scaleToWidth(500);\n            img.scaleToHeight(500);\n            img.set({\n              selectable: false,\n              evented: false,\n              opacity: 0.8,\n            });\n            fabricCanvas.setBackgroundImage(\n              img,\n              fabricCanvas.renderAll.bind(fabricCanvas)\n            );\n          }\n        },\n        { crossOrigin: \"anonymous\" }\n      );\n    }\n\n    // Canvas event listeners\n    fabricCanvas.on(\"object:added\", saveState);\n    fabricCanvas.on(\"object:removed\", saveState);\n    fabricCanvas.on(\"object:modified\", saveState);\n\n    setCanvas(fabricCanvas);\n    saveState();\n\n    return () => {\n      fabricCanvas.dispose();\n    };\n  }, [productImage, fabricLoaded]);\n\n  // Save canvas state for undo/redo\n  const saveState = () => {\n    if (!canvas) return;\n\n    const state = JSON.stringify(canvas.toJSON());\n    setHistory((prev) => {\n      const newHistory = prev.slice(0, historyIndex + 1);\n      newHistory.push(state);\n      return newHistory.slice(-20); // Keep last 20 states\n    });\n    setHistoryIndex((prev) => prev + 1);\n\n    // Notify parent of changes\n    onCustomizationChange({\n      canvasData: state,\n      preview: canvas.toDataURL(),\n    });\n  };\n\n  // Undo functionality\n  const undo = () => {\n    if (historyIndex > 0 && canvas) {\n      setHistoryIndex((prev) => prev - 1);\n      canvas.loadFromJSON(history[historyIndex - 1], () => {\n        canvas.renderAll();\n      });\n    }\n  };\n\n  // Redo functionality\n  const redo = () => {\n    if (historyIndex < history.length - 1 && canvas) {\n      setHistoryIndex((prev) => prev + 1);\n      canvas.loadFromJSON(history[historyIndex + 1], () => {\n        canvas.renderAll();\n      });\n    }\n  };\n\n  // Add text to canvas\n  const addText = () => {\n    if (!canvas || !fabric) return;\n\n    const text = new fabric.IText(\"Click to edit\", {\n      left: 100,\n      top: 100,\n      fontFamily: fontFamily,\n      fontSize: fontSize,\n      fill: textColor,\n    });\n\n    canvas.add(text);\n    canvas.setActiveObject(text);\n    canvas.renderAll();\n  };\n\n  // Add shape to canvas\n  const addShape = (shapeType: string) => {\n    if (!canvas || !fabric) return;\n\n    let shape;\n    switch (shapeType) {\n      case \"rectangle\":\n        shape = new fabric.Rect({\n          left: 100,\n          top: 100,\n          width: 100,\n          height: 100,\n          fill: textColor,\n        });\n        break;\n      case \"circle\":\n        shape = new fabric.Circle({\n          left: 100,\n          top: 100,\n          radius: 50,\n          fill: textColor,\n        });\n        break;\n      case \"triangle\":\n        shape = new fabric.Triangle({\n          left: 100,\n          top: 100,\n          width: 100,\n          height: 100,\n          fill: textColor,\n        });\n        break;\n    }\n\n    if (shape) {\n      canvas.add(shape);\n      canvas.setActiveObject(shape);\n      canvas.renderAll();\n    }\n  };\n\n  // Upload and add image\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !canvas || !fabric) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const imgUrl = e.target?.result as string;\n      fabric.Image.fromURL(imgUrl, (img: any) => {\n        if (img) {\n          img.scaleToWidth(150);\n          img.set({\n            left: 100,\n            top: 100,\n          });\n          canvas.add(img);\n          canvas.setActiveObject(img);\n          canvas.renderAll();\n        }\n      });\n    };\n    reader.readAsDataURL(file);\n  };\n\n  // Delete selected object\n  const deleteSelected = () => {\n    if (!canvas) return;\n\n    const activeObject = canvas.getActiveObject();\n    if (activeObject) {\n      canvas.remove(activeObject);\n      canvas.renderAll();\n    }\n  };\n\n  // Clear canvas\n  const clearCanvas = () => {\n    if (!canvas) return;\n\n    canvas.clear();\n    // Re-add background image\n    if (productImage) {\n      fabric.Image.fromURL(\n        productImage,\n        (img) => {\n          if (img) {\n            img.scaleToWidth(500);\n            img.scaleToHeight(500);\n            img.set({\n              selectable: false,\n              evented: false,\n              opacity: 0.8,\n            });\n            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas));\n          }\n        },\n        { crossOrigin: \"anonymous\" }\n      );\n    }\n  };\n\n  // Export canvas as image\n  const exportCanvas = () => {\n    if (!canvas) return;\n\n    const dataURL = canvas.toDataURL({\n      format: \"png\",\n      quality: 1,\n      multiplier: 2,\n    });\n\n    const link = document.createElement(\"a\");\n    link.download = \"custom-product.png\";\n    link.href = dataURL;\n    link.click();\n  };\n\n  // Show loading state while Fabric.js is loading\n  if (!fabricLoaded) {\n    return (\n      <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading Advanced Customizer...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n          🎨 Advanced Product Customizer\n        </h3>\n        <p className=\"text-gray-600\">\n          Design your product with text, shapes, and images\n        </p>\n      </div>\n\n      {/* Toolbar */}\n      <div className=\"mb-6 p-4 bg-gray-50 rounded-xl\">\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {/* Tool Buttons */}\n          <button\n            onClick={addText}\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          >\n            <Type size={16} />\n            Add Text\n          </button>\n\n          <button\n            onClick={() => addShape(\"rectangle\")}\n            className=\"flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n          >\n            Rectangle\n          </button>\n\n          <button\n            onClick={() => addShape(\"circle\")}\n            className=\"flex items-center gap-2 px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\"\n          >\n            Circle\n          </button>\n\n          <button\n            onClick={() => addShape(\"triangle\")}\n            className=\"flex items-center gap-2 px-3 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors\"\n          >\n            Triangle\n          </button>\n\n          <label className=\"flex items-center gap-2 px-3 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors cursor-pointer\">\n            <ImageIcon size={16} />\n            Upload Image\n            <input\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleImageUpload}\n              className=\"hidden\"\n            />\n          </label>\n        </div>\n\n        {/* Controls */}\n        <div className=\"flex flex-wrap gap-4 items-center\">\n          <div className=\"flex items-center gap-2\">\n            <label className=\"text-sm font-medium text-gray-700\">Color:</label>\n            <input\n              type=\"color\"\n              value={textColor}\n              onChange={(e) => setTextColor(e.target.value)}\n              className=\"w-8 h-8 border border-gray-300 rounded cursor-pointer\"\n            />\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <label className=\"text-sm font-medium text-gray-700\">\n              Font Size:\n            </label>\n            <input\n              type=\"range\"\n              min=\"10\"\n              max=\"100\"\n              value={fontSize}\n              onChange={(e) => setFontSize(Number(e.target.value))}\n              className=\"w-20\"\n            />\n            <span className=\"text-sm text-gray-600\">{fontSize}px</span>\n          </div>\n\n          <select\n            value={fontFamily}\n            onChange={(e) => setFontFamily(e.target.value)}\n            className=\"px-3 py-1 border border-gray-300 rounded text-sm\"\n          >\n            <option value=\"Arial\">Arial</option>\n            <option value=\"Times New Roman\">Times New Roman</option>\n            <option value=\"Helvetica\">Helvetica</option>\n            <option value=\"Georgia\">Georgia</option>\n            <option value=\"Verdana\">Verdana</option>\n          </select>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex gap-2 mt-4\">\n          <button\n            onClick={undo}\n            disabled={historyIndex <= 0}\n            className=\"flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Undo size={14} />\n            Undo\n          </button>\n\n          <button\n            onClick={redo}\n            disabled={historyIndex >= history.length - 1}\n            className=\"flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Redo size={14} />\n            Redo\n          </button>\n\n          <button\n            onClick={deleteSelected}\n            className=\"flex items-center gap-1 px-2 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600\"\n          >\n            <Trash2 size={14} />\n            Delete\n          </button>\n\n          <button\n            onClick={clearCanvas}\n            className=\"flex items-center gap-1 px-2 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600\"\n          >\n            Clear All\n          </button>\n\n          <button\n            onClick={exportCanvas}\n            className=\"flex items-center gap-1 px-2 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\"\n          >\n            <Download size={14} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      {/* Canvas */}\n      <div className=\"flex justify-center\">\n        <div className=\"border-2 border-gray-300 rounded-lg overflow-hidden\">\n          <canvas ref={canvasRef} />\n        </div>\n      </div>\n\n      <div className=\"mt-4 text-sm text-gray-500 text-center\">\n        💡 Tip: Click and drag to move objects, double-click text to edit\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCustomizer;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;AAkBA,gDAAgD;AAChD,IAAI,SAAc;AAClB,wCAAmC;IACjC,SAAS,kGAAkB,MAAM;AACnC;AAaA,MAAM,oBAAsD,CAAC,EAC3D,YAAY,EACZ,qBAAqB,EACrB,oBAAoB,EACrB;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;0DAAa;oBACjB,IAAI,aAAkB,eAAe,CAAC,QAAQ;wBAC5C,IAAI;4BACF,MAAM,eAAe;4BACrB,SAAS,aAAa,MAAM;4BAC5B,gBAAgB;wBAClB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C;oBACF,OAAO,IAAI,QAAQ;wBACjB,gBAAgB;oBAClB;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;YAEpD,MAAM,eAAe,IAAI,OAAO,MAAM,CAAC,UAAU,OAAO,EAAE;gBACxD,OAAO;gBACP,QAAQ;gBACR,iBAAiB;YACnB;YAEA,mCAAmC;YACnC,IAAI,cAAc;gBAChB,OAAO,KAAK,CAAC,OAAO,CAClB;mDACA,CAAC;wBACC,IAAI,KAAK;4BACP,IAAI,YAAY,CAAC;4BACjB,IAAI,aAAa,CAAC;4BAClB,IAAI,GAAG,CAAC;gCACN,YAAY;gCACZ,SAAS;gCACT,SAAS;4BACX;4BACA,aAAa,kBAAkB,CAC7B,KACA,aAAa,SAAS,CAAC,IAAI,CAAC;wBAEhC;oBACF;kDACA;oBAAE,aAAa;gBAAY;YAE/B;YAEA,yBAAyB;YACzB,aAAa,EAAE,CAAC,gBAAgB;YAChC,aAAa,EAAE,CAAC,kBAAkB;YAClC,aAAa,EAAE,CAAC,mBAAmB;YAEnC,UAAU;YACV;YAEA;+CAAO;oBACL,aAAa,OAAO;gBACtB;;QACF;sCAAG;QAAC;QAAc;KAAa;IAE/B,kCAAkC;IAClC,MAAM,YAAY;QAChB,IAAI,CAAC,QAAQ;QAEb,MAAM,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM;QAC1C,WAAW,CAAC;YACV,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG,eAAe;YAChD,WAAW,IAAI,CAAC;YAChB,OAAO,WAAW,KAAK,CAAC,CAAC,KAAK,sBAAsB;QACtD;QACA,gBAAgB,CAAC,OAAS,OAAO;QAEjC,2BAA2B;QAC3B,sBAAsB;YACpB,YAAY;YACZ,SAAS,OAAO,SAAS;QAC3B;IACF;IAEA,qBAAqB;IACrB,MAAM,OAAO;QACX,IAAI,eAAe,KAAK,QAAQ;YAC9B,gBAAgB,CAAC,OAAS,OAAO;YACjC,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE;gBAC7C,OAAO,SAAS;YAClB;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,OAAO;QACX,IAAI,eAAe,QAAQ,MAAM,GAAG,KAAK,QAAQ;YAC/C,gBAAgB,CAAC,OAAS,OAAO;YACjC,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE;gBAC7C,OAAO,SAAS;YAClB;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,UAAU;QACd,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,MAAM,OAAO,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAC7C,MAAM;YACN,KAAK;YACL,YAAY;YACZ,UAAU;YACV,MAAM;QACR;QAEA,OAAO,GAAG,CAAC;QACX,OAAO,eAAe,CAAC;QACvB,OAAO,SAAS;IAClB;IAEA,sBAAsB;IACtB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ;QAExB,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,QAAQ,IAAI,OAAO,IAAI,CAAC;oBACtB,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM;gBACR;gBACA;YACF,KAAK;gBACH,QAAQ,IAAI,OAAO,MAAM,CAAC;oBACxB,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,MAAM;gBACR;gBACA;YACF,KAAK;gBACH,QAAQ,IAAI,OAAO,QAAQ,CAAC;oBAC1B,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM;gBACR;gBACA;QACJ;QAEA,IAAI,OAAO;YACT,OAAO,GAAG,CAAC;YACX,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;IACF;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ;QAEjC,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,SAAS,EAAE,MAAM,EAAE;YACzB,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC5B,IAAI,KAAK;oBACP,IAAI,YAAY,CAAC;oBACjB,IAAI,GAAG,CAAC;wBACN,MAAM;wBACN,KAAK;oBACP;oBACA,OAAO,GAAG,CAAC;oBACX,OAAO,eAAe,CAAC;oBACvB,OAAO,SAAS;gBAClB;YACF;QACF;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ;QAEb,MAAM,eAAe,OAAO,eAAe;QAC3C,IAAI,cAAc;YAChB,OAAO,MAAM,CAAC;YACd,OAAO,SAAS;QAClB;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;QAEb,OAAO,KAAK;QACZ,0BAA0B;QAC1B,IAAI,cAAc;YAChB,OAAO,KAAK,CAAC,OAAO,CAClB,cACA,CAAC;gBACC,IAAI,KAAK;oBACP,IAAI,YAAY,CAAC;oBACjB,IAAI,aAAa,CAAC;oBAClB,IAAI,GAAG,CAAC;wBACN,YAAY;wBACZ,SAAS;wBACT,SAAS;oBACX;oBACA,OAAO,kBAAkB,CAAC,KAAK,OAAO,SAAS,CAAC,IAAI,CAAC;gBACvD;YACF,GACA;gBAAE,aAAa;YAAY;QAE/B;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,MAAM,UAAU,OAAO,SAAS,CAAC;YAC/B,QAAQ;YACR,SAAS;YACT,YAAY;QACd;QAEA,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,GAAG;QACZ,KAAK,KAAK;IACZ;IAEA,gDAAgD;IAChD,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAIpB,6LAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;0CAID,6LAAC;gCAAM,WAAU;;kDACf,6LAAC,uMAAA,CAAA,QAAS;wCAAC,MAAM;;;;;;oCAAM;kDAEvB,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;;4CAAyB;4CAAS;;;;;;;;;;;;;0CAGpD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB;gCAC1B,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAIpB,6LAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB,QAAQ,MAAM,GAAG;gCAC3C,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAIpB,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAItB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAO,KAAK;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BAAyC;;;;;;;;;;;;AAK9D;GAraM;KAAA;uCAuaS", "debugId": null}}]}