{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/product/%5Bslug%5D/Tshirt3D.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Canvas } from \"@react-three/fiber\";\r\nimport { OrbitControls, Text } from \"@react-three/drei\";\r\nimport { useState } from \"react\";\r\nimport { HexColorPicker } from \"react-colorful\";\r\n\r\ntype Tshirt3DProps = {\r\n  color?: string;\r\n  text?: string;\r\n  onColorChange?: (color: string) => void;\r\n};\r\n\r\nexport default function Tshirt3D({\r\n  color = \"white\",\r\n  text = \"\",\r\n  onColorChange,\r\n}: Tshirt3DProps) {\r\n  const [showPicker, setShowPicker] = useState(false);\r\n  return (\r\n    <div style={{ width: \"100%\" }}>\r\n      <Canvas\r\n        style={{ height: 320, width: \"100%\", background: \"#f9f9f9\" }}\r\n        camera={{ position: [0, 0, 5], fov: 50 }}\r\n      >\r\n        <ambientLight intensity={0.7} />\r\n        <directionalLight position={[5, 5, 5]} intensity={0.7} />\r\n        {/* T-shirt body (box for demo) */}\r\n        <mesh position={[0, 0, 0]}>\r\n          <boxGeometry args={[2, 2.2, 0.5]} />\r\n          <meshStandardMaterial color={color} />\r\n        </mesh>\r\n        {/* T-shirt sleeves (cylinders for demo) */}\r\n        <mesh position={[-1.2, 0.5, 0]} rotation={[0, 0, Math.PI / 2]}>\r\n          <cylinderGeometry args={[0.25, 0.25, 1, 32]} />\r\n          <meshStandardMaterial color={color} />\r\n        </mesh>\r\n        <mesh position={[1.2, 0.5, 0]} rotation={[0, 0, Math.PI / 2]}>\r\n          <cylinderGeometry args={[0.25, 0.25, 1, 32]} />\r\n          <meshStandardMaterial color={color} />\r\n        </mesh>\r\n        {/* Custom text */}\r\n        {text && text.trim() !== \"\" && (\r\n          <Text\r\n            position={[0, 0.3, 0.28]}\r\n            fontSize={0.3}\r\n            color=\"#222\"\r\n            anchorX=\"center\"\r\n            anchorY=\"middle\"\r\n            maxWidth={1.5}\r\n          >\r\n            {text}\r\n          </Text>\r\n        )}\r\n        <OrbitControls enablePan enableZoom enableRotate />\r\n      </Canvas>\r\n      <div className=\"flex flex-col items-center mt-2\">\r\n        <button\r\n          className=\"bg-[#ffd600] text-[#171717] font-semibold px-4 py-1 rounded-full shadow hover:bg-[#ffe066] transition mb-2\"\r\n          onClick={() => setShowPicker((v) => !v)}\r\n        >\r\n          {showPicker ? \"Close Color Picker\" : \"Pick Custom Color\"}\r\n        </button>\r\n        {showPicker && (\r\n          <HexColorPicker\r\n            color={color}\r\n            onChange={onColorChange}\r\n            style={{ width: 180, height: 120 }}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAae,SAAS,SAAS,EAC/B,QAAQ,OAAO,EACf,OAAO,EAAE,EACT,aAAa,EACC;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO;QAAO;;0BAC1B,6LAAC,sMAAA,CAAA,SAAM;gBACL,OAAO;oBAAE,QAAQ;oBAAK,OAAO;oBAAQ,YAAY;gBAAU;gBAC3D,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;;kCAEvC,6LAAC;wBAAa,WAAW;;;;;;kCACzB,6LAAC;wBAAiB,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,WAAW;;;;;;kCAElD,6LAAC;wBAAK,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;;0CACvB,6LAAC;gCAAY,MAAM;oCAAC;oCAAG;oCAAK;iCAAI;;;;;;0CAChC,6LAAC;gCAAqB,OAAO;;;;;;;;;;;;kCAG/B,6LAAC;wBAAK,UAAU;4BAAC,CAAC;4BAAK;4BAAK;yBAAE;wBAAE,UAAU;4BAAC;4BAAG;4BAAG,KAAK,EAAE,GAAG;yBAAE;;0CAC3D,6LAAC;gCAAiB,MAAM;oCAAC;oCAAM;oCAAM;oCAAG;iCAAG;;;;;;0CAC3C,6LAAC;gCAAqB,OAAO;;;;;;;;;;;;kCAE/B,6LAAC;wBAAK,UAAU;4BAAC;4BAAK;4BAAK;yBAAE;wBAAE,UAAU;4BAAC;4BAAG;4BAAG,KAAK,EAAE,GAAG;yBAAE;;0CAC1D,6LAAC;gCAAiB,MAAM;oCAAC;oCAAM;oCAAM;oCAAG;iCAAG;;;;;;0CAC3C,6LAAC;gCAAqB,OAAO;;;;;;;;;;;;oBAG9B,QAAQ,KAAK,IAAI,OAAO,oBACvB,6LAAC,2JAAA,CAAA,OAAI;wBACH,UAAU;4BAAC;4BAAG;4BAAK;yBAAK;wBACxB,UAAU;wBACV,OAAM;wBACN,SAAQ;wBACR,SAAQ;wBACR,UAAU;kCAET;;;;;;kCAGL,6LAAC,oKAAA,CAAA,gBAAa;wBAAC,SAAS;wBAAC,UAAU;wBAAC,YAAY;;;;;;;;;;;;0BAElD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc,CAAC,IAAM,CAAC;kCAEpC,aAAa,uBAAuB;;;;;;oBAEtC,4BACC,6LAAC,sJAAA,CAAA,iBAAc;wBACb,OAAO;wBACP,UAAU;wBACV,OAAO;4BAAE,OAAO;4BAAK,QAAQ;wBAAI;;;;;;;;;;;;;;;;;;AAM7C;GA5DwB;KAAA", "debugId": null}}]}