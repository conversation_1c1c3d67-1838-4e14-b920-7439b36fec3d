const http = require('http');

async function testRegistration() {
  console.log('🧪 Testing Registration with Email Verification...');
  
  const testUser = {
    email: `test${Date.now()}@example.com`,
    password: 'TestPassword123!',
    name: 'Test User'
  };
  
  console.log(`📧 Test email: ${testUser.email}`);
  
  try {
    console.log('📤 Sending registration request...');
    
    const postData = JSON.stringify(testUser);
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const response = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            data: data
          });
        });
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.write(postData);
      req.end();
    });
    
    console.log(`📊 Response status: ${response.status}`);
    
    try {
      const result = JSON.parse(response.data);
      console.log('📋 Response body:', JSON.stringify(result, null, 2));
    } catch {
      console.log('📋 Response body (raw):', response.data);
    }
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Registration request successful!');
      console.log('📧 Check the backend logs for email sending details');
    } else {
      console.log('❌ Registration failed');
    }
    
  } catch (error) {
    console.error('❌ Registration test failed:', error.message);
  }
}

testRegistration();
