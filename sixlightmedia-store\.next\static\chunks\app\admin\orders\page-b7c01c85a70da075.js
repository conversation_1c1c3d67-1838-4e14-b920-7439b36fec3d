(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[798],{24:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(5155),r=s(2115),l=s(5695),i=s(4615),c=s(3389),d=s(3843);function n(e){let{user:t}=e,[s,n]=(0,r.useState)([]),[o,x]=(0,r.useState)(!0),[m,u]=(0,r.useState)(""),[h,p]=(0,r.useState)(0),[g,N]=(0,r.useState)(!1),y=(0,l.useRouter)(),j=()=>{p(JSON.parse(localStorage.getItem("cart")||"[]").length)};(0,r.useEffect)(()=>{j()},[]),(0,r.useEffect)(()=>{f()},[]);let f=async()=>{let e=localStorage.getItem("token");try{let t=await fetch((0,d.e9)(d.i3.ENDPOINTS.ADMIN.ORDERS),{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();n(e)}else u("Failed to fetch orders")}catch(e){u("Failed to fetch orders")}finally{x(!1)}},v=async(e,t)=>{let a=localStorage.getItem("token");try{(await fetch((0,d.e9)("".concat(d.i3.ENDPOINTS.ADMIN.ORDERS,"/").concat(e,"/status")),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify({status:t})})).ok?n(s.map(s=>s.id===e?{...s,status:t}:s)):u("Failed to update order status")}catch(e){u("Failed to update order status")}},b=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PROCESSING":return"bg-blue-100 text-blue-800";case"COMPLETED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return o?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{cartCount:h,onCartClick:()=>{N(!0)}}),(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-[#1a237e]",children:"Order Management"}),(0,a.jsxs)("div",{className:"mt-2 inline-flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full border border-green-200",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:["\uD83D\uDD12 Secure admin access for ",(null==t?void 0:t.name)||(null==t?void 0:t.email)]})]})]}),(0,a.jsx)("button",{onClick:()=>y.push("/admin/dashboard"),className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition",children:"Back to Dashboard"})]}),m&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:m}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h2",{className:"text-xl font-semibold",children:["All Orders (",s.length,")"]})}),0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Orders will appear here when customers place them."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order Details"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer Info"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customization"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"font-medium text-gray-900",children:["Order #",e.id]}),(0,a.jsx)("div",{className:"text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("div",{className:"text-gray-500",children:["Qty: ",e.quantity]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.customerName}),(0,a.jsx)("div",{className:"text-gray-500",children:e.customerPhone}),(0,a.jsx)("div",{className:"text-gray-500",children:e.customerEmail}),(0,a.jsx)("div",{className:"text-gray-500 text-xs mt-1 max-w-xs",children:e.customerAddress})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.product.name}),(0,a.jsxs)("div",{className:"text-gray-500",children:["K",e.unitPrice.toFixed(2)," each"]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm",children:e.isCustomized?(0,a.jsxs)("div",{className:"space-y-2",children:[e.customizationPreview&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-16 h-16 border border-gray-300 rounded-lg overflow-hidden bg-gray-50 flex-shrink-0",children:(0,a.jsx)("img",{src:e.customizationPreview,alt:"Customization preview",className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded mb-1",children:[(0,a.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"})}),"Advanced Design"]}),(0,a.jsx)("button",{onClick:()=>{if(e.customizationPreview){let t=document.createElement("a");t.href=e.customizationPreview,t.download="order-".concat(e.id,"-design.png"),t.click()}},className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"Download Design"})]})]}),e.customColor&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:e.customColor}}),(0,a.jsx)("span",{className:"text-xs font-mono",children:e.customColor})]}),e.customText&&(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Text: “",e.customText,"”"]})]}):(0,a.jsx)("span",{className:"text-gray-400 text-xs",children:"No customization"})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["K",e.totalPrice.toFixed(2)]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(b(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:(0,a.jsxs)("select",{value:e.status,onChange:t=>v(e.id,t.target.value),className:"border border-gray-300 rounded px-2 py-1 text-xs",children:[(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"PROCESSING",children:"Processing"}),(0,a.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,a.jsx)("option",{value:"CANCELLED",children:"Cancelled"})]})})]},e.id))})]})})]})]})}),(0,a.jsx)(c.A,{isOpen:g,onClose:()=>{N(!1),j()}})]})}},2567:(e,t,s)=>{Promise.resolve().then(s.bind(s,24))},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:x,iconNode:m,...u}=e;return(0,a.createElement)("svg",{ref:t,...n,width:r,height:r,stroke:s,strokeWidth:i?24*Number(l)/Number(r):l,className:c("lucide",o),...!x&&!d(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:d,...n}=s;return(0,a.createElement)(o,{ref:l,iconNode:t,className:c("lucide-".concat(r(i(e))),"lucide-".concat(e),d),...n})});return s.displayName=i(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,211,441,684,358],()=>t(2567)),_N_E=e.O()}]);