{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACK;AAAtB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,uCAAgC,aAAa,OAAO,CAAC;IACvD,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport default function Header({\r\n  cartCount = 0,\r\n  onCartClick,\r\n}: {\r\n  cartCount?: number;\r\n  onCartClick?: () => void;\r\n}) {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n  const [userRole, setUserRole] = useState<string | null>(null);\r\n  const [profileImage, setProfileImage] = useState<string | null>(null);\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function handleProfileImageUpdated() {\r\n      const token = localStorage.getItem(\"token\");\r\n      if (token) {\r\n        try {\r\n          // Fetch latest user data from API instead of relying on JWT token\r\n          const response = await fetch(\r\n            getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD),\r\n            {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n          );\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data && data.user) {\r\n              setProfileImage(data.user.profileImage || null);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Failed to fetch updated profile image:\", error);\r\n          // Fallback to JWT token method\r\n          try {\r\n            const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n            setProfileImage(payload.profileImage || null);\r\n          } catch {\r\n            setProfileImage(null);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n      const token = localStorage.getItem(\"token\");\r\n      setIsLoggedIn(!!token);\r\n      if (token) {\r\n        // Decode JWT to get role (simple base64 decode, not secure for prod, but fine for client display)\r\n        try {\r\n          const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n          setUserRole(payload.role || null);\r\n          setProfileImage(payload.profileImage || null);\r\n        } catch {\r\n          setUserRole(null);\r\n          setProfileImage(null);\r\n        }\r\n      } else {\r\n        setUserRole(null);\r\n        setProfileImage(null);\r\n      }\r\n      // Listen for profile image update event\r\n      window.addEventListener(\"profileImageUpdated\", handleProfileImageUpdated);\r\n    }\r\n    // Cleanup event listener\r\n    return () => {\r\n      window.removeEventListener(\r\n        \"profileImageUpdated\",\r\n        handleProfileImageUpdated\r\n      );\r\n    };\r\n  }, []);\r\n\r\n  // Prevent background scroll and horizontal scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n      document.documentElement.style.overflowX = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close menus on Escape key\r\n  useEffect(() => {\r\n    function handleKeyDown(e: KeyboardEvent) {\r\n      if (e.key === \"Escape\") {\r\n        setShowDropdown(false);\r\n        setMobileMenuOpen(false);\r\n      }\r\n    }\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, []);\r\n\r\n  // Ensure dropdown closes when mobile menu opens\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) setShowDropdown(false);\r\n  }, [mobileMenuOpen]);\r\n\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"token\");\r\n    setIsLoggedIn(false);\r\n    window.location.href = \"/login\";\r\n  }\r\n\r\n  function handleDropdownToggle() {\r\n    setShowDropdown((prev) => !prev);\r\n  }\r\n\r\n  function handleDropdownClose() {\r\n    setShowDropdown(false);\r\n  }\r\n\r\n  return (\r\n    <header className=\"w-full bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-30\">\r\n      <nav className=\"max-w-7xl mx-auto flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 md:py-4\">\r\n        {/* Brand Logo */}\r\n        <Link href=\"/\" className=\"flex items-center gap-2 min-w-0 group\">\r\n          <div className=\"relative\">\r\n            <Image\r\n              src=\"/6 Light Logo.png\"\r\n              alt=\"Six Light Media Logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"h-8 w-auto sm:h-10 md:h-12 transition-transform duration-300 group-hover:scale-110\"\r\n              priority\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl\"></div>\r\n          </div>\r\n          <span className=\"truncate text-base xs:text-lg sm:text-2xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-indigo-500 group-hover:via-purple-500 group-hover:to-pink-500 transition-all duration-300\">\r\n            Store\r\n          </span>\r\n        </Link>\r\n        {/* Desktop Nav */}\r\n        <div className=\"hidden sm:flex gap-2 md:gap-4 lg:gap-6 items-center min-w-0\">\r\n          <Link\r\n            href=\"/\"\r\n            className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n          >\r\n            🏠 Home\r\n          </Link>\r\n          {isLoggedIn && (\r\n            <Link\r\n              href=\"/user/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n            >\r\n              📊 Dashboard\r\n            </Link>\r\n          )}\r\n          {isLoggedIn && userRole === \"ADMIN\" && (\r\n            <Link\r\n              href=\"/admin/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n            >\r\n              ⚡ Admin\r\n            </Link>\r\n          )}\r\n          {!isLoggedIn ? (\r\n            <>\r\n              <Link\r\n                href=\"/login\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                🔐 Login\r\n              </Link>\r\n              <Link\r\n                href=\"/register\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                ✨ Register\r\n              </Link>\r\n            </>\r\n          ) : (\r\n            <div className=\"relative min-w-0\">\r\n              <button\r\n                className=\"flex items-center gap-2 focus:outline-none min-w-0 px-2 py-1 rounded-2xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                onClick={handleDropdownToggle}\r\n                aria-label=\"Open user menu\"\r\n              >\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                    className=\"rounded-full border-2 border-gradient-to-r from-indigo-200 to-purple-200 object-cover bg-white h-8 w-8 md:h-9 md:w-9 shadow-md\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <span className=\"hidden md:inline font-semibold text-gray-700 truncate max-w-[80px]\">\r\n                  Account\r\n                </span>\r\n                <svg\r\n                  className={`w-4 h-4 ml-1 text-gray-600 transition-transform duration-300 ${\r\n                    showDropdown ? \"rotate-180\" : \"\"\r\n                  }`}\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M19 9l-7 7-7-7\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n              {showDropdown && (\r\n                <div\r\n                  className=\"absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-lg border border-gray-200 rounded-2xl shadow-2xl z-50 overflow-hidden\"\r\n                  onMouseLeave={handleDropdownClose}\r\n                >\r\n                  <div className=\"p-2\">\r\n                    <Link\r\n                      href=\"/user/dashboard\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">📊</span>\r\n                      Dashboard\r\n                    </Link>\r\n                    <Link\r\n                      href=\"/user/profile\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">⚙️</span>\r\n                      Profile Settings\r\n                    </Link>\r\n                    <div className=\"border-t border-gray-100 my-2\"></div>\r\n                    <button\r\n                      onClick={() => {\r\n                        handleLogout();\r\n                        handleDropdownClose();\r\n                      }}\r\n                      className=\"flex items-center gap-3 w-full text-left px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                    >\r\n                      <span className=\"text-lg\">🚪</span>\r\n                      Logout\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {/* Responsive Mobile Menu Button */}\r\n        <button\r\n          type=\"button\"\r\n          className=\"flex items-center justify-center p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 sm:hidden hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300\"\r\n          aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\r\n          onClick={() => setMobileMenuOpen((open) => !open)}\r\n        >\r\n          {mobileMenuOpen ? (\r\n            <X\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={!mobileMenuOpen}\r\n            />\r\n          ) : (\r\n            <Menu\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={mobileMenuOpen}\r\n            />\r\n          )}\r\n        </button>\r\n        {/* Cart Button */}\r\n        <button\r\n          className=\"relative px-3 py-1 md:px-4 md:py-2 rounded-2xl font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg hover:from-red-600 hover:to-pink-700 hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 ml-1 md:ml-2 text-sm md:text-base transform hover:-translate-y-0.5\"\r\n          onClick={onCartClick}\r\n          aria-label=\"Open cart\"\r\n        >\r\n          <span className=\"hidden sm:inline\">🛒 Cart</span>\r\n          <span className=\"sm:hidden\">🛒</span>\r\n          <span className=\"sm:ml-1\">({cartCount})</span>\r\n          {cartCount > 0 && (\r\n            <span className=\"absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs rounded-full px-1.5 py-0.5 animate-bounce font-bold shadow-lg\">\r\n              {cartCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </nav>\r\n      {/* Mobile Nav Drawer Overlay & Drawer: Only render when open */}\r\n      {mobileMenuOpen && (\r\n        <>\r\n          {/* Overlay */}\r\n          <div\r\n            className=\"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300 block\"\r\n            style={{ left: 0, right: 0 }}\r\n            onClick={() => setMobileMenuOpen(false)}\r\n            aria-hidden={!mobileMenuOpen}\r\n          />\r\n          {/* Drawer */}\r\n          <div\r\n            className=\"fixed top-0 right-0 z-50 w-full max-w-xs h-screen bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs min-w-0 sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden translate-x-0 border-l border-gray-200\"\r\n            role=\"dialog\"\r\n            aria-modal=\"true\"\r\n            tabIndex={-1}\r\n            style={{ right: 0, left: \"auto\" }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Tab\") {\r\n                // Basic focus trap: keep focus inside drawer\r\n                const focusable = Array.from(\r\n                  (e.currentTarget as HTMLElement).querySelectorAll(\r\n                    'a, button, input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n                  )\r\n                ) as HTMLElement[];\r\n                if (focusable.length === 0) return;\r\n                const first = focusable[0];\r\n                const last = focusable[focusable.length - 1];\r\n                if (!e.shiftKey && document.activeElement === last) {\r\n                  e.preventDefault();\r\n                  first.focus();\r\n                } else if (e.shiftKey && document.activeElement === first) {\r\n                  e.preventDefault();\r\n                  last.focus();\r\n                }\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center justify-between px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n              <span className=\"font-black text-lg bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\r\n                📱 Menu\r\n              </span>\r\n              <button\r\n                className=\"p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 hover:bg-white/50 transition-all duration-300\"\r\n                aria-label=\"Close menu\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n                tabIndex={mobileMenuOpen ? 0 : -1}\r\n              >\r\n                <X className=\"w-6 h-6 text-gray-700\" />\r\n              </button>\r\n            </div>\r\n            <nav className=\"flex flex-col gap-2 px-4 py-6 flex-1 overflow-y-auto max-h-[calc(100vh-80px)]\">\r\n              <Link\r\n                href=\"/\"\r\n                className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n              >\r\n                <span className=\"text-lg\">🏠</span>\r\n                Home\r\n              </Link>\r\n              {isLoggedIn && (\r\n                <Link\r\n                  href=\"/user/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">📊</span>\r\n                  Dashboard\r\n                </Link>\r\n              )}\r\n              {isLoggedIn && userRole === \"ADMIN\" && (\r\n                <Link\r\n                  href=\"/admin/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">⚡</span>\r\n                  Admin Panel\r\n                </Link>\r\n              )}\r\n              {!isLoggedIn ? (\r\n                <>\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">🔐</span>\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">✨</span>\r\n                    Register\r\n                  </Link>\r\n                </>\r\n              ) : (\r\n                <div className=\"flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200\">\r\n                  <Link\r\n                    href=\"/user/profile\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">⚙️</span>\r\n                    Profile Settings\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleLogout();\r\n                      setMobileMenuOpen(false);\r\n                    }}\r\n                    className=\"flex items-center gap-3 w-full text-left px-4 py-3 rounded-2xl font-semibold text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  >\r\n                    <span className=\"text-lg\">🚪</span>\r\n                    Logout\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </nav>\r\n            {isLoggedIn && (\r\n              <div className=\"flex items-center gap-3 px-4 py-4 border-t border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={40}\r\n                    height={40}\r\n                    className=\"rounded-full border-2 border-indigo-200 object-cover bg-white shadow-md w-10 h-10\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"font-bold text-gray-900 truncate text-sm\">\r\n                    Account\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-600 truncate\">Logged in</p>\r\n                  <span className=\"inline-block mt-1 px-2 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full\">\r\n                    {userRole}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAQe,SAAS,OAAO,EAC7B,YAAY,CAAC,EACb,WAAW,EAIZ;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,eAAe;gBACb,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,IAAI;wBACF,kEAAkE;wBAClE,MAAM,WAAW,MAAM,MACrB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAC7C;4BACE,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBAGF,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,IAAI,QAAQ,KAAK,IAAI,EAAE;gCACrB,gBAAgB,KAAK,IAAI,CAAC,YAAY,IAAI;4BAC5C;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0CAA0C;wBACxD,+BAA+B;wBAC/B,IAAI;4BACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;4BACnD,gBAAgB,QAAQ,YAAY,IAAI;wBAC1C,EAAE,OAAM;4BACN,gBAAgB;wBAClB;oBACF;gBACF;YACF;YACA,wCAAmC;gBACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,cAAc,CAAC,CAAC;gBAChB,IAAI,OAAO;oBACT,kGAAkG;oBAClG,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;wBACnD,YAAY,QAAQ,IAAI,IAAI;wBAC5B,gBAAgB,QAAQ,YAAY,IAAI;oBAC1C,EAAE,OAAM;wBACN,YAAY;wBACZ,gBAAgB;oBAClB;gBACF,OAAO;oBACL,YAAY;oBACZ,gBAAgB;gBAClB;gBACA,wCAAwC;gBACxC,OAAO,gBAAgB,CAAC,uBAAuB;YACjD;YACA,yBAAyB;YACzB;oCAAO;oBACL,OAAO,mBAAmB,CACxB,uBACA;gBAEJ;;QACF;2BAAG,EAAE;IAEL,2EAA2E;IAC3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;YAC7C,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;YAC7C;YACA;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;oBAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;gBAC7C;;QACF;2BAAG;QAAC;KAAe;IAEnB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,cAAc,CAAgB;gBACrC,IAAI,EAAE,GAAG,KAAK,UAAU;oBACtB,gBAAgB;oBAChB,kBAAkB;gBACpB;YACF;YACA,OAAO,gBAAgB,CAAC,WAAW;YACnC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;2BAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB,gBAAgB;QACtC;2BAAG;QAAC;KAAe;IAEnB,SAAS;QACP,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,SAAS;QACP,gBAAgB,CAAC,OAAS,CAAC;IAC7B;IAEA,SAAS;QACP,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,QAAQ;;;;;;kDAEV,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;gCAAK,WAAU;0CAA2Q;;;;;;;;;;;;kCAK7R,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,4BACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,cAAc,aAAa,yBAC1B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,CAAC,2BACA;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;6DAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;;0DAEX,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,gBAAgB;wDACrB,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,6LAAC;gDAAK,WAAU;0DAAqE;;;;;;0DAGrF,6LAAC;gDACC,WAAW,CAAC,6DAA6D,EACvE,eAAe,eAAe,IAC9B;gDACF,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;oCAIP,8BACC,6LAAC;wCACC,WAAU;wCACV,cAAc;kDAEd,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,6LAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,6LAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDACC,SAAS;wDACP;wDACA;oDACF;oDACA,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUjD,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,cAAY,iBAAiB,eAAe;wBAC5C,SAAS,IAAM,kBAAkB,CAAC,OAAS,CAAC;kCAE3C,+BACC,6LAAC,+LAAA,CAAA,IAAC;4BACA,WAAU;4BACV,eAAa,CAAC;;;;;iDAGhB,6LAAC,qMAAA,CAAA,OAAI;4BACH,WAAU;4BACV,eAAa;;;;;;;;;;;kCAKnB,6LAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAW;;0CAEX,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,6LAAC;gCAAK,WAAU;0CAAY;;;;;;0CAC5B,6LAAC;gCAAK,WAAU;;oCAAU;oCAAE;oCAAU;;;;;;;4BACrC,YAAY,mBACX,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAMR,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,MAAM;4BAAG,OAAO;wBAAE;wBAC3B,SAAS,IAAM,kBAAkB;wBACjC,eAAa,CAAC;;;;;;kCAGhB,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,cAAW;wBACX,UAAU,CAAC;wBACX,OAAO;4BAAE,OAAO;4BAAG,MAAM;wBAAO;wBAChC,WAAW,CAAC;4BACV,IAAI,EAAE,GAAG,KAAK,OAAO;gCACnB,6CAA6C;gCAC7C,MAAM,YAAY,MAAM,IAAI,CAC1B,AAAC,EAAE,aAAa,CAAiB,gBAAgB,CAC/C;gCAGJ,IAAI,UAAU,MAAM,KAAK,GAAG;gCAC5B,MAAM,QAAQ,SAAS,CAAC,EAAE;gCAC1B,MAAM,OAAO,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;gCAC5C,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,MAAM;oCAClD,EAAE,cAAc;oCAChB,MAAM,KAAK;gCACb,OAAO,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,OAAO;oCACzD,EAAE,cAAc;oCAChB,KAAK,KAAK;gCACZ;4BACF;wBACF;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAkG;;;;;;kDAGlH,6LAAC;wCACC,WAAU;wCACV,cAAW;wCACX,SAAS,IAAM,kBAAkB;wCACjC,UAAU,iBAAiB,IAAI,CAAC;kDAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAGpC,4BACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAItC,cAAc,aAAa,yBAC1B,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAQ;;;;;;;oCAIrC,CAAC,2BACA;;0DACE,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;qEAKtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,6LAAC;gDACC,SAAS;oDACP;oDACA,kBAAkB;gDACpB;gDACA,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;4BAM1C,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,gBAAgB;gDACrB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,6LAAC;gDAAE,WAAU;0DAAiC;;;;;;0DAC9C,6LAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;GAtbwB;KAAA", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/OrderForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  price: number;\n  quantity?: number;\n};\n\ntype OrderFormProps = {\n  cartItems: CartItem[];\n  onOrderSuccess: () => void;\n  onCancel: () => void;\n};\n\ntype OrderFormData = {\n  customerName: string;\n  customerPhone: string;\n  customerAddress: string;\n};\n\nexport default function OrderForm({\n  cartItems,\n  onOrderSuccess,\n  onCancel,\n}: OrderFormProps) {\n  const [formData, setFormData] = useState<OrderFormData>({\n    customerName: \"\",\n    customerPhone: \"\",\n    customerAddress: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  useEffect(() => {\n    // Get user info to pre-fill name\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD), {\n        headers: { Authorization: `Bearer ${token}` },\n      })\n        .then((res) => res.json())\n        .then((data) => {\n          if (data && data.user) {\n            setFormData((prev) => ({\n              ...prev,\n              customerName: data.user.name || \"\",\n            }));\n          }\n        })\n        .catch(() => {\n          // Ignore error, user can still fill form manually\n        });\n    }\n  }, []);\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setError(\"Please log in to place an order\");\n      setLoading(false);\n      return;\n    }\n\n    try {\n      // Create orders for each cart item\n      const orderPromises = cartItems.map((item) =>\n        fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.ORDERS), {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({\n            productId: item.productId,\n            customerName: formData.customerName,\n            customerPhone: formData.customerPhone,\n            customerAddress: formData.customerAddress,\n            customColor: item.color,\n            customText: item.text,\n            isCustomized: item.customized,\n            quantity: item.quantity || 1,\n          }),\n        })\n      );\n\n      const responses = await Promise.all(orderPromises);\n\n      // Check if all orders were successful\n      const allSuccessful = responses.every((res) => res.ok);\n\n      if (allSuccessful) {\n        // Clear cart\n        localStorage.removeItem(\"cart\");\n        onOrderSuccess();\n      } else {\n        setError(\"Some orders failed to process. Please try again.\");\n      }\n    } catch {\n      setError(\"Failed to place order. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">\n              Complete Your Order\n            </h2>\n            <button\n              onClick={onCancel}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close\"\n            >\n              ×\n            </button>\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h3 className=\"font-semibold mb-3\">Order Summary</h3>\n            {cartItems.map((item, index) => (\n              <div\n                key={index}\n                className=\"flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0\"\n              >\n                <div>\n                  <div className=\"font-medium\">{item.name}</div>\n                  {item.customized && (\n                    <div className=\"text-sm text-gray-600\">\n                      {item.color && <span>Color: {item.color}</span>}\n                      {item.text && (\n                        <span className=\"ml-2\">\n                          Text: &ldquo;{item.text}&rdquo;\n                        </span>\n                      )}\n                    </div>\n                  )}\n                  <div className=\"text-sm text-gray-500\">\n                    Qty: {item.quantity || 1}\n                  </div>\n                </div>\n                <div className=\"font-semibold\">\n                  K{(item.price * (item.quantity || 1)).toFixed(2)}\n                </div>\n              </div>\n            ))}\n            <div className=\"flex justify-between items-center pt-3 mt-3 border-t border-gray-300\">\n              <div className=\"font-bold text-lg\">Total:</div>\n              <div className=\"font-bold text-lg text-[#1a237e]\">\n                K{calculateTotal().toFixed(2)}\n              </div>\n            </div>\n          </div>\n\n          {/* Order Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label\n                htmlFor=\"customerName\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Full Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"customerName\"\n                name=\"customerName\"\n                value={formData.customerName}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerPhone\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Phone Number *\n              </label>\n              <input\n                type=\"tel\"\n                id=\"customerPhone\"\n                name=\"customerPhone\"\n                value={formData.customerPhone}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"e.g. +260 971 781 907\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerAddress\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Delivery/Pickup Address *\n              </label>\n              <textarea\n                id=\"customerAddress\"\n                name=\"customerAddress\"\n                value={formData.customerAddress}\n                onChange={handleInputChange}\n                required\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full address or preferred pickup location\"\n              />\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm bg-red-50 p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"flex gap-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={onCancel}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold transition ${\n                  loading\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"hover:bg-[#2a3490]\"\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center gap-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                    <span>Placing Order...</span>\n                  </div>\n                ) : (\n                  `Place Order - K${calculateTotal().toFixed(2)}`\n                )}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-4 text-xs text-gray-500 text-center\">\n            By placing this order, you agree to our terms and conditions. You\n            will be contacted for payment and delivery arrangements.\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA4Be,SAAS,UAAU,EAChC,SAAS,EACT,cAAc,EACd,QAAQ,EACO;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,cAAc;QACd,eAAe;QACf,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,iCAAiC;YACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG;oBACpD,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAC9C,GACG,IAAI;2CAAC,CAAC,MAAQ,IAAI,IAAI;0CACtB,IAAI;2CAAC,CAAC;wBACL,IAAI,QAAQ,KAAK,IAAI,EAAE;4BACrB;uDAAY,CAAC,OAAS,CAAC;wCACrB,GAAG,IAAI;wCACP,cAAc,KAAK,IAAI,CAAC,IAAI,IAAI;oCAClC,CAAC;;wBACH;oBACF;0CACC,KAAK;2CAAC;oBACL,kDAAkD;oBACpD;;YACJ;QACF;8BAAG,EAAE;IAEL,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,mCAAmC;YACnC,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAC,OACnC,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;oBAClC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,WAAW,KAAK,SAAS;wBACzB,cAAc,SAAS,YAAY;wBACnC,eAAe,SAAS,aAAa;wBACrC,iBAAiB,SAAS,eAAe;wBACzC,aAAa,KAAK,KAAK;wBACvB,YAAY,KAAK,IAAI;wBACrB,cAAc,KAAK,UAAU;wBAC7B,UAAU,KAAK,QAAQ,IAAI;oBAC7B;gBACF;YAGF,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC;YAEpC,sCAAsC;YACtC,MAAM,gBAAgB,UAAU,KAAK,CAAC,CAAC,MAAQ,IAAI,EAAE;YAErD,IAAI,eAAe;gBACjB,aAAa;gBACb,aAAa,UAAU,CAAC;gBACxB;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;4BAClC,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAe,KAAK,IAAI;;;;;;gDACtC,KAAK,UAAU,kBACd,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,kBAAI,6LAAC;;gEAAK;gEAAQ,KAAK,KAAK;;;;;;;wDACtC,KAAK,IAAI,kBACR,6LAAC;4DAAK,WAAU;;gEAAO;gEACP,KAAK,IAAI;gEAAC;;;;;;;;;;;;;8DAKhC,6LAAC;oDAAI,WAAU;;wDAAwB;wDAC/B,KAAK,QAAQ,IAAI;;;;;;;;;;;;;sDAG3B,6LAAC;4CAAI,WAAU;;gDAAgB;gDAC3B,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;;;;;;;;mCApB3C;;;;;0CAwBT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoB;;;;;;kDACnC,6LAAC;wCAAI,WAAU;;4CAAmC;4CAC9C,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kCAMjC,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;;kDACC,6LAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,QAAQ;wCACR,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAIf,uBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,6EAA6E,EACvF,UACI,kCACA,sBACJ;kDAED,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;mDAGR,CAAC,eAAe,EAAE,iBAAiB,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;kCAMvD,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;;;;;AAQlE;GApQwB;KAAA", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Cart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport OrderForm from \"./OrderForm\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  price: number;\n  quantity?: number;\n};\n\ntype CartProps = {\n  isOpen: boolean;\n  onClose: () => void;\n};\n\nexport default function Cart({ isOpen, onClose }: CartProps) {\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n  const [showOrderForm, setShowOrderForm] = useState(false);\n  const [orderSuccess, setOrderSuccess] = useState(false);\n\n  useEffect(() => {\n    if (isOpen) {\n      loadCartItems();\n    }\n  }, [isOpen]);\n\n  const loadCartItems = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\n    setCartItems(cart);\n  };\n\n  const removeItem = (itemId: number) => {\n    const updatedCart = cartItems.filter((item) => item.id !== itemId);\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const updateQuantity = (itemId: number, newQuantity: number) => {\n    if (newQuantity < 1) {\n      removeItem(itemId);\n      return;\n    }\n\n    const updatedCart = cartItems.map((item) =>\n      item.id === itemId ? { ...item, quantity: newQuantity } : item\n    );\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleCheckout = () => {\n    setShowOrderForm(true);\n  };\n\n  const handleOrderSuccess = () => {\n    setShowOrderForm(false);\n    setOrderSuccess(true);\n    setCartItems([]);\n\n    // Close success message after 3 seconds\n    setTimeout(() => {\n      setOrderSuccess(false);\n      onClose();\n    }, 3000);\n  };\n\n  if (!isOpen) return null;\n\n  if (showOrderForm) {\n    return (\n      <OrderForm\n        cartItems={cartItems}\n        onOrderSuccess={handleOrderSuccess}\n        onCancel={() => setShowOrderForm(false)}\n      />\n    );\n  }\n\n  if (orderSuccess) {\n    return (\n      <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-xl shadow-lg p-8 text-center max-w-md\">\n          <div className=\"text-green-600 text-6xl mb-4\">✓</div>\n          <h2 className=\"text-2xl font-bold text-[#1a237e] mb-2\">\n            Order Placed Successfully!\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Thank you for your order. We will contact you soon for payment and\n            delivery arrangements.\n          </p>\n          <div className=\"animate-pulse text-sm text-gray-500\">\n            Closing automatically...\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">Shopping Cart</h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close cart\"\n            >\n              ×\n            </button>\n          </div>\n\n          {cartItems.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">🛒</div>\n              <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n                Your cart is empty\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                Add some products to get started!\n              </p>\n              <button\n                onClick={onClose}\n                className=\"px-6 py-2 bg-[#1a237e] text-white rounded-md hover:bg-[#2a3490] transition\"\n              >\n                Continue Shopping\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Cart Items */}\n              <div className=\"space-y-4 mb-6\">\n                {cartItems.map((item) => (\n                  <div\n                    key={item.id}\n                    className=\"flex items-center gap-4 p-4 border border-gray-200 rounded-lg\"\n                  >\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-lg\">{item.name}</h3>\n                      {item.customized && (\n                        <div className=\"text-sm text-gray-600 mt-1\">\n                          {item.color && (\n                            <div className=\"flex items-center gap-2\">\n                              <span>Color:</span>\n                              <div\n                                className=\"w-4 h-4 rounded border border-gray-300\"\n                                style={{ backgroundColor: item.color }}\n                              ></div>\n                              <span className=\"font-mono text-xs\">\n                                {item.color}\n                              </span>\n                            </div>\n                          )}\n                          {item.text && (\n                            <div className=\"mt-1\">\n                              <span>Text: &ldquo;</span>\n                              <span className=\"font-medium\">{item.text}</span>\n                              <span>&rdquo;</span>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                      <div className=\"text-lg font-semibold text-[#1a237e] mt-2\">\n                        K{item.price.toFixed(2)} each\n                      </div>\n                    </div>\n\n                    {/* Quantity Controls */}\n                    <div className=\"flex items-center gap-2\">\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) - 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        -\n                      </button>\n                      <span className=\"w-8 text-center font-semibold\">\n                        {item.quantity || 1}\n                      </span>\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) + 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        +\n                      </button>\n                    </div>\n\n                    {/* Remove Button */}\n                    <button\n                      onClick={() => removeItem(item.id)}\n                      className=\"text-red-500 hover:text-red-700 p-2\"\n                      aria-label=\"Remove item\"\n                    >\n                      🗑️\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              {/* Cart Total */}\n              <div className=\"border-t border-gray-200 pt-4 mb-6\">\n                <div className=\"flex justify-between items-center text-xl font-bold\">\n                  <span>Total:</span>\n                  <span className=\"text-[#1a237e]\">\n                    K{calculateTotal().toFixed(2)}\n                  </span>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={onClose}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n                >\n                  Continue Shopping\n                </button>\n                <button\n                  onClick={handleCheckout}\n                  className=\"flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold hover:bg-[#2a3490] transition\"\n                >\n                  Proceed to Checkout\n                </button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAqBe,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO,EAAa;;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;yBAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QAC3D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,cAAc,GAAG;YACnB,WAAW;YACX;QACF;QAEA,MAAM,cAAc,UAAU,GAAG,CAAC,CAAC,OACjC,KAAK,EAAE,KAAK,SAAS;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY,IAAI;QAE5D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,gBAAgB;QAChB,aAAa,EAAE;QAEf,wCAAwC;QACxC,WAAW;YACT,gBAAgB;YAChB;QACF,GAAG;IACL;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,IAAI,eAAe;QACjB,qBACE,6LAAC,kIAAA,CAAA,UAAS;YACR,WAAW;YACX,gBAAgB;YAChB,UAAU,IAAM,iBAAiB;;;;;;IAGvC;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;kCAC9C,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAM7D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;oBAKF,UAAU,MAAM,KAAK,kBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;6CAKH;;0CAEE,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyB,KAAK,IAAI;;;;;;oDAC/C,KAAK,UAAU,kBACd,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,kBACT,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,KAAK,KAAK;wEAAC;;;;;;kFAEvC,6LAAC;wEAAK,WAAU;kFACb,KAAK,KAAK;;;;;;;;;;;;4DAIhB,KAAK,IAAI,kBACR,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAKd,6LAAC;wDAAI,WAAU;;4DAA4C;4DACvD,KAAK,KAAK,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAK5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ,IAAI;;;;;;kEAEpB,6LAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;;;;;;;0DAMH,6LAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;gDACV,cAAW;0DACZ;;;;;;;uCA7DI,KAAK,EAAE;;;;;;;;;;0CAqElB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;;gDAAiB;gDAC7B,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAlOwB;KAAA", "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/ProductCustomizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { fabric } from \"fabric\";\nimport {\n  Type,\n  Image as ImageIcon,\n  Palette,\n  RotateCw,\n  Move,\n  Trash2,\n  Download,\n  Upload,\n  Undo,\n  Redo,\n  Save,\n} from \"lucide-react\";\n\ninterface CustomizationData {\n  canvasData?: string;\n  preview?: string;\n}\n\ninterface ProductCustomizerProps {\n  productImage: string;\n  onCustomizationChange: (customization: CustomizationData) => void;\n  initialCustomization?: CustomizationData | null;\n}\n\nconst ProductCustomizer: React.FC<ProductCustomizerProps> = ({\n  productImage,\n  onCustomizationChange,\n  initialCustomization,\n}) => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);\n  const [selectedTool, setSelectedTool] = useState<string>(\"select\");\n  const [textColor, setTextColor] = useState(\"#000000\");\n  const [fontSize, setFontSize] = useState(20);\n  const [fontFamily, setFontFamily] = useState(\"Arial\");\n  const [history, setHistory] = useState<string[]>([]);\n  const [historyIndex, setHistoryIndex] = useState(-1);\n\n  // Initialize Fabric.js canvas\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    const fabricCanvas = new fabric.Canvas(canvasRef.current, {\n      width: 500,\n      height: 500,\n      backgroundColor: \"#ffffff\",\n    });\n\n    // Load product image as background\n    if (productImage) {\n      fabric.Image.fromURL(\n        productImage,\n        (img) => {\n          if (img) {\n            img.scaleToWidth(500);\n            img.scaleToHeight(500);\n            img.set({\n              selectable: false,\n              evented: false,\n              opacity: 0.8,\n            });\n            fabricCanvas.setBackgroundImage(\n              img,\n              fabricCanvas.renderAll.bind(fabricCanvas)\n            );\n          }\n        },\n        { crossOrigin: \"anonymous\" }\n      );\n    }\n\n    // Canvas event listeners\n    fabricCanvas.on(\"object:added\", saveState);\n    fabricCanvas.on(\"object:removed\", saveState);\n    fabricCanvas.on(\"object:modified\", saveState);\n\n    setCanvas(fabricCanvas);\n    saveState();\n\n    return () => {\n      fabricCanvas.dispose();\n    };\n  }, [productImage]);\n\n  // Save canvas state for undo/redo\n  const saveState = () => {\n    if (!canvas) return;\n\n    const state = JSON.stringify(canvas.toJSON());\n    setHistory((prev) => {\n      const newHistory = prev.slice(0, historyIndex + 1);\n      newHistory.push(state);\n      return newHistory.slice(-20); // Keep last 20 states\n    });\n    setHistoryIndex((prev) => prev + 1);\n\n    // Notify parent of changes\n    onCustomizationChange({\n      canvasData: state,\n      preview: canvas.toDataURL(),\n    });\n  };\n\n  // Undo functionality\n  const undo = () => {\n    if (historyIndex > 0 && canvas) {\n      setHistoryIndex((prev) => prev - 1);\n      canvas.loadFromJSON(history[historyIndex - 1], () => {\n        canvas.renderAll();\n      });\n    }\n  };\n\n  // Redo functionality\n  const redo = () => {\n    if (historyIndex < history.length - 1 && canvas) {\n      setHistoryIndex((prev) => prev + 1);\n      canvas.loadFromJSON(history[historyIndex + 1], () => {\n        canvas.renderAll();\n      });\n    }\n  };\n\n  // Add text to canvas\n  const addText = () => {\n    if (!canvas) return;\n\n    const text = new fabric.IText(\"Click to edit\", {\n      left: 100,\n      top: 100,\n      fontFamily: fontFamily,\n      fontSize: fontSize,\n      fill: textColor,\n    });\n\n    canvas.add(text);\n    canvas.setActiveObject(text);\n    canvas.renderAll();\n  };\n\n  // Add shape to canvas\n  const addShape = (shapeType: string) => {\n    if (!canvas) return;\n\n    let shape;\n    switch (shapeType) {\n      case \"rectangle\":\n        shape = new fabric.Rect({\n          left: 100,\n          top: 100,\n          width: 100,\n          height: 100,\n          fill: textColor,\n        });\n        break;\n      case \"circle\":\n        shape = new fabric.Circle({\n          left: 100,\n          top: 100,\n          radius: 50,\n          fill: textColor,\n        });\n        break;\n      case \"triangle\":\n        shape = new fabric.Triangle({\n          left: 100,\n          top: 100,\n          width: 100,\n          height: 100,\n          fill: textColor,\n        });\n        break;\n    }\n\n    if (shape) {\n      canvas.add(shape);\n      canvas.setActiveObject(shape);\n      canvas.renderAll();\n    }\n  };\n\n  // Upload and add image\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !canvas) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const imgUrl = e.target?.result as string;\n      fabric.Image.fromURL(imgUrl, (img) => {\n        if (img) {\n          img.scaleToWidth(150);\n          img.set({\n            left: 100,\n            top: 100,\n          });\n          canvas.add(img);\n          canvas.setActiveObject(img);\n          canvas.renderAll();\n        }\n      });\n    };\n    reader.readAsDataURL(file);\n  };\n\n  // Delete selected object\n  const deleteSelected = () => {\n    if (!canvas) return;\n\n    const activeObject = canvas.getActiveObject();\n    if (activeObject) {\n      canvas.remove(activeObject);\n      canvas.renderAll();\n    }\n  };\n\n  // Clear canvas\n  const clearCanvas = () => {\n    if (!canvas) return;\n\n    canvas.clear();\n    // Re-add background image\n    if (productImage) {\n      fabric.Image.fromURL(\n        productImage,\n        (img) => {\n          if (img) {\n            img.scaleToWidth(500);\n            img.scaleToHeight(500);\n            img.set({\n              selectable: false,\n              evented: false,\n              opacity: 0.8,\n            });\n            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas));\n          }\n        },\n        { crossOrigin: \"anonymous\" }\n      );\n    }\n  };\n\n  // Export canvas as image\n  const exportCanvas = () => {\n    if (!canvas) return;\n\n    const dataURL = canvas.toDataURL({\n      format: \"png\",\n      quality: 1,\n      multiplier: 2,\n    });\n\n    const link = document.createElement(\"a\");\n    link.download = \"custom-product.png\";\n    link.href = dataURL;\n    link.click();\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n          🎨 Advanced Product Customizer\n        </h3>\n        <p className=\"text-gray-600\">\n          Design your product with text, shapes, and images\n        </p>\n      </div>\n\n      {/* Toolbar */}\n      <div className=\"mb-6 p-4 bg-gray-50 rounded-xl\">\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {/* Tool Buttons */}\n          <button\n            onClick={addText}\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          >\n            <Type size={16} />\n            Add Text\n          </button>\n\n          <button\n            onClick={() => addShape(\"rectangle\")}\n            className=\"flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n          >\n            Rectangle\n          </button>\n\n          <button\n            onClick={() => addShape(\"circle\")}\n            className=\"flex items-center gap-2 px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\"\n          >\n            Circle\n          </button>\n\n          <button\n            onClick={() => addShape(\"triangle\")}\n            className=\"flex items-center gap-2 px-3 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors\"\n          >\n            Triangle\n          </button>\n\n          <label className=\"flex items-center gap-2 px-3 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors cursor-pointer\">\n            <ImageIcon size={16} />\n            Upload Image\n            <input\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleImageUpload}\n              className=\"hidden\"\n            />\n          </label>\n        </div>\n\n        {/* Controls */}\n        <div className=\"flex flex-wrap gap-4 items-center\">\n          <div className=\"flex items-center gap-2\">\n            <label className=\"text-sm font-medium text-gray-700\">Color:</label>\n            <input\n              type=\"color\"\n              value={textColor}\n              onChange={(e) => setTextColor(e.target.value)}\n              className=\"w-8 h-8 border border-gray-300 rounded cursor-pointer\"\n            />\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <label className=\"text-sm font-medium text-gray-700\">\n              Font Size:\n            </label>\n            <input\n              type=\"range\"\n              min=\"10\"\n              max=\"100\"\n              value={fontSize}\n              onChange={(e) => setFontSize(Number(e.target.value))}\n              className=\"w-20\"\n            />\n            <span className=\"text-sm text-gray-600\">{fontSize}px</span>\n          </div>\n\n          <select\n            value={fontFamily}\n            onChange={(e) => setFontFamily(e.target.value)}\n            className=\"px-3 py-1 border border-gray-300 rounded text-sm\"\n          >\n            <option value=\"Arial\">Arial</option>\n            <option value=\"Times New Roman\">Times New Roman</option>\n            <option value=\"Helvetica\">Helvetica</option>\n            <option value=\"Georgia\">Georgia</option>\n            <option value=\"Verdana\">Verdana</option>\n          </select>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex gap-2 mt-4\">\n          <button\n            onClick={undo}\n            disabled={historyIndex <= 0}\n            className=\"flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Undo size={14} />\n            Undo\n          </button>\n\n          <button\n            onClick={redo}\n            disabled={historyIndex >= history.length - 1}\n            className=\"flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Redo size={14} />\n            Redo\n          </button>\n\n          <button\n            onClick={deleteSelected}\n            className=\"flex items-center gap-1 px-2 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600\"\n          >\n            <Trash2 size={14} />\n            Delete\n          </button>\n\n          <button\n            onClick={clearCanvas}\n            className=\"flex items-center gap-1 px-2 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600\"\n          >\n            Clear All\n          </button>\n\n          <button\n            onClick={exportCanvas}\n            className=\"flex items-center gap-1 px-2 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\"\n          >\n            <Download size={14} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      {/* Canvas */}\n      <div className=\"flex justify-center\">\n        <div className=\"border-2 border-gray-300 rounded-lg overflow-hidden\">\n          <canvas ref={canvasRef} />\n        </div>\n      </div>\n\n      <div className=\"mt-4 text-sm text-gray-500 text-center\">\n        💡 Tip: Click and drag to move objects, double-click text to edit\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCustomizer;\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AA6BA,MAAM,oBAAsD,CAAC,EAC3D,YAAY,EACZ,qBAAqB,EACrB,oBAAoB,EACrB;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAElD,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,MAAM,eAAe,IAAI,OAAO,MAAM,CAAC,UAAU,OAAO,EAAE;gBACxD,OAAO;gBACP,QAAQ;gBACR,iBAAiB;YACnB;YAEA,mCAAmC;YACnC,IAAI,cAAc;gBAChB,OAAO,KAAK,CAAC,OAAO,CAClB;mDACA,CAAC;wBACC,IAAI,KAAK;4BACP,IAAI,YAAY,CAAC;4BACjB,IAAI,aAAa,CAAC;4BAClB,IAAI,GAAG,CAAC;gCACN,YAAY;gCACZ,SAAS;gCACT,SAAS;4BACX;4BACA,aAAa,kBAAkB,CAC7B,KACA,aAAa,SAAS,CAAC,IAAI,CAAC;wBAEhC;oBACF;kDACA;oBAAE,aAAa;gBAAY;YAE/B;YAEA,yBAAyB;YACzB,aAAa,EAAE,CAAC,gBAAgB;YAChC,aAAa,EAAE,CAAC,kBAAkB;YAClC,aAAa,EAAE,CAAC,mBAAmB;YAEnC,UAAU;YACV;YAEA;+CAAO;oBACL,aAAa,OAAO;gBACtB;;QACF;sCAAG;QAAC;KAAa;IAEjB,kCAAkC;IAClC,MAAM,YAAY;QAChB,IAAI,CAAC,QAAQ;QAEb,MAAM,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM;QAC1C,WAAW,CAAC;YACV,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG,eAAe;YAChD,WAAW,IAAI,CAAC;YAChB,OAAO,WAAW,KAAK,CAAC,CAAC,KAAK,sBAAsB;QACtD;QACA,gBAAgB,CAAC,OAAS,OAAO;QAEjC,2BAA2B;QAC3B,sBAAsB;YACpB,YAAY;YACZ,SAAS,OAAO,SAAS;QAC3B;IACF;IAEA,qBAAqB;IACrB,MAAM,OAAO;QACX,IAAI,eAAe,KAAK,QAAQ;YAC9B,gBAAgB,CAAC,OAAS,OAAO;YACjC,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE;gBAC7C,OAAO,SAAS;YAClB;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,OAAO;QACX,IAAI,eAAe,QAAQ,MAAM,GAAG,KAAK,QAAQ;YAC/C,gBAAgB,CAAC,OAAS,OAAO;YACjC,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE;gBAC7C,OAAO,SAAS;YAClB;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,UAAU;QACd,IAAI,CAAC,QAAQ;QAEb,MAAM,OAAO,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAC7C,MAAM;YACN,KAAK;YACL,YAAY;YACZ,UAAU;YACV,MAAM;QACR;QAEA,OAAO,GAAG,CAAC;QACX,OAAO,eAAe,CAAC;QACvB,OAAO,SAAS;IAClB;IAEA,sBAAsB;IACtB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,QAAQ;QAEb,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,QAAQ,IAAI,OAAO,IAAI,CAAC;oBACtB,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM;gBACR;gBACA;YACF,KAAK;gBACH,QAAQ,IAAI,OAAO,MAAM,CAAC;oBACxB,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,MAAM;gBACR;gBACA;YACF,KAAK;gBACH,QAAQ,IAAI,OAAO,QAAQ,CAAC;oBAC1B,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM;gBACR;gBACA;QACJ;QAEA,IAAI,OAAO;YACT,OAAO,GAAG,CAAC;YACX,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;IACF;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAEtB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,SAAS,EAAE,MAAM,EAAE;YACzB,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC5B,IAAI,KAAK;oBACP,IAAI,YAAY,CAAC;oBACjB,IAAI,GAAG,CAAC;wBACN,MAAM;wBACN,KAAK;oBACP;oBACA,OAAO,GAAG,CAAC;oBACX,OAAO,eAAe,CAAC;oBACvB,OAAO,SAAS;gBAClB;YACF;QACF;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ;QAEb,MAAM,eAAe,OAAO,eAAe;QAC3C,IAAI,cAAc;YAChB,OAAO,MAAM,CAAC;YACd,OAAO,SAAS;QAClB;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;QAEb,OAAO,KAAK;QACZ,0BAA0B;QAC1B,IAAI,cAAc;YAChB,OAAO,KAAK,CAAC,OAAO,CAClB,cACA,CAAC;gBACC,IAAI,KAAK;oBACP,IAAI,YAAY,CAAC;oBACjB,IAAI,aAAa,CAAC;oBAClB,IAAI,GAAG,CAAC;wBACN,YAAY;wBACZ,SAAS;wBACT,SAAS;oBACX;oBACA,OAAO,kBAAkB,CAAC,KAAK,OAAO,SAAS,CAAC,IAAI,CAAC;gBACvD;YACF,GACA;gBAAE,aAAa;YAAY;QAE/B;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,MAAM,UAAU,OAAO,SAAS,CAAC;YAC/B,QAAQ;YACR,SAAS;YACT,YAAY;QACd;QAEA,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,GAAG;QACZ,KAAK,KAAK;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAIpB,6LAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;0CACX;;;;;;0CAID,6LAAC;gCAAM,WAAU;;kDACf,6LAAC,uMAAA,CAAA,QAAS;wCAAC,MAAM;;;;;;oCAAM;kDAEvB,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;;4CAAyB;4CAAS;;;;;;;;;;;;;0CAGpD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB;gCAC1B,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAIpB,6LAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB,QAAQ,MAAM,GAAG;gCAC3C,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAIpB,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAItB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAO,KAAK;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BAAyC;;;;;;;;;;;;AAK9D;GAnYM;KAAA;uCAqYS", "debugId": null}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/product/%5Bslug%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Link from \"next/link\";\r\nimport dynamic from \"next/dynamic\";\r\nimport gsap from \"gsap\";\r\nimport Header from \"@/components/Header\";\r\nimport Cart from \"@/components/Cart\";\r\nimport ProductCustomizer from \"@/components/ProductCustomizer\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nconst Bottle3D = dynamic(() => import(\"./Bottle3D\"), { ssr: false });\r\nconst Tshirt3D = dynamic(() => import(\"./Tshirt3D\"), { ssr: false });\r\n\r\ntype Product = {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  description: string;\r\n  price: number;\r\n  image: string;\r\n  customizable: boolean;\r\n  category: { id: number; name: string };\r\n  modelUrl?: string;\r\n};\r\n\r\ntype CustomizationData = {\r\n  canvasData?: string;\r\n  preview?: string;\r\n} | null;\r\n\r\nexport default function ProductPage({\r\n  params,\r\n}: {\r\n  params: Promise<{ slug: string }>;\r\n}) {\r\n  // Unwrap params for Next.js 15+ compatibility\r\n  const resolvedParams = React.use(params);\r\n  const slug = resolvedParams.slug;\r\n  const router = useRouter();\r\n  const [product, setProduct] = useState<Product | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(false);\r\n  const [color, setColor] = useState(\"#ffffff\");\r\n  const [text, setText] = useState(\"\");\r\n  const [added, setAdded] = useState(false);\r\n  const [addingToCart, setAddingToCart] = useState(false);\r\n  const [customize, setCustomize] = useState(false);\r\n  const [useAdvancedCustomizer, setUseAdvancedCustomizer] = useState(false);\r\n  const [customizationData, setCustomizationData] =\r\n    useState<CustomizationData>(null);\r\n  const [cartCount, setCartCount] = useState(0);\r\n  const [isCartOpen, setIsCartOpen] = useState(false);\r\n  const cardRef = useRef<HTMLDivElement>(null);\r\n  const previewRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    setError(false);\r\n\r\n    // Fetch product\r\n    fetch(getApiUrl(`${API_CONFIG.ENDPOINTS.PRODUCTS}/${slug}`))\r\n      .then(async (productRes) => {\r\n        if (!productRes.ok) throw new Error(\"Product not found\");\r\n\r\n        const productData = await productRes.json();\r\n        setProduct(productData);\r\n\r\n        // Set initial customization state based on product's customizable property\r\n        setCustomize(productData.customizable || false);\r\n\r\n        setLoading(false);\r\n      })\r\n      .catch(() => {\r\n        setError(true);\r\n        setLoading(false);\r\n      });\r\n  }, [slug]);\r\n\r\n  useEffect(() => {\r\n    if (cardRef.current) {\r\n      gsap.fromTo(\r\n        cardRef.current,\r\n        { opacity: 0, y: 40 },\r\n        { opacity: 1, y: 0, duration: 0.7, ease: \"power3.out\" }\r\n      );\r\n    }\r\n  }, [product]);\r\n\r\n  useEffect(() => {\r\n    if (previewRef.current) {\r\n      gsap.fromTo(\r\n        previewRef.current,\r\n        { scale: 0.92, opacity: 0 },\r\n        { scale: 1, opacity: 1, duration: 0.5, ease: \"power2.out\" }\r\n      );\r\n    }\r\n  }, [customize, product?.slug]);\r\n\r\n  // Update cart count on mount and when cart changes\r\n  const updateCartCount = () => {\r\n    if (typeof window !== \"undefined\") {\r\n      const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\r\n      setCartCount(cart.length);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    updateCartCount();\r\n  }, []);\r\n\r\n  const handleCartClick = () => {\r\n    setIsCartOpen(true);\r\n  };\r\n\r\n  const handleCartClose = () => {\r\n    setIsCartOpen(false);\r\n    updateCartCount(); // Update count when cart closes\r\n  };\r\n\r\n  function isAuthenticated() {\r\n    if (typeof window === \"undefined\") return false;\r\n    return !!localStorage.getItem(\"token\");\r\n  }\r\n\r\n  async function handleAddToCart(e: React.FormEvent) {\r\n    e.preventDefault();\r\n    if (!product) return;\r\n    if (!isAuthenticated()) {\r\n      router.push(`/login?redirect=/product/${slug}`);\r\n      return;\r\n    }\r\n\r\n    setAddingToCart(true);\r\n\r\n    try {\r\n      // Simulate a small delay for better UX\r\n      await new Promise((resolve) => setTimeout(resolve, 500));\r\n\r\n      const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\r\n      cart.push({\r\n        id: Date.now(),\r\n        productId: product.id,\r\n        name: product.name,\r\n        color: customize ? color : undefined,\r\n        text: customize ? text : undefined,\r\n        customized: customize,\r\n        customizationData: useAdvancedCustomizer\r\n          ? customizationData\r\n          : undefined,\r\n        price: product.price,\r\n        quantity: 1,\r\n      });\r\n      localStorage.setItem(\"cart\", JSON.stringify(cart));\r\n      updateCartCount(); // Update cart count after adding item\r\n\r\n      setAdded(true);\r\n      setTimeout(() => {\r\n        setAdded(false);\r\n        router.push(\"/\");\r\n      }, 1200);\r\n    } catch (error) {\r\n      console.error(\"Error adding to cart:\", error);\r\n    } finally {\r\n      setAddingToCart(false);\r\n    }\r\n  }\r\n\r\n  function handleOrderNow() {\r\n    if (!isAuthenticated()) {\r\n      router.push(`/login?redirect=/product/${slug}`);\r\n      return;\r\n    }\r\n    // You can implement direct order logic here (e.g., open checkout modal)\r\n    alert(\"Order Now functionality coming soon!\");\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n        <Header cartCount={cartCount} onCartClick={handleCartClick} />\r\n        <div className=\"min-h-screen flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <div className=\"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4\"></div>\r\n            <p className=\"text-xl text-gray-600 font-medium\">\r\n              Loading product...\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n  if (error || !product) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n        <Header cartCount={cartCount} onCartClick={handleCartClick} />\r\n        <div className=\"min-h-screen flex items-center justify-center\">\r\n          <div className=\"text-center max-w-md mx-auto px-4\">\r\n            <div className=\"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n              <svg\r\n                className=\"w-12 h-12 text-red-500\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\r\n              Product Not Found\r\n            </h1>\r\n            <p className=\"text-gray-600 mb-8\">\r\n              Sorry, we couldn&apos;t find the product you&apos;re looking for.\r\n            </p>\r\n            <Link\r\n              href=\"/\"\r\n              className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg\"\r\n            >\r\n              <svg\r\n                className=\"w-5 h-5 mr-2\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n                />\r\n              </svg>\r\n              Back to Shop\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n      <Header cartCount={cartCount} onCartClick={handleCartClick} />\r\n\r\n      {/* Breadcrumb */}\r\n      <div className=\"max-w-7xl mx-auto px-4 pt-8\">\r\n        <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-8\">\r\n          <Link\r\n            href=\"/\"\r\n            className=\"hover:text-indigo-600 transition-colors duration-200\"\r\n          >\r\n            Home\r\n          </Link>\r\n          <svg\r\n            className=\"w-4 h-4\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth={2}\r\n              d=\"M9 5l7 7-7 7\"\r\n            />\r\n          </svg>\r\n          <Link\r\n            href=\"/\"\r\n            className=\"hover:text-indigo-600 transition-colors duration-200\"\r\n          >\r\n            Products\r\n          </Link>\r\n          <svg\r\n            className=\"w-4 h-4\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth={2}\r\n              d=\"M9 5l7 7-7 7\"\r\n            />\r\n          </svg>\r\n          <span className=\"text-gray-900 font-medium\">{product.name}</span>\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Main Product Section */}\r\n      <div className=\"max-w-7xl mx-auto px-4 pb-16\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\r\n          {/* Product Images/3D Preview */}\r\n          <div className=\"space-y-6\">\r\n            <div\r\n              ref={previewRef}\r\n              className=\"bg-white rounded-3xl shadow-xl p-8 border border-gray-100\"\r\n            >\r\n              {customize && product.slug === \"engraved-bottle\" ? (\r\n                <div className=\"w-full flex flex-col items-center\">\r\n                  <Bottle3D\r\n                    color={color}\r\n                    text={text}\r\n                    onColorChange={setColor}\r\n                    modelUrl={product.modelUrl}\r\n                  />\r\n                  <div className=\"text-sm text-gray-500 mt-4 text-center\">\r\n                    🎮 3D Preview: Drag to rotate, scroll to zoom\r\n                  </div>\r\n                </div>\r\n              ) : customize && product.slug === \"custom-tshirt\" ? (\r\n                <div className=\"w-full flex flex-col items-center\">\r\n                  <Tshirt3D\r\n                    color={color}\r\n                    text={text}\r\n                    onColorChange={setColor}\r\n                  />\r\n                  <div className=\"text-sm text-gray-500 mt-4 text-center\">\r\n                    🎮 3D Preview: Drag to rotate, scroll to zoom\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"w-full flex flex-col items-center\">\r\n                  <div className=\"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden\">\r\n                    <Image\r\n                      src={\r\n                        product.image && product.image.startsWith(\"http\")\r\n                          ? product.image\r\n                          : product.image\r\n                          ? \"/\" + product.image.replace(/^\\/+/, \"\")\r\n                          : \"/bottle-dummy.jpg\"\r\n                      }\r\n                      alt={product.name}\r\n                      width={400}\r\n                      height={400}\r\n                      className=\"w-full h-full object-contain hover:scale-105 transition-transform duration-500\"\r\n                      unoptimized={\r\n                        !!(product.image && product.image.startsWith(\"http\"))\r\n                      }\r\n                      onError={(e) => {\r\n                        (e.target as HTMLImageElement).src =\r\n                          \"/bottle-dummy.jpg\";\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  {!product.image && (\r\n                    <div className=\"text-sm text-amber-600 mt-4 bg-amber-50 px-4 py-2 rounded-lg\">\r\n                      ⚠️ No product image found. Showing fallback.\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Product Features */}\r\n            <div className=\"bg-white rounded-3xl shadow-lg p-6 border border-gray-100\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">\r\n                Product Features\r\n              </h3>\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\r\n                    <svg\r\n                      className=\"w-4 h-4 text-green-600\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M5 13l4 4L19 7\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-700\">Premium Quality</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <svg\r\n                      className=\"w-4 h-4 text-blue-600\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M13 10V3L4 14h7v7l9-11h-7z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-700\">Fast Delivery</span>\r\n                </div>\r\n                {product.customizable && (\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\r\n                      <svg\r\n                        className=\"w-4 h-4 text-purple-600\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-sm text-gray-700\">Customizable</span>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\">\r\n                    <svg\r\n                      className=\"w-4 h-4 text-yellow-600\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-700\">Guaranteed</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Product Details */}\r\n          <div className=\"space-y-6\">\r\n            <div\r\n              ref={cardRef}\r\n              className=\"bg-white rounded-3xl shadow-xl p-8 border border-gray-100\"\r\n            >\r\n              {/* Product Header */}\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <span className=\"px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full\">\r\n                    {product.category?.name || \"Product\"}\r\n                  </span>\r\n                  {product.customizable && (\r\n                    <span className=\"px-3 py-1 bg-purple-100 text-purple-700 text-sm font-semibold rounded-full\">\r\n                      ✨ Customizable\r\n                    </span>\r\n                  )}\r\n                </div>\r\n\r\n                <h1 className=\"text-4xl font-black text-gray-900 mb-4 leading-tight\">\r\n                  {product.name}\r\n                </h1>\r\n\r\n                <p className=\"text-lg text-gray-600 leading-relaxed mb-6\">\r\n                  {product.description}\r\n                </p>\r\n\r\n                <div className=\"flex items-center gap-4 mb-6\">\r\n                  <div className=\"text-3xl font-black text-green-600\">\r\n                    K{product.price ?? 0}\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-500\">\r\n                    💰 Best price guaranteed\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Customization Toggle */}\r\n              {product.customizable && (\r\n                <div className=\"mb-8 space-y-4\">\r\n                  <div className=\"flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border border-purple-200\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"customize-toggle\"\r\n                      checked={customize}\r\n                      onChange={() => setCustomize((v) => !v)}\r\n                      className=\"w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2\"\r\n                    />\r\n                    <label\r\n                      htmlFor=\"customize-toggle\"\r\n                      className=\"text-lg font-semibold text-gray-900 cursor-pointer select-none\"\r\n                    >\r\n                      🎨 Customize this product\r\n                    </label>\r\n                  </div>\r\n\r\n                  {customize && (\r\n                    <div className=\"flex items-center gap-4 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-200\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"advanced-customizer-toggle\"\r\n                        checked={useAdvancedCustomizer}\r\n                        onChange={() => setUseAdvancedCustomizer((v) => !v)}\r\n                        className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2\"\r\n                      />\r\n                      <label\r\n                        htmlFor=\"advanced-customizer-toggle\"\r\n                        className=\"text-sm font-medium text-gray-800 cursor-pointer select-none\"\r\n                      >\r\n                        🚀 Use Advanced Designer (Fabric.js)\r\n                      </label>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Customization Form */}\r\n              <form onSubmit={handleAddToCart} className=\"space-y-6\">\r\n                {customize && !useAdvancedCustomizer && (\r\n                  <div className=\"space-y-6 p-6 bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl border border-gray-200\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\r\n                      🎨 Basic Customization\r\n                    </h3>\r\n\r\n                    {/* Color Picker */}\r\n                    <div className=\"space-y-3\">\r\n                      <label className=\"block text-sm font-semibold text-gray-700\">\r\n                        Choose Color\r\n                      </label>\r\n                      <div className=\"flex items-center gap-4\">\r\n                        <input\r\n                          type=\"color\"\r\n                          name=\"color\"\r\n                          value={color}\r\n                          onChange={(e) => setColor(e.target.value)}\r\n                          className=\"w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer hover:border-indigo-400 transition-colors duration-200\"\r\n                          title=\"Choose color\"\r\n                        />\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"text-sm font-mono text-gray-600 bg-white px-3 py-2 rounded-lg border\">\r\n                            {color.toUpperCase()}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Text Input */}\r\n                    <div className=\"space-y-3\">\r\n                      <label className=\"block text-sm font-semibold text-gray-700\">\r\n                        Text to Engrave/Print\r\n                        <span className=\"text-xs text-gray-500 font-normal ml-2\">\r\n                          ({text.length}/30 characters)\r\n                        </span>\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"text\"\r\n                        value={text}\r\n                        onChange={(e) => setText(e.target.value)}\r\n                        maxLength={30}\r\n                        placeholder=\"e.g. Your Name or Message\"\r\n                        className=\"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg\"\r\n                        required\r\n                      />\r\n                      {text.length > 25 && (\r\n                        <div className=\"text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg\">\r\n                          ⚠️ Character limit almost reached\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Advanced Customizer */}\r\n                {customize && useAdvancedCustomizer && (\r\n                  <ProductCustomizer\r\n                    productImage={\r\n                      product.image && product.image.startsWith(\"http\")\r\n                        ? product.image\r\n                        : product.image\r\n                        ? \"/\" + product.image.replace(/^\\/+/, \"\")\r\n                        : \"/bottle-dummy.jpg\"\r\n                    }\r\n                    onCustomizationChange={setCustomizationData}\r\n                    initialCustomization={customizationData}\r\n                  />\r\n                )}\r\n\r\n                {/* Action Buttons */}\r\n                <div className=\"space-y-4\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    className={`w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ${\r\n                      addingToCart || added\r\n                        ? \"bg-gray-400 cursor-not-allowed\"\r\n                        : \"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white hover:shadow-xl transform hover:-translate-y-1\"\r\n                    }`}\r\n                    disabled={addingToCart || added}\r\n                  >\r\n                    {addingToCart ? (\r\n                      <div className=\"flex items-center justify-center gap-3\">\r\n                        <div className=\"w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n                        <span>Adding to Cart...</span>\r\n                      </div>\r\n                    ) : added ? (\r\n                      <div className=\"flex items-center justify-center gap-2\">\r\n                        <svg\r\n                          className=\"w-6 h-6\"\r\n                          fill=\"none\"\r\n                          stroke=\"currentColor\"\r\n                          viewBox=\"0 0 24 24\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            strokeWidth={2}\r\n                            d=\"M5 13l4 4L19 7\"\r\n                          />\r\n                        </svg>\r\n                        <span>Added to Cart!</span>\r\n                      </div>\r\n                    ) : (\r\n                      <span>\r\n                        🛒{\" \"}\r\n                        {customize\r\n                          ? useAdvancedCustomizer\r\n                            ? \"Add Advanced Custom Product to Cart\"\r\n                            : \"Add Custom Product to Cart\"\r\n                          : \"Add to Cart\"}\r\n                      </span>\r\n                    )}\r\n                  </button>\r\n\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleOrderNow}\r\n                    className=\"w-full py-4 px-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n                  >\r\n                    ⚡ Order Now - Fast Delivery\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n\r\n            {/* Additional Product Info */}\r\n            <div className=\"bg-white rounded-3xl shadow-lg p-6 border border-gray-100\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">\r\n                📋 Product Information\r\n              </h3>\r\n              <div className=\"space-y-3 text-sm\">\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-600\">Category:</span>\r\n                  <span className=\"font-semibold text-gray-900\">\r\n                    {product.category?.name || \"General\"}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-600\">Customizable:</span>\r\n                  <span\r\n                    className={`font-semibold ${\r\n                      product.customizable ? \"text-green-600\" : \"text-gray-500\"\r\n                    }`}\r\n                  >\r\n                    {product.customizable ? \"✅ Yes\" : \"❌ No\"}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-600\">Delivery:</span>\r\n                  <span className=\"font-semibold text-blue-600\">\r\n                    🚚 24-48 hours\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span className=\"text-gray-600\">Warranty:</span>\r\n                  <span className=\"font-semibold text-green-600\">\r\n                    ✅ 30 days\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Trust Badges */}\r\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-6 border border-green-200\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">\r\n                🛡️ Why Choose Us?\r\n              </h3>\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl mb-2\">🏆</div>\r\n                  <div className=\"text-sm font-semibold text-gray-700\">\r\n                    Premium Quality\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl mb-2\">⚡</div>\r\n                  <div className=\"text-sm font-semibold text-gray-700\">\r\n                    Fast Delivery\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl mb-2\">💯</div>\r\n                  <div className=\"text-sm font-semibold text-gray-700\">\r\n                    100% Guaranteed\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl mb-2\">🎨</div>\r\n                  <div className=\"text-sm font-semibold text-gray-700\">\r\n                    Custom Design\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Cart Modal */}\r\n      <Cart isOpen={isCartOpen} onClose={handleCartClose} />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAXA;;;;;;;;;;;AAaA,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAA8B,KAAK;;KAAtD;AACN,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAA8B,KAAK;;MAAtD;AAmBS,SAAS,YAAY,EAClC,MAAM,EAGP;;IACC,8CAA8C;IAC9C,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IACjC,MAAM,OAAO,eAAe,IAAI;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YACX,SAAS;YAET,gBAAgB;YAChB,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,GAAG,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,GACvD,IAAI;yCAAC,OAAO;oBACX,IAAI,CAAC,WAAW,EAAE,EAAE,MAAM,IAAI,MAAM;oBAEpC,MAAM,cAAc,MAAM,WAAW,IAAI;oBACzC,WAAW;oBAEX,2EAA2E;oBAC3E,aAAa,YAAY,YAAY,IAAI;oBAEzC,WAAW;gBACb;wCACC,KAAK;yCAAC;oBACL,SAAS;oBACT,WAAW;gBACb;;QACJ;gCAAG;QAAC;KAAK;IAET,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,QAAQ,OAAO,EACf;oBAAE,SAAS;oBAAG,GAAG;gBAAG,GACpB;oBAAE,SAAS;oBAAG,GAAG;oBAAG,UAAU;oBAAK,MAAM;gBAAa;YAE1D;QACF;gCAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW,OAAO,EAAE;gBACtB,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,WAAW,OAAO,EAClB;oBAAE,OAAO;oBAAM,SAAS;gBAAE,GAC1B;oBAAE,OAAO;oBAAG,SAAS;oBAAG,UAAU;oBAAK,MAAM;gBAAa;YAE9D;QACF;gCAAG;QAAC;QAAW,SAAS;KAAK;IAE7B,mDAAmD;IACnD,MAAM,kBAAkB;QACtB,wCAAmC;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;YACxD,aAAa,KAAK,MAAM;QAC1B;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;QACd,mBAAmB,gCAAgC;IACrD;IAEA,SAAS;QACP,uCAAmC;;QAAY;QAC/C,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;IAEA,eAAe,gBAAgB,CAAkB;QAC/C,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,mBAAmB;YACtB,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,MAAM;YAC9C;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,uCAAuC;YACvC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;YACxD,KAAK,IAAI,CAAC;gBACR,IAAI,KAAK,GAAG;gBACZ,WAAW,QAAQ,EAAE;gBACrB,MAAM,QAAQ,IAAI;gBAClB,OAAO,YAAY,QAAQ;gBAC3B,MAAM,YAAY,OAAO;gBACzB,YAAY;gBACZ,mBAAmB,wBACf,oBACA;gBACJ,OAAO,QAAQ,KAAK;gBACpB,UAAU;YACZ;YACA,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC5C,mBAAmB,sCAAsC;YAEzD,SAAS;YACT,WAAW;gBACT,SAAS;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;QACP,IAAI,CAAC,mBAAmB;YACtB,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,MAAM;YAC9C;QACF;QACA,wEAAwE;QACxE,MAAM;IACR;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;oBAAC,WAAW;oBAAW,aAAa;;;;;;8BAC3C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;IAO3D;IACA,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;oBAAC,WAAW;oBAAW,aAAa;;;;;;8BAC3C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;0CAIR,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;oCAEA;;;;;;;;;;;;;;;;;;;;;;;;IAOlB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;gBAAC,WAAW;gBAAW,aAAa;;;;;;0BAG3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;sCAGN,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;sCAGN,6LAAC;4BAAK,WAAU;sCAA6B,QAAQ,IAAI;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAK;oCACL,WAAU;8CAET,aAAa,QAAQ,IAAI,KAAK,kCAC7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO;gDACP,MAAM;gDACN,eAAe;gDACf,UAAU,QAAQ,QAAQ;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;+CAIxD,aAAa,QAAQ,IAAI,KAAK,gCAChC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO;gDACP,MAAM;gDACN,eAAe;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;6DAK1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KACE,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,UAAU,CAAC,UACtC,QAAQ,KAAK,GACb,QAAQ,KAAK,GACb,MAAM,QAAQ,KAAK,CAAC,OAAO,CAAC,QAAQ,MACpC;oDAEN,KAAK,QAAQ,IAAI;oDACjB,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,aACE,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,UAAU,CAAC,OAAO;oDAEtD,SAAS,CAAC;wDACP,EAAE,MAAM,CAAsB,GAAG,GAChC;oDACJ;;;;;;;;;;;4CAGH,CAAC,QAAQ,KAAK,kBACb,6LAAC;gDAAI,WAAU;0DAA+D;;;;;;;;;;;;;;;;;8CAStF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,MAAK;gEACL,QAAO;gEACP,SAAQ;0EAER,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;sEAIR,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,MAAK;gEACL,QAAO;gEACP,SAAQ;0EAER,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;sEAIR,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;gDAEzC,QAAQ,YAAY,kBACnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,MAAK;gEACL,QAAO;gEACP,SAAQ;0EAER,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;sEAIR,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAG5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,MAAK;gEACL,QAAO;gEACP,SAAQ;0EAER,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;sEAIR,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAK;oCACL,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ,EAAE,QAAQ;;;;;;wDAE5B,QAAQ,YAAY,kBACnB,6LAAC;4DAAK,WAAU;sEAA6E;;;;;;;;;;;;8DAMjG,6LAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;8DAGf,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAqC;gEAChD,QAAQ,KAAK,IAAI;;;;;;;sEAErB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;wCAO1C,QAAQ,YAAY,kBACnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,SAAS;4DACT,UAAU,IAAM,aAAa,CAAC,IAAM,CAAC;4DACrC,WAAU;;;;;;sEAEZ,6LAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;gDAKF,2BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,SAAS;4DACT,UAAU,IAAM,yBAAyB,CAAC,IAAM,CAAC;4DACjD,WAAU;;;;;;sEAEZ,6LAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAST,6LAAC;4CAAK,UAAU;4CAAiB,WAAU;;gDACxC,aAAa,CAAC,uCACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuC;;;;;;sEAKrD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAA4C;;;;;;8EAG7D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4EACxC,WAAU;4EACV,OAAM;;;;;;sFAER,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;0FACZ,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;sEAO1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;;wEAA4C;sFAE3D,6LAAC;4EAAK,WAAU;;gFAAyC;gFACrD,KAAK,MAAM;gFAAC;;;;;;;;;;;;;8EAGlB,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO;oEACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oEACvC,WAAW;oEACX,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;gEAET,KAAK,MAAM,GAAG,oBACb,6LAAC;oEAAI,WAAU;8EAA0D;;;;;;;;;;;;;;;;;;gDAShF,aAAa,uCACZ,6LAAC,0IAAA,CAAA,UAAiB;oDAChB,cACE,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,UAAU,CAAC,UACtC,QAAQ,KAAK,GACb,QAAQ,KAAK,GACb,MAAM,QAAQ,KAAK,CAAC,OAAO,CAAC,QAAQ,MACpC;oDAEN,uBAAuB;oDACvB,sBAAsB;;;;;;8DAK1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,WAAW,CAAC,qFAAqF,EAC/F,gBAAgB,QACZ,mCACA,sJACJ;4DACF,UAAU,gBAAgB;sEAEzB,6BACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;uEAEN,sBACF,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,WAAU;wEACV,MAAK;wEACL,QAAO;wEACP,SAAQ;kFAER,cAAA,6LAAC;4EACC,eAAc;4EACd,gBAAe;4EACf,aAAa;4EACb,GAAE;;;;;;;;;;;kFAGN,6LAAC;kFAAK;;;;;;;;;;;qFAGR,6LAAC;;oEAAK;oEACD;oEACF,YACG,wBACE,wCACA,+BACF;;;;;;;;;;;;sEAKV,6LAAC;4DACC,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;8CAQP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ,EAAE,QAAQ;;;;;;;;;;;;8DAG/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DACC,WAAW,CAAC,cAAc,EACxB,QAAQ,YAAY,GAAG,mBAAmB,iBAC1C;sEAED,QAAQ,YAAY,GAAG,UAAU;;;;;;;;;;;;8DAGtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAIhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;;;;;;;;;;;;;;8CAQrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAI,WAAU;sEAAsC;;;;;;;;;;;;8DAIvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAI,WAAU;sEAAsC;;;;;;;;;;;;8DAIvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAI,WAAU;sEAAsC;;;;;;;;;;;;8DAIvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAI,WAAU;sEAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjE,6LAAC,6HAAA,CAAA,UAAI;gBAAC,QAAQ;gBAAY,SAAS;;;;;;;;;;;;AAGzC;GAprBwB;;QAQP,qIAAA,CAAA,YAAS;;;MARF", "debugId": null}}]}