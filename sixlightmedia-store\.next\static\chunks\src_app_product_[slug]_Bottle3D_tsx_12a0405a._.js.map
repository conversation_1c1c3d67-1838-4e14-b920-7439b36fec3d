{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/product/%5Bslug%5D/Bottle3D.tsx"], "sourcesContent": ["import { Canvas } from \"@react-three/fiber\";\r\nimport { OrbitControls, Text, useGLTF } from \"@react-three/drei\";\r\nimport { useState } from \"react\";\r\nimport { HexColorPicker } from \"react-colorful\";\r\nimport React from \"react\";\r\nimport { MeshStandardMaterial } from \"three\";\r\n\r\ntype Bottle3DProps = {\r\n  color?: string;\r\n  text?: string;\r\n  onColorChange?: (color: string) => void;\r\n  modelUrl?: string; // Optional prop for dynamic model path\r\n};\r\n\r\nexport default function Bottle3D({\r\n  color = \"white\",\r\n  text = \"\",\r\n  onColorChange,\r\n  modelUrl, // Accept optional modelUrl from props\r\n}: Bottle3DProps) {\r\n  const [showPicker, setShowPicker] = useState(false);\r\n  // Simple 3D bottle shape using primitives. Replace with GLTF model for realism.\r\n  return (\r\n    <div style={{ width: \"100%\" }}>\r\n      <Canvas\r\n        style={{ height: 320, width: \"100%\", background: \"#f9f9f9\" }}\r\n        camera={{ position: [0, 0, 5], fov: 50 }}\r\n      >\r\n        <ambientLight intensity={0.7} />\r\n        <directionalLight position={[5, 5, 5]} intensity={0.7} />\r\n        {/* <PERSON><PERSON> body and neck replaced with realistic model */}\r\n        {modelUrl && modelUrl.trim() !== \"\" ? (\r\n          <RealisticBottle color={color} modelUrl={modelUrl} />\r\n        ) : (\r\n          <mesh>\r\n            <boxGeometry args={[1, 2, 1]} />\r\n            <meshStandardMaterial color={color} />\r\n          </mesh>\r\n        )}\r\n        {/* Custom text */}\r\n        <ErrorBoundary>\r\n          {typeof text === \"string\" &&\r\n          text.trim() !== \"\" &&\r\n          text.length <= 24 ? (\r\n            <Text\r\n              position={[0, 0, 0.75]}\r\n              fontSize={0.25}\r\n              color=\"#222\"\r\n              anchorX=\"center\"\r\n              anchorY=\"middle\"\r\n              maxWidth={1.2}\r\n            >\r\n              {text}\r\n            </Text>\r\n          ) : null}\r\n        </ErrorBoundary>\r\n        <OrbitControls enablePan enableZoom enableRotate />\r\n      </Canvas>\r\n      <div className=\"flex flex-col items-center mt-2\">\r\n        <button\r\n          className=\"bg-[#ffd600] text-[#171717] font-semibold px-4 py-1 rounded-full shadow hover:bg-[#ffe066] transition mb-2\"\r\n          onClick={() => setShowPicker((v) => !v)}\r\n        >\r\n          {showPicker ? \"Close Color Picker\" : \"Pick Custom Color\"}\r\n        </button>\r\n        {showPicker && (\r\n          <HexColorPicker\r\n            color={color}\r\n            onChange={onColorChange}\r\n            style={{ width: 180, height: 120 }}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// ErrorBoundary to catch errors in 3D Text rendering\r\nclass ErrorBoundary extends React.Component<\r\n  { children: React.ReactNode },\r\n  { hasError: boolean }\r\n> {\r\n  constructor(props: { children: React.ReactNode }) {\r\n    super(props);\r\n    this.state = { hasError: false };\r\n  }\r\n  static getDerivedStateFromError() {\r\n    return { hasError: true };\r\n  }\r\n  // componentDidCatch is required for error boundaries, but we don't need to use the arguments\r\n  componentDidCatch() {}\r\n  render() {\r\n    if (this.state.hasError) return null;\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nfunction RealisticBottle({\r\n  color,\r\n  modelUrl,\r\n}: {\r\n  color: string;\r\n  modelUrl: string;\r\n}) {\r\n  const { scene, materials } = useGLTF(modelUrl);\r\n  React.useEffect(() => {\r\n    if (materials && materials.BottleMaterial) {\r\n      const material = materials.BottleMaterial as MeshStandardMaterial;\r\n      if (material.color) {\r\n        material.color.set(color);\r\n      }\r\n    }\r\n  }, [color, materials]);\r\n  return <primitive object={scene} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;;AAWe,SAAS,SAAS,EAC/B,QAAQ,OAAO,EACf,OAAO,EAAE,EACT,aAAa,EACb,QAAQ,EACM;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,gFAAgF;IAChF,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO;QAAO;;0BAC1B,6LAAC,sMAAA,CAAA,SAAM;gBACL,OAAO;oBAAE,QAAQ;oBAAK,OAAO;oBAAQ,YAAY;gBAAU;gBAC3D,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;;kCAEvC,6LAAC;wBAAa,WAAW;;;;;;kCACzB,6LAAC;wBAAiB,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,WAAW;;;;;;oBAEjD,YAAY,SAAS,IAAI,OAAO,mBAC/B,6LAAC;wBAAgB,OAAO;wBAAO,UAAU;;;;;6CAEzC,6LAAC;;0CACC,6LAAC;gCAAY,MAAM;oCAAC;oCAAG;oCAAG;iCAAE;;;;;;0CAC5B,6LAAC;gCAAqB,OAAO;;;;;;;;;;;;kCAIjC,6LAAC;kCACE,OAAO,SAAS,YACjB,KAAK,IAAI,OAAO,MAChB,KAAK,MAAM,IAAI,mBACb,6LAAC,2JAAA,CAAA,OAAI;4BACH,UAAU;gCAAC;gCAAG;gCAAG;6BAAK;4BACtB,UAAU;4BACV,OAAM;4BACN,SAAQ;4BACR,SAAQ;4BACR,UAAU;sCAET;;;;;mCAED;;;;;;kCAEN,6LAAC,oKAAA,CAAA,gBAAa;wBAAC,SAAS;wBAAC,UAAU;wBAAC,YAAY;;;;;;;;;;;;0BAElD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc,CAAC,IAAM,CAAC;kCAEpC,aAAa,uBAAuB;;;;;;oBAEtC,4BACC,6LAAC,sJAAA,CAAA,iBAAc;wBACb,OAAO;wBACP,UAAU;wBACV,OAAO;4BAAE,OAAO;4BAAK,QAAQ;wBAAI;;;;;;;;;;;;;;;;;;AAM7C;GA7DwB;KAAA;AA+DxB,qDAAqD;AACrD,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAIzC,YAAY,KAAoC,CAAE;QAChD,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IACA,OAAO,2BAA2B;QAChC,OAAO;YAAE,UAAU;QAAK;IAC1B;IACA,6FAA6F;IAC7F,oBAAoB,CAAC;IACrB,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,gBAAgB,EACvB,KAAK,EACL,QAAQ,EAIT;;IACC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE;IACrC,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,IAAI,aAAa,UAAU,cAAc,EAAE;gBACzC,MAAM,WAAW,UAAU,cAAc;gBACzC,IAAI,SAAS,KAAK,EAAE;oBAClB,SAAS,KAAK,CAAC,GAAG,CAAC;gBACrB;YACF;QACF;oCAAG;QAAC;QAAO;KAAU;IACrB,qBAAO,6LAAC;QAAU,QAAQ;;;;;;AAC5B;IAjBS;;QAOsB,2JAAA,CAAA,UAAO;;;MAP7B", "debugId": null}}]}