exports.id=249,exports.ids=[249],exports.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1755:()=>{},1891:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(687),a=r(5814),o=r.n(a),n=r(3210),l=r(474),i=r(1860),d=r(2941);function c({cartCount:e=0,onCartClick:t}){let[r,a]=(0,n.useState)(!1),[c,m]=(0,n.useState)(null),[x,h]=(0,n.useState)(null),[u,g]=(0,n.useState)(!1),[p,b]=(0,n.useState)(!1);function f(){localStorage.removeItem("token"),a(!1),window.location.href="/login"}function v(){g(!1)}return(0,s.jsxs)("header",{className:"w-full bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-30",children:[(0,s.jsxs)("nav",{className:"max-w-7xl mx-auto flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 md:py-4",children:[(0,s.jsxs)(o(),{href:"/",className:"flex items-center gap-2 min-w-0 group",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.default,{src:"/6 Light Logo.png",alt:"Six Light Media Logo",width:40,height:40,className:"h-8 w-auto sm:h-10 md:h-12 transition-transform duration-300 group-hover:scale-110",priority:!0}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"})]}),(0,s.jsx)("span",{className:"truncate text-base xs:text-lg sm:text-2xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-indigo-500 group-hover:via-purple-500 group-hover:to-pink-500 transition-all duration-300",children:"Store"})]}),(0,s.jsxs)("div",{className:"hidden sm:flex gap-2 md:gap-4 lg:gap-6 items-center min-w-0",children:[(0,s.jsx)(o(),{href:"/",className:"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5",children:"\uD83C\uDFE0 Home"}),r&&(0,s.jsx)(o(),{href:"/user/dashboard",className:"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5",children:"\uD83D\uDCCA Dashboard"}),r&&"ADMIN"===c&&(0,s.jsx)(o(),{href:"/admin/dashboard",className:"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:"⚡ Admin"}),r?(0,s.jsxs)("div",{className:"relative min-w-0",children:[(0,s.jsxs)("button",{className:"flex items-center gap-2 focus:outline-none min-w-0 px-2 py-1 rounded-2xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 transform hover:-translate-y-0.5",onClick:function(){g(e=>!e)},"aria-label":"Open user menu",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.default,{src:x||"/usericon.png",alt:"Profile",width:32,height:32,className:"rounded-full border-2 border-gradient-to-r from-indigo-200 to-purple-200 object-cover bg-white h-8 w-8 md:h-9 md:w-9 shadow-md"}),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"})]}),(0,s.jsx)("span",{className:"hidden md:inline font-semibold text-gray-700 truncate max-w-[80px]",children:"Account"}),(0,s.jsx)("svg",{className:`w-4 h-4 ml-1 text-gray-600 transition-transform duration-300 ${u?"rotate-180":""}`,fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"})})]}),u&&(0,s.jsx)("div",{className:"absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-lg border border-gray-200 rounded-2xl shadow-2xl z-50 overflow-hidden",onMouseLeave:v,children:(0,s.jsxs)("div",{className:"p-2",children:[(0,s.jsxs)(o(),{href:"/user/dashboard",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5",onClick:v,children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDCCA"}),"Dashboard"]}),(0,s.jsxs)(o(),{href:"/user/profile",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5",onClick:v,children:[(0,s.jsx)("span",{className:"text-lg",children:"⚙️"}),"Profile Settings"]}),(0,s.jsx)("div",{className:"border-t border-gray-100 my-2"}),(0,s.jsxs)("button",{onClick:()=>{f(),v()},className:"flex items-center gap-3 w-full text-left px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5",children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDEAA"}),"Logout"]})]})})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o(),{href:"/login",className:"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:"\uD83D\uDD10 Login"}),(0,s.jsx)(o(),{href:"/register",className:"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:"✨ Register"})]})]}),(0,s.jsx)("button",{type:"button",className:"flex items-center justify-center p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 sm:hidden hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300","aria-label":p?"Close menu":"Open menu",onClick:()=>b(e=>!e),children:p?(0,s.jsx)(i.A,{className:"w-7 h-7 text-gray-700","aria-hidden":!p}):(0,s.jsx)(d.A,{className:"w-7 h-7 text-gray-700","aria-hidden":p})}),(0,s.jsxs)("button",{className:"relative px-3 py-1 md:px-4 md:py-2 rounded-2xl font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg hover:from-red-600 hover:to-pink-700 hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 ml-1 md:ml-2 text-sm md:text-base transform hover:-translate-y-0.5",onClick:t,"aria-label":"Open cart",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"\uD83D\uDED2 Cart"}),(0,s.jsx)("span",{className:"sm:hidden",children:"\uD83D\uDED2"}),(0,s.jsxs)("span",{className:"sm:ml-1",children:["(",e,")"]}),e>0&&(0,s.jsx)("span",{className:"absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs rounded-full px-1.5 py-0.5 animate-bounce font-bold shadow-lg",children:e})]})]}),p&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300 block",style:{left:0,right:0},onClick:()=>b(!1),"aria-hidden":!p}),(0,s.jsxs)("div",{className:"fixed top-0 right-0 z-50 w-full max-w-xs h-screen bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs min-w-0 sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden translate-x-0 border-l border-gray-200",role:"dialog","aria-modal":"true",tabIndex:-1,style:{right:0,left:"auto"},onKeyDown:e=>{if("Tab"===e.key){let t=Array.from(e.currentTarget.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'));if(0===t.length)return;let r=t[0],s=t[t.length-1];e.shiftKey||document.activeElement!==s?e.shiftKey&&document.activeElement===r&&(e.preventDefault(),s.focus()):(e.preventDefault(),r.focus())}},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50",children:[(0,s.jsx)("span",{className:"font-black text-lg bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83D\uDCF1 Menu"}),(0,s.jsx)("button",{className:"p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 hover:bg-white/50 transition-all duration-300","aria-label":"Close menu",onClick:()=>b(!1),tabIndex:p?0:-1,children:(0,s.jsx)(i.A,{className:"w-6 h-6 text-gray-700"})})]}),(0,s.jsxs)("nav",{className:"flex flex-col gap-2 px-4 py-6 flex-1 overflow-y-auto max-h-[calc(100vh-80px)]",children:[(0,s.jsxs)(o(),{href:"/",className:"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base transform hover:-translate-y-0.5",onClick:()=>b(!1),children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83C\uDFE0"}),"Home"]}),r&&(0,s.jsxs)(o(),{href:"/user/dashboard",className:"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base transform hover:-translate-y-0.5",onClick:()=>b(!1),children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDCCA"}),"Dashboard"]}),r&&"ADMIN"===c&&(0,s.jsxs)(o(),{href:"/admin/dashboard",className:"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5",onClick:()=>b(!1),children:[(0,s.jsx)("span",{className:"text-lg",children:"⚡"}),"Admin Panel"]}),r?(0,s.jsxs)("div",{className:"flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200",children:[(0,s.jsxs)(o(),{href:"/user/profile",className:"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-300 text-base transform hover:-translate-y-0.5",onClick:()=>b(!1),children:[(0,s.jsx)("span",{className:"text-lg",children:"⚙️"}),"Profile Settings"]}),(0,s.jsxs)("button",{onClick:()=>{f(),b(!1)},className:"flex items-center gap-3 w-full text-left px-4 py-3 rounded-2xl font-semibold text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-300 text-base transform hover:-translate-y-0.5",children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDEAA"}),"Logout"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(o(),{href:"/login",className:"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5",onClick:()=>b(!1),children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDD10"}),"Login"]}),(0,s.jsxs)(o(),{href:"/register",className:"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5",onClick:()=>b(!1),children:[(0,s.jsx)("span",{className:"text-lg",children:"✨"}),"Register"]})]})]}),r&&(0,s.jsxs)("div",{className:"flex items-center gap-3 px-4 py-4 border-t border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.default,{src:x||"/usericon.png",alt:"Profile",width:40,height:40,className:"rounded-full border-2 border-indigo-200 object-cover bg-white shadow-md w-10 h-10"}),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"})]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-bold text-gray-900 truncate text-sm",children:"Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 truncate",children:"Logged in"}),(0,s.jsx)("span",{className:"inline-block mt-1 px-2 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full",children:c})]})]})]})]})]})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>i});var s=r(7413),a=r(2376),o=r.n(a),n=r(8726),l=r.n(n);r(1135);let i={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${o().variable} ${l().variable} antialiased`,children:e})})}},4899:()=>{},5356:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(687),a=r(3210);function o({cartItems:e,onOrderSuccess:t,onCancel:r}){let[o,n]=(0,a.useState)({customerName:"",customerPhone:"",customerAddress:""}),[l,i]=(0,a.useState)(!1),[d,c]=(0,a.useState)(""),m=e=>{let{name:t,value:r}=e.target;n(e=>({...e,[t]:r}))},x=()=>e.reduce((e,t)=>e+t.price*(t.quantity||1),0),h=async r=>{r.preventDefault(),i(!0),c("");let s=localStorage.getItem("token");if(!s){c("Please log in to place an order"),i(!1);return}try{let r=e.map(e=>fetch("http://localhost:3001/user/orders",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({productId:e.productId,customerName:o.customerName,customerPhone:o.customerPhone,customerAddress:o.customerAddress,customColor:e.color,customText:e.text,isCustomized:e.customized,quantity:e.quantity||1})}));(await Promise.all(r)).every(e=>e.ok)?(localStorage.removeItem("cart"),t()):c("Some orders failed to process. Please try again.")}catch{c("Failed to place order. Please try again.")}finally{i(!1)}};return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-[#1a237e]",children:"Complete Your Order"}),(0,s.jsx)("button",{onClick:r,className:"text-gray-500 hover:text-gray-700 text-2xl","aria-label":"Close",children:"\xd7"})]}),(0,s.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-3",children:"Order Summary"}),e.map((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.name}),e.customized&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[e.color&&(0,s.jsxs)("span",{children:["Color: ",e.color]}),e.text&&(0,s.jsxs)("span",{className:"ml-2",children:["Text: “",e.text,"”"]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Qty: ",e.quantity||1]})]}),(0,s.jsxs)("div",{className:"font-semibold",children:["K",(e.price*(e.quantity||1)).toFixed(2)]})]},t)),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-3 mt-3 border-t border-gray-300",children:[(0,s.jsx)("div",{className:"font-bold text-lg",children:"Total:"}),(0,s.jsxs)("div",{className:"font-bold text-lg text-[#1a237e]",children:["K",x().toFixed(2)]})]})]}),(0,s.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"customerName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name *"}),(0,s.jsx)("input",{type:"text",id:"customerName",name:"customerName",value:o.customerName,onChange:m,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent",placeholder:"Enter your full name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"customerPhone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number *"}),(0,s.jsx)("input",{type:"tel",id:"customerPhone",name:"customerPhone",value:o.customerPhone,onChange:m,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent",placeholder:"e.g. +260 971 781 907"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"customerAddress",className:"block text-sm font-medium text-gray-700 mb-1",children:"Delivery/Pickup Address *"}),(0,s.jsx)("textarea",{id:"customerAddress",name:"customerAddress",value:o.customerAddress,onChange:m,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent",placeholder:"Enter your full address or preferred pickup location"})]}),d&&(0,s.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded-md",children:d}),(0,s.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,s.jsx)("button",{type:"button",onClick:r,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:l,className:`flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold transition ${l?"opacity-50 cursor-not-allowed":"hover:bg-[#2a3490]"}`,children:l?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Placing Order..."})]}):`Place Order - K${x().toFixed(2)}`})]})]}),(0,s.jsx)("div",{className:"mt-4 text-xs text-gray-500 text-center",children:"By placing this order, you agree to our terms and conditions. You will be contacted for payment and delivery arrangements."})]})})})}function n({isOpen:e,onClose:t}){let[r,n]=(0,a.useState)([]),[l,i]=(0,a.useState)(!1),[d,c]=(0,a.useState)(!1),m=e=>{let t=r.filter(t=>t.id!==e);n(t),localStorage.setItem("cart",JSON.stringify(t))},x=(e,t)=>{if(t<1)return void m(e);let s=r.map(r=>r.id===e?{...r,quantity:t}:r);n(s),localStorage.setItem("cart",JSON.stringify(s))};return e?l?(0,s.jsx)(o,{cartItems:r,onOrderSuccess:()=>{i(!1),c(!0),n([]),setTimeout(()=>{c(!1),t()},3e3)},onCancel:()=>i(!1)}):d?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-md",children:[(0,s.jsx)("div",{className:"text-green-600 text-6xl mb-4",children:"✓"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-[#1a237e] mb-2",children:"Order Placed Successfully!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Thank you for your order. We will contact you soon for payment and delivery arrangements."}),(0,s.jsx)("div",{className:"animate-pulse text-sm text-gray-500",children:"Closing automatically..."})]})}):(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-[#1a237e]",children:"Shopping Cart"}),(0,s.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 text-2xl","aria-label":"Close cart",children:"\xd7"})]}),0===r.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDED2"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Your cart is empty"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Add some products to get started!"}),(0,s.jsx)("button",{onClick:t,className:"px-6 py-2 bg-[#1a237e] text-white rounded-md hover:bg-[#2a3490] transition",children:"Continue Shopping"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"space-y-4 mb-6",children:r.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.name}),e.customized&&(0,s.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:[e.color&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:"Color:"}),(0,s.jsx)("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,s.jsx)("span",{className:"font-mono text-xs",children:e.color})]}),e.text&&(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("span",{children:"Text: “"}),(0,s.jsx)("span",{className:"font-medium",children:e.text}),(0,s.jsx)("span",{children:"”"})]})]}),(0,s.jsxs)("div",{className:"text-lg font-semibold text-[#1a237e] mt-2",children:["K",e.price.toFixed(2)," each"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{onClick:()=>x(e.id,(e.quantity||1)-1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition",children:"-"}),(0,s.jsx)("span",{className:"w-8 text-center font-semibold",children:e.quantity||1}),(0,s.jsx)("button",{onClick:()=>x(e.id,(e.quantity||1)+1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition",children:"+"})]}),(0,s.jsx)("button",{onClick:()=>m(e.id),className:"text-red-500 hover:text-red-700 p-2","aria-label":"Remove item",children:"\uD83D\uDDD1️"})]},e.id))}),(0,s.jsx)("div",{className:"border-t border-gray-200 pt-4 mb-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center text-xl font-bold",children:[(0,s.jsx)("span",{children:"Total:"}),(0,s.jsxs)("span",{className:"text-[#1a237e]",children:["K",r.reduce((e,t)=>e+t.price*(t.quantity||1),0).toFixed(2)]})]})}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{onClick:t,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition",children:"Continue Shopping"}),(0,s.jsx)("button",{onClick:()=>{i(!0)},className:"flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold hover:bg-[#2a3490] transition",children:"Proceed to Checkout"})]})]})]})})}):null}},7219:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7467:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};