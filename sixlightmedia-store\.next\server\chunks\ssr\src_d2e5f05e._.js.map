{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n      VERIFY_EMAIL: \"/auth/verify-email\",\n      FORGOT_PASSWORD: \"/auth/forgot-password\",\n      RESET_PASSWORD: \"/auth/reset-password\",\n      RESEND_VERIFICATION: \"/auth/resend-verification\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      UPDATE_PROFILE: \"/user/profile\",\n      UPLOAD_PROFILE_IMAGE: \"/user/upload-profile-image\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACjB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;QACvB;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,6EAAgE;IAClE,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/api/auth.ts"], "sourcesContent": ["import { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport async function login(email: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function register(email: string, password: string, name?: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password, name }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function verifyEmail(token: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.VERIFY_EMAIL), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function forgotPassword(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.FORGOT_PASSWORD),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n\r\nexport async function resetPassword(token: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function resendVerification(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESEND_VERIFICATION),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,eAAe,MAAM,KAAa,EAAE,QAAgB;IACzD,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG;QAClE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAa;IAC3E,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;YAAU;QAAK;IAC/C;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,YAAY,KAAa;IAC7C,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG;QACzE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,eAAe,KAAa;IAChD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GACnD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,cAAc,KAAa,EAAE,QAAgB;IACjE,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,GAAG;QAC3E,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,mBAAmB,KAAa;IACpD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,GACvD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/verify-email/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState, Suspense } from \"react\";\nimport { useSearchParams, useRouter } from \"next/navigation\";\nimport { verifyEmail, resendVerification } from \"@/api/auth\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { CheckCircle, XCircle, Mail, RefreshCw } from \"lucide-react\";\n\nfunction VerifyEmailForm() {\n  const [status, setStatus] = useState<\n    \"loading\" | \"success\" | \"error\" | \"expired\"\n  >(\"loading\");\n  const [message, setMessage] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [resending, setResending] = useState(false);\n  const searchParams = useSearchParams();\n  const router = useRouter();\n\n  useEffect(() => {\n    const token = searchParams?.get(\"token\");\n\n    if (!token) {\n      setStatus(\"error\");\n      setMessage(\n        \"Invalid verification link. Please check your email for the correct link.\"\n      );\n      return;\n    }\n\n    // Verify the email token\n    verifyEmail(token)\n      .then((result) => {\n        if (result.message) {\n          setStatus(\"success\");\n          setMessage(result.message);\n          // Redirect to login after 3 seconds\n          setTimeout(() => {\n            router.push(\n              \"/login?message=Email verified successfully! You can now log in.\"\n            );\n          }, 3000);\n        } else {\n          setStatus(\"error\");\n          setMessage(result.error || \"Verification failed\");\n        }\n      })\n      .catch((error) => {\n        setStatus(\"error\");\n        if (error.message?.includes(\"expired\")) {\n          setStatus(\"expired\");\n          setMessage(\n            \"Your verification link has expired. Please request a new one.\"\n          );\n        } else if (error.message?.includes(\"already verified\")) {\n          setStatus(\"success\");\n          setMessage(\"Your email is already verified! You can log in now.\");\n          setTimeout(() => {\n            router.push(\"/login\");\n          }, 2000);\n        } else {\n          setMessage(\n            \"Verification failed. Please try again or contact support.\"\n          );\n        }\n      });\n  }, [searchParams, router]);\n\n  const handleResendVerification = async () => {\n    if (!email) {\n      alert(\"Please enter your email address\");\n      return;\n    }\n\n    setResending(true);\n    try {\n      const result = await resendVerification(email);\n      alert(\n        result.message || \"Verification email sent! Please check your inbox.\"\n      );\n    } catch (error) {\n      alert(\"Failed to resend verification email. Please try again.\");\n    } finally {\n      setResending(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4\">\n      <div className=\"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100\">\n        <div className=\"flex flex-col items-center mb-6\">\n          <Image\n            src=\"/6 Light Logo.png\"\n            alt=\"6 Light Logo\"\n            width={64}\n            height={64}\n            className=\"mb-4\"\n          />\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Email Verification\n          </h1>\n        </div>\n\n        {status === \"loading\" && (\n          <div className=\"space-y-4\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"text-gray-600\">Verifying your email...</p>\n          </div>\n        )}\n\n        {status === \"success\" && (\n          <div className=\"space-y-4\">\n            <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto\" />\n            <div className=\"space-y-2\">\n              <h2 className=\"text-xl font-semibold text-green-700\">\n                Email Verified!\n              </h2>\n              <p className=\"text-gray-600\">{message}</p>\n              <p className=\"text-sm text-gray-500\">\n                Redirecting to login page...\n              </p>\n            </div>\n            <Link\n              href=\"/login\"\n              className=\"inline-block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors\"\n            >\n              Continue to Login\n            </Link>\n          </div>\n        )}\n\n        {status === \"error\" && (\n          <div className=\"space-y-4\">\n            <XCircle className=\"h-16 w-16 text-red-500 mx-auto\" />\n            <div className=\"space-y-2\">\n              <h2 className=\"text-xl font-semibold text-red-700\">\n                Verification Failed\n              </h2>\n              <p className=\"text-gray-600\">{message}</p>\n            </div>\n            <div className=\"space-y-3\">\n              <Link\n                href=\"/login\"\n                className=\"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Go to Login\n              </Link>\n              <Link\n                href=\"/register\"\n                className=\"inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                Create New Account\n              </Link>\n            </div>\n          </div>\n        )}\n\n        {status === \"expired\" && (\n          <div className=\"space-y-4\">\n            <Mail className=\"h-16 w-16 text-orange-500 mx-auto\" />\n            <div className=\"space-y-2\">\n              <h2 className=\"text-xl font-semibold text-orange-700\">\n                Link Expired\n              </h2>\n              <p className=\"text-gray-600\">{message}</p>\n            </div>\n            <div className=\"space-y-3\">\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"Enter your email address\"\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n              <button\n                onClick={handleResendVerification}\n                disabled={resending}\n                className=\"w-full bg-orange-600 text-white py-3 px-6 rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors flex items-center justify-center gap-2\"\n              >\n                {resending ? (\n                  <>\n                    <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                    Sending...\n                  </>\n                ) : (\n                  <>\n                    <Mail className=\"h-4 w-4\" />\n                    Resend Verification Email\n                  </>\n                )}\n              </button>\n              <Link\n                href=\"/register\"\n                className=\"inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                Create New Account\n              </Link>\n            </div>\n          </div>\n        )}\n\n        <div className=\"mt-6 pt-6 border-t border-gray-200\">\n          <p className=\"text-sm text-gray-500\">\n            Need help?{\" \"}\n            <Link href=\"/contact\" className=\"text-blue-600 hover:text-blue-800\">\n              Contact Support\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function VerifyEmailPage() {\n  return (\n    <Suspense\n      fallback={\n        <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md\">\n            <div className=\"text-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n              <p className=\"mt-4 text-gray-600\">Verifying email...</p>\n            </div>\n          </div>\n        </div>\n      }\n    >\n      <VerifyEmailForm />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEjC;IACF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,cAAc,IAAI;QAEhC,IAAI,CAAC,OAAO;YACV,UAAU;YACV,WACE;YAEF;QACF;QAEA,yBAAyB;QACzB,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD,EAAE,OACT,IAAI,CAAC,CAAC;YACL,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU;gBACV,WAAW,OAAO,OAAO;gBACzB,oCAAoC;gBACpC,WAAW;oBACT,OAAO,IAAI,CACT;gBAEJ,GAAG;YACL,OAAO;gBACL,UAAU;gBACV,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF,GACC,KAAK,CAAC,CAAC;YACN,UAAU;YACV,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY;gBACtC,UAAU;gBACV,WACE;YAEJ,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,qBAAqB;gBACtD,UAAU;gBACV,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,WACE;YAEJ;QACF;IACJ,GAAG;QAAC;QAAc;KAAO;IAEzB,MAAM,2BAA2B;QAC/B,IAAI,CAAC,OAAO;YACV,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAE;YACxC,MACE,OAAO,OAAO,IAAI;QAEtB,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;gBAKlD,WAAW,2BACV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAIhC,WAAW,2BACV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAGrD,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;gBAMJ,WAAW,yBACV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAGnD,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;sCAEhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAON,WAAW,2BACV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;sCAEhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAyB;;qEAIhD;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;8CAKlC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAwB;4BACxB;0CACX,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhF;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QACP,wBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;kBAM1C,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}