(()=>{var t={};t.id=957,t.ids=[957],t.modules={504:(t,e,i)=>{"use strict";i.d(e,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\admin\\\\dashboard\\\\AdminDashboardClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\AdminDashboardClient.tsx","default")},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1031:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>o,dynamic:()=>n});var s=i(7413),a=i(2909),r=i(504);let n="force-dynamic";async function o(){let t=await (0,a.ZT)();return(0,s.jsx)(r.default,{user:t.user})}},1860:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2688:(t,e,i)=>{"use strict";i.d(e,{A:()=>c});var s=i(3210);let a=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),n=t=>{let e=r(t);return e.charAt(0).toUpperCase()+e.slice(1)},o=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim(),l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:r="",children:n,iconNode:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...h,width:e,height:e,stroke:t,strokeWidth:a?24*Number(i)/Number(e):i,className:o("lucide",r),...!n&&!l(c)&&{"aria-hidden":"true"},...c},[...d.map(([t,e])=>(0,s.createElement)(t,e)),...Array.isArray(n)?n:[n]])),c=(t,e)=>{let i=(0,s.forwardRef)(({className:i,...r},l)=>(0,s.createElement)(d,{ref:l,iconNode:e,className:o(`lucide-${a(n(t))}`,`lucide-${t}`,i),...r}));return i.displayName=n(t),i}},2909:(t,e,i)=>{"use strict";i.d(e,{ZT:()=>o,oC:()=>n});var s=i(4999),a=i(9916);async function r(){try{let t=await (0,s.UL)(),e=t.get("jwt")?.value;if(!e)return console.log("No JWT token found in cookies"),{isAuthenticated:!1};let i="https://backendapi-sixlight.onrender.com";console.log(`Verifying auth with: ${i}/auth/verify`);let a=await fetch(`${i}/auth/verify`,{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},cache:"no-store"});if(console.log(`Auth verification response: ${a.status}`),!a.ok)return console.log(`Auth verification failed: ${a.status} ${a.statusText}`),{isAuthenticated:!1};let r=await a.json();return console.log(`Auth verification successful for user: ${r.email}`),{isAuthenticated:!0,user:{id:r.id,email:r.email,role:r.role,name:r.name,profileImage:r.profileImage}}}catch(t){return console.error("Auth verification failed:",t),{isAuthenticated:!1}}}async function n(t="/login"){let e=await r();return e.isAuthenticated||(0,a.redirect)(t),e}async function o(t="/"){let e=await n("/login");return e.user?.role!=="ADMIN"&&(0,a.redirect)(t),e}},2941:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3544:(t,e,i)=>{"use strict";let s;i.d(e,{default:()=>aG});var a=i(687),r=i(3210),n=i(5814),o=i.n(n),l=i(6189);function h(t){return t+.5|0}let d=(t,e,i)=>Math.max(Math.min(t,i),e);function c(t){return d(h(2.55*t),0,255)}function u(t){return d(h(255*t),0,255)}function f(t){return d(h(t/2.55)/100,0,1)}function g(t){return d(h(100*t),0,100)}let p={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},m=[..."0123456789ABCDEF"],b=t=>m[15&t],x=t=>m[(240&t)>>4]+m[15&t],_=t=>(240&t)>>4==(15&t),y=t=>_(t.r)&&_(t.g)&&_(t.b)&&_(t.a),v=(t,e)=>t<255?e(t):"",M=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function w(t,e,i){let s=e*Math.min(i,1-i),a=(e,a=(e+t/30)%12)=>i-s*Math.max(Math.min(a-3,9-a,1),-1);return[a(0),a(8),a(4)]}function k(t,e,i){let s=(s,a=(s+t/60)%6)=>i-i*e*Math.max(Math.min(a,4-a,1),0);return[s(5),s(3),s(1)]}function P(t,e,i){let s,a=w(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)a[s]*=1-e-i,a[s]+=e;return a}function S(t){let e,i,s,a=t.r/255,r=t.g/255,n=t.b/255,o=Math.max(a,r,n),l=Math.min(a,r,n),h=(o+l)/2;o!==l&&(s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=a===o?(r-n)/s+6*(r<n):r===o?(n-a)/s+2:(a-r)/s+4)+.5);return[0|e,i||0,h]}function C(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(u)}function D(t){return(t%360+360)%360}let O={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},T={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},A=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,L=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,E=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function R(t,e,i){if(t){let s=S(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),t.r=(s=C(w,s,void 0,void 0))[0],t.g=s[1],t.b=s[2]}}function I(t,e){return t?Object.assign(e||{},t):t}function F(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=u(t[3]))):(e=I(t,{r:0,g:0,b:0,a:1})).a=u(e.a),e}class z{constructor(t){let e;if(t instanceof z)return t;let i=typeof t;"object"===i?e=F(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*p[t[1]],g:255&17*p[t[2]],b:255&17*p[t[3]],a:5===i?17*p[t[4]]:255}:(7===i||9===i)&&(e={r:p[t[1]]<<4|p[t[2]],g:p[t[3]]<<4|p[t[4]],b:p[t[5]]<<4|p[t[6]],a:9===i?p[t[7]]<<4|p[t[8]]:255})),e}(t)||function(t){s||((s=function(){let t,e,i,s,a,r={},n=Object.keys(T),o=Object.keys(O);for(t=0;t<n.length;t++){for(e=0,s=a=n[t];e<o.length;e++)i=o[e],a=a.replace(i,O[i]);i=parseInt(T[s],16),r[a]=[i>>16&255,i>>8&255,255&i]}return r}()).transparent=[0,0,0,0]);let e=s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s,a=A.exec(t),r=255;if(a){if(a[7]!==e){let t=+a[7];r=a[8]?c(t):d(255*t,0,255)}return e=+a[1],i=+a[3],s=+a[5],e=255&(a[2]?c(e):d(e,0,255)),{r:e,g:i=255&(a[4]?c(i):d(i,0,255)),b:s=255&(a[6]?c(s):d(s,0,255)),a:r}}}(t):function(t){let e,i=M.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?c(+i[5]):u(+i[5]));let a=D(+i[2]),r=i[3]/100,n=i[4]/100;return{r:(e="hwb"===i[1]?C(P,a,r,n):"hsv"===i[1]?C(k,a,r,n):C(w,a,r,n))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=I(this._rgb);return t&&(t.a=f(t.a)),t}set rgb(t){this._rgb=F(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${f(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=y(t=this._rgb)?b:x,t?"#"+e(t.r)+e(t.g)+e(t.b)+v(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=S(t),i=e[0],s=g(e[1]),a=g(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${a}%, ${f(t.a)})`:`hsl(${i}, ${s}%, ${a}%)`}(this._rgb):void 0}mix(t,e){if(t){let i,s=this.rgb,a=t.rgb,r=e===i?.5:e,n=2*r-1,o=s.a-a.a,l=((n*o==-1?n:(n+o)/(1+n*o))+1)/2;i=1-l,s.r=255&l*s.r+i*a.r+.5,s.g=255&l*s.g+i*a.g+.5,s.b=255&l*s.b+i*a.b+.5,s.a=r*s.a+(1-r)*a.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=E(f(t.r)),a=E(f(t.g)),r=E(f(t.b));return{r:u(L(s+i*(E(f(e.r))-s))),g:u(L(a+i*(E(f(e.g))-a))),b:u(L(r+i*(E(f(e.b))-r))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new z(this.rgb)}alpha(t){return this._rgb.a=u(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=h(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return R(this._rgb,2,t),this}darken(t){return R(this._rgb,2,-t),this}saturate(t){return R(this._rgb,1,t),this}desaturate(t){return R(this._rgb,1,-t),this}rotate(t){var e,i;return e=this._rgb,(i=S(e))[0]=D(i[0]+t),e.r=(i=C(w,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function N(){}let j=(()=>{let t=0;return()=>t++})();function V(t){return null==t}function B(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function W(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function H(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function $(t,e){return H(t)?t:e}function U(t,e){return void 0===t?e:t}let Y=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,q=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function X(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function G(t,e,i,s){let a,r,n;if(B(t))if(r=t.length,s)for(a=r-1;a>=0;a--)e.call(i,t[a],a);else for(a=0;a<r;a++)e.call(i,t[a],a);else if(W(t))for(a=0,r=(n=Object.keys(t)).length;a<r;a++)e.call(i,t[n[a]],n[a])}function K(t,e){let i,s,a,r;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(a=t[i],r=e[i],a.datasetIndex!==r.datasetIndex||a.index!==r.index)return!1;return!0}function Z(t){if(B(t))return t.map(Z);if(W(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,a=0;for(;a<s;++a)e[i[a]]=Z(t[i[a]]);return e}return t}function J(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function Q(t,e,i,s){if(!J(t))return;let a=e[t],r=i[t];W(a)&&W(r)?tt(a,r,s):e[t]=Z(r)}function tt(t,e,i){let s,a=B(e)?e:[e],r=a.length;if(!W(t))return t;let n=(i=i||{}).merger||Q;for(let e=0;e<r;++e){if(!W(s=a[e]))continue;let r=Object.keys(s);for(let e=0,a=r.length;e<a;++e)n(r[e],t,s,i)}return t}function te(t,e){return tt(t,e,{merger:ti})}function ti(t,e,i){if(!J(t))return;let s=e[t],a=i[t];W(s)&&W(a)?te(s,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Z(a))}let ts={"":t=>t,x:t=>t.x,y:t=>t.y};function ta(t,e){return(ts[e]||(ts[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function tr(t){return t.charAt(0).toUpperCase()+t.slice(1)}let tn=t=>void 0!==t,to=t=>"function"==typeof t,tl=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},th=Math.PI,td=2*th,tc=td+th,tu=Number.POSITIVE_INFINITY,tf=th/180,tg=th/2,tp=th/4,tm=2*th/3,tb=Math.log10,tx=Math.sign;function t_(t,e,i){return Math.abs(t-e)<i}function ty(t){let e=Math.round(t),i=Math.pow(10,Math.floor(tb(t=t_(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function tv(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tM(t,e,i){let s,a,r;for(s=0,a=t.length;s<a;s++)isNaN(r=t[s][i])||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function tw(t){return th/180*t}function tk(t){if(!H(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function tP(t,e){let i=e.x-t.x,s=e.y-t.y,a=Math.sqrt(i*i+s*s),r=Math.atan2(s,i);return r<-.5*th&&(r+=td),{angle:r,distance:a}}function tS(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tC(t,e){return(t-e+tc)%td-th}function tD(t){return(t%td+td)%td}function tO(t,e,i,s){let a=tD(t),r=tD(e),n=tD(i),o=tD(r-a),l=tD(n-a),h=tD(a-r),d=tD(a-n);return a===r||a===n||s&&r===n||o>l&&h<d}function tT(t,e,i){return Math.max(e,Math.min(i,t))}function tA(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function tL(t,e,i){let s;i=i||(i=>t[i]<e);let a=t.length-1,r=0;for(;a-r>1;)i(s=r+a>>1)?r=s:a=s;return{lo:r,hi:a}}let tE=(t,e,i,s)=>tL(t,i,s?s=>{let a=t[s][e];return a<i||a===i&&t[s+1][e]===i}:s=>t[s][e]<i),tR=(t,e,i)=>tL(t,i,s=>t[s][e]>=i),tI=["push","pop","shift","splice","unshift"];function tF(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,a=s.indexOf(e);-1!==a&&s.splice(a,1),s.length>0||(tI.forEach(e=>{delete t[e]}),delete t._chartjs)}function tz(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tN="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tj(t,e){let i=[],s=!1;return function(...a){i=a,s||(s=!0,tN.call(window,()=>{s=!1,t.apply(e,i)}))}}let tV=t=>"start"===t?"left":"end"===t?"right":"center",tB=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,tW=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function tH(t,e,i){let s=e.length,a=0,r=s;if(t._sorted){let{iScale:n,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=n.axis,{min:c,max:u,minDefined:f,maxDefined:g}=n.getUserBounds();if(f){if(a=Math.min(tE(l,d,c).lo,i?s:tE(e,d,n.getPixelForValue(c)).lo),h){let t=l.slice(0,a+1).reverse().findIndex(t=>!V(t[o.axis]));a-=Math.max(0,t)}a=tT(a,0,s-1)}if(g){let t=Math.max(tE(l,n.axis,u,!0).hi+1,i?0:tE(e,d,n.getPixelForValue(u),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!V(t[o.axis]));t+=Math.max(0,e)}r=tT(t,a,s)-a}else r=s-a}return{start:a,count:r}}function t$(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,a={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=a,!0;let r=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,a),r}let tU=t=>0===t||1===t,tY=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*td/i)),tq=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*td/i)+1,tX={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*tg)+1,easeOutSine:t=>Math.sin(t*tg),easeInOutSine:t=>-.5*(Math.cos(th*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tU(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tU(t)?t:tY(t,.075,.3),easeOutElastic:t=>tU(t)?t:tq(t,.075,.3),easeInOutElastic:t=>tU(t)?t:t<.5?.5*tY(2*t,.1125,.45):.5+.5*tq(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tX.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tX.easeInBounce(2*t):.5*tX.easeOutBounce(2*t-1)+.5};function tG(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tK(t){return tG(t)?t:new z(t)}function tZ(t){return tG(t)?t:new z(t).saturate(.5).darken(.1).hexString()}let tJ=["x","y","borderWidth","radius","tension"],tQ=["color","borderColor","backgroundColor"],t0=new Map;function t1(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=t0.get(i);return s||(s=new Intl.NumberFormat(t,e),t0.set(i,s)),s})(e,i).format(t)}let t2={values:t=>B(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let a=this.chart.options.locale,r=t;if(i.length>1){var n,o;let e,a=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(a<1e-4||a>1e15)&&(s="scientific"),n=t,Math.abs(e=(o=i).length>3?o[2].value-o[1].value:o[1].value-o[0].value)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),r=e}let l=tb(Math.abs(r)),h=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),d={notation:s,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(d,this.options.ticks.format),t1(t,a,d)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(tb(t))))||e>.8*i.length?t2.numeric.call(this,t,e,i):""}};var t5={formatters:t2};let t4=Object.create(null),t8=Object.create(null);function t6(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function t3(t,e,i){return"string"==typeof e?tt(t6(t,e),i):tt(t6(t,""),e)}class t9{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>tZ(e.backgroundColor),this.hoverBorderColor=(t,e)=>tZ(e.borderColor),this.hoverColor=(t,e)=>tZ(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return t3(this,t,e)}get(t){return t6(this,t)}describe(t,e){return t3(t8,t,e)}override(t,e){return t3(t4,t,e)}route(t,e,i,s){let a=t6(this,t),r=t6(this,i),n="_"+e;Object.defineProperties(a,{[n]:{value:a[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[n],e=r[s];return W(t)?Object.assign({},e,t):U(t,e)},set(t){this[n]=t}}})}apply(t){t.forEach(t=>t(this))}}var t7=new t9({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:tQ},numbers:{type:"number",properties:tJ}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t5.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function et(t,e,i,s,a){let r=e[a];return r||(r=e[a]=t.measureText(a).width,i.push(a)),r>s&&(s=r),s}function ee(t,e,i){let s=t.currentDevicePixelRatio,a=0!==i?Math.max(i/2,.5):0;return Math.round((e-a)*s)/s+a}function ei(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function es(t,e,i,s){ea(t,e,i,s,null)}function ea(t,e,i,s,a){let r,n,o,l,h,d,c,u,f=e.pointStyle,g=e.rotation,p=e.radius,m=(g||0)*tf;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(r=f.toString())||"[object HTMLCanvasElement]"===r)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(p)&&!(p<=0)){switch(t.beginPath(),f){default:a?t.ellipse(i,s,a/2,p,0,0,td):t.arc(i,s,p,0,td),t.closePath();break;case"triangle":d=a?a/2:p,t.moveTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=tm,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=tm,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),t.closePath();break;case"rectRounded":h=.516*p,n=Math.cos(m+tp)*(l=p-h),c=Math.cos(m+tp)*(a?a/2-h:l),o=Math.sin(m+tp)*l,u=Math.sin(m+tp)*(a?a/2-h:l),t.arc(i-c,s-o,h,m-th,m-tg),t.arc(i+u,s-n,h,m-tg,m),t.arc(i+c,s+o,h,m,m+tg),t.arc(i-u,s+n,h,m+tg,m+th),t.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,d=a?a/2:l,t.rect(i-d,s-l,2*d,2*l);break}m+=tp;case"rectRot":c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+u,s-n),t.lineTo(i+c,s+o),t.lineTo(i-u,s+n),t.closePath();break;case"crossRot":m+=tp;case"cross":c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n);break;case"star":c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n),m+=tp,c=Math.cos(m)*(a?a/2:p),n=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n);break;case"line":n=a?a/2:Math.cos(m)*p,o=Math.sin(m)*p,t.moveTo(i-n,s-o),t.lineTo(i+n,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(a?a/2:p),s+Math.sin(m)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function er(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function en(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function eo(t){t.restore()}function el(t,e,i,s,a){if(!e)return t.lineTo(i.x,i.y);if("middle"===a){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===a!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function eh(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function ed(t,e,i,s,a,r={}){let n,o,l=B(e)?e:[e],h=r.strokeWidth>0&&""!==r.strokeColor;for(t.save(),t.font=a.string,r.translation&&t.translate(r.translation[0],r.translation[1]),V(r.rotation)||t.rotate(r.rotation),r.color&&(t.fillStyle=r.color),r.textAlign&&(t.textAlign=r.textAlign),r.textBaseline&&(t.textBaseline=r.textBaseline),n=0;n<l.length;++n)o=l[n],r.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,r.backdrop),h&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),V(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(o,i,s,r.maxWidth)),t.fillText(o,i,s,r.maxWidth),function(t,e,i,s,a){if(a.strikethrough||a.underline){let r=t.measureText(s),n=e-r.actualBoundingBoxLeft,o=e+r.actualBoundingBoxRight,l=i-r.actualBoundingBoxAscent,h=i+r.actualBoundingBoxDescent,d=a.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=a.decorationWidth||2,t.moveTo(n,d),t.lineTo(o,d),t.stroke()}}(t,i,s,o,r),s+=Number(a.lineHeight);t.restore()}function ec(t,e){let{x:i,y:s,w:a,h:r,radius:n}=e;t.arc(i+n.topLeft,s+n.topLeft,n.topLeft,1.5*th,th,!0),t.lineTo(i,s+r-n.bottomLeft),t.arc(i+n.bottomLeft,s+r-n.bottomLeft,n.bottomLeft,th,tg,!0),t.lineTo(i+a-n.bottomRight,s+r),t.arc(i+a-n.bottomRight,s+r-n.bottomRight,n.bottomRight,tg,0,!0),t.lineTo(i+a,s+n.topRight),t.arc(i+a-n.topRight,s+n.topRight,n.topRight,0,-tg,!0),t.lineTo(i+n.topLeft,s)}let eu=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ef=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,eg=t=>+t||0;function ep(t,e){let i={},s=W(e),a=s?Object.keys(e):e,r=W(t)?s?i=>U(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of a)i[t]=eg(r(t));return i}function em(t){return ep(t,{top:"y",right:"x",bottom:"y",left:"x"})}function eb(t){return ep(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ex(t){let e=em(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function e_(t,e){t=t||{},e=e||t7.font;let i=U(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=U(t.style,e.style);s&&!(""+s).match(ef)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let a={family:U(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(eu);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(U(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:U(t.weight,e.weight),string:""};return a.string=!a||V(a.size)||V(a.family)?null:(a.style?a.style+" ":"")+(a.weight?a.weight+" ":"")+a.size+"px "+a.family,a}function ey(t,e,i,s){let a,r,n,o=!0;for(a=0,r=t.length;a<r;++a)if(void 0!==(n=t[a])&&(void 0!==e&&"function"==typeof n&&(n=n(e),o=!1),void 0!==i&&B(n)&&(n=n[i%n.length],o=!1),void 0!==n))return s&&!o&&(s.cacheable=!1),n}function ev(t,e){return Object.assign(Object.create(t),e)}function eM(t,e=[""],i,s,a=()=>t[0]){let r=i||t;return void 0===s&&(s=eA("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:r,_fallback:s,_getTarget:a,override:i=>eM([i,...t],e,r,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>eC(i,s,()=>(function(t,e,i,s){let a;for(let r of e)if(void 0!==(a=eA(eP(r,t),i)))return eS(t,a)?eO(i,s,t,a):a})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eL(t).includes(e),ownKeys:t=>eL(t),set(t,e,i){let s=t._storage||(t._storage=a());return t[e]=s[e]=i,delete t._keys,!0}})}function ew(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:ek(t,s),setContext:e=>ew(t,e,i,s),override:a=>ew(t.override(a),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>eC(t,e,()=>(function(t,e,i){let{_proxy:s,_context:a,_subProxy:r,_descriptors:n}=t,o=s[e];return to(o)&&n.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:a,_context:r,_subProxy:n,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(r,n||s);return o.delete(t),eS(t,l)&&(l=eO(a._scopes,a,t,l)),l}(e,o,t,i)),B(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:a,_context:r,_subProxy:n,_descriptors:o}=i;if(void 0!==r.index&&s(t))return e[r.index%e.length];if(W(e[0])){let i=e,s=a._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=eO(s,a,t,l);e.push(ew(i,r,n&&n[t],o))}}return e}(e,o,t,n.isIndexable)),eS(e,o)&&(o=ew(o,a,r&&r[e],n)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function ek(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:a=e.allKeys}=t;return{allKeys:a,scriptable:i,indexable:s,isScriptable:to(i)?i:()=>i,isIndexable:to(s)?s:()=>s}}let eP=(t,e)=>t?t+tr(e):e,eS=(t,e)=>W(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eC(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let eD=(t,e)=>!0===t?e:"string"==typeof t?ta(e,t):void 0;function eO(t,e,i,s){var a;let r=e._rootScopes,n=(a=e._fallback,to(a)?a(i,s):a),o=[...t,...r],l=new Set;l.add(s);let h=eT(l,o,i,n||i,s);return null!==h&&(void 0===n||n===i||null!==(h=eT(l,o,n,h,s)))&&eM(Array.from(l),[""],r,n,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let a=s[e];return B(a)&&W(i)?i:a||{}})(e,i,s))}function eT(t,e,i,s,a){for(;i;)i=function(t,e,i,s,a){for(let n of e){let e=eD(i,n);if(e){var r;t.add(e);let n=(r=e._fallback,to(r)?r(i,a):r);if(void 0!==n&&n!==i&&n!==s)return n}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,a);return i}function eA(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function eL(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eE(t,e,i,s){let a,r,n,{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(a=0;a<s;++a)n=e[r=a+i],h[a]={r:o.parse(ta(n,l),r)};return h}let eR=Number.EPSILON||1e-14,eI=(t,e)=>e<t.length&&!t[e].skip&&t[e],eF=t=>"x"===t?"y":"x";function ez(t,e,i){return Math.max(Math.min(t,i),e)}function eN(){return"undefined"!=typeof window&&"undefined"!=typeof document}function ej(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eV(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let eB=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),eW=["top","right","bottom","left"];function eH(t,e,i){let s={};i=i?"-"+i:"";for(let a=0;a<4;a++){let r=eW[a];s[r]=parseFloat(t[e+"-"+r+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let e$=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function eU(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,a=eB(i),r="border-box"===a.boxSizing,n=eH(a,"padding"),o=eH(a,"border","width"),{x:l,y:h,box:d}=function(t,e){let i,s,a=t.touches,r=a&&a.length?a[0]:t,{offsetX:n,offsetY:o}=r,l=!1;if(e$(n,o,t.target))i=n,s=o;else{let t=e.getBoundingClientRect();i=r.clientX-t.left,s=r.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),c=n.left+(d&&o.left),u=n.top+(d&&o.top),{width:f,height:g}=e;return r&&(f-=n.width+o.width,g-=n.height+o.height),{x:Math.round((l-c)/f*i.width/s),y:Math.round((h-u)/g*i.height/s)}}let eY=t=>Math.round(10*t)/10;function eq(t,e,i){let s=e||1,a=Math.floor(t.height*s),r=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let n=t.canvas;return n.style&&(i||!n.style.height&&!n.style.width)&&(n.style.height=`${t.height}px`,n.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||n.height!==a||n.width!==r)&&(t.currentDevicePixelRatio=s,n.height=a,n.width=r,t.ctx.setTransform(s,0,0,s,0,0),!0)}let eX=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eN()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function eG(t,e){let i=eB(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function eK(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function eZ(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function eJ(t,e,i,s){let a={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},n=eK(t,a,i),o=eK(a,r,i),l=eK(r,e,i),h=eK(n,o,i),d=eK(o,l,i);return eK(h,d,i)}function eQ(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e0(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function e1(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e2(t){return"angle"===t?{between:tO,compare:tC,normalize:tD}:{between:tA,compare:(t,e)=>t-e,normalize:t=>t}}function e5({start:t,end:e,count:i,loop:s,style:a}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:a}}function e4(t,e,i){let s,a,r;if(!i)return[t];let{property:n,start:o,end:l}=i,h=e.length,{compare:d,between:c,normalize:u}=e2(n),{start:f,end:g,loop:p,style:m}=function(t,e,i){let s,{property:a,start:r,end:n}=i,{between:o,normalize:l}=e2(a),h=e.length,{start:d,end:c,loop:u}=t;if(u){for(d+=h,c+=h,s=0;s<h&&o(l(e[d%h][a]),r,n);++s)d--,c--;d%=h,c%=h}return c<d&&(c+=h),{start:d,end:c,loop:u,style:t.style}}(t,e,i),b=[],x=!1,_=null,y=()=>c(o,r,s)&&0!==d(o,r),v=()=>0===d(l,s)||c(l,r,s),M=()=>x||y(),w=()=>!x||v();for(let t=f,i=f;t<=g;++t)(a=e[t%h]).skip||(s=u(a[n]))!==r&&(x=c(s,o,l),null===_&&M()&&(_=0===d(s,o)?t:i),null!==_&&w()&&(b.push(e5({start:_,end:t,loop:p,count:h,style:m})),_=null),i=t,r=s);return null!==_&&b.push(e5({start:_,end:g,loop:p,count:h,style:m})),b}function e8(t,e){let i=[],s=t.segments;for(let a=0;a<s.length;a++){let r=e4(s[a],t.points,e);r.length&&i.push(...r)}return i}function e6(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let a=t._chart.getContext(),r=e3(t.options),{_datasetIndex:n,options:{spanGaps:o}}=t,l=i.length,h=[],d=r,c=e[0].start,u=c;function f(t,e,s,a){let r=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=r;for(;i[e%l].skip;)e+=r;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:a}),d=a,c=e%l)}}for(let t of e){let e,r=i[(c=o?c:t.start)%l];for(u=c+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return tG(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=e3(s.setContext(ev(a,{type:"segment",p0:r,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:n}))),d)&&f(c,u-1,t.loop,d),r=o,d=e}c<u-1&&f(c,u-1,t.loop,d)}return h}(t,e,i,s):e}function e3(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function e9(t,e,i){return t.options.clip?t[i]:e[i]}function e7(t,e){let i=e._clip;if(i.disabled)return!1;let s=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:e9(i,e,"left"),right:e9(i,e,"right"),top:e9(s,e,"top"),bottom:e9(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}class it{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let a=e.listeners[s],r=e.duration;a.forEach(s=>s({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(i-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=tN.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let a;if(!i.running||!i.items.length)return;let r=i.items,n=r.length-1,o=!1;for(;n>=0;--n)(a=r[n])._active?(a._total>i.duration&&(i.duration=a._total),a.tick(t),o=!0):(r[n]=r[r.length-1],r.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),r.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=r.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var ie=new it;let ii="transparent",is={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=tK(t||ii),a=s.valid&&tK(e||ii);return a&&a.valid?a.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class ia{constructor(t,e,i,s){let a=e[i];s=ey([t.to,s,a,t.from]);let r=ey([t.from,a,s]);this._active=!0,this._fn=t.fn||is[t.type||typeof r],this._easing=tX[t.easing]||tX.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],a=i-this._start,r=this._duration-a;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=a,this._loop=!!t.loop,this._to=ey([t.to,e,s,t.from]),this._from=ey([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e,i=t-this._start,s=this._duration,a=this._prop,r=this._from,n=this._loop,o=this._to;if(this._active=r!==o&&(n||i<s),!this._active){this._target[a]=o,this._notify(!0);return}if(i<0){this._target[a]=r;return}e=i/s%2,e=n&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[a]=this._fn(r,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class ir{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!W(t))return;let e=Object.keys(t7.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let a=t[s];if(!W(a))return;let r={};for(let t of e)r[t]=a[t];(B(a.properties)&&a.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,r)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let a=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let a=t[s[e]];a&&a.active()&&i.push(a.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),a}_createAnimations(t,e){let i,s=this._properties,a=[],r=t.$animations||(t.$animations={}),n=Object.keys(e),o=Date.now();for(i=n.length-1;i>=0;--i){let l=n[i];if("$"===l.charAt(0))continue;if("options"===l){a.push(...this._animateOptions(t,e));continue}let h=e[l],d=r[l],c=s.get(l);if(d)if(c&&d.active()){d.update(c,h,o);continue}else d.cancel();if(!c||!c.duration){t[l]=h;continue}r[l]=d=new ia(c,t,l,h),a.push(d)}return a}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);let i=this._createAnimations(t,e);if(i.length)return ie.add(this._chart,i),!0}}function io(t,e){let i=t&&t.options||{},s=i.reverse,a=void 0===i.min?e:0,r=void 0===i.max?e:0;return{start:s?r:a,end:s?a:r}}function il(t,e){let i,s,a=[],r=t._getSortedDatasetMetas(e);for(i=0,s=r.length;i<s;++i)a.push(r[i].index);return a}function ih(t,e,i,s={}){let a,r,n,o,l=t.keys,h="single"===s.mode;if(null===e)return;let d=!1;for(a=0,r=l.length;a<r;++a){if((n=+l[a])===i){if(d=!0,s.all)continue;break}H(o=t.values[n])&&(h||0===e||tx(e)===tx(o))&&(e+=o)}return d||s.all?e:0}function id(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function ic(t,e,i,s){for(let a of e.getMatchingVisibleMetas(s).reverse()){let e=t[a.index];if(i&&e>0||!i&&e<0)return a.index}return null}function iu(t,e){let i,{chart:s,_cachedMeta:a}=t,r=s._stacks||(s._stacks={}),{iScale:n,vScale:o,index:l}=a,h=n.axis,d=o.axis,c=`${n.id}.${o.id}.${a.stack||a.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:n,[d]:u}=s;(i=(s._stacks||(s._stacks={}))[d]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(r,c,n))[l]=u,i._top=ic(i,o,!0,a.type),i._bottom=ic(i,o,!1,a.type),(i._visualValues||(i._visualValues={}))[l]=u}}function ig(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function ip(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let a of e=e||t._parsed){let t=a._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let im=t=>"reset"===t||"none"===t,ib=(t,e)=>e?t:Object.assign({},t),ix=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:il(i,!0),values:null};class i_{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=id(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&ip(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,a=e.xAxisID=U(i.xAxisID,ig(t,"x")),r=e.yAxisID=U(i.yAxisID,ig(t,"y")),n=e.rAxisID=U(i.rAxisID,ig(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,a,r,n),h=e.vAxisID=s(o,r,a,n);e.xScale=this.getScaleForId(a),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(n),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&tF(this._data,this),t._stacked&&ip(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(W(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,a,{iScale:r,vScale:n}=e,o="x"===r.axis?"x":"y",l="x"===n.axis?"x":"y",h=Object.keys(t),d=Array(h.length);for(i=0,s=h.length;i<s;++i)a=h[i],d[i]={[o]:a,[l]:t[a]};return d}(e,t)}else if(i!==e){if(i){tF(i,this);let t=this._cachedMeta;ip(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs)return t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tI.forEach(e=>{let i="_onData"+tr(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let a=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),a}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let a=e._stacked;e._stacked=id(e.vScale,e),e.stack!==i.stack&&(s=!0,ip(e),e.stack=i.stack),this._resyncElements(t),(s||a!==e._stacked)&&(iu(this,e._parsed),e._stacked=id(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,a,{_cachedMeta:r,_data:n}=this,{iScale:o,_stacked:l}=r,h=o.axis,d=0===t&&e===n.length||r._sorted,c=t>0&&r._parsed[t-1];if(!1===this._parsing)r._parsed=n,r._sorted=!0,a=n;else{a=B(n[t])?this.parseArrayData(r,n,t,e):W(n[t])?this.parseObjectData(r,n,t,e):this.parsePrimitiveData(r,n,t,e);let o=()=>null===s[h]||c&&s[h]<c[h];for(i=0;i<e;++i)r._parsed[i+t]=s=a[i],d&&(o()&&(d=!1),c=s);r._sorted=d}l&&iu(this,a)}parsePrimitiveData(t,e,i,s){let a,r,{iScale:n,vScale:o}=t,l=n.axis,h=o.axis,d=n.getLabels(),c=n===o,u=Array(s);for(a=0;a<s;++a)r=a+i,u[a]={[l]:c||n.parse(d[r],r),[h]:o.parse(e[r],r)};return u}parseArrayData(t,e,i,s){let a,r,n,{xScale:o,yScale:l}=t,h=Array(s);for(a=0;a<s;++a)n=e[r=a+i],h[a]={x:o.parse(n[0],r),y:l.parse(n[1],r)};return h}parseObjectData(t,e,i,s){let a,r,n,{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:d="y"}=this._parsing,c=Array(s);for(a=0;a<s;++a)n=e[r=a+i],c[a]={x:o.parse(ta(n,h),r),y:l.parse(ta(n,d),r)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,a=this._cachedMeta,r=e[t.axis];return ih({keys:il(s,!0),values:e._stacks[t.axis]._visualValues},r,a.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let a=i[e.axis],r=null===a?NaN:a,n=s&&i._stacks[e.axis];s&&n&&(s.values=n,r=ih(s,a,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){let i,s,a=this._cachedMeta,r=a._parsed,n=a._sorted&&t===a.iScale,o=r.length,l=this._getOtherScale(t),h=ix(e,a,this.chart),d={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:u}=function(t){let{min:e,max:i,minDefined:s,maxDefined:a}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}(l);function f(){let e=(s=r[i])[l.axis];return!H(s[t.axis])||c>e||u<e}for(i=0;i<o&&(f()||(this.updateRangeFromParsed(d,t,s,h),!n));++i);if(n){for(i=o-1;i>=0;--i)if(!f()){this.updateRangeFromParsed(d,t,s,h);break}}return d}getAllParsedValues(t){let e,i,s,a=this._cachedMeta._parsed,r=[];for(e=0,i=a.length;e<i;++e)H(s=a[e][t.axis])&&r.push(s);return r}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:s?""+s.getLabelForValue(a[s.axis]):""}}_update(t){var e;let i,s,a,r,n=this._cachedMeta;this.update(t||"default"),W(e=U(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=io(t,i),a=io(e,i);return{top:a.end,right:s.end,bottom:a.start,left:s.start}}(n.xScale,n.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,a=e.bottom,r=e.left):i=s=a=r=e,n._clip={top:i,right:s,bottom:a,left:r,disabled:!1===e}}update(t){}draw(){let t,e=this._ctx,i=this.chart,s=this._cachedMeta,a=s.data||[],r=i.chartArea,n=[],o=this._drawStart||0,l=this._drawCount||a.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,r,o,l),t=o;t<o+l;++t){let i=a[t];i.hidden||(i.active&&h?n.push(i):i.draw(e,r))}for(t=0;t<n.length;++t)n[t].draw(e,r)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s,a;let r,n=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(r=e.$context||(s=this.getContext(),e.$context=ev(s,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),r.raw=n.data[t],r.index=r.dataIndex=t}else(r=this.$context||(this.$context=ev(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:a=this.index,index:a,mode:"default",type:"dataset"}))).dataset=n,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,a=this._cachedDataOpts,r=t+"-"+e,n=a[r],o=this.enableOptionSharing&&tn(i);if(n)return ib(n,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),d=s?[`${t}Hover`,"hover",t,""]:[t,""],c=l.getOptionScopes(this.getDataset(),h),u=Object.keys(t7.elements[t]),f=l.resolveNamedOptions(c,u,()=>this.getContext(i,s,e),d);return f.$shared&&(f.$shared=o,a[r]=Object.freeze(ib(f,o))),f}_resolveAnimations(t,e,i){let s,a=this.chart,r=this._cachedDataOpts,n=`animation-${e}`,o=r[n];if(o)return o;if(!1!==a.options.animation){let a=this.chart.config,r=a.datasetAnimationScopeKeys(this._type,e),n=a.getOptionScopes(this.getDataset(),r);s=a.createResolver(n,this.getContext(t,i,e))}let l=new ir(a,s&&s.animations);return s&&s._cacheable&&(r[n]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||im(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,a=this.getSharedOptions(i),r=this.includeOptions(e,a)||a!==s;return this.updateSharedOptions(a,e,i),{sharedOptions:a,includeOptions:r}}updateElement(t,e,i,s){im(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!im(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let a=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(a)||a})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,a=e.length,r=Math.min(a,s);r&&this.parse(0,r),a>s?this._insertElements(s,a-s,t):a<s&&this._removeElements(a,s-a)}_insertElements(t,e,i=!0){let s,a=this._cachedMeta,r=a.data,n=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=n;s--)t[s]=t[s-e]};for(o(r),s=t;s<n;++s)r[s]=new this.dataElementType;this._parsing&&o(a._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&ip(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function iy(t,e,i,s){return B(t)?!function(t,e,i,s){let a=i.parse(t[0],s),r=i.parse(t[1],s),n=Math.min(a,r),o=Math.max(a,r),l=n,h=o;Math.abs(n)>Math.abs(o)&&(l=o,h=n),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:a,end:r,min:n,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function iv(t,e,i,s){let a,r,n,o,l=t.iScale,h=t.vScale,d=l.getLabels(),c=l===h,u=[];for(a=i,r=i+s;a<r;++a)o=e[a],(n={})[l.axis]=c||l.parse(d[a],a),u.push(iy(o,n,h,a));return u}function iM(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function iw(t,e,i,s){var a,r,n;return t=s?ik((a=t,r=e,n=i,t=a===r?n:a===n?r:a),i,e):ik(t,e,i)}function ik(t,e,i){return"start"===t?e:"end"===t?i:t}class iP extends i_{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return iv(t,e,i,s)}parseArrayData(t,e,i,s){return iv(t,e,i,s)}parseObjectData(t,e,i,s){let a,r,n,o,{iScale:l,vScale:h}=t,{xAxisKey:d="x",yAxisKey:c="y"}=this._parsing,u="x"===l.axis?d:c,f="x"===h.axis?d:c,g=[];for(a=i,r=i+s;a<r;++a)o=e[a],(n={})[l.axis]=l.parse(ta(o,u),a),g.push(iy(ta(o,f),n,h,a));return g}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),a=s._custom,r=iM(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let a="reset"===s,{index:r,_cachedMeta:{vScale:n}}=this,o=n.getBasePixel(),l=n.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:c}=this._getSharedOptions(e,s);for(let u=e;u<e+i;u++){let e=this.getParsed(u),i=a||V(e[n.axis])?{base:o,head:o}:this._calculateBarValuePixels(u),f=this._calculateBarIndexPixels(u,h),g=(e._stacks||{})[n.axis],p={horizontal:l,base:i.base,enableBorderRadius:!g||iM(e._custom)||r===g._top||r===g._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};c&&(p.options=d||this.resolveDataElementOptions(u,t[u].active?"active":s));let m=p.options||t[u].options;!function(t,e,i,s){let a,r,n,o,l,h=e.borderSkipped,d={};if(!h){t.borderSkipped=d;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:c,end:u,reverse:f,top:g,bottom:p}=(t.horizontal?(a=t.base>t.x,r="left",n="right"):(a=t.base<t.y,r="bottom",n="top"),a?(o="end",l="start"):(o="start",l="end"),{start:r,end:n,reverse:a,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=g:(i._bottom||0)===s?h=p:(d[iw(p,c,u,f)]=!0,h=g)),d[iw(h,c,u,f)]=!0,t.borderSkipped=d}(p,m,g,r),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?.33*(1===i):e}(p,m,h.ratio),this.updateElement(t[u],u,p,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),a=i.options.stacked,r=[],n=this._cachedMeta.controller.getParsed(e),o=n&&n[i.axis],l=t=>{let e=t._parsed.find(t=>t[i.axis]===o),s=e&&e[t.vScale.axis];if(V(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&l(i))&&((!1===a||-1===r.indexOf(i.stack)||void 0===a&&void 0===i.stack)&&r.push(i.stack),i.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),a=void 0!==e?s.indexOf(e):-1;return -1===a?s.length-1:a}_getRuler(){let t,e,i=this.options,s=this._cachedMeta,a=s.iScale,r=[];for(t=0,e=s.data.length;t<e;++t)r.push(a.getPixelForValue(this.getParsed(t)[a.axis],t));let n=i.barThickness;return{min:n||function(t){let e,i,s,a,r=t.iScale,n=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,a=i.length;e<a;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=tz(s.sort((t,e)=>t-e))}return t._cache.$bar}(r,t.type),o=r._length,l=()=>{32767!==s&&-32768!==s&&(tn(a)&&(o=Math.min(o,Math.abs(s-a)||o)),a=s)};for(e=0,i=n.length;e<i;++e)s=r.getPixelForValue(n[e]),l();for(e=0,a=void 0,i=r.ticks.length;e<i;++e)s=r.getPixelForTick(e),l();return o}(s),pixels:r,start:a._startPixel,end:a._endPixel,stackCount:this._getStackCount(),scale:a,grouped:i.grouped,ratio:n?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i,{_cachedMeta:{vScale:s,_stacked:a,index:r},options:{base:n,minBarLength:o}}=this,l=n||0,h=this.getParsed(t),d=h._custom,c=iM(d),u=h[s.axis],f=0,g=a?this.applyStack(s,h,a):u;g!==u&&(f=g-u,g=u),c&&(u=d.barStart,g=d.barEnd-d.barStart,0!==u&&tx(u)!==tx(d.barEnd)&&(f=0),f+=u);let p=V(n)||c?f:n,m=s.getPixelForValue(p);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(f+g):m)-m)<o){var b;i=(0!==(b=i)?tx(b):(s.isHorizontal()?1:-1)*(s.min>=l?1:-1))*o,u===l&&(m-=i/2);let t=s.getPixelForDecimal(0),n=s.getPixelForDecimal(1),d=Math.min(t,n);e=(m=Math.max(Math.min(m,Math.max(t,n)),d))+i,a&&!c&&(h._stacks[s.axis]._visualValues[r]=s.getValueForPixel(e)-s.getValueForPixel(m))}if(m===s.getPixelForValue(l)){let t=tx(i)*s.getLineWidthForValue(l)/2;m+=t,i-=t}return{size:i,base:m,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s,a=e.scale,r=this.options,n=r.skipNull,o=U(r.maxBarThickness,1/0);if(e.grouped){let a=n?this._getStackCount(t):e.stackCount,l="flex"===r.barThickness?function(t,e,i,s){let a=e.pixels,r=a[t],n=t>0?a[t-1]:null,o=t<a.length-1?a[t+1]:null,l=i.categoryPercentage;null===n&&(n=r-(null===o?e.end-e.start:o-r)),null===o&&(o=r+r-n);let h=r-(r-Math.min(n,o))/2*l;return{chunk:Math.abs(o-n)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,r,a):function(t,e,i,s){let a,r,n=i.barThickness;return V(n)?(a=e.min*i.categoryPercentage,r=i.barPercentage):(a=n*s,r=1),{chunk:a/s,ratio:r,start:e.pixels[t]-a/2}}(t,e,r,a),h=this._getStackIndex(this.index,this._cachedMeta.stack,n?t:void 0);i=l.start+l.chunk*h+l.chunk/2,s=Math.min(o,l.chunk*l.ratio)}else i=a.getPixelForValue(this.getParsed(t)[a.axis],t),s=Math.min(o,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,a=0;for(;a<s;++a)null===this.getParsed(a)[e.axis]||i[a].hidden||i[a].draw(this._ctx)}}class iS extends i_{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let a=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<a.length;t++)a[t]._custom=this.resolveDataElementOptions(t+i).radius;return a}parseArrayData(t,e,i,s){let a=super.parseArrayData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=U(s[2],this.resolveDataElementOptions(t+i).radius)}return a}parseObjectData(t,e,i,s){let a=super.parseObjectData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=U(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return a}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,r=this.getParsed(t),n=s.getLabelForValue(r.x),o=a.getLabelForValue(r.y),l=r._custom;return{label:i[t]||"",value:"("+n+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:r,vScale:n}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=r.axis,d=n.axis;for(let c=e;c<e+i;c++){let e=t[c],i=!a&&this.getParsed(c),u={},f=u[h]=a?r.getPixelForDecimal(.5):r.getPixelForValue(i[h]),g=u[d]=a?n.getBasePixel():n.getPixelForValue(i[d]);u.skip=isNaN(f)||isNaN(g),l&&(u.options=o||this.resolveDataElementOptions(c,e.active?"active":s),a&&(u.options.radius=0)),this.updateElement(e,c,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let a=s.radius;return"active"!==e&&(s.radius=0),s.radius+=U(i&&i._custom,a),s}}class iC extends i_{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let r=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:r.backgroundColor,strokeStyle:r.borderColor,fontColor:s,lineWidth:r.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let a,r,n=t=>+i[t];if(W(i[t])){let{key:t="value"}=this._parsing;n=e=>+ta(i[e],t)}for(a=t,r=t+e;a<r;++a)s._parsed[a]=n(a)}}_getRotation(){return tw(this.options.rotation-90)}_getCircumference(){return tw(this.options.circumference)}_getRotationExtents(){let t=td,e=-td;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,a=s._getRotation(),r=s._getCircumference();t=Math.min(t,a),e=Math.max(e,a+r)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,r=Math.max((Math.min(e.width,e.height)-a)/2,0),n=Math.min(Y(this.options.cutout,r),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:d,ratioY:c,offsetX:u,offsetY:f}=function(t,e,i){let s=1,a=1,r=0,n=0;if(e<td){let o=t+e,l=Math.cos(t),h=Math.sin(t),d=Math.cos(o),c=Math.sin(o),u=(e,s,a)=>tO(e,t,o,!0)?1:Math.max(s,s*i,a,a*i),f=(e,s,a)=>tO(e,t,o,!0)?-1:Math.min(s,s*i,a,a*i),g=u(0,l,d),p=u(tg,h,c),m=f(th,l,d),b=f(th+tg,h,c);s=(g-m)/2,a=(p-b)/2,r=-(g+m)/2,n=-(p+b)/2}return{ratioX:s,ratioY:a,offsetX:r,offsetY:n}}(h,l,n),g=Math.max(Math.min((e.width-a)/d,(e.height-a)/c)/2,0),p=q(this.options.radius,g),m=Math.max(p*n,0),b=(p-m)/this._getVisibleDatasetWeightTotal();this.offsetX=u*p,this.offsetY=f*p,i.total=this.calculateTotal(),this.outerRadius=p-b*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-b*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,a=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*a/td)}updateElements(t,e,i,s){let a,r="reset"===s,n=this.chart,o=n.chartArea,l=n.options.animation,h=(o.left+o.right)/2,d=(o.top+o.bottom)/2,c=r&&l.animateScale,u=c?0:this.innerRadius,f=c?0:this.outerRadius,{sharedOptions:g,includeOptions:p}=this._getSharedOptions(e,s),m=this._getRotation();for(a=0;a<e;++a)m+=this._circumference(a,r);for(a=e;a<e+i;++a){let e=this._circumference(a,r),i=t[a],n={x:h+this.offsetX,y:d+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:f,innerRadius:u};p&&(n.options=g||this.resolveDataElementOptions(a,i.active?"active":s)),m+=e,this.updateElement(i,a,n,s)}}calculateTotal(){let t,e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let a=e._parsed[t];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(a))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*td:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=t1(e._parsed[t],i.options.locale);return{label:s[t]||"",value:a}}getMaxBorderWidth(t){let e,i,s,a,r,n=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,a=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(r=a.resolveDataElementOptions(e)).borderAlign&&(n=Math.max(n,r.borderWidth||0,r.hoverBorderWidth||0));return n}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(U(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class iD extends i_{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:a}=e,r=this.chart._animationsDisabled,{start:n,count:o}=tH(e,s,r);this._drawStart=n,this._drawCount=o,t$(e)&&(n=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!a._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:l},t),this.updateElements(s,n,o,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:r,vScale:n,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,s),c=r.axis,u=n.axis,{spanGaps:f,segment:g}=this.options,p=tv(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||a||"none"===s,b=e+i,x=t.length,_=e>0&&this.getParsed(e-1);for(let i=0;i<x;++i){let f=t[i],x=m?f:{};if(i<e||i>=b){x.skip=!0;continue}let y=this.getParsed(i),v=V(y[u]),M=x[c]=r.getPixelForValue(y[c],i),w=x[u]=a||v?n.getBasePixel():n.getPixelForValue(o?this.applyStack(n,y,o):y[u],i);x.skip=isNaN(M)||isNaN(w)||v,x.stop=i>0&&Math.abs(y[c]-_[c])>p,g&&(x.parsed=y,x.raw=l.data[i]),d&&(x.options=h||this.resolveDataElementOptions(i,f.active?"active":s)),m||this.updateElement(f,i,x,s),_=y}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class iO extends i_{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let r=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:r.backgroundColor,strokeStyle:r.borderColor,fontColor:s,lineWidth:r.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=t1(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:a}}parseObjectData(t,e,i,s){return eE.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),a=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),r=(s-a)/t.getVisibleDatasetCount();this.outerRadius=s-r*this.index,this.innerRadius=this.outerRadius-r}updateElements(t,e,i,s){let a,r="reset"===s,n=this.chart,o=n.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,d=l.yCenter,c=l.getIndexAngle(0)-.5*th,u=c,f=360/this.countVisibleElements();for(a=0;a<e;++a)u+=this._computeAngle(a,s,f);for(a=e;a<e+i;a++){let e=t[a],i=u,g=u+this._computeAngle(a,s,f),p=n.getDataVisibility(a)?l.getDistanceFromCenterForValue(this.getParsed(a).r):0;u=g,r&&(o.animateScale&&(p=0),o.animateRotate&&(i=g=c));let m={x:h,y:d,innerRadius:0,outerRadius:p,startAngle:i,endAngle:g,options:this.resolveDataElementOptions(a,e.active?"active":s)};this.updateElement(e,a,m,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?tw(this.resolveDataElementOptions(t,e).angle||i):0}}class iT extends iC{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class iA extends i_{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return eE.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],a=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let r={_loop:!0,_fullLoop:a.length===s.length,options:e};this.updateElement(i,void 0,r,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let a=this._cachedMeta.rScale,r="reset"===s;for(let n=e;n<e+i;n++){let e=t[n],i=this.resolveDataElementOptions(n,e.active?"active":s),o=a.getPointPositionForValue(n,this.getParsed(n).r),l=r?a.xCenter:o.x,h=r?a.yCenter:o.y,d={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,n,d,s)}}}class iL extends i_{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,r=this.getParsed(t),n=s.getLabelForValue(r.x),o=a.getLabelForValue(r.y);return{label:i[t]||"",value:"("+n+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:a,count:r}=tH(e,i,s);if(this._drawStart=a,this._drawCount=r,t$(e)&&(a=0,r=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:a,_dataset:r}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!r._decimated,a.points=i;let n=this.resolveDatasetElementOptions(t);n.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:n},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,a,r,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let a="reset"===s,{iScale:r,vScale:n,_stacked:o,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),d=this.getSharedOptions(h),c=this.includeOptions(s,d),u=r.axis,f=n.axis,{spanGaps:g,segment:p}=this.options,m=tv(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||a||"none"===s,x=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){let e=t[h],i=this.getParsed(h),g=b?e:{},_=V(i[f]),y=g[u]=r.getPixelForValue(i[u],h),v=g[f]=a||_?n.getBasePixel():n.getPixelForValue(o?this.applyStack(n,i,o):i[f],h);g.skip=isNaN(y)||isNaN(v)||_,g.stop=h>0&&Math.abs(i[u]-x[u])>m,p&&(g.parsed=i,g.raw=l.data[h]),c&&(g.options=d||this.resolveDataElementOptions(h,e.active?"active":s)),b||this.updateElement(e,h,g,s),x=i}this.updateSharedOptions(d,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function iE(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class iR{static override(t){Object.assign(iR.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return iE()}parse(){return iE()}format(){return iE()}add(){return iE()}diff(){return iE()}startOf(){return iE()}endOf(){return iE()}}var iI={_date:iR};function iF(t,e,i,s,a){let r=t.getSortedVisibleDatasetMetas(),n=i[e];for(let t=0,i=r.length;t<i;++t){let{index:i,data:o}=r[t],{lo:l,hi:h}=function(t,e,i,s){let{controller:a,data:r,_sorted:n}=t,o=a._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&"r"!==e&&n&&r.length){let n=o._reversePixels?tR:tE;if(s){if(a._sharedOptions){let t=r[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=n(r,e,i-s),a=n(r,e,i+s);return{lo:t.lo,hi:a.hi}}}}else{let s=n(r,e,i);if(l){let{vScale:e}=a._cachedMeta,{_parsed:i}=t,r=i.slice(0,s.lo+1).reverse().findIndex(t=>!V(t[e.axis]));s.lo-=Math.max(0,r);let n=i.slice(s.hi).findIndex(t=>!V(t[e.axis]));s.hi+=Math.max(0,n)}return s}}return{lo:0,hi:r.length-1}}(r[t],e,n,a);for(let t=l;t<=h;++t){let e=o[t];e.skip||s(e,i,t)}}}function iz(t,e,i,s,a){let r=[];return(a||t.isPointInArea(e))&&iF(t,i,e,function(i,n,o){(a||er(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&r.push({element:i,datasetIndex:n,index:o})},!0),r}function iN(t,e,i,s,a,r){let n;return r||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,a,r){let n=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return iF(t,i,e,function(i,h,d){let c=i.inRange(e.x,e.y,a);if(s&&!c)return;let u=i.getCenterPoint(a);if(!(r||t.isPointInArea(u))&&!c)return;let f=o(e,u);f<l?(n=[{element:i,datasetIndex:h,index:d}],l=f):f===l&&n.push({element:i,datasetIndex:h,index:d})}),n}(t,e,i,s,a,r):(n=[],iF(t,i,e,function(t,i,s){let{startAngle:r,endAngle:o}=t.getProps(["startAngle","endAngle"],a),{angle:l}=tP(t,{x:e.x,y:e.y});tO(l,r,o)&&n.push({element:t,datasetIndex:i,index:s})}),n):[]}function ij(t,e,i,s,a){let r=[],n="x"===i?"inXRange":"inYRange",o=!1;return(iF(t,i,e,(t,s,l)=>{t[n]&&t[n](e[i],a)&&(r.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,a))}),s&&!o)?[]:r}var iV={modes:{index(t,e,i,s){let a=eU(e,t),r=i.axis||"x",n=i.includeInvisible||!1,o=i.intersect?iz(t,a,r,s,n):iN(t,a,r,!1,s,n),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let a=eU(e,t),r=i.axis||"xy",n=i.includeInvisible||!1,o=i.intersect?iz(t,a,r,s,n):iN(t,a,r,!1,s,n);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let a=eU(e,t);return iz(t,a,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let a=eU(e,t),r=i.axis||"xy",n=i.includeInvisible||!1;return iN(t,a,r,i.intersect,s,n)},x(t,e,i,s){let a=eU(e,t);return ij(t,a,"x",i.intersect,s)},y(t,e,i,s){let a=eU(e,t);return ij(t,a,"y",i.intersect,s)}}};let iB=["left","top","right","bottom"];function iW(t,e){return t.filter(t=>t.pos===e)}function iH(t,e){return t.filter(t=>-1===iB.indexOf(t.pos)&&t.box.axis===e)}function i$(t,e){return t.sort((t,i)=>{let s=e?i:t,a=e?t:i;return s.weight===a.weight?s.index-a.index:s.weight-a.weight})}function iU(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function iY(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function iq(t,e,i,s){let a,r,n,o,l,h,d=[];for(a=0,r=t.length,l=0;a<r;++a){(o=(n=t[a]).box).update(n.width||e.w,n.height||e.h,function(t,e){let i=e.maxPadding;var s=t?["left","right"]:["top","bottom"];let a={left:0,top:0,right:0,bottom:0};return s.forEach(t=>{a[t]=Math.max(e[t],i[t])}),a}(n.horizontal,e));let{same:r,other:c}=function(t,e,i,s){let{pos:a,box:r}=i,n=t.maxPadding;if(!W(a)){i.size&&(t[a]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?r.height:r.width),i.size=e.size/e.count,t[a]+=i.size}r.getPadding&&iY(n,r.getPadding());let o=Math.max(0,e.outerWidth-iU(n,t,"left","right")),l=Math.max(0,e.outerHeight-iU(n,t,"top","bottom")),h=o!==t.w,d=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:h,other:d}:{same:d,other:h}}(e,i,n,s);l|=r&&d.length,h=h||c,o.fullSize||d.push(n)}return l&&iq(d,e,i,s)||h}function iX(t,e,i,s,a){t.top=i,t.left=e,t.right=e+s,t.bottom=i+a,t.width=s,t.height=a}function iG(t,e,i,s){let a=i.padding,{x:r,y:n}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,r=l.size||t.height;tn(l.start)&&(n=l.start),t.fullSize?iX(t,a.left,n,i.outerWidth-a.right-a.left,r):iX(t,e.left+l.placed,n,s,r),l.start=n,l.placed+=s,n=t.bottom}else{let s=e.h*h,n=l.size||t.width;tn(l.start)&&(r=l.start),t.fullSize?iX(t,r,a.top,n,i.outerHeight-a.bottom-a.top):iX(t,r,e.top+l.placed,n,s),l.start=r,l.placed+=s,r=t.right}}e.x=r,e.y=n}var iK={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let a=ex(t.options.layout.padding),r=Math.max(e-a.width,0),n=Math.max(i-a.height,0),o=function(t){let e=function(t){let e,i,s,a,r,n,o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:a,options:{stack:r,stackWeight:n=1}}=s),o.push({index:e,box:s,pos:a,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&a+r,stackWeight:n});return o}(t),i=i$(e.filter(t=>t.box.fullSize),!0),s=i$(iW(e,"left"),!0),a=i$(iW(e,"right")),r=i$(iW(e,"top"),!0),n=i$(iW(e,"bottom")),o=iH(e,"x"),l=iH(e,"y");return{fullSize:i,leftAndTop:s.concat(r),rightAndBottom:a.concat(l).concat(n).concat(o),chartArea:iW(e,"chartArea"),vertical:s.concat(a).concat(l),horizontal:r.concat(n).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;G(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let d=Object.freeze({outerWidth:e,outerHeight:i,padding:a,availableWidth:r,availableHeight:n,vBoxMaxWidth:r/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:n/2}),c=Object.assign({},a);iY(c,ex(s));let u=Object.assign({maxPadding:c,w:r,h:n,x:a.left,y:a.top},a),f=function(t,e){let i,s,a,r=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:a}=i;if(!t||!iB.includes(s))continue;let r=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=a}return e}(t),{vBoxMaxWidth:n,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(a=t[i]).box,l=r[a.stack],h=l&&a.stackWeight/l.weight;a.horizontal?(a.width=h?h*n:s&&e.availableWidth,a.height=o):(a.width=n,a.height=h?h*o:s&&e.availableHeight)}return r}(l.concat(h),d);iq(o.fullSize,u,d,f),iq(l,u,d,f),iq(h,u,d,f)&&iq(l,u,d,f);let g=u.maxPadding;function p(t){let e=Math.max(g[t]-u[t],0);return u[t]+=e,e}u.y+=p("top"),u.x+=p("left"),p("right"),p("bottom"),iG(o.leftAndTop,u,d,f),u.x+=u.w,u.y+=u.h,iG(o.rightAndBottom,u,d,f),t.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},G(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class iZ{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class iJ extends iZ{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let iQ="$chartjs",i0={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},i1=t=>null===t||""===t,i2=!!eX&&{passive:!0};function i5(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function i4(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||i5(i.addedNodes,s))&&!i5(i.removedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}function i8(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||i5(i.removedNodes,s))&&!i5(i.addedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}let i6=new Map,i3=0;function i9(){let t=window.devicePixelRatio;t!==i3&&(i3=t,i6.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function i7(t,e,i){let s=t.canvas,a=s&&ej(s);if(!a)return;let r=tj((t,e)=>{let s=a.clientWidth;i(t,e),s<a.clientWidth&&i()},window),n=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&r(i,s)});return n.observe(a),i6.size||window.addEventListener("resize",i9),i6.set(t,r),n}function st(t,e,i){i&&i.disconnect(),"resize"===e&&(i6.delete(t),i6.size||window.removeEventListener("resize",i9))}function se(t,e,i){let s=t.canvas,a=tj(e=>{null!==t.ctx&&i(function(t,e){let i=i0[t.type]||t.type,{x:s,y:a}=eU(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==a?a:null}}(e,t))},t);return s&&s.addEventListener(e,a,i2),a}class si extends iZ{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(!function(t,e){let i=t.style,s=t.getAttribute("height"),a=t.getAttribute("width");if(t[iQ]={initial:{height:s,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",i1(a)){let e=eG(t,"width");void 0!==e&&(t.width=e)}if(i1(s))if(""===t.style.height)t.height=t.width/(e||2);else{let e=eG(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[iQ])return!1;let i=e[iQ].initial;["height","width"].forEach(t=>{let s=i[t];V(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[iQ],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),a={attach:i4,detach:i8,resize:i7}[e]||se;s[e]=a(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:st,detach:st,resize:st})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,i2)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let a=eB(t),r=eH(a,"margin"),n=eV(a.maxWidth,t,"clientWidth")||tu,o=eV(a.maxHeight,t,"clientHeight")||tu,l=function(t,e,i){let s,a;if(void 0===e||void 0===i){let r=t&&ej(t);if(r){let t=r.getBoundingClientRect(),n=eB(r),o=eH(n,"border","width"),l=eH(n,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=eV(n.maxWidth,r,"clientWidth"),a=eV(n.maxHeight,r,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||tu,maxHeight:a||tu}}(t,e,i),{width:h,height:d}=l;if("content-box"===a.boxSizing){let t=eH(a,"border","width"),e=eH(a,"padding");h-=e.width+t.width,d-=e.height+t.height}return h=Math.max(0,h-r.width),d=Math.max(0,s?h/s:d-r.height),h=eY(Math.min(h,n,l.maxWidth)),d=eY(Math.min(d,o,l.maxHeight)),h&&!d&&(d=eY(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&d>l.height&&(h=eY(Math.floor((d=l.height)*s))),{width:h,height:d}}(t,e,i,s)}isAttached(t){let e=t&&ej(t);return!!(e&&e.isConnected)}}class ss{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return tv(this.x)&&tv(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function sa(t,e,i,s,a){let r,n,o,l=U(s,0),h=Math.min(U(a,t.length),t.length),d=0;for(i=Math.ceil(i),a&&(i=(r=a-s)/Math.floor(r/i)),o=l;o<0;)o=Math.round(l+ ++d*i);for(n=Math.max(l,0);n<h;n++)n===o&&(e.push(t[n]),o=Math.round(l+ ++d*i))}let sr=t=>"left"===t?"right":"right"===t?"left":t,sn=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,so=(t,e)=>Math.min(e||t,t);function sl(t,e){let i=[],s=t.length/e,a=t.length,r=0;for(;r<a;r+=s)i.push(t[Math.floor(r)]);return i}function sh(t){return t.drawTicks?t.tickLength:0}function sd(t,e){if(!t.display)return 0;let i=e_(t.font,e),s=ex(t.padding);return(B(t.text)?t.text.length:1)*i.lineHeight+s.height}class sc extends ss{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=$(t,Number.POSITIVE_INFINITY),e=$(e,Number.NEGATIVE_INFINITY),i=$(i,Number.POSITIVE_INFINITY),s=$(s,Number.NEGATIVE_INFINITY),{min:$(t,i),max:$(e,s),minDefined:H(t),maxDefined:H(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:a,maxDefined:r}=this.getUserBounds();if(a&&r)return{min:i,max:s};let n=this.getMatchingVisibleMetas();for(let o=0,l=n.length;o<l;++o)e=n[o].controller.getMinMax(this,t),a||(i=Math.min(i,e.min)),r||(s=Math.max(s,e.max));return i=r&&i>s?s:i,s=a&&i>s?i:s,{min:$(i,$(s,i)),max:$(s,$(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){X(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:a,ticks:r}=this.options,n=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:a}=t,r=q(e,(a-s)/2),n=(t,e)=>i&&0===t?0:t+e;return{min:n(s,-Math.abs(r)),max:n(a,r)}}(this,a,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let o=n<this.ticks.length;this._convertTicksToLabels(o?sl(this.ticks,n):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||"auto"===r.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+ +!e,t._maxLength/i))}(t),a=Math.min(i.maxTicksLimit||s,s),r=i.major.enabled?function(t){let e,i,s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],n=r.length,o=r[0],l=r[n-1],h=[];if(n>a)return function(t,e,i,s){let a,r=0,n=i[0];for(a=0,s=Math.ceil(s);a<t.length;a++)a===n&&(e.push(t[a]),n=i[++r*s])}(e,h,r,n/a),h;let d=function(t,e,i){let s=function(t){let e,i,s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),a=e.length/i;if(!s)return Math.max(a,1);let r=function(t){let e,i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=r.length-1;t<e;t++){let e=r[t];if(e>a)return e}return Math.max(a,1)}(r,e,a);if(n>0){let t,i,s=n>1?Math.round((l-o)/(n-1)):null;for(sa(e,h,d,V(s)?0:o-s,o),t=0,i=n-1;t<i;t++)sa(e,h,d,r[t],r[t+1]);return sa(e,h,d,l,V(s)?e.length:l+s),h}return sa(e,h,d),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){X(this.options.afterUpdate,[this])}beforeSetDimensions(){X(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){X(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),X(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){X(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s,a=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=X(a.callback,[s.value,e,t],this)}afterTickToLabelConversion(){X(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){X(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i,s=this.options,a=s.ticks,r=so(this.ticks.length,s.ticks.maxTicksLimit),n=a.minRotation||0,o=a.maxRotation,l=n;if(!this._isVisible()||!a.display||n>=o||r<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),d=h.widest.width,c=h.highest.height,u=tT(this.chart.width-d,0,this.maxWidth);d+6>(t=s.offset?this.maxWidth/r:u/(r-1))&&(t=u/(r-(s.offset?.5:1)),e=this.maxHeight-sh(s.grid)-a.padding-sd(s.title,this.chart.options.font),i=Math.sqrt(d*d+c*c),l=Math.max(n,Math.min(o,l=180/th*Math.min(Math.asin(tT((h.highest.height+6)/t,-1,1)),Math.asin(tT(e/i,-1,1))-Math.asin(tT(c/i,-1,1)))))),this.labelRotation=l}afterCalculateLabelRotation(){X(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){X(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:a}}=this,r=this._isVisible(),n=this.isHorizontal();if(r){let r=sd(s,e.options.font);if(n?(t.width=this.maxWidth,t.height=sh(a)+r):(t.height=this.maxHeight,t.width=sh(a)+r),i.display&&this.ticks.length){let{first:e,last:s,widest:a,highest:r}=this._getLabelSizes(),o=2*i.padding,l=tw(this.labelRotation),h=Math.cos(l),d=Math.sin(l);if(n){let e=i.mirror?0:d*a.width+h*r.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*a.width+d*r.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,d,h)}}this._handleMargins(),n?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:a,padding:r},position:n}=this.options,o=0!==this.labelRotation,l="top"!==n&&"x"===this.axis;if(this.isHorizontal()){let n=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),d=0,c=0;o?l?(d=s*t.width,c=i*e.height):(d=i*t.height,c=s*e.width):"start"===a?c=e.width:"end"===a?d=t.width:"inner"!==a&&(d=t.width/2,c=e.width/2),this.paddingLeft=Math.max((d-n+r)*this.width/(this.width-n),0),this.paddingRight=Math.max((c-h+r)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===a?(i=0,s=t.height):"end"===a&&(i=e.height,s=0),this.paddingTop=i+r,this.paddingBottom=s+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){X(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)V(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=sl(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,a,r,n,o,l,h,d,c,u,f,{ctx:g,_longestTextCache:p}=this,m=[],b=[],x=Math.floor(e/so(e,i)),_=0,y=0;for(s=0;s<e;s+=x){if(n=t[s].label,g.font=l=(o=this._resolveTickFontOptions(s)).string,h=p[l]=p[l]||{data:{},gc:[]},d=o.lineHeight,c=u=0,V(n)||B(n)){if(B(n))for(a=0,r=n.length;a<r;++a)V(f=n[a])||B(f)||(c=et(g,h.data,h.gc,c,f),u+=d)}else c=et(g,h.data,h.gc,c,n),u=d;m.push(c),b.push(u),_=Math.max(c,_),y=Math.max(u,y)}G(p,t=>{let i,s=t.gc,a=s.length/2;if(a>e){for(i=0;i<a;++i)delete t.data[s[i]];s.splice(0,a)}});let v=m.indexOf(_),M=b.indexOf(y),w=t=>({width:m[t]||0,height:b[t]||0});return{first:w(0),last:w(e-1),widest:w(v),highest:w(M),widths:m,heights:b}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return tT(this._alignToPixels?ee(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){var e,i;let s=this.ticks||[];if(t>=0&&t<s.length){let i=s[t];return i.$context||(e=this.getContext(),i.$context=ev(e,{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=(i=this.chart.getContext(),ev(i,{scale:this,type:"scale"})))}_tickSize(){let t=this.options.ticks,e=tw(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),a=this._getLabelSizes(),r=t.autoSkipPadding||0,n=a?a.widest.width+r:0,o=a?a.highest.height+r:0;return this.isHorizontal()?o*i>n*s?n/i:o/s:o*s<n*i?o/i:n/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,a,r,n,o,l,h,d,c,u,f=this.axis,g=this.chart,p=this.options,{grid:m,position:b,border:x}=p,_=m.offset,y=this.isHorizontal(),v=this.ticks.length+ +!!_,M=sh(m),w=[],k=x.setContext(this.getContext()),P=k.display?k.width:0,S=P/2,C=function(t){return ee(g,t,P)};if("top"===b)e=C(this.bottom),n=this.bottom-M,l=e-S,d=C(t.top)+S,u=t.bottom;else if("bottom"===b)e=C(this.top),d=t.top,u=C(t.bottom)-S,n=e+S,l=this.top+M;else if("left"===b)e=C(this.right),r=this.right-M,o=e-S,h=C(t.left)+S,c=t.right;else if("right"===b)e=C(this.left),h=t.left,c=C(t.right)-S,r=e+S,o=this.left+M;else if("x"===f){if("center"===b)e=C((t.top+t.bottom)/2+.5);else if(W(b)){let t=Object.keys(b)[0],i=b[t];e=C(this.chart.scales[t].getPixelForValue(i))}d=t.top,u=t.bottom,l=(n=e+S)+M}else if("y"===f){if("center"===b)e=C((t.left+t.right)/2);else if(W(b)){let t=Object.keys(b)[0],i=b[t];e=C(this.chart.scales[t].getPixelForValue(i))}o=(r=e-S)-M,h=t.left,c=t.right}let D=U(p.ticks.maxTicksLimit,v),O=Math.max(1,Math.ceil(v/D));for(i=0;i<v;i+=O){let t=this.getContext(i),e=m.setContext(t),f=x.setContext(t),p=e.lineWidth,b=e.color,v=f.dash||[],M=f.dashOffset,k=e.tickWidth,P=e.tickColor,S=e.tickBorderDash||[],C=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s,a=t.ticks.length,r=Math.min(e,a-1),n=t._startPixel,o=t._endPixel,l=t.getPixelForTick(r);if(!i||(s=1===a?Math.max(l-n,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(r-1))/2,!((l+=r<e?s:-s)<n-1e-6)&&!(l>o+1e-6)))return l}(this,i,_))&&(a=ee(g,s,p),y?r=o=h=c=a:n=l=d=u=a,w.push({tx1:r,ty1:n,tx2:o,ty2:l,x1:h,y1:d,x2:c,y2:u,width:p,color:b,borderDash:v,borderDashOffset:M,tickWidth:k,tickColor:P,tickBorderDash:S,tickBorderDashOffset:C}))}return this._ticksLength=v,this._borderValue=e,w}_computeLabelItems(t){let e,i,s,a,r,n,o,l,h,d,c,u=this.axis,f=this.options,{position:g,ticks:p}=f,m=this.isHorizontal(),b=this.ticks,{align:x,crossAlign:_,padding:y,mirror:v}=p,M=sh(f.grid),w=M+y,k=v?-y:w,P=-tw(this.labelRotation),S=[],C="middle";if("top"===g)r=this.bottom-k,n=this._getXAxisLabelAlignment();else if("bottom"===g)r=this.top+k,n=this._getXAxisLabelAlignment();else if("left"===g){let t=this._getYAxisLabelAlignment(M);n=t.textAlign,a=t.x}else if("right"===g){let t=this._getYAxisLabelAlignment(M);n=t.textAlign,a=t.x}else if("x"===u){if("center"===g)r=(t.top+t.bottom)/2+w;else if(W(g)){let t=Object.keys(g)[0],e=g[t];r=this.chart.scales[t].getPixelForValue(e)+w}n=this._getXAxisLabelAlignment()}else if("y"===u){if("center"===g)a=(t.left+t.right)/2-w;else if(W(g)){let t=Object.keys(g)[0],e=g[t];a=this.chart.scales[t].getPixelForValue(e)}n=this._getYAxisLabelAlignment(M).textAlign}"y"===u&&("start"===x?C="top":"end"===x&&(C="bottom"));let D=this._getLabelSizes();for(e=0,i=b.length;e<i;++e){let t;s=b[e].label;let u=p.setContext(this.getContext(e));o=this.getPixelForTick(e)+p.labelOffset,h=(l=this._resolveTickFontOptions(e)).lineHeight;let f=(d=B(s)?s.length:1)/2,x=u.color,y=u.textStrokeColor,M=u.textStrokeWidth,w=n;if(m?(a=o,"inner"===n&&(w=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),c="top"===g?"near"===_||0!==P?-d*h+h/2:"center"===_?-D.highest.height/2-f*h+h:-D.highest.height+h/2:"near"===_||0!==P?h/2:"center"===_?D.highest.height/2-f*h:D.highest.height-d*h,v&&(c*=-1),0===P||u.showLabelBackdrop||(a+=h/2*Math.sin(P))):(r=o,c=(1-d)*h/2),u.showLabelBackdrop){let s=ex(u.backdropPadding),a=D.heights[e],r=D.widths[e],o=c-s.top,l=0-s.left;switch(C){case"middle":o-=a/2;break;case"bottom":o-=a}switch(n){case"center":l-=r/2;break;case"right":l-=r;break;case"inner":e===i-1?l-=r:e>0&&(l-=r/2)}t={left:l,top:o,width:r+s.width,height:a+s.height,color:u.backdropColor}}S.push({label:s,font:l,textOffset:c,options:{rotation:P,color:x,strokeColor:y,strokeWidth:M,textAlign:w,textBaseline:C,translation:[a,r],backdrop:t}})}return S}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-tw(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i,{position:s,ticks:{crossAlign:a,mirror:r,padding:n}}=this.options,o=this._getLabelSizes(),l=t+n,h=o.widest.width;return"left"===s?r?(i=this.right+n,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?r?(i=this.left+n,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:a,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,a,r),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i,s=this.options.grid,a=this.ctx,r=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),n=(t,e,i)=>{i.width&&i.color&&(a.save(),a.lineWidth=i.width,a.strokeStyle=i.color,a.setLineDash(i.borderDash||[]),a.lineDashOffset=i.borderDashOffset,a.beginPath(),a.moveTo(t.x,t.y),a.lineTo(e.x,e.y),a.stroke(),a.restore())};if(s.display)for(e=0,i=r.length;e<i;++e){let t=r[e];s.drawOnChartArea&&n({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&n({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s,{chart:a,ctx:r,options:{border:n,grid:o}}=this,l=n.setContext(this.getContext()),h=n.display?l.width:0;if(!h)return;let d=o.setContext(this.getContext(0)).lineWidth,c=this._borderValue;this.isHorizontal()?(t=ee(a,this.left,h)-h/2,e=ee(a,this.right,d)+d/2,i=s=c):(i=ee(a,this.top,h)-h/2,s=ee(a,this.bottom,d)+d/2,t=e=c),r.save(),r.lineWidth=l.width,r.strokeStyle=l.color,r.beginPath(),r.moveTo(t,i),r.lineTo(e,s),r.stroke(),r.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&en(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;ed(e,s.label,0,s.textOffset,i,t)}i&&eo(e)}drawTitle(){let t,{ctx:e,options:{position:i,title:s,reverse:a}}=this;if(!s.display)return;let r=e_(s.font),n=ex(s.padding),o=s.align,l=r.lineHeight/2;"bottom"===i||"center"===i||W(i)?(l+=n.bottom,B(s.text)&&(l+=r.lineHeight*(s.text.length-1))):l+=n.top;let{titleX:h,titleY:d,maxWidth:c,rotation:u}=function(t,e,i,s){let a,r,n,{top:o,left:l,bottom:h,right:d,chart:c}=t,{chartArea:u,scales:f}=c,g=0,p=h-o,m=d-l;if(t.isHorizontal()){if(r=tB(s,l,d),W(i)){let t=Object.keys(i)[0],s=i[t];n=f[t].getPixelForValue(s)+p-e}else n="center"===i?(u.bottom+u.top)/2+p-e:sn(t,i,e);a=d-l}else{if(W(i)){let t=Object.keys(i)[0],s=i[t];r=f[t].getPixelForValue(s)-m+e}else r="center"===i?(u.left+u.right)/2-m+e:sn(t,i,e);n=tB(s,h,o),g="left"===i?-tg:tg}return{titleX:r,titleY:n,maxWidth:a,rotation:g}}(this,l,i,o);ed(e,s.text,0,0,r,{color:s.color,maxWidth:c,rotation:u,textAlign:(t=tV(o),(a&&"right"!==i||!a&&"right"===i)&&(t=sr(t)),t),textBaseline:"middle",translation:[h,d]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=U(t.grid&&t.grid.z,-1),s=U(t.border&&t.border.z,0);return this._isVisible()&&this.draw===sc.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i,s=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",r=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[a]!==this.id||t&&i.type!==t||r.push(i)}return r}_resolveTickFontOptions(t){return e_(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class su{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e;let i,s=Object.getPrototypeOf(t);"id"in(e=s)&&"defaults"in e&&(i=this.register(s));let a=this.items,r=t.id,n=this.scope+"."+r;if(!r)throw Error("class does not have id: "+t);return r in a||(a[r]=t,function(t,e,i){var s,a;let r=tt(Object.create(null),[i?t7.get(i):{},t7.get(e),t.defaults]);t7.set(e,r),t.defaultRoutes&&(s=e,Object.keys(a=t.defaultRoutes).forEach(t=>{let e=t.split("."),i=e.pop(),r=[s].concat(e).join("."),n=a[t].split("."),o=n.pop(),l=n.join(".");t7.route(r,i,l,o)})),t.descriptors&&t7.describe(e,t.descriptors)}(t,n,i),this.override&&t7.override(t.id,t.overrides)),n}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in t7[s]&&(delete t7[s][i],this.override&&delete t4[i])}}class sf{constructor(){this.controllers=new su(i_,"datasets",!0),this.elements=new su(ss,"elements"),this.plugins=new su(Object,"plugins"),this.scales=new su(sc,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):G(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=tr(t);X(i["before"+s],[],i),e[t](i),X(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var sg=new sf;class sp{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let a=s?this._descriptors(t).filter(s):this._descriptors(t),r=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,i,s){for(let a of(s=s||{},t)){let t=a.plugin;if(!1===X(t[i],[e,s,a.options],t)&&s.cancelable)return!1}return!0}invalidate(){V(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=U(i.options&&i.options.plugins,{}),a=function(t){let e={},i=[],s=Object.keys(sg.plugins.items);for(let t=0;t<s.length;t++)i.push(sg.getPlugin(s[t]));let a=t.plugins||[];for(let t=0;t<a.length;t++){let s=a[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,a){let r=[],n=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],a||!1!==o?!0===o?{}:o:null);null!==h&&r.push({plugin:l,options:function(t,{plugin:e,local:i},s,a){let r=t.pluginScopeKeys(e),n=t.getOptionScopes(s,r);return i&&e.defaults&&n.push(e.defaults),t.createResolver(n,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,n)})}return r}(t,a,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function sm(t,e){let i=t7.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function sb(t){if("x"===t||"y"===t||"r"===t)return t}function sx(t,...e){if(sb(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&sb(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function s_(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function sy(t){let e=t.options||(t.options={});e.plugins=U(e.plugins,{}),e.scales=function(t,e){let i=t4[t.type]||{scales:{}},s=e.scales||{},a=sm(t.type,e),r=Object.create(null);return Object.keys(s).forEach(e=>{let n=s[e];if(!W(n))return console.error(`Invalid scale configuration for scale: ${e}`);if(n._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=sx(e,n,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return s_(t,"x",i[0])||s_(t,"y",i[0])}return{}}(e,t),t7.scales[n.type]),l=o===a?"_index_":"_value_",h=i.scales||{};r[e]=te(Object.create(null),[{axis:o},n,h[o],h[l]])}),t.data.datasets.forEach(i=>{let a=i.type||t.type,n=i.indexAxis||sm(a,e),o=(t4[a]||{}).scales||{};Object.keys(o).forEach(t=>{let e,a=(e=t,"_index_"===t?e=n:"_value_"===t&&(e="x"===n?"y":"x"),e),l=i[a+"AxisID"]||a;r[l]=r[l]||Object.create(null),te(r[l],[{axis:a},s[l],o[t]])})}),Object.keys(r).forEach(t=>{let e=r[t];te(e,[t7.scales[e.type],t7.scale])}),r}(t,e)}function sv(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let sM=new Map,sw=new Set;function sk(t,e){let i=sM.get(t);return i||(i=e(),sM.set(t,i),sw.add(i)),i}let sP=(t,e,i)=>{let s=ta(e,i);void 0!==s&&t.add(s)};class sS{constructor(t){this._config=function(t){return(t=t||{}).data=sv(t.data),sy(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=sv(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),sy(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return sk(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return sk(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return sk(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return sk(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:a}=this,r=this._cachedScopes(t,i),n=r.get(e);if(n)return n;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>sP(o,t,e))),e.forEach(t=>sP(o,s,t)),e.forEach(t=>sP(o,t4[a]||{},t)),e.forEach(t=>sP(o,t7,t)),e.forEach(t=>sP(o,t8,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),sw.has(e)&&r.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,t4[e]||{},t7.datasets[e]||{},{type:e},t7,t8]}resolveNamedOptions(t,e,i,s=[""]){let a={$shared:!0},{resolver:r,subPrefixes:n}=sC(this._resolverCache,t,s),o=r;if(function(t,e){let{isScriptable:i,isIndexable:s}=ek(t);for(let a of e){let e=i(a),r=s(a),n=(r||e)&&t[a];if(e&&(to(n)||sD(n))||r&&B(n))return!0}return!1}(r,e)){a.$shared=!1,i=to(i)?i():i;let e=this.createResolver(t,i,n);o=ew(r,i,e)}for(let t of e)a[t]=o[t];return a}createResolver(t,e,i=[""],s){let{resolver:a}=sC(this._resolverCache,t,i);return W(e)?ew(a,e,void 0,s):a}}function sC(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let a=i.join(),r=s.get(a);return r||(r={resolver:eM(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(a,r)),r}let sD=t=>W(t)&&Object.getOwnPropertyNames(t).some(e=>to(t[e])),sO=["top","bottom","left","right","chartArea"];function sT(t,e){return"top"===t||"bottom"===t||-1===sO.indexOf(t)&&"x"===e}function sA(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function sL(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),X(i&&i.onComplete,[t],e)}function sE(t){let e=t.chart,i=e.options.animation;X(i&&i.onProgress,[t],e)}function sR(t){return eN()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let sI={},sF=t=>{let e=sR(t);return Object.values(sI).filter(t=>t.canvas===e).pop()};class sz{static defaults=t7;static instances=sI;static overrides=t4;static registry=sg;static version="4.4.9";static getChart=sF;static register(...t){sg.add(...t),sN()}static unregister(...t){sg.remove(...t),sN()}constructor(t,e){let i=this.config=new sS(e),s=sR(t),a=sF(s);if(a)throw Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");let r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!eN()||"undefined"!=typeof OffscreenCanvas&&s instanceof OffscreenCanvas?iJ:si)),this.platform.updateConfig(i);let n=this.platform.acquireContext(s,r.aspectRatio),o=n&&n.canvas,l=o&&o.height,h=o&&o.width;if(this.id=j(),this.ctx=n,this.canvas=o,this.width=h,this.height=l,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new sp,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}(t=>this.update(t),r.resizeDelay||0),this._dataChanges=[],sI[this.id]=this,!n||!o)return void console.error("Failed to create chart: can't acquire context from the given item");ie.listen(this,"complete",sL),ie.listen(this,"progress",sE),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:a}=this;return V(t)?e&&a?a:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return sg}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():eq(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ei(this.canvas,this.ctx),this}stop(){return ie.stop(this),this}resize(t,e){ie.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,a=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,t,e,a),n=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,eq(this,n,!0)&&(this.notifyPlugins("resize",{size:r}),X(i.onResize,[this,r],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){G(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),a=[];e&&(a=a.concat(Object.keys(e).map(t=>{let i=e[t],s=sx(t,i),a="r"===s,r="x"===s;return{options:i,dposition:a?"chartArea":r?"bottom":"left",dtype:a?"radialLinear":r?"category":"linear"}}))),G(a,e=>{let a=e.options,r=a.id,n=sx(r,a),o=U(a.type,e.dtype);(void 0===a.position||sT(a.position,n)!==sT(e.dposition))&&(a.position=e.dposition),s[r]=!0;let l=null;r in i&&i[r].type===o?l=i[r]:i[(l=new(sg.getScale(o))({id:r,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(a,t)}),G(s,(t,e)=>{t||delete i[e]}),G(i,t=>{iK.configure(this,t,t.options),iK.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(sA("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e,i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],a=this.getDatasetMeta(t),r=e.type||this.config.type;if(a.type&&a.type!==r&&(this._destroyDatasetMeta(t),a=this.getDatasetMeta(t)),a.type=r,a.indexAxis=e.indexAxis||sm(r,this.options),a.order=e.order||0,a.index=t,a.label=""+e.label,a.visible=this.isDatasetVisible(t),a.controller)a.controller.updateIndex(t),a.controller.linkScales();else{let e=sg.getController(r),{datasetElementType:s,dataElementType:n}=t7.datasets[r];Object.assign(e,{dataElementType:sg.getElement(n),datasetElementType:s&&sg.getElement(s)}),a.controller=new e(this,t),i.push(a.controller)}}return this._updateMetasets(),i}_resetElements(){G(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===a.indexOf(e);e.buildOrUpdateElements(i),r=Math.max(+e.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),s||G(a,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(sA("z","_idx"));let{_active:n,_lastEvent:o}=this;o?this._eventHandler(o,!0):n.length&&this._updateHoverStyles(n,n,!0),this.render()}_updateScales(){G(this.scales,t=>{iK.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;tl(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:i,start:s,count:a}of this._getUniformDataChanges()||[]){var e="_removeElements"===i?-a:a;for(let i of Object.keys(t)){let a=+i;if(a>=s){let r=t[i];delete t[i],(e>0||a>s)&&(t[a+e]=r)}}}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!tl(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;iK.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],G(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,to(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(ie.has(this)?this.attached&&!ie.running(this)&&ie.start(this):(this.draw(),sL({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i,s=this._sortedMetasets,a=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&a.push(i)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=e7(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&en(e,s),t.controller.draw(),s&&eo(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return er(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let a=iV.modes[e];return"function"==typeof a?a(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=ev(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",a=this.getDatasetMeta(t),r=a.controller._resolveAnimations(void 0,s);tn(e)?(a.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(a,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),ie.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),ei(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete sI[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};G(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},a=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},r=(t,e)=>{this.canvas&&this.resize(t,e)},n=()=>{a("attach",n),this.attached=!0,this.resize(),s("resize",r),s("detach",t)};t=()=>{this.attached=!1,a("resize",r),this._stop(),this._resize(0,0),s("attach",n)},i.isAttached(this.canvas)?n():t()}unbindEvents(){G(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},G(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,a,r,n=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+n+"DatasetHoverStyle"](),a=0,r=t.length;a<r;++a){let e=(s=t[a])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[n+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});K(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,a=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),r=a(e,t),n=i?t:a(t,e);r.length&&this.updateHoverStyle(r,s.mode,!1),n.length&&s.mode&&this.updateHoverStyle(n,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let a=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(a||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:a=[],options:r}=this,n=this._getActiveElements(t,a,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,X(r.onHover,[t,n,this],this),o&&X(r.onClick,[t,n,this],this));let h=!K(n,a);return(h||e)&&(this._active=n,this._updateHoverStyles(n,a,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,s)}}function sN(){return G(sz.instances,t=>t._plugins.invalidate())}function sj(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function sV(t,e,i,s,a,r){let{x:n,y:o,startAngle:l,pixelMargin:h,innerRadius:d}=e,c=Math.max(e.outerRadius+s+i-h,0),u=d>0?d+s+i+h:0,f=0,g=a-l;if(s){let t=c>0?c-s:0,e=((d>0?d-s:0)+t)/2;f=(g-(0!==e?g*e/(e+s):g))/2}let p=Math.max(.001,g*c-i/th)/c,m=(g-p)/2,b=l+m+f,x=a-m-f,{outerStart:_,outerEnd:y,innerStart:v,innerEnd:M}=function(t,e,i,s){let a=ep(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),r=(i-e)/2,n=Math.min(r,s*e/2),o=t=>{let e=(i-Math.min(r,t))*s/2;return tT(t,0,Math.min(r,e))};return{outerStart:o(a.outerStart),outerEnd:o(a.outerEnd),innerStart:tT(a.innerStart,0,n),innerEnd:tT(a.innerEnd,0,n)}}(e,u,c,x-b),w=c-_,k=c-y,P=b+_/w,S=x-y/k,C=u+v,D=u+M,O=b+v/C,T=x-M/D;if(t.beginPath(),r){let e=(P+S)/2;if(t.arc(n,o,c,P,e),t.arc(n,o,c,e,S),y>0){let e=sj(k,S,n,o);t.arc(e.x,e.y,y,S,x+tg)}let i=sj(D,x,n,o);if(t.lineTo(i.x,i.y),M>0){let e=sj(D,T,n,o);t.arc(e.x,e.y,M,x+tg,T+Math.PI)}let s=(x-M/u+(b+v/u))/2;if(t.arc(n,o,u,x-M/u,s,!0),t.arc(n,o,u,s,b+v/u,!0),v>0){let e=sj(C,O,n,o);t.arc(e.x,e.y,v,O+Math.PI,b-tg)}let a=sj(w,b,n,o);if(t.lineTo(a.x,a.y),_>0){let e=sj(w,P,n,o);t.arc(e.x,e.y,_,b-tg,P)}}else{t.moveTo(n,o);let e=Math.cos(P)*c+n,i=Math.sin(P)*c+o;t.lineTo(e,i);let s=Math.cos(S)*c+n,a=Math.sin(S)*c+o;t.lineTo(s,a)}t.closePath()}class sB extends ss{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:a}=tP(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:r,endAngle:n,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,c=U(h,n-r),u=tO(s,r,n)&&r!==n,f=c>=td||u,g=tA(a,o+d,l+d);return f&&g}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:a,innerRadius:r,outerRadius:n}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+a)/2,d=(r+n+l+o)/2;return{x:e+Math.cos(h)*d,y:i+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,a=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=.33*("inner"===e.borderAlign),this.fullCircles=i>td?Math.floor(i/td):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let n=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(n)*s,Math.sin(n)*s);let o=s*(1-Math.sin(Math.min(th,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,a){let{fullCircles:r,startAngle:n,circumference:o}=e,l=e.endAngle;if(r){sV(t,e,i,s,l,a);for(let e=0;e<r;++e)t.fill();isNaN(o)||(l=n+(o%td||td))}sV(t,e,i,s,l,a),t.fill()}(t,this,o,a,r),function(t,e,i,s,a){let{fullCircles:r,startAngle:n,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:d,borderDash:c,borderDashOffset:u}=l,f="inner"===l.borderAlign;if(!h)return;t.setLineDash(c||[]),t.lineDashOffset=u,f?(t.lineWidth=2*h,t.lineJoin=d||"round"):(t.lineWidth=h,t.lineJoin=d||"bevel");let g=e.endAngle;if(r){sV(t,e,i,s,g,a);for(let e=0;e<r;++e)t.stroke();isNaN(o)||(g=n+(o%td||td))}f&&function(t,e,i){let{startAngle:s,pixelMargin:a,x:r,y:n,outerRadius:o,innerRadius:l}=e,h=a/o;t.beginPath(),t.arc(r,n,o,s-h,i+h),l>a?(h=a/l,t.arc(r,n,l,i+h,s-h,!0)):t.arc(r,n,a,i+tg,s-tg),t.closePath(),t.clip()}(t,e,g),r||(sV(t,e,i,s,g,a),t.stroke())}(t,this,o,a,r),t.restore()}}function sW(t,e,i=e){t.lineCap=U(i.borderCapStyle,e.borderCapStyle),t.setLineDash(U(i.borderDash,e.borderDash)),t.lineDashOffset=U(i.borderDashOffset,e.borderDashOffset),t.lineJoin=U(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=U(i.borderWidth,e.borderWidth),t.strokeStyle=U(i.borderColor,e.borderColor)}function sH(t,e,i){t.lineTo(i.x,i.y)}function s$(t,e,i={}){let s=t.length,{start:a=0,end:r=s-1}=i,{start:n,end:o}=e,l=Math.max(a,n),h=Math.min(r,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(a<n&&r<n||a>o&&r>o)?s+h-l:h-l}}function sU(t,e,i,s){let a,r,n,{points:o,options:l}=e,{count:h,start:d,loop:c,ilen:u}=s$(o,i,s),f=l.stepped?el:l.tension||"monotone"===l.cubicInterpolationMode?eh:sH,{move:g=!0,reverse:p}=s||{};for(a=0;a<=u;++a)(r=o[(d+(p?u-a:a))%h]).skip||(g?(t.moveTo(r.x,r.y),g=!1):f(t,n,r,p,l.stepped),n=r);return c&&f(t,n,r=o[(d+(p?u:0))%h],p,l.stepped),!!c}function sY(t,e,i,s){let a,r,n,o,l,h,d=e.points,{count:c,start:u,ilen:f}=s$(d,i,s),{move:g=!0,reverse:p}=s||{},m=0,b=0,x=t=>(u+(p?f-t:t))%c,_=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(g&&(r=d[x(0)],t.moveTo(r.x,r.y)),a=0;a<=f;++a){if((r=d[x(a)]).skip)continue;let e=r.x,i=r.y,s=0|e;s===n?(i<o?o=i:i>l&&(l=i),m=(b*m+e)/++b):(_(),t.lineTo(e,i),n=s,b=0,o=l=i),h=i}_()}function sq(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?sU:sY}let sX="function"==typeof Path2D;class sG extends ss{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;!function(t,e,i,s,a){let r,n,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,a,r=eF(e),n=t.length,o=Array(n).fill(0),l=Array(n),h=eI(t,0);for(i=0;i<n;++i)if(s=a,a=h,h=eI(t,i+1),a){if(h){let t=h[e]-a[e];o[i]=0!==t?(h[r]-a[r])/t:0}l[i]=s?h?tx(o[i-1])!==tx(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}!function(t,e,i){let s,a,r,n,o,l=t.length,h=eI(t,0);for(let d=0;d<l-1;++d)if(o=h,h=eI(t,d+1),o&&h){if(t_(e[d],0,eR)){i[d]=i[d+1]=0;continue}(n=Math.pow(s=i[d]/e[d],2)+Math.pow(a=i[d+1]/e[d],2))<=9||(r=3/Math.sqrt(n),i[d]=s*r*e[d],i[d+1]=a*r*e[d])}}(t,o,l),function(t,e,i="x"){let s,a,r,n=eF(i),o=t.length,l=eI(t,0);for(let h=0;h<o;++h){if(a=r,r=l,l=eI(t,h+1),!r)continue;let o=r[i],d=r[n];a&&(s=(o-a[i])/3,r[`cp1${i}`]=o-s,r[`cp1${n}`]=d-s*e[h]),l&&(s=(l[i]-o)/3,r[`cp2${i}`]=o+s,r[`cp2${n}`]=d+s*e[h])}}(t,l,e)}(t,a);else{let i=s?t[t.length-1]:t[0];for(r=0,n=t.length;r<n;++r)l=function(t,e,i,s){let a=t.skip?e:t,r=i.skip?e:i,n=tS(e,a),o=tS(r,e),l=n/(n+o),h=o/(n+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let d=s*l,c=s*h;return{previous:{x:e.x-d*(r.x-a.x),y:e.y-d*(r.y-a.y)},next:{x:e.x+c*(r.x-a.x),y:e.y+c*(r.y-a.y)}}}(i,o=t[r],t[Math.min(r+1,n-!s)%n],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,a,r,n,o=er(t[0],e);for(i=0,s=t.length;i<s;++i)n=r,r=o,o=i<s-1&&er(t[i+1],e),r&&(a=t[i],n&&(a.cp1x=ez(a.cp1x,e.left,e.right),a.cp1y=ez(a.cp1y,e.top,e.bottom)),o&&(a.cp2x=ez(a.cp2x,e.left,e.right),a.cp2y=ez(a.cp2y,e.top,e.bottom)))}(t,i)}(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,a=i.length;if(!a)return[];let r=!!t._loop,{start:n,end:o}=function(t,e,i,s){let a=0,r=e-1;if(i&&!s)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(r+=a);r>a&&t[r%e].skip;)r--;return{start:a,end:r%=e}}(i,a,r,s);if(!0===s)return e6(t,[{start:n,end:o,loop:r}],i,e);let l=o<n?o+a:o,h=!!t._fullLoop&&0===n&&o===a-1;return e6(t,function(t,e,i,s){let a,r=t.length,n=[],o=e,l=t[e];for(a=e+1;a<=i;++a){let i=t[a%r];i.skip||i.stop?l.skip||(s=!1,n.push({start:e%r,end:(a-1)%r,loop:s}),e=o=i.stop?a:null):(o=a,l.skip&&(e=a)),l=i}return null!==o&&n.push({start:e%r,end:o%r,loop:s}),n}(i,n,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s,a=this.options,r=t[e],n=this.points,o=e8(this,{property:e,start:r,end:r});if(!o.length)return;let l=[],h=a.stepped?eZ:a.tension||"monotone"===a.cubicInterpolationMode?eJ:eK;for(i=0,s=o.length;i<s;++i){let{start:s,end:d}=o[i],c=n[s],u=n[d];if(c===u){l.push(c);continue}let f=Math.abs((r-c[e])/(u[e]-c[e])),g=h(c,u,f,a.stepped);g[e]=t[e],l.push(g)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return sq(this)(t,this,e,i)}path(t,e,i){let s=this.segments,a=sq(this),r=this._loop;for(let n of(e=e||0,i=i||this.points.length-e,s))r&=a(t,this,n,{start:e,end:e+i-1});return!!r}draw(t,e,i,s){let a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),function(t,e,i,s){if(sX&&!e.options.segment){let a;(a=e._path)||(a=e._path=new Path2D,e.path(a,i,s)&&a.closePath()),sW(t,e.options),t.stroke(a)}else{let{segments:a,options:r}=e,n=sq(e);for(let o of a)sW(t,r,o.style),t.beginPath(),n(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function sK(t,e,i,s){let a=t.options,{[i]:r}=t.getProps([i],s);return Math.abs(e-r)<a.radius+a.hitRadius}class sZ extends ss{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:a,y:r}=this.getProps(["x","y"],i);return Math.pow(t-a,2)+Math.pow(e-r,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return sK(this,t,"x",e)}inYRange(t,e){return sK(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&er(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,es(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function sJ(t,e){let i,s,a,r,n,{x:o,y:l,base:h,width:d,height:c}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(n=c/2,i=Math.min(o,h),s=Math.max(o,h),a=l-n,r=l+n):(i=o-(n=d/2),s=o+n,a=Math.min(l,h),r=Math.max(l,h)),{left:i,top:a,right:s,bottom:r}}function sQ(t,e,i,s){return t?0:tT(e,i,s)}function s0(t,e,i,s){let a=null===e,r=null===i,n=t&&!(a&&r)&&sJ(t,s);return n&&(a||tA(e,n.left,n.right))&&(r||tA(i,n.top,n.bottom))}function s1(t,e){t.rect(e.x,e.y,e.w,e.h)}function s2(t,e,i={}){let s=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,r=(t.x+t.w!==i.x+i.w?e:0)-s,n=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+s,y:t.y+a,w:t.w+r,h:t.h+n,radius:t.radius}}class s5 extends ss{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:a}}=this,{inner:r,outer:n}=function(t){let e=sJ(t),i=e.right-e.left,s=e.bottom-e.top,a=function(t,e,i){let s=t.options.borderWidth,a=t.borderSkipped,r=em(s);return{t:sQ(a.top,r.top,0,i),r:sQ(a.right,r.right,0,e),b:sQ(a.bottom,r.bottom,0,i),l:sQ(a.left,r.left,0,e)}}(t,i/2,s/2),r=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),a=t.options.borderRadius,r=eb(a),n=Math.min(e,i),o=t.borderSkipped,l=s||W(a);return{topLeft:sQ(!l||o.top||o.left,r.topLeft,0,n),topRight:sQ(!l||o.top||o.right,r.topRight,0,n),bottomLeft:sQ(!l||o.bottom||o.left,r.bottomLeft,0,n),bottomRight:sQ(!l||o.bottom||o.right,r.bottomRight,0,n)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:r},inner:{x:e.left+a.l,y:e.top+a.t,w:i-a.l-a.r,h:s-a.t-a.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,r.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(a.b,a.r))}}}}(this),o=(e=n.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?ec:s1;t.save(),(n.w!==r.w||n.h!==r.h)&&(t.beginPath(),o(t,s2(n,i,r)),t.clip(),o(t,s2(r,-i,n)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,s2(r,i)),t.fillStyle=a,t.fill(),t.restore()}inRange(t,e,i){return s0(this,t,e,i)}inXRange(t,e){return s0(this,t,null,e)}inYRange(t,e){return s0(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(e+s)/2:e,y:a?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}let s4=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],s8=s4.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function s6(t,e,i,s){if(s)return;let a=e[t],r=i[t];return"angle"===t&&(a=tD(a),r=tD(r)),{property:t,start:a,end:r}}function s3(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function s9(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function s7(t,e){let i=[],s=!1;return B(t)?(s=!0,i=t):i=function(t,e){let{x:i=null,y:s=null}=t||{},a=e.points,r=[];return e.segments.forEach(({start:t,end:e})=>{e=s3(t,e,a);let n=a[t],o=a[e];null!==s?(r.push({x:n.x,y:s}),r.push({x:o.x,y:s})):null!==i&&(r.push({x:i,y:n.y}),r.push({x:i,y:o.y}))}),r}(t,e),i.length?new sG({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}class at{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:a,radius:r}=this;return e=e||{start:0,end:td},t.arc(s,a,r,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,a=t.angle;return{x:e+Math.cos(a)*s,y:i+Math.sin(a)*s,angle:a}}}function ae(t,e,i){let{segments:s,points:a}=e,r=!0,n=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=a[s],d=a[s3(s,l,a)];r?(t.moveTo(h.x,h.y),r=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),(n=!!e.pathSegment(t,o,{move:n}))?t.closePath():t.lineTo(d.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function ai(t,e){let{line:i,target:s,property:a,color:r,scale:n,clip:o}=e;for(let{source:e,target:l,start:h,end:d}of function(t,e,i){let s=t.segments,a=t.points,r=e.points,n=[];for(let t of s){let{start:s,end:o}=t;o=s3(s,o,a);let l=s6(i,a[s],a[o],t.loop);if(!e.segments){n.push({source:t,target:l,start:a[s],end:a[o]});continue}for(let s of e8(e,l)){let e=s6(i,r[s.start],r[s.end],s.loop);for(let r of e4(t,a,e))n.push({source:r,target:s,start:{[i]:s9(l,e,"start",Math.max)},end:{[i]:s9(l,e,"end",Math.min)}})}}return n}(i,s,a)){let c,{style:{backgroundColor:u=r}={}}=e,f=!0!==s;t.save(),t.fillStyle=u,function(t,e,i,s){let a=e.chart.chartArea,{property:r,start:n,end:o}=s||{};if("x"===r||"y"===r){let e,s,l,h;"x"===r?(e=n,s=a.top,l=o,h=a.bottom):(e=a.left,s=n,l=a.right,h=o),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),s=Math.max(s,i.top),h=Math.min(h,i.bottom)),t.rect(e,s,l-e,h-s),t.clip()}}(t,n,o,f&&s6(a,h,d)),t.beginPath();let g=!!i.pathSegment(t,e);if(f){g?t.closePath():as(t,s,d,a);let e=!!s.pathSegment(t,l,{move:g,reverse:!0});(c=g&&e)||as(t,s,h,a)}t.closePath(),t.fill(c?"evenodd":"nonzero"),t.restore()}}function as(t,e,i,s){let a=e.interpolate(i,s);a&&t.lineTo(a.x,a.y)}let aa=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},ar=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class an extends ss{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=X(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e,{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let a=i.labels,r=e_(a.font),n=r.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=aa(a,n);s.font=r.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,n,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,r,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:a,maxWidth:r,options:{labels:{padding:n}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+n,d=t;a.textAlign="left",a.textBaseline="middle";let c=-1,u=-h;return this.legendItems.forEach((t,f)=>{let g=i+e/2+a.measureText(t.text).width;(0===f||l[l.length-1]+g+2*n>r)&&(d+=h,l[l.length-(f>0?0:1)]=0,u+=h,c++),o[f]={left:0,top:u,row:c,width:g,height:s},l[l.length-1]+=g+n}),d}_fitCols(t,e,i,s){let{ctx:a,maxHeight:r,options:{labels:{padding:n}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=r-t,d=n,c=0,u=0,f=0,g=0;return this.legendItems.forEach((t,r)=>{var p,m,b,x,_,y,v,M,w,k,P,S;let C,D,{itemWidth:O,itemHeight:T}=(p=i,m=e,b=a,x=t,_=s,{itemWidth:(y=x,v=p,M=m,w=b,(C=y.text)&&"string"!=typeof C&&(C=C.reduce((t,e)=>t.length>e.length?t:e)),v+M.size/2+w.measureText(C).width),itemHeight:(k=_,P=x,S=m.lineHeight,D=k,"string"!=typeof P.text&&(D=ao(P,S)),D)});r>0&&u+T+2*n>h&&(d+=c+n,l.push({width:c,height:u}),f+=c+n,g++,c=u=0),o[r]={left:f,top:u,col:g,width:O,height:T},c=Math.max(c,O),u+=T+n}),d+=c,l.push({width:c,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:a}}=this,r=eQ(a,this.left,this.width);if(this.isHorizontal()){let a=0,n=tB(i,this.left+s,this.right-this.lineWidths[a]);for(let o of e)a!==o.row&&(a=o.row,n=tB(i,this.left+s,this.right-this.lineWidths[a])),o.top+=this.top+t+s,o.left=r.leftForLtr(r.x(n),o.width),n+=o.width+s}else{let a=0,n=tB(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(let o of e)o.col!==a&&(a=o.col,n=tB(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),o.top=n,o.left+=this.left+s,o.left=r.leftForLtr(r.x(o.left),o.width),n+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;en(t,this),this._draw(),eo(t)}}_draw(){let t,{options:e,columnSizes:i,lineWidths:s,ctx:a}=this,{align:r,labels:n}=e,o=t7.color,l=eQ(e.rtl,this.left,this.width),h=e_(n.font),{padding:d}=n,c=h.size,u=c/2;this.drawTitle(),a.textAlign=l.textAlign("left"),a.textBaseline="middle",a.lineWidth=.5,a.font=h.string;let{boxWidth:f,boxHeight:g,itemHeight:p}=aa(n,c),m=function(t,e,i){if(isNaN(f)||f<=0||isNaN(g)||g<0)return;a.save();let s=U(i.lineWidth,1);if(a.fillStyle=U(i.fillStyle,o),a.lineCap=U(i.lineCap,"butt"),a.lineDashOffset=U(i.lineDashOffset,0),a.lineJoin=U(i.lineJoin,"miter"),a.lineWidth=s,a.strokeStyle=U(i.strokeStyle,o),a.setLineDash(U(i.lineDash,[])),n.usePointStyle){let r={radius:g*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s};ea(a,r,l.xPlus(t,f/2),e+u,n.pointStyleWidth&&f)}else{let r=e+Math.max((c-g)/2,0),n=l.leftForLtr(t,f),o=eb(i.borderRadius);a.beginPath(),Object.values(o).some(t=>0!==t)?ec(a,{x:n,y:r,w:f,h:g,radius:o}):a.rect(n,r,f,g),a.fill(),0!==s&&a.stroke()}a.restore()},b=function(t,e,i){ed(a,i.text,t,e+p/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},x=this.isHorizontal(),_=this._computeTitleHeight();t=x?{x:tB(r,this.left+d,this.right-s[0]),y:this.top+d+_,line:0}:{x:this.left+d,y:tB(r,this.top+_+d,this.bottom-i[0].height),line:0},e0(this.ctx,e.textDirection);let y=p+d;this.legendItems.forEach((o,c)=>{a.strokeStyle=o.fontColor,a.fillStyle=o.fontColor;let g=a.measureText(o.text).width,p=l.textAlign(o.textAlign||(o.textAlign=n.textAlign)),v=f+u+g,M=t.x,w=t.y;if(l.setWidth(this.width),x?c>0&&M+v+d>this.right&&(w=t.y+=y,t.line++,M=t.x=tB(r,this.left+d,this.right-s[t.line])):c>0&&w+y>this.bottom&&(M=t.x=M+i[t.line].width+d,t.line++,w=t.y=tB(r,this.top+_+d,this.bottom-i[t.line].height)),m(l.x(M),w,o),M=tW(p,M+f+u,x?M+v:this.right,e.rtl),b(l.x(M),w,o),x)t.x+=v+d;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=ao(o,e)+d}else t.y+=y}),e1(this.ctx,e.textDirection)}drawTitle(){let t,e=this.options,i=e.title,s=e_(i.font),a=ex(i.padding);if(!i.display)return;let r=eQ(e.rtl,this.left,this.width),n=this.ctx,o=i.position,l=s.size/2,h=a.top+l,d=this.left,c=this.width;if(this.isHorizontal())c=Math.max(...this.lineWidths),t=this.top+h,d=tB(e.align,d,this.right-c);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+tB(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let u=tB(o,d,d+c);n.textAlign=r.textAlign(tV(o)),n.textBaseline="middle",n.strokeStyle=i.color,n.fillStyle=i.color,n.font=s.string,ed(n,i.text,u,t,s)}_computeTitleHeight(){let t=this.options.title,e=e_(t.font),i=ex(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,a;if(tA(t,this.left,this.right)&&tA(e,this.top,this.bottom)){for(i=0,a=this.legendHitBoxes;i<a.length;++i)if(tA(t,(s=a[i]).left,s.left+s.width)&&tA(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e,i;let s=this.options;if(e=t.type,i=s,("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let a=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,i=ar(e,a);e&&!i&&X(s.onLeave,[t,e,this],this),this._hoveredItem=a,a&&!i&&X(s.onHover,[t,a,this],this)}else a&&X(s.onClick,[t,a,this],this)}}function ao(t,e){return e*(t.text?t.text.length:0)}new WeakMap;let al={average(t){let e,i;if(!t.length)return!1;let s=new Set,a=0,r=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),a+=t.y,++r}}return 0!==r&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:a/r}},nearest(t,e){let i,s,a;if(!t.length)return!1;let r=e.x,n=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=tS(e,s.getCenterPoint());t<o&&(o=t,a=s)}}if(a){let t=a.tooltipPosition();r=t.x,n=t.y}return{x:r,y:n}}};function ah(t,e){return e&&(B(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function ad(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function ac(t,e){let i=t.chart.ctx,{body:s,footer:a,title:r}=t,{boxWidth:n,boxHeight:o}=e,l=e_(e.bodyFont),h=e_(e.titleFont),d=e_(e.footerFont),c=r.length,u=a.length,f=s.length,g=ex(e.padding),p=g.height,m=0,b=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);b+=t.beforeBody.length+t.afterBody.length,c&&(p+=c*h.lineHeight+(c-1)*e.titleSpacing+e.titleMarginBottom),b&&(p+=f*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*e.bodySpacing),u&&(p+=e.footerMarginTop+u*d.lineHeight+(u-1)*e.footerSpacing);let x=0,_=function(t){m=Math.max(m,i.measureText(t).width+x)};return i.save(),i.font=h.string,G(t.title,_),i.font=l.string,G(t.beforeBody.concat(t.afterBody),_),x=e.displayColors?n+2+e.boxPadding:0,G(s,t=>{G(t.before,_),G(t.lines,_),G(t.after,_)}),x=0,i.font=d.string,G(t.footer,_),i.restore(),{width:m+=g.width,height:p}}function au(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:a,width:r}=i,{width:n,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=a<=(o+l)/2?"left":"right":a<=r/2?h="left":a>=n-r/2&&(h="right"),function(t,e,i,s){let{x:a,width:r}=s,n=i.caretSize+i.caretPadding;if("left"===t&&a+r+n>e.width||"right"===t&&a-r-n<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function af(t,e,i,s){let{caretSize:a,caretPadding:r,cornerRadius:n}=t,{xAlign:o,yAlign:l}=i,h=a+r,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:f}=eb(n),g=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),p=function(t,e,i){let{y:s,height:a}=t;return"top"===e?s+=i:"bottom"===e?s-=a+i:s-=a/2,s}(e,l,h);return"center"===l?"left"===o?g+=h:"right"===o&&(g-=h):"left"===o?g-=Math.max(d,u)+a:"right"===o&&(g+=Math.max(c,f)+a),{x:tT(g,0,s.width-e.width),y:tT(p,0,s.height-e.height)}}function ag(t,e,i){let s=ex(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function ap(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let am={beforeTitle:N,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:N,beforeBody:N,beforeLabel:N,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return V(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:N,afterBody:N,beforeFooter:N,footer:N,afterFooter:N};function ab(t,e,i,s){let a=t[e].call(i,s);return void 0===a?am[e].call(i,s):a}class ax extends ss{static positioners=al;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,a=new ir(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){var t;return this.$context||(this.$context=(t=this.chart.getContext(),ev(t,{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"})))}getTitle(t,e){let{callbacks:i}=e,s=ab(i,"beforeTitle",this,t),a=ab(i,"title",this,t),r=ab(i,"afterTitle",this,t),n=[];return n=ah(n,ad(s)),n=ah(n,ad(a)),n=ah(n,ad(r))}getBeforeBody(t,e){return ah([],ad(ab(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return G(t,t=>{let e={before:[],lines:[],after:[]},a=ap(i,t);ah(e.before,ad(ab(a,"beforeLabel",this,t))),ah(e.lines,ab(a,"label",this,t)),ah(e.after,ad(ab(a,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return ah([],ad(ab(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=ab(i,"beforeFooter",this,t),a=ab(i,"footer",this,t),r=ab(i,"afterFooter",this,t),n=[];return n=ah(n,ad(s)),n=ah(n,ad(a)),n=ah(n,ad(r))}_createItems(t){let e,i,s=this._active,a=this.chart.data,r=[],n=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:a}=e,r=t.getDatasetMeta(s).controller,{label:n,value:o}=r.getLabelAndValue(a);return{chart:t,label:n,parsed:r.getParsed(a),raw:t.data.datasets[s].data[a],formattedValue:o,dataset:r.getDataset(),dataIndex:a,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,a))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,a))),G(l,e=>{let i=ap(t.callbacks,e);r.push(ab(i,"labelColor",this,e)),n.push(ab(i,"labelPointStyle",this,e)),o.push(ab(i,"labelTextColor",this,e))}),this.labelColors=r,this.labelPointStyles=n,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i,s=this.options.setContext(this.getContext()),a=this._active,r=[];if(a.length){let t=al[s.position].call(this,a,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);let e=this._size=ac(this,s),n=Object.assign({},t,e),o=au(this.chart,s,n),l=af(s,n,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=r,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let a=this.getCaretPosition(t,i,s);e.lineTo(a.x1,a.y1),e.lineTo(a.x2,a.y2),e.lineTo(a.x3,a.y3)}getCaretPosition(t,e,i){let s,a,r,n,o,l,{xAlign:h,yAlign:d}=this,{caretSize:c,cornerRadius:u}=i,{topLeft:f,topRight:g,bottomLeft:p,bottomRight:m}=eb(u),{x:b,y:x}=t,{width:_,height:y}=e;return"center"===d?(o=x+y/2,"left"===h?(a=(s=b)-c,n=o+c,l=o-c):(a=(s=b+_)+c,n=o-c,l=o+c),r=s):(a="left"===h?b+Math.max(f,p)+c:"right"===h?b+_-Math.max(g,m)-c:this.caretX,"top"===d?(o=(n=x)-c,s=a-c,r=a+c):(o=(n=x+y)+c,s=a+c,r=a-c),l=n),{x1:s,x2:a,x3:r,y1:n,y2:o,y3:l}}drawTitle(t,e,i){let s,a,r,n=this.title,o=n.length;if(o){let l=eQ(i.rtl,this.x,this.width);for(r=0,t.x=ag(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=e_(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;r<o;++r)e.fillText(n[r],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+a,r+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,a){let r=this.labelColors[i],n=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=a,h=e_(a.bodyFont),d=ag(this,"left",a),c=s.x(d),u=o<h.lineHeight?(h.lineHeight-o)/2:0,f=e.y+u;if(a.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:n.pointStyle,rotation:n.rotation,borderWidth:1},i=s.leftForLtr(c,l)+l/2,h=f+o/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,es(t,e,i,h),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,es(t,e,i,h)}else{t.lineWidth=W(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;let e=s.leftForLtr(c,l),i=s.leftForLtr(s.xPlus(c,1),l-2),n=eb(r.borderRadius);Object.values(n).some(t=>0!==t)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,ec(t,{x:e,y:f,w:l,h:o,radius:n}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ec(t,{x:i,y:f+1,w:l-2,h:o-2,radius:n}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(e,f,l,o),t.strokeRect(e,f,l,o),t.fillStyle=r.backgroundColor,t.fillRect(i,f+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,a,r,n,o,l,{body:h}=this,{bodySpacing:d,bodyAlign:c,displayColors:u,boxHeight:f,boxWidth:g,boxPadding:p}=i,m=e_(i.bodyFont),b=m.lineHeight,x=0,_=eQ(i.rtl,this.x,this.width),y=function(i){e.fillText(i,_.x(t.x+x),t.y+b/2),t.y+=b+d},v=_.textAlign(c);for(e.textAlign=c,e.textBaseline="middle",e.font=m.string,t.x=ag(this,v,i),e.fillStyle=i.bodyColor,G(this.beforeBody,y),x=u&&"right"!==v?"center"===c?g/2+p:g+2+p:0,r=0,o=h.length;r<o;++r){for(s=h[r],e.fillStyle=this.labelTextColors[r],G(s.before,y),a=s.lines,u&&a.length&&(this._drawColorBox(e,t,r,_,i),b=Math.max(m.lineHeight,f)),n=0,l=a.length;n<l;++n)y(a[n]),b=m.lineHeight;G(s.after,y)}x=0,b=m.lineHeight,G(this.afterBody,y),t.y-=d}drawFooter(t,e,i){let s,a,r=this.footer,n=r.length;if(n){let o=eQ(i.rtl,this.x,this.width);for(t.x=ag(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=e_(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,a=0;a<n;++a)e.fillText(r[a],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:a,yAlign:r}=this,{x:n,y:o}=t,{width:l,height:h}=i,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:f}=eb(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(n+d,o),"top"===r&&this.drawCaret(t,e,i,s),e.lineTo(n+l-c,o),e.quadraticCurveTo(n+l,o,n+l,o+c),"center"===r&&"right"===a&&this.drawCaret(t,e,i,s),e.lineTo(n+l,o+h-f),e.quadraticCurveTo(n+l,o+h,n+l-f,o+h),"bottom"===r&&this.drawCaret(t,e,i,s),e.lineTo(n+u,o+h),e.quadraticCurveTo(n,o+h,n,o+h-u),"center"===r&&"left"===a&&this.drawCaret(t,e,i,s),e.lineTo(n,o+d),e.quadraticCurveTo(n,o,n+d,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,a=i&&i.y;if(s||a){let i=al[t.position].call(this,this._active,this._eventPosition);if(!i)return;let r=this._size=ac(this,t),n=Object.assign({},i,this._size),o=au(e,t,n),l=af(t,n,o,e);(s._to!==l.x||a._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=r.width,this.height=r.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},a={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let r=ex(e.padding),n=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&n&&(t.save(),t.globalAlpha=i,this.drawBackground(a,t,s,e),e0(t,e.textDirection),a.y+=r.top,this.drawTitle(a,t,e),this.drawBody(a,t,e),this.drawFooter(a,t,e),e1(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),a=!K(i,s),r=this._positionChanged(s,e);(a||r)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,a=this._active||[],r=this._getActiveElements(t,a,e,i),n=this._positionChanged(r,t),o=e||!K(r,a)||n;return o&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let a=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let r=this.chart.getElementsAtEventForMode(t,a.mode,a,i);return a.reverse&&r.reverse(),r}_positionChanged(t,e){let{caretX:i,caretY:s,options:a}=this,r=al[a.position].call(this,t,e);return!1!==r&&(i!==r.x||s!==r.y)}}let a_=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),ay=(t,e)=>null===t?null:tT(Math.round(t),0,e);function av(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class aM extends sc{static id="category";static defaults={ticks:{callback:av}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(V(t))return null;let i=this.getLabels();return ay(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let a=t.indexOf(e);return -1===a?a_(t,e,i,s):a!==t.lastIndexOf(e)?i:a}(i,t,U(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],a=this.getLabels();a=0===t&&e===a.length-1?a:a.slice(t,e+1),this._valueRange=Math.max(a.length-!i,1),this._startValue=this.min-.5*!!i;for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return av.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function aw(t,e,{horizontal:i,minRotation:s}){let a=tw(s),r=(i?Math.sin(a):Math.cos(a))||.001,n=.75*e*(""+t).length;return Math.min(e/r,n)}class ak extends sc{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return V(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:a}=this,r=t=>s=e?s:t,n=t=>a=i?a:t;if(t){let t=tx(s),e=tx(a);t<0&&e<0?n(0):t>0&&e>0&&r(0)}if(s===a){let e=0===a?1:Math.abs(.05*a);n(a+e),t||r(s-e)}this.min=s,this.max=a}getTickLimit(){let t,{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,a,r,n=[],{bounds:o,step:l,min:h,max:d,precision:c,count:u,maxTicks:f,maxDigits:g,includeBounds:p}=t,m=l||1,b=f-1,{min:x,max:_}=e,y=!V(h),v=!V(d),M=!V(u),w=(_-x)/(g+1),k=ty((_-x)/b/m)*m;if(k<1e-14&&!y&&!v)return[{value:x},{value:_}];(r=Math.ceil(_/k)-Math.floor(x/k))>b&&(k=ty(r*k/b/m)*m),V(c)||(k=Math.ceil(k*(i=Math.pow(10,c)))/i),"ticks"===o?(s=Math.floor(x/k)*k,a=Math.ceil(_/k)*k):(s=x,a=_),y&&v&&l&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((d-h)/l,k/1e3)?(r=Math.round(Math.min((d-h)/k,f)),k=(d-h)/r,s=h,a=d):M?(s=y?h:s,k=((a=v?d:a)-s)/(r=u-1)):r=t_(r=(a-s)/k,Math.round(r),k/1e3)?Math.round(r):Math.ceil(r);let P=Math.max(tk(k),tk(s));s=Math.round(s*(i=Math.pow(10,V(c)?P:c)))/i,a=Math.round(a*i)/i;let S=0;for(y&&(p&&s!==h?(n.push({value:h}),s<h&&S++,t_(Math.round((s+S*k)*i)/i,h,aw(h,w,t))&&S++):s<h&&S++);S<r;++S){let t=Math.round((s+S*k)*i)/i;if(v&&t>d)break;n.push({value:t})}return v&&p&&a!==d?n.length&&t_(n[n.length-1].value,d,aw(d,w,t))?n[n.length-1].value=d:n.push({value:d}):v&&a!==d||n.push({value:a}),n}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&tM(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return t1(t,this.chart.options.locale,this.options.ticks.format)}}class aP extends ak{static id="linear";static defaults={ticks:{callback:t5.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=H(t)?t:0,this.max=H(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=tw(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001;return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let aS=t=>Math.floor(tb(t)),aC=(t,e)=>Math.pow(10,aS(t)+e);function aD(t){return 1==t/Math.pow(10,aS(t))}function aO(t,e,i){let s=Math.pow(10,i),a=Math.floor(t/s);return Math.ceil(e/s)-a}class aT extends sc{static id="logarithmic";static defaults={ticks:{callback:t5.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=ak.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return H(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=H(t)?Math.max(0,t):null,this.max=H(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!H(this._userMin)&&(this.min=t===aC(this.min,0)?aC(this.min,-1):aC(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,a=e=>i=t?i:e,r=t=>s=e?s:t;i===s&&(i<=0?(a(1),r(10)):(a(aC(i,-1)),r(aC(s,1)))),i<=0&&a(aC(s,-1)),s<=0&&r(aC(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=$(t.min,e);let s=[],a=aS(e),r=function(t,e){let i=aS(e-t);for(;aO(t,e,i)>10;)i++;for(;10>aO(t,e,i);)i--;return Math.min(i,aS(t))}(e,i),n=r<0?Math.pow(10,Math.abs(r)):1,o=Math.pow(10,r),l=a>r?Math.pow(10,a):0,h=Math.round((e-l)*n)/n,d=Math.floor((e-l)/o/10)*o*10,c=Math.floor((h-d)/Math.pow(10,r)),u=$(t.min,Math.round((l+d+c*Math.pow(10,r))*n)/n);for(;u<i;)s.push({value:u,major:aD(u),significand:c}),c>=10?c=c<15?15:20:c++,c>=20&&(c=2,n=++r>=0?1:n),u=Math.round((l+d+c*Math.pow(10,r))*n)/n;let f=$(t.max,u);return s.push({value:f,major:aD(f),significand:c}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&tM(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":t1(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=tb(t),this._valueRange=tb(this.max)-tb(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(tb(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function aA(t){let e=t.ticks;if(e.display&&t.display){let t=ex(e.backdropPadding);return U(e.font&&e.font.size,t7.font.size)+t.height}return 0}function aL(t,e,i,s,a){return t===s||t===a?{start:e-i/2,end:e+i/2}:t<s||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function aE(t,e,i,s){let{ctx:a}=t;if(i)a.arc(t.xCenter,t.yCenter,e,0,td);else{let i=t.getPointPosition(0,e);a.moveTo(i.x,i.y);for(let r=1;r<s;r++)i=t.getPointPosition(r,e),a.lineTo(i.x,i.y)}}class aR extends ak{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:t5.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=ex(aA(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=H(t)&&!isNaN(t)?t:0,this.max=H(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/aA(this.options))}generateTickLabels(t){ak.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=X(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],a=[],r=t._pointLabels.length,n=t.options.pointLabels,o=n.centerPointLabels?th/r:0;for(let d=0;d<r;d++){var l,h;let r=n.setContext(t.getPointLabelContext(d));a[d]=r.padding;let c=t.getPointPosition(d,t.drawingArea+a[d],o),u=e_(r.font),f=(l=t.ctx,h=B(h=t._pointLabels[d])?h:[h],{w:function(t,e,i,s){let a,r,n,o,l,h=(s=s||{}).data=s.data||{},d=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},d=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let c=0,u=i.length;for(a=0;a<u;a++)if(null==(o=i[a])||B(o)){if(B(o))for(r=0,n=o.length;r<n;r++)null==(l=o[r])||B(l)||(c=et(t,h,d,c,l))}else c=et(t,h,d,c,o);t.restore();let f=d.length/2;if(f>i.length){for(a=0;a<f;a++)delete h[d[a]];d.splice(0,f)}return c}(l,u.string,h),h:h.length*u.lineHeight});s[d]=f;let g=tD(t.getIndexAngle(d)+o),p=Math.round(180/th*g);!function(t,e,i,s,a){let r=Math.abs(Math.sin(i)),n=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/r,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/r,t.r=Math.max(t.r,e.r+o)),a.start<e.t?(l=(e.t-a.start)/n,t.t=Math.min(t.t,e.t-l)):a.end>e.b&&(l=(a.end-e.b)/n,t.b=Math.max(t.b,e.b+l))}(i,e,g,aL(p,c.x,f.w,0,180),aL(p,c.y,f.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s,a=[],r=t._pointLabels.length,n=t.options,{centerPointLabels:o,display:l}=n.pointLabels,h={extra:aA(n)/2,additionalAngle:o?th/r:0};for(let n=0;n<r;n++){h.padding=i[n],h.size=e[n];let r=function(t,e,i){var s,a,r,n,o,l,h;let d=t.drawingArea,{extra:c,additionalAngle:u,padding:f,size:g}=i,p=t.getPointPosition(e,d+c+f,u),m=Math.round(180/th*tD(p.angle+tg)),b=(s=p.y,a=g.h,90===(r=m)||270===r?s-=a/2:(r>270||r<90)&&(s-=a),s),x=0===(n=m)||180===n?"center":n<180?"left":"right",_=(o=p.x,l=g.w,"right"===(h=x)?o-=l:"center"===h&&(o-=l/2),o);return{visible:!0,x:p.x,y:b,textAlign:x,left:_,top:b,right:_+g.w,bottom:b+g.h}}(t,n,h);a.push(r),"auto"===l&&(r.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:a,bottom:r}=t;return!(er({x:i,y:s},e)||er({x:i,y:r},e)||er({x:a,y:s},e)||er({x:a,y:r},e))}(r,s),r.visible&&(s=r))}return a}(t,s,a)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return tD(t*(td/(this._pointLabels.length||1))+tw(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(V(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(V(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){var i;let s=e[t];return i=this.getContext(),ev(i,{label:s,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-tg+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:a}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:a}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),aE(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i,s=this.ctx,a=this.options,{angleLines:r,grid:n,border:o}=a,l=this._pointLabels.length;if(a.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let a=e-1;a>=0;a--){let e=t._pointLabelItems[a];if(!e.visible)continue;let r=s.setContext(t.getPointLabelContext(a));!function(t,e,i){let{left:s,top:a,right:r,bottom:n}=i,{backdropColor:o}=e;if(!V(o)){let i=eb(e.borderRadius),l=ex(e.backdropPadding);t.fillStyle=o;let h=s-l.left,d=a-l.top,c=r-s+l.width,u=n-a+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),ec(t,{x:h,y:d,w:c,h:u,radius:i}),t.fill()):t.fillRect(h,d,c,u)}}(i,r,e);let n=e_(r.font),{x:o,y:l,textAlign:h}=e;ed(i,t._pointLabels[a],o,l+n.lineHeight/2,n,{color:r.color,textAlign:h,textBaseline:"middle"})}}(this,l),n.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),a=n.setContext(s),r=o.setContext(s);!function(t,e,i,s,a){let r=t.ctx,n=e.circular,{color:o,lineWidth:l}=e;(n||s)&&o&&l&&!(i<0)&&(r.save(),r.strokeStyle=o,r.lineWidth=l,r.setLineDash(a.dash||[]),r.lineDashOffset=a.dashOffset,r.beginPath(),aE(t,i,n,s),r.closePath(),r.stroke(),r.restore())}(this,a,e,l,r)}}),r.display){for(s.save(),t=l-1;t>=0;t--){let n=r.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=n;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(n.borderDash),s.lineDashOffset=n.borderDashOffset,e=this.getDistanceFromCenterForValue(a.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e,i=this.ctx,s=this.options,a=s.ticks;if(!a.display)return;let r=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(r),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((r,n)=>{if(0===n&&this.min>=0&&!s.reverse)return;let o=a.setContext(this.getContext(n)),l=e_(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[n].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(r.label).width,i.fillStyle=o.backdropColor;let s=ex(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}ed(i,r.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let aI={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},aF=Object.keys(aI);function az(t,e){return t-e}function aN(t,e){if(V(e))return null;let i=t._adapter,{parser:s,round:a,isoWeekday:r}=t._parseOpts,n=e;return("function"==typeof s&&(n=s(n)),H(n)||(n="string"==typeof s?i.parse(n,s):i.parse(n)),null===n)?null:(a&&(n="week"===a&&(tv(r)||!0===r)?i.startOf(n,"isoWeek",r):i.startOf(n,a)),+n)}function aj(t,e,i,s){let a=aF.length;for(let r=aF.indexOf(t);r<a-1;++r){let t=aI[aF[r]],a=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(a*t.size))<=s)return aF[r]}return aF[a-1]}function aV(t,e,i){if(i){if(i.length){let{lo:s,hi:a}=tL(i,e);t[i[s]>=e?i[s]:i[a]]=!0}}else t[e]=!0}function aB(t,e,i){let s,a,r=[],n={},o=e.length;for(s=0;s<o;++s)n[a=e[s]]=s,r.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){let a,r,n=t._adapter,o=+n.startOf(e[0].value,s),l=e[e.length-1].value;for(a=o;a<=l;a=+n.add(a,1,s))(r=i[a])>=0&&(e[r].major=!0);return e}(t,r,n,i):r}class aW extends sc{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new iI._date(t.adapters.date);s.init(e),te(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:aN(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:a,minDefined:r,maxDefined:n}=this.getUserBounds();function o(t){r||isNaN(t.min)||(s=Math.min(s,t.min)),n||isNaN(t.max)||(a=Math.max(a,t.max))}r&&n||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=H(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),a=H(a)&&!isNaN(a)?a:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,a-1),this.max=Math.max(s+1,a)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let a=this.min,r=function(t,e,i){let s=0,a=t.length;for(;s<a&&t[s]<e;)s++;for(;a>s&&t[a-1]>i;)a--;return s>0||a<t.length?t.slice(s,a):t}(s,a,this.max);return this._unit=e.unit||(i.autoSkip?aj(e.minUnit,this.min,this.max,this._getLabelCapacity(a)):function(t,e,i,s,a){for(let r=aF.length-1;r>=aF.indexOf(i);r--){let i=aF[r];if(aI[i].common&&t._adapter.diff(a,s,i)>=e-1)return i}return aF[i?aF.indexOf(i):0]}(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=aF.indexOf(t)+1,i=aF.length;e<i;++e)if(aI[aF[e]].common)return aF[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&r.reverse(),aB(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,a=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),a=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let r=t.length<3?.5:.25;s=tT(s,0,r),a=tT(a,0,r),this._offsets={start:s,end:a,factor:1/(s+1+a)}}_generate(){let t,e,i=this._adapter,s=this.min,a=this.max,r=this.options,n=r.time,o=n.unit||aj(n.minUnit,s,a,this._getLabelCapacity(s)),l=U(r.ticks.stepSize,1),h="week"===o&&n.isoWeekday,d=tv(h)||!0===h,c={},u=s;if(d&&(u=+i.startOf(u,"isoWeek",h)),u=+i.startOf(u,d?"day":o),i.diff(a,s,o)>1e5*l)throw Error(s+" and "+a+" are too far apart with stepSize of "+l+" "+o);let f="data"===r.ticks.source&&this.getDataTimestamps();for(t=u,e=0;t<a;t=+i.add(t,l,o),e++)aV(c,t,f);return(t===a||"ticks"===r.bounds||1===e)&&aV(c,t,f),Object.keys(c).sort(az).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,a=e||i[s];return this._adapter.format(t,a)}_tickFormatFunction(t,e,i,s){let a=this.options,r=a.ticks.callback;if(r)return X(r,[t,e,i],this);let n=a.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&n[o],d=l&&n[l],c=i[e],u=l&&d&&c&&c.major;return this._adapter.format(t,s||(u?d:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=tw(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(s),r=Math.sin(s),n=this._resolveTickFontOptions(0).size;return{w:i*a+n*r,h:i*r+n*a}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,a=this._tickFormatFunction(t,0,aB(this,[t],this._majorUnit),s),r=this._getLabelSize(a),n=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return n>0?n:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e,i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(aN(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return tz(t.sort(az))}}function aH(t,e,i){let s,a,r,n,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=tE(t,"pos",e)),{pos:s,time:r}=t[o],{pos:a,time:n}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=tE(t,"time",e)),{time:s,pos:r}=t[o],{time:a,pos:n}=t[l]);let h=a-s;return h?r+(n-r)*(e-s)/h:r}class a$ extends aW{static id="timeseries";static defaults=aW.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=aH(e,this.min),this._tableRange=aH(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s,{min:a,max:r}=this,n=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=a&&s<=r&&n.push(s);if(n.length<2)return[{time:a,pos:0},{time:r,pos:1}];for(e=0,i=n.length;e<i;++e)Math.round((n[e+1]+n[e-1])/2)!==(s=n[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(aH(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return aH(this._table,i*this._tableRange+this._minPos,!0)}}var aU=i(1891),aY=i(5356);function aq({isOpen:t,title:e,message:i,confirmText:s="Confirm",cancelText:r="Cancel",onConfirm:n,onCancel:o,type:l="info"}){if(!t)return null;let h=(()=>{switch(l){case"warning":return{icon:"⚠️",iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmBg:"bg-yellow-600 hover:bg-yellow-700",titleColor:"text-yellow-800"};case"danger":return{icon:"\uD83D\uDEA8",iconBg:"bg-red-100",iconColor:"text-red-600",confirmBg:"bg-red-600 hover:bg-red-700",titleColor:"text-red-800"};case"success":return{icon:"✅",iconBg:"bg-green-100",iconColor:"text-green-600",confirmBg:"bg-green-600 hover:bg-green-700",titleColor:"text-green-800"};default:return{icon:"\uD83D\uDCB0",iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmBg:"bg-blue-600 hover:bg-blue-700",titleColor:"text-blue-800"}}})();return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out scale-100",children:[(0,a.jsx)("div",{className:"p-6 pb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-full ${h.iconBg} flex items-center justify-center flex-shrink-0`,children:(0,a.jsx)("span",{className:"text-2xl",children:h.icon})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("h3",{className:`text-xl font-bold ${h.titleColor} leading-tight`,children:e})})]})}),(0,a.jsx)("div",{className:"px-6 pb-6",children:(0,a.jsx)("p",{className:"text-gray-700 text-base leading-relaxed",children:i})}),(0,a.jsxs)("div",{className:"px-6 pb-6 flex gap-3 justify-end",children:[(0,a.jsx)("button",{onClick:o,className:"px-6 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300",children:r}),(0,a.jsx)("button",{onClick:n,className:`px-6 py-2.5 text-white ${h.confirmBg} rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg`,children:s})]})]})})}var aX=i(5475);function aG({user:t}){let[e,i]=(0,r.useState)(null),[s,n]=(0,r.useState)(""),[h,d]=(0,r.useState)(1),[c,u]=(0,r.useState)("products"),[f,g]=(0,r.useState)(0),[p,m]=(0,r.useState)(!1),[b,x]=(0,r.useState)(!1),[_,y]=(0,r.useState)(null);(0,l.useRouter)();let v=()=>{g(JSON.parse(localStorage.getItem("cart")||"[]").length)};async function M(t){if(!t)return;let e=localStorage.getItem("token");try{if(!(await fetch((0,aX.e9)(`${aX.i3.ENDPOINTS.ADMIN.ORDER_COLLECTED}/${t}/collected`),{method:"PUT",headers:{Authorization:`Bearer ${e}`}})).ok)throw Error("Failed to mark as collected");i(e=>e&&e.orders?{...e,orders:e.orders.map(e=>e.id===t?{...e,status:"COLLECTED"}:e),stats:{...e.stats,collectedOrders:(e.stats?.collectedOrders??0)+1}}:e)}catch{alert("Error updating order status")}}if(s)return(0,a.jsx)("div",{className:"text-red-600 text-center mt-16",children:s});if(!e)return(0,a.jsx)("div",{className:"text-center mt-16",children:"Loading..."});let w=e.products||[];return w.length,w.slice((h-1)*5,5*h),e.stats?.orders??e.orders?.length,e.stats?.collectedOrders??e.orders?.filter(t=>"COLLECTED"===t.status).length,(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,a.jsx)(aU.A,{cartCount:f,onCartClick:()=>{m(!0)}}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,a.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Admin"}),(0,a.jsx)("span",{className:"text-white",children:" Dashboard"})]}),(0,a.jsxs)("p",{className:"text-xl text-gray-200 mb-4 max-w-2xl mx-auto",children:["Welcome back, ",t?.name||t?.email,"! Manage your Six Light Media store with powerful tools."]}),(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,a.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDD12 Secure Admin Access"})]})]})})]}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"✅ Server-side authentication active"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDEA7 Dashboard Under Construction"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"The admin dashboard is being migrated to use secure server-side authentication."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)(o(),{href:"/admin/products",className:"p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCE6"}),(0,a.jsx)("div",{className:"font-semibold text-blue-900",children:"Manage Products"})]}),(0,a.jsxs)(o(),{href:"/admin/orders",className:"p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-colors",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCCB"}),(0,a.jsx)("div",{className:"font-semibold text-green-900",children:"View Orders"})]}),(0,a.jsxs)(o(),{href:"/admin/categories",className:"p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFF7️"}),(0,a.jsx)("div",{className:"font-semibold text-purple-900",children:"Categories"})]})]})]})]}),(0,a.jsx)(aY.A,{isOpen:p,onClose:()=>{m(!1),v()}}),(0,a.jsx)(aq,{isOpen:b,title:"Confirm Order Collection",message:"Has the customer paid and collected this order? This action will mark the order as completed.",confirmText:"Yes, Mark as Collected",cancelText:"Cancel",onConfirm:()=>{_&&M(_),x(!1),y(null)},onCancel:()=>{x(!1),y(null)},type:"info"})]})}sz.register(sB,{id:"tooltip",_element:ax,positioners:al,afterInit(t,e,i){i&&(t.tooltip=new ax({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:am},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},{id:"legend",_element:an,start(t,e,i){let s=t.legend=new an({ctx:t.ctx,options:i,chart:t});iK.configure(t,s,i),iK.addBox(t,s)},stop(t){iK.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;iK.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,a=i.chart;a.isDatasetVisible(s)?(a.hide(s),e.hidden=!0):(a.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:a,color:r,useBorderRadius:n,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=ex(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:r,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:a||l.textAlign,borderRadius:n&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}})},3873:t=>{"use strict";t.exports=require("path")},6189:(t,e,i)=>{"use strict";var s=i(5773);i.o(s,"useParams")&&i.d(e,{useParams:function(){return s.useParams}}),i.o(s,"useRouter")&&i.d(e,{useRouter:function(){return s.useRouter}}),i.o(s,"useSearchParams")&&i.d(e,{useSearchParams:function(){return s.useSearchParams}})},6705:(t,e,i)=>{Promise.resolve().then(i.bind(i,504))},8414:(t,e,i)=>{Promise.resolve().then(i.bind(i,3544))},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9144:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>h});var s=i(5239),a=i(8088),r=i(8170),n=i.n(r),o=i(893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1031)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\dashboard\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[728,474,814,994,834],()=>i(9144));module.exports=s})();