(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[503],{2783:(e,t,r)=>{Promise.resolve().then(r.bind(r,6445))},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},6445:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(5155),l=r(2115),a=r(6766),i=r(6874),o=r.n(i),n=r(4615),d=r(3389);function c(){let[e,t]=(0,l.useState)(null),[r,i]=(0,l.useState)([]),[c,x]=(0,l.useState)(""),[m,h]=(0,l.useState)(0),[u,b]=(0,l.useState)(!1),[g,f]=(0,l.useState)(!0),j=()=>{h(JSON.parse(localStorage.getItem("cart")||"[]").length)},p=()=>{b(!0)};return((0,l.useEffect)(()=>{j()},[]),(0,l.useEffect)(()=>{let e=localStorage.getItem("token");if(!e){x("Not authenticated"),f(!1);return}fetch("http://localhost:3001/user/dashboard",{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{e&&e.user?(t(e.user),i(e.orders||[])):x(e.error||"Failed to load user info"),f(!1)}).catch(()=>{x("Failed to load user info"),f(!1)})},[]),g)?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:p}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading dashboard..."})]})})]}):c?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:p}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Error"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:c}),(0,s.jsxs)(o(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Home"]})]})})]}):e?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:p}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"w-24 h-24 mx-auto mb-6 relative",children:[(0,s.jsx)(a.default,{src:e.profileImage||"/usericon.png",alt:"Profile",width:96,height:96,className:"w-full h-full rounded-full object-cover border-4 border-white/20 shadow-2xl"}),(0,s.jsx)("div",{className:"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})]})}),(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:["Welcome back,",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:e.name||e.email.split("@")[0]})]}),(0,s.jsxs)("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto",children:[e.email," • ",e.role]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,s.jsx)(o(),{href:"/user/profile",className:"px-8 py-4 bg-white/10 backdrop-blur-lg text-white font-semibold rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300",children:"⚙️ Edit Profile"}),(0,s.jsx)(o(),{href:"/",className:"px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold rounded-2xl hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 shadow-lg",children:"\uD83D\uDECD️ Continue Shopping"})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-blue-700 bg-blue-100 px-3 py-1 rounded-full",children:"Total Orders"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-blue-700 mb-2",children:r.length}),(0,s.jsx)("div",{className:"text-sm text-blue-600",children:"Orders placed"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-green-700 bg-green-100 px-3 py-1 rounded-full",children:"Completed"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-green-700 mb-2",children:r.filter(e=>"COLLECTED"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-green-600",children:"Orders collected"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-yellow-700 bg-yellow-100 px-3 py-1 rounded-full",children:"Pending"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-yellow-700 mb-2",children:r.filter(e=>"PENDING"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-yellow-600",children:"Awaiting collection"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:"\uD83D\uDCCB Order History"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-100 px-4 py-2 rounded-xl",children:[r.length," ",1===r.length?"order":"orders"]})]}),0===r.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Start shopping to see your orders here."}),(0,s.jsx)(o(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:"\uD83D\uDECD️ Start Shopping"})]}):(0,s.jsx)("div",{className:"space-y-4",children:r.map(e=>{var t,r,l,i,o;return(0,s.jsxs)("div",{className:"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-white rounded-xl overflow-hidden border border-gray-200 shadow-sm",children:(0,s.jsx)(a.default,{src:(null==(t=e.product)?void 0:t.image)||"/bottle-dummy.jpg",alt:(null==(r=e.product)?void 0:r.name)||"Product",width:64,height:64,className:"w-full h-full object-contain"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-gray-900 text-lg",children:(null==(l=e.product)?void 0:l.name)||"Product"}),(0,s.jsxs)("p",{className:"text-green-600 font-semibold",children:["K",null!=(o=null==(i=e.product)?void 0:i.price)?o:0]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ".concat("COLLECTED"===e.status?"bg-green-100 text-green-700":"PENDING"===e.status?"bg-yellow-100 text-yellow-700":"bg-gray-100 text-gray-700"),children:"COLLECTED"===e.status?"✅ Collected":"PENDING"===e.status?"⏳ Pending":e.status}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:new Date(e.createdAt).toLocaleDateString()})]})]}),e.customization&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-xl border border-blue-200",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-blue-900 mb-2",children:"\uD83C\uDFA8 Customization Details"}),(0,s.jsxs)("div",{className:"space-y-1",children:[e.customization.color&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-blue-700",children:[(0,s.jsx)("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:e.customization.color}}),(0,s.jsxs)("span",{children:["Color: ",e.customization.color]})]}),e.customization.text&&(0,s.jsxs)("div",{className:"text-sm text-blue-700",children:["\uD83D\uDCDD Text: “",e.customization.text,"”"]})]})]})]},e.id)})})]})]}),(0,s.jsx)(d.A,{isOpen:u,onClose:()=>{b(!1),j()}})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(n.A,{cartCount:m,onCartClick:p}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading user data..."})]})})]})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:c="",children:x,iconNode:m,...h}=e;return(0,s.createElement)("svg",{ref:t,...d,width:l,height:l,stroke:r,strokeWidth:i?24*Number(a)/Number(l):a,className:o("lucide",c),...!x&&!n(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let r=(0,s.forwardRef)((r,a)=>{let{className:n,...d}=r;return(0,s.createElement)(c,{ref:a,iconNode:t,className:o("lucide-".concat(l(i(e))),"lucide-".concat(e),n),...d})});return r.displayName=i(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,211,441,684,358],()=>t(2783)),_N_E=e.O()}]);