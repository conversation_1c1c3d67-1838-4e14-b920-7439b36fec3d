import type { NextApiRequest, NextApiResponse } from "next";
import ImageKit from "imagekit";

const imagekit = new ImageKit({
  publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY!,
  privateKey: process.env.IMAGEKIT_PRIVATE_KEY!,
  urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT!,
});

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const token = imagekit.getAuthenticationParameters();
    res.status(200).json(token);
  } catch (error) {
    res.status(500).json({
      error: "ImageKit server error",
      details: (error as Error).message,
    });
  }
}
