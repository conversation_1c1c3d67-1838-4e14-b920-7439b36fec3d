{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACjB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,6EAAgE;IAClE,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport default function Header({\r\n  cartCount = 0,\r\n  onCartClick,\r\n}: {\r\n  cartCount?: number;\r\n  onCartClick?: () => void;\r\n}) {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n  const [userRole, setUserRole] = useState<string | null>(null);\r\n  const [profileImage, setProfileImage] = useState<string | null>(null);\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function handleProfileImageUpdated() {\r\n      const token = localStorage.getItem(\"token\");\r\n      if (token) {\r\n        try {\r\n          // Fetch latest user data from API instead of relying on JWT token\r\n          const response = await fetch(\r\n            getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD),\r\n            {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n          );\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data && data.user) {\r\n              setProfileImage(data.user.profileImage || null);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Failed to fetch updated profile image:\", error);\r\n          // Fallback to JWT token method\r\n          try {\r\n            const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n            setProfileImage(payload.profileImage || null);\r\n          } catch {\r\n            setProfileImage(null);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n      const token = localStorage.getItem(\"token\");\r\n      setIsLoggedIn(!!token);\r\n      if (token) {\r\n        // Decode JWT to get role (simple base64 decode, not secure for prod, but fine for client display)\r\n        try {\r\n          const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n          setUserRole(payload.role || null);\r\n          setProfileImage(payload.profileImage || null);\r\n        } catch {\r\n          setUserRole(null);\r\n          setProfileImage(null);\r\n        }\r\n      } else {\r\n        setUserRole(null);\r\n        setProfileImage(null);\r\n      }\r\n      // Listen for profile image update event\r\n      window.addEventListener(\"profileImageUpdated\", handleProfileImageUpdated);\r\n    }\r\n    // Cleanup event listener\r\n    return () => {\r\n      window.removeEventListener(\r\n        \"profileImageUpdated\",\r\n        handleProfileImageUpdated\r\n      );\r\n    };\r\n  }, []);\r\n\r\n  // Prevent background scroll and horizontal scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n      document.documentElement.style.overflowX = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close menus on Escape key\r\n  useEffect(() => {\r\n    function handleKeyDown(e: KeyboardEvent) {\r\n      if (e.key === \"Escape\") {\r\n        setShowDropdown(false);\r\n        setMobileMenuOpen(false);\r\n      }\r\n    }\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, []);\r\n\r\n  // Ensure dropdown closes when mobile menu opens\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) setShowDropdown(false);\r\n  }, [mobileMenuOpen]);\r\n\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"token\");\r\n    setIsLoggedIn(false);\r\n    window.location.href = \"/login\";\r\n  }\r\n\r\n  function handleDropdownToggle() {\r\n    setShowDropdown((prev) => !prev);\r\n  }\r\n\r\n  function handleDropdownClose() {\r\n    setShowDropdown(false);\r\n  }\r\n\r\n  return (\r\n    <header className=\"w-full bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-30\">\r\n      <nav className=\"max-w-7xl mx-auto flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 md:py-4\">\r\n        {/* Brand Logo */}\r\n        <Link href=\"/\" className=\"flex items-center gap-2 min-w-0 group\">\r\n          <div className=\"relative\">\r\n            <Image\r\n              src=\"/6 Light Logo.png\"\r\n              alt=\"Six Light Media Logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"h-8 w-auto sm:h-10 md:h-12 transition-transform duration-300 group-hover:scale-110\"\r\n              priority\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl\"></div>\r\n          </div>\r\n          <span className=\"truncate text-base xs:text-lg sm:text-2xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-indigo-500 group-hover:via-purple-500 group-hover:to-pink-500 transition-all duration-300\">\r\n            Store\r\n          </span>\r\n        </Link>\r\n        {/* Desktop Nav */}\r\n        <div className=\"hidden sm:flex gap-2 md:gap-4 lg:gap-6 items-center min-w-0\">\r\n          <Link\r\n            href=\"/\"\r\n            className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n          >\r\n            🏠 Home\r\n          </Link>\r\n          {isLoggedIn && (\r\n            <Link\r\n              href=\"/user/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n            >\r\n              📊 Dashboard\r\n            </Link>\r\n          )}\r\n          {isLoggedIn && userRole === \"ADMIN\" && (\r\n            <Link\r\n              href=\"/admin/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n            >\r\n              ⚡ Admin\r\n            </Link>\r\n          )}\r\n          {!isLoggedIn ? (\r\n            <>\r\n              <Link\r\n                href=\"/login\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                🔐 Login\r\n              </Link>\r\n              <Link\r\n                href=\"/register\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                ✨ Register\r\n              </Link>\r\n            </>\r\n          ) : (\r\n            <div className=\"relative min-w-0\">\r\n              <button\r\n                className=\"flex items-center gap-2 focus:outline-none min-w-0 px-2 py-1 rounded-2xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                onClick={handleDropdownToggle}\r\n                aria-label=\"Open user menu\"\r\n              >\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                    className=\"rounded-full border-2 border-gradient-to-r from-indigo-200 to-purple-200 object-cover bg-white h-8 w-8 md:h-9 md:w-9 shadow-md\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <span className=\"hidden md:inline font-semibold text-gray-700 truncate max-w-[80px]\">\r\n                  Account\r\n                </span>\r\n                <svg\r\n                  className={`w-4 h-4 ml-1 text-gray-600 transition-transform duration-300 ${\r\n                    showDropdown ? \"rotate-180\" : \"\"\r\n                  }`}\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M19 9l-7 7-7-7\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n              {showDropdown && (\r\n                <div\r\n                  className=\"absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-lg border border-gray-200 rounded-2xl shadow-2xl z-50 overflow-hidden\"\r\n                  onMouseLeave={handleDropdownClose}\r\n                >\r\n                  <div className=\"p-2\">\r\n                    <Link\r\n                      href=\"/user/dashboard\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">📊</span>\r\n                      Dashboard\r\n                    </Link>\r\n                    <Link\r\n                      href=\"/user/profile\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">⚙️</span>\r\n                      Profile Settings\r\n                    </Link>\r\n                    <div className=\"border-t border-gray-100 my-2\"></div>\r\n                    <button\r\n                      onClick={() => {\r\n                        handleLogout();\r\n                        handleDropdownClose();\r\n                      }}\r\n                      className=\"flex items-center gap-3 w-full text-left px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                    >\r\n                      <span className=\"text-lg\">🚪</span>\r\n                      Logout\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {/* Responsive Mobile Menu Button */}\r\n        <button\r\n          type=\"button\"\r\n          className=\"flex items-center justify-center p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 sm:hidden hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300\"\r\n          aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\r\n          onClick={() => setMobileMenuOpen((open) => !open)}\r\n        >\r\n          {mobileMenuOpen ? (\r\n            <X\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={!mobileMenuOpen}\r\n            />\r\n          ) : (\r\n            <Menu\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={mobileMenuOpen}\r\n            />\r\n          )}\r\n        </button>\r\n        {/* Cart Button */}\r\n        <button\r\n          className=\"relative px-3 py-1 md:px-4 md:py-2 rounded-2xl font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg hover:from-red-600 hover:to-pink-700 hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 ml-1 md:ml-2 text-sm md:text-base transform hover:-translate-y-0.5\"\r\n          onClick={onCartClick}\r\n          aria-label=\"Open cart\"\r\n        >\r\n          <span className=\"hidden sm:inline\">🛒 Cart</span>\r\n          <span className=\"sm:hidden\">🛒</span>\r\n          <span className=\"sm:ml-1\">({cartCount})</span>\r\n          {cartCount > 0 && (\r\n            <span className=\"absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs rounded-full px-1.5 py-0.5 animate-bounce font-bold shadow-lg\">\r\n              {cartCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </nav>\r\n      {/* Mobile Nav Drawer Overlay & Drawer: Only render when open */}\r\n      {mobileMenuOpen && (\r\n        <>\r\n          {/* Overlay */}\r\n          <div\r\n            className=\"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300 block\"\r\n            style={{ left: 0, right: 0 }}\r\n            onClick={() => setMobileMenuOpen(false)}\r\n            aria-hidden={!mobileMenuOpen}\r\n          />\r\n          {/* Drawer */}\r\n          <div\r\n            className=\"fixed top-0 right-0 z-50 w-full max-w-xs h-screen bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs min-w-0 sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden translate-x-0 border-l border-gray-200\"\r\n            role=\"dialog\"\r\n            aria-modal=\"true\"\r\n            tabIndex={-1}\r\n            style={{ right: 0, left: \"auto\" }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Tab\") {\r\n                // Basic focus trap: keep focus inside drawer\r\n                const focusable = Array.from(\r\n                  (e.currentTarget as HTMLElement).querySelectorAll(\r\n                    'a, button, input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n                  )\r\n                ) as HTMLElement[];\r\n                if (focusable.length === 0) return;\r\n                const first = focusable[0];\r\n                const last = focusable[focusable.length - 1];\r\n                if (!e.shiftKey && document.activeElement === last) {\r\n                  e.preventDefault();\r\n                  first.focus();\r\n                } else if (e.shiftKey && document.activeElement === first) {\r\n                  e.preventDefault();\r\n                  last.focus();\r\n                }\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center justify-between px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n              <span className=\"font-black text-lg bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\r\n                📱 Menu\r\n              </span>\r\n              <button\r\n                className=\"p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 hover:bg-white/50 transition-all duration-300\"\r\n                aria-label=\"Close menu\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n                tabIndex={mobileMenuOpen ? 0 : -1}\r\n              >\r\n                <X className=\"w-6 h-6 text-gray-700\" />\r\n              </button>\r\n            </div>\r\n            <nav className=\"flex flex-col gap-2 px-4 py-6 flex-1 overflow-y-auto max-h-[calc(100vh-80px)]\">\r\n              <Link\r\n                href=\"/\"\r\n                className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n              >\r\n                <span className=\"text-lg\">🏠</span>\r\n                Home\r\n              </Link>\r\n              {isLoggedIn && (\r\n                <Link\r\n                  href=\"/user/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">📊</span>\r\n                  Dashboard\r\n                </Link>\r\n              )}\r\n              {isLoggedIn && userRole === \"ADMIN\" && (\r\n                <Link\r\n                  href=\"/admin/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">⚡</span>\r\n                  Admin Panel\r\n                </Link>\r\n              )}\r\n              {!isLoggedIn ? (\r\n                <>\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">🔐</span>\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">✨</span>\r\n                    Register\r\n                  </Link>\r\n                </>\r\n              ) : (\r\n                <div className=\"flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200\">\r\n                  <Link\r\n                    href=\"/user/profile\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">⚙️</span>\r\n                    Profile Settings\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleLogout();\r\n                      setMobileMenuOpen(false);\r\n                    }}\r\n                    className=\"flex items-center gap-3 w-full text-left px-4 py-3 rounded-2xl font-semibold text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  >\r\n                    <span className=\"text-lg\">🚪</span>\r\n                    Logout\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </nav>\r\n            {isLoggedIn && (\r\n              <div className=\"flex items-center gap-3 px-4 py-4 border-t border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={40}\r\n                    height={40}\r\n                    className=\"rounded-full border-2 border-indigo-200 object-cover bg-white shadow-md w-10 h-10\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"font-bold text-gray-900 truncate text-sm\">\r\n                    Account\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-600 truncate\">Logged in</p>\r\n                  <span className=\"inline-block mt-1 px-2 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full\">\r\n                    {userRole}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS,OAAO,EAC7B,YAAY,CAAC,EACb,WAAW,EAIZ;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,IAAI;oBACF,kEAAkE;oBAClE,MAAM,WAAW,MAAM,MACrB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAC7C;wBACE,SAAS;4BAAE,eAAe,CAAC,OAAO,EAAE,OAAO;wBAAC;oBAC9C;oBAGF,IAAI,SAAS,EAAE,EAAE;wBACf,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,IAAI,QAAQ,KAAK,IAAI,EAAE;4BACrB,gBAAgB,KAAK,IAAI,CAAC,YAAY,IAAI;wBAC5C;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,+BAA+B;oBAC/B,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;wBACnD,gBAAgB,QAAQ,YAAY,IAAI;oBAC1C,EAAE,OAAM;wBACN,gBAAgB;oBAClB;gBACF;YACF;QACF;QACA,uCAAmC;;QAmBnC;QACA,yBAAyB;QACzB,OAAO;YACL,OAAO,mBAAmB,CACxB,uBACA;QAEJ;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;QAC7C,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;QAC7C;QACA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;QAC7C;IACF,GAAG;QAAC;KAAe;IAEnB,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,cAAc,CAAgB;YACrC,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,gBAAgB;gBAChB,kBAAkB;YACpB;QACF;QACA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,gBAAgB;IACtC,GAAG;QAAC;KAAe;IAEnB,SAAS;QACP,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,SAAS;QACP,gBAAgB,CAAC,OAAS,CAAC;IAC7B;IAEA,SAAS;QACP,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,QAAQ;;;;;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAK,WAAU;0CAA2Q;;;;;;;;;;;;kCAK7R,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,4BACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,cAAc,aAAa,yBAC1B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,CAAC,2BACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;6DAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;;0DAEX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,gBAAgB;wDACrB,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAK,WAAU;0DAAqE;;;;;;0DAGrF,8OAAC;gDACC,WAAW,CAAC,6DAA6D,EACvE,eAAe,eAAe,IAC9B;gDACF,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,SAAQ;0DAER,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;oCAIP,8BACC,8OAAC;wCACC,WAAU;wCACV,cAAc;kDAEd,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDACC,SAAS;wDACP;wDACA;oDACF;oDACA,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUjD,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,cAAY,iBAAiB,eAAe;wBAC5C,SAAS,IAAM,kBAAkB,CAAC,OAAS,CAAC;kCAE3C,+BACC,8OAAC,4LAAA,CAAA,IAAC;4BACA,WAAU;4BACV,eAAa,CAAC;;;;;iDAGhB,8OAAC,kMAAA,CAAA,OAAI;4BACH,WAAU;4BACV,eAAa;;;;;;;;;;;kCAKnB,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAW;;0CAEX,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,8OAAC;gCAAK,WAAU;0CAAY;;;;;;0CAC5B,8OAAC;gCAAK,WAAU;;oCAAU;oCAAE;oCAAU;;;;;;;4BACrC,YAAY,mBACX,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAMR,gCACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,MAAM;4BAAG,OAAO;wBAAE;wBAC3B,SAAS,IAAM,kBAAkB;wBACjC,eAAa,CAAC;;;;;;kCAGhB,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,cAAW;wBACX,UAAU,CAAC;wBACX,OAAO;4BAAE,OAAO;4BAAG,MAAM;wBAAO;wBAChC,WAAW,CAAC;4BACV,IAAI,EAAE,GAAG,KAAK,OAAO;gCACnB,6CAA6C;gCAC7C,MAAM,YAAY,MAAM,IAAI,CAC1B,AAAC,EAAE,aAAa,CAAiB,gBAAgB,CAC/C;gCAGJ,IAAI,UAAU,MAAM,KAAK,GAAG;gCAC5B,MAAM,QAAQ,SAAS,CAAC,EAAE;gCAC1B,MAAM,OAAO,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;gCAC5C,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,MAAM;oCAClD,EAAE,cAAc;oCAChB,MAAM,KAAK;gCACb,OAAO,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,OAAO;oCACzD,EAAE,cAAc;oCAChB,KAAK,KAAK;gCACZ;4BACF;wBACF;;0CAEA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAkG;;;;;;kDAGlH,8OAAC;wCACC,WAAU;wCACV,cAAW;wCACX,SAAS,IAAM,kBAAkB;wCACjC,UAAU,iBAAiB,IAAI,CAAC;kDAEhC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAGpC,4BACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAItC,cAAc,aAAa,yBAC1B,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAQ;;;;;;;oCAIrC,CAAC,2BACA;;0DACE,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;qEAKtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,8OAAC;gDACC,SAAS;oDACP;oDACA,kBAAkB;gDACpB;gDACA,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;4BAM1C,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,gBAAgB;gDACrB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,8OAAC;gDAAE,WAAU;0DAAiC;;;;;;0DAC9C,8OAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/OrderForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  price: number;\n  quantity?: number;\n};\n\ntype OrderFormProps = {\n  cartItems: CartItem[];\n  onOrderSuccess: () => void;\n  onCancel: () => void;\n};\n\ntype OrderFormData = {\n  customerName: string;\n  customerPhone: string;\n  customerAddress: string;\n};\n\nexport default function OrderForm({\n  cartItems,\n  onOrderSuccess,\n  onCancel,\n}: OrderFormProps) {\n  const [formData, setFormData] = useState<OrderFormData>({\n    customerName: \"\",\n    customerPhone: \"\",\n    customerAddress: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  useEffect(() => {\n    // Get user info to pre-fill name\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      fetch(`${API_URL}/user/dashboard`, {\n        headers: { Authorization: `Bearer ${token}` },\n      })\n        .then((res) => res.json())\n        .then((data) => {\n          if (data && data.user) {\n            setFormData((prev) => ({\n              ...prev,\n              customerName: data.user.name || \"\",\n            }));\n          }\n        })\n        .catch(() => {\n          // Ignore error, user can still fill form manually\n        });\n    }\n  }, []);\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setError(\"Please log in to place an order\");\n      setLoading(false);\n      return;\n    }\n\n    try {\n      // Create orders for each cart item\n      const orderPromises = cartItems.map((item) =>\n        fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.ORDERS), {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({\n            productId: item.productId,\n            customerName: formData.customerName,\n            customerPhone: formData.customerPhone,\n            customerAddress: formData.customerAddress,\n            customColor: item.color,\n            customText: item.text,\n            isCustomized: item.customized,\n            quantity: item.quantity || 1,\n          }),\n        })\n      );\n\n      const responses = await Promise.all(orderPromises);\n\n      // Check if all orders were successful\n      const allSuccessful = responses.every((res) => res.ok);\n\n      if (allSuccessful) {\n        // Clear cart\n        localStorage.removeItem(\"cart\");\n        onOrderSuccess();\n      } else {\n        setError(\"Some orders failed to process. Please try again.\");\n      }\n    } catch {\n      setError(\"Failed to place order. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">\n              Complete Your Order\n            </h2>\n            <button\n              onClick={onCancel}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close\"\n            >\n              ×\n            </button>\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h3 className=\"font-semibold mb-3\">Order Summary</h3>\n            {cartItems.map((item, index) => (\n              <div\n                key={index}\n                className=\"flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0\"\n              >\n                <div>\n                  <div className=\"font-medium\">{item.name}</div>\n                  {item.customized && (\n                    <div className=\"text-sm text-gray-600\">\n                      {item.color && <span>Color: {item.color}</span>}\n                      {item.text && (\n                        <span className=\"ml-2\">\n                          Text: &ldquo;{item.text}&rdquo;\n                        </span>\n                      )}\n                    </div>\n                  )}\n                  <div className=\"text-sm text-gray-500\">\n                    Qty: {item.quantity || 1}\n                  </div>\n                </div>\n                <div className=\"font-semibold\">\n                  K{(item.price * (item.quantity || 1)).toFixed(2)}\n                </div>\n              </div>\n            ))}\n            <div className=\"flex justify-between items-center pt-3 mt-3 border-t border-gray-300\">\n              <div className=\"font-bold text-lg\">Total:</div>\n              <div className=\"font-bold text-lg text-[#1a237e]\">\n                K{calculateTotal().toFixed(2)}\n              </div>\n            </div>\n          </div>\n\n          {/* Order Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label\n                htmlFor=\"customerName\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Full Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"customerName\"\n                name=\"customerName\"\n                value={formData.customerName}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerPhone\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Phone Number *\n              </label>\n              <input\n                type=\"tel\"\n                id=\"customerPhone\"\n                name=\"customerPhone\"\n                value={formData.customerPhone}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"e.g. +260 971 781 907\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerAddress\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Delivery/Pickup Address *\n              </label>\n              <textarea\n                id=\"customerAddress\"\n                name=\"customerAddress\"\n                value={formData.customerAddress}\n                onChange={handleInputChange}\n                required\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full address or preferred pickup location\"\n              />\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm bg-red-50 p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"flex gap-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={onCancel}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold transition ${\n                  loading\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"hover:bg-[#2a3490]\"\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center gap-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                    <span>Placing Order...</span>\n                  </div>\n                ) : (\n                  `Place Order - K${calculateTotal().toFixed(2)}`\n                )}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-4 text-xs text-gray-500 text-center\">\n            By placing this order, you agree to our terms and conditions. You\n            will be contacted for payment and delivery arrangements.\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA4Be,SAAS,UAAU,EAChC,SAAS,EACT,cAAc,EACd,QAAQ,EACO;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,cAAc;QACd,eAAe;QACf,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,MAAM,GAAG,QAAQ,eAAe,CAAC,EAAE;gBACjC,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C,GACG,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,IACtB,IAAI,CAAC,CAAC;gBACL,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,YAAY,CAAC,OAAS,CAAC;4BACrB,GAAG,IAAI;4BACP,cAAc,KAAK,IAAI,CAAC,IAAI,IAAI;wBAClC,CAAC;gBACH;YACF,GACC,KAAK,CAAC;YACL,kDAAkD;YACpD;QACJ;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,mCAAmC;YACnC,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAC,OACnC,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;oBAClC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,WAAW,KAAK,SAAS;wBACzB,cAAc,SAAS,YAAY;wBACnC,eAAe,SAAS,aAAa;wBACrC,iBAAiB,SAAS,eAAe;wBACzC,aAAa,KAAK,KAAK;wBACvB,YAAY,KAAK,IAAI;wBACrB,cAAc,KAAK,UAAU;wBAC7B,UAAU,KAAK,QAAQ,IAAI;oBAC7B;gBACF;YAGF,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC;YAEpC,sCAAsC;YACtC,MAAM,gBAAgB,UAAU,KAAK,CAAC,CAAC,MAAQ,IAAI,EAAE;YAErD,IAAI,eAAe;gBACjB,aAAa;gBACb,aAAa,UAAU,CAAC;gBACxB;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;4BAClC,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAe,KAAK,IAAI;;;;;;gDACtC,KAAK,UAAU,kBACd,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,kBAAI,8OAAC;;gEAAK;gEAAQ,KAAK,KAAK;;;;;;;wDACtC,KAAK,IAAI,kBACR,8OAAC;4DAAK,WAAU;;gEAAO;gEACP,KAAK,IAAI;gEAAC;;;;;;;;;;;;;8DAKhC,8OAAC;oDAAI,WAAU;;wDAAwB;wDAC/B,KAAK,QAAQ,IAAI;;;;;;;;;;;;;sDAG3B,8OAAC;4CAAI,WAAU;;gDAAgB;gDAC3B,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;;;;;;;;mCApB3C;;;;;0CAwBT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;;4CAAmC;4CAC9C,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,QAAQ;wCACR,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAIf,uBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,6EAA6E,EACvF,UACI,kCACA,sBACJ;kDAED,wBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;mDAGR,CAAC,eAAe,EAAE,iBAAiB,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;kCAMvD,8OAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;;;;;AAQlE", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Cart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport OrderForm from \"./OrderForm\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  price: number;\n  quantity?: number;\n};\n\ntype CartProps = {\n  isOpen: boolean;\n  onClose: () => void;\n};\n\nexport default function Cart({ isOpen, onClose }: CartProps) {\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n  const [showOrderForm, setShowOrderForm] = useState(false);\n  const [orderSuccess, setOrderSuccess] = useState(false);\n\n  useEffect(() => {\n    if (isOpen) {\n      loadCartItems();\n    }\n  }, [isOpen]);\n\n  const loadCartItems = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\n    setCartItems(cart);\n  };\n\n  const removeItem = (itemId: number) => {\n    const updatedCart = cartItems.filter((item) => item.id !== itemId);\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const updateQuantity = (itemId: number, newQuantity: number) => {\n    if (newQuantity < 1) {\n      removeItem(itemId);\n      return;\n    }\n\n    const updatedCart = cartItems.map((item) =>\n      item.id === itemId ? { ...item, quantity: newQuantity } : item\n    );\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleCheckout = () => {\n    setShowOrderForm(true);\n  };\n\n  const handleOrderSuccess = () => {\n    setShowOrderForm(false);\n    setOrderSuccess(true);\n    setCartItems([]);\n\n    // Close success message after 3 seconds\n    setTimeout(() => {\n      setOrderSuccess(false);\n      onClose();\n    }, 3000);\n  };\n\n  if (!isOpen) return null;\n\n  if (showOrderForm) {\n    return (\n      <OrderForm\n        cartItems={cartItems}\n        onOrderSuccess={handleOrderSuccess}\n        onCancel={() => setShowOrderForm(false)}\n      />\n    );\n  }\n\n  if (orderSuccess) {\n    return (\n      <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-xl shadow-lg p-8 text-center max-w-md\">\n          <div className=\"text-green-600 text-6xl mb-4\">✓</div>\n          <h2 className=\"text-2xl font-bold text-[#1a237e] mb-2\">\n            Order Placed Successfully!\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Thank you for your order. We will contact you soon for payment and\n            delivery arrangements.\n          </p>\n          <div className=\"animate-pulse text-sm text-gray-500\">\n            Closing automatically...\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">Shopping Cart</h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close cart\"\n            >\n              ×\n            </button>\n          </div>\n\n          {cartItems.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">🛒</div>\n              <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n                Your cart is empty\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                Add some products to get started!\n              </p>\n              <button\n                onClick={onClose}\n                className=\"px-6 py-2 bg-[#1a237e] text-white rounded-md hover:bg-[#2a3490] transition\"\n              >\n                Continue Shopping\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Cart Items */}\n              <div className=\"space-y-4 mb-6\">\n                {cartItems.map((item) => (\n                  <div\n                    key={item.id}\n                    className=\"flex items-center gap-4 p-4 border border-gray-200 rounded-lg\"\n                  >\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-lg\">{item.name}</h3>\n                      {item.customized && (\n                        <div className=\"text-sm text-gray-600 mt-1\">\n                          {item.color && (\n                            <div className=\"flex items-center gap-2\">\n                              <span>Color:</span>\n                              <div\n                                className=\"w-4 h-4 rounded border border-gray-300\"\n                                style={{ backgroundColor: item.color }}\n                              ></div>\n                              <span className=\"font-mono text-xs\">\n                                {item.color}\n                              </span>\n                            </div>\n                          )}\n                          {item.text && (\n                            <div className=\"mt-1\">\n                              <span>Text: &ldquo;</span>\n                              <span className=\"font-medium\">{item.text}</span>\n                              <span>&rdquo;</span>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                      <div className=\"text-lg font-semibold text-[#1a237e] mt-2\">\n                        K{item.price.toFixed(2)} each\n                      </div>\n                    </div>\n\n                    {/* Quantity Controls */}\n                    <div className=\"flex items-center gap-2\">\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) - 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        -\n                      </button>\n                      <span className=\"w-8 text-center font-semibold\">\n                        {item.quantity || 1}\n                      </span>\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) + 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        +\n                      </button>\n                    </div>\n\n                    {/* Remove Button */}\n                    <button\n                      onClick={() => removeItem(item.id)}\n                      className=\"text-red-500 hover:text-red-700 p-2\"\n                      aria-label=\"Remove item\"\n                    >\n                      🗑️\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              {/* Cart Total */}\n              <div className=\"border-t border-gray-200 pt-4 mb-6\">\n                <div className=\"flex justify-between items-center text-xl font-bold\">\n                  <span>Total:</span>\n                  <span className=\"text-[#1a237e]\">\n                    K{calculateTotal().toFixed(2)}\n                  </span>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={onClose}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n                >\n                  Continue Shopping\n                </button>\n                <button\n                  onClick={handleCheckout}\n                  className=\"flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold hover:bg-[#2a3490] transition\"\n                >\n                  Proceed to Checkout\n                </button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAqBe,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO,EAAa;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QAC3D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,cAAc,GAAG;YACnB,WAAW;YACX;QACF;QAEA,MAAM,cAAc,UAAU,GAAG,CAAC,CAAC,OACjC,KAAK,EAAE,KAAK,SAAS;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY,IAAI;QAE5D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,gBAAgB;QAChB,aAAa,EAAE;QAEf,wCAAwC;QACxC,WAAW;YACT,gBAAgB;YAChB;QACF,GAAG;IACL;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,IAAI,eAAe;QACjB,qBACE,8OAAC,+HAAA,CAAA,UAAS;YACR,WAAW;YACX,gBAAgB;YAChB,UAAU,IAAM,iBAAiB;;;;;;IAGvC;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;kCAC9C,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAIlC,8OAAC;wBAAI,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAM7D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;oBAKF,UAAU,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;6CAKH;;0CAEE,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyB,KAAK,IAAI;;;;;;oDAC/C,KAAK,UAAU,kBACd,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,kBACT,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,KAAK,KAAK;wEAAC;;;;;;kFAEvC,8OAAC;wEAAK,WAAU;kFACb,KAAK,KAAK;;;;;;;;;;;;4DAIhB,KAAK,IAAI,kBACR,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAKd,8OAAC;wDAAI,WAAU;;4DAA4C;4DACvD,KAAK,KAAK,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAK5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ,IAAI;;;;;;kEAEpB,8OAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;;;;;;;0DAMH,8OAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;gDACV,cAAW;0DACZ;;;;;;;uCA7DI,KAAK,EAAE;;;;;;;;;;0CAqElB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAiB;gDAC7B,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;0CAMjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 1797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\n\ninterface ConfirmationModalProps {\n  isOpen: boolean;\n  title: string;\n  message: string;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n  type?: \"warning\" | \"danger\" | \"info\" | \"success\";\n}\n\nexport default function ConfirmationModal({\n  isOpen,\n  title,\n  message,\n  confirmText = \"Confirm\",\n  cancelText = \"Cancel\",\n  onConfirm,\n  onCancel,\n  type = \"info\",\n}: ConfirmationModalProps) {\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === \"Escape\" && isOpen) {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener(\"keydown\", handleEscape);\n      // Prevent body scroll when modal is open\n      document.body.style.overflow = \"hidden\";\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleEscape);\n      document.body.style.overflow = \"unset\";\n    };\n  }, [isOpen, onCancel]);\n\n  if (!isOpen) return null;\n\n  const getTypeStyles = () => {\n    switch (type) {\n      case \"warning\":\n        return {\n          icon: \"⚠️\",\n          iconBg: \"bg-yellow-100\",\n          iconColor: \"text-yellow-600\",\n          confirmBg: \"bg-yellow-600 hover:bg-yellow-700\",\n          titleColor: \"text-yellow-800\",\n        };\n      case \"danger\":\n        return {\n          icon: \"🚨\",\n          iconBg: \"bg-red-100\",\n          iconColor: \"text-red-600\",\n          confirmBg: \"bg-red-600 hover:bg-red-700\",\n          titleColor: \"text-red-800\",\n        };\n      case \"success\":\n        return {\n          icon: \"✅\",\n          iconBg: \"bg-green-100\",\n          iconColor: \"text-green-600\",\n          confirmBg: \"bg-green-600 hover:bg-green-700\",\n          titleColor: \"text-green-800\",\n        };\n      default: // info\n        return {\n          icon: \"💰\",\n          iconBg: \"bg-blue-100\",\n          iconColor: \"text-blue-600\",\n          confirmBg: \"bg-blue-600 hover:bg-blue-700\",\n          titleColor: \"text-blue-800\",\n        };\n    }\n  };\n\n  const styles = getTypeStyles();\n\n  return (\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out scale-100\">\n        {/* Header */}\n        <div className=\"p-6 pb-4\">\n          <div className=\"flex items-center gap-4\">\n            <div className={`w-12 h-12 rounded-full ${styles.iconBg} flex items-center justify-center flex-shrink-0`}>\n              <span className=\"text-2xl\">{styles.icon}</span>\n            </div>\n            <div className=\"flex-1\">\n              <h3 className={`text-xl font-bold ${styles.titleColor} leading-tight`}>\n                {title}\n              </h3>\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"px-6 pb-6\">\n          <p className=\"text-gray-700 text-base leading-relaxed\">\n            {message}\n          </p>\n        </div>\n\n        {/* Actions */}\n        <div className=\"px-6 pb-6 flex gap-3 justify-end\">\n          <button\n            onClick={onCancel}\n            className=\"px-6 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300\"\n          >\n            {cancelText}\n          </button>\n          <button\n            onClick={onConfirm}\n            className={`px-6 py-2.5 text-white ${styles.confirmBg} rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg`}\n          >\n            {confirmText}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAee,SAAS,kBAAkB,EACxC,MAAM,EACN,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACU;IACvB,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;gBAChC;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,yCAAyC;YACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAS;IAErB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;YACF;gBACE,OAAO;oBACL,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;QACJ;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,OAAO,MAAM,CAAC,+CAA+C,CAAC;0CACtG,cAAA,8OAAC;oCAAK,WAAU;8CAAY,OAAO,IAAI;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAW,CAAC,kBAAkB,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC;8CAClE;;;;;;;;;;;;;;;;;;;;;;8BAOT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAET;;;;;;sCAEH,8OAAC;4BACC,SAAS;4BACT,WAAW,CAAC,uBAAuB,EAAE,OAAO,SAAS,CAAC,wIAAwI,CAAC;sCAE9L;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport { Doughnut } from \"react-chartjs-2\";\r\nimport {\r\n  Chart as ChartJS,\r\n  ArcElement,\r\n  Tooltip,\r\n  Legend,\r\n  TooltipItem,\r\n} from \"chart.js\";\r\nimport Header from \"@/components/Header\";\r\nimport Cart from \"@/components/Cart\";\r\nimport ConfirmationModal from \"@/components/ConfirmationModal\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nChartJS.register(ArcElement, Tooltip, Legend);\r\n\r\ntype UserOrOrder = {\r\n  id?: string | number;\r\n  name?: string;\r\n  email?: string;\r\n  status?: string;\r\n  user?: { id: string | number; name?: string; email: string };\r\n  product?: {\r\n    id: string | number;\r\n    name: string;\r\n    price?: number;\r\n    image?: string;\r\n  };\r\n  customization?: {\r\n    color?: string;\r\n    text?: string;\r\n  };\r\n  createdAt?: string;\r\n};\r\n\r\ntype Product = {\r\n  id: string | number;\r\n  name: string;\r\n  price?: number;\r\n  image?: string;\r\n  category?: { id: number; name: string } | null;\r\n};\r\n\r\ntype User = {\r\n  id: string | number;\r\n  name?: string;\r\n  email: string;\r\n  role?: string;\r\n  createdAt?: string;\r\n};\r\n\r\nexport default function AdminDashboard() {\r\n  const [data, setData] = useState<{\r\n    users?: User[];\r\n    orders?: UserOrOrder[];\r\n    products?: Product[];\r\n    stats?: {\r\n      users?: number;\r\n      orders?: number;\r\n      products?: number;\r\n      collectedOrders?: number;\r\n    };\r\n  } | null>(null);\r\n  const [error, setError] = useState(\"\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [tab, setTab] = useState<\"products\" | \"users\" | \"orders\">(\"products\");\r\n  const [cartCount, setCartCount] = useState(0);\r\n  const [isCartOpen, setIsCartOpen] = useState(false);\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [pendingOrderId, setPendingOrderId] = useState<string | number | null>(\r\n    null\r\n  );\r\n  const ITEMS_PER_PAGE = 5;\r\n  const router = useRouter();\r\n\r\n  const updateCartCount = () => {\r\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\r\n    setCartCount(cart.length);\r\n  };\r\n\r\n  const handleCartClick = () => {\r\n    setIsCartOpen(true);\r\n  };\r\n\r\n  const handleCartClose = () => {\r\n    setIsCartOpen(false);\r\n    updateCartCount(); // Update count when cart closes\r\n  };\r\n\r\n  const handleConfirmCollection = (orderId: string | number) => {\r\n    setPendingOrderId(orderId);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const handleConfirmModalConfirm = () => {\r\n    if (pendingOrderId) {\r\n      handleMarkCollected(pendingOrderId);\r\n    }\r\n    setShowConfirmModal(false);\r\n    setPendingOrderId(null);\r\n  };\r\n\r\n  const handleConfirmModalCancel = () => {\r\n    setShowConfirmModal(false);\r\n    setPendingOrderId(null);\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Load cart count\r\n    updateCartCount();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setError(\"Not authenticated\");\r\n      return;\r\n    }\r\n    fetch(getApiUrl(API_CONFIG.ENDPOINTS.ADMIN.DASHBOARD), {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    })\r\n      .then((res) => res.json())\r\n      .then((dashboardData) => {\r\n        setData(dashboardData);\r\n      })\r\n      .catch(() => setError(\"Failed to load dashboard\"));\r\n  }, []);\r\n\r\n  if (error)\r\n    return <div className=\"text-red-600 text-center mt-16\">{error}</div>;\r\n  if (!data) return <div className=\"text-center mt-16\">Loading...</div>;\r\n\r\n  // Always show products in the dashboard list for management\r\n  const items: Product[] = data.products || [];\r\n  const totalPages = Math.max(1, Math.ceil(items.length / ITEMS_PER_PAGE));\r\n  const paginatedItems = items.slice(\r\n    (currentPage - 1) * ITEMS_PER_PAGE,\r\n    currentPage * ITEMS_PER_PAGE\r\n  );\r\n\r\n  // Chart data for order status\r\n  const totalOrders = data.stats?.orders ?? data.orders?.length ?? 0;\r\n  const collectedOrders =\r\n    data.stats?.collectedOrders ??\r\n    data.orders?.filter((o) => o.status === \"COLLECTED\").length ??\r\n    0;\r\n  const pendingOrders = totalOrders - collectedOrders;\r\n  const doughnutData = {\r\n    labels: [\"Collected\", \"Pending\"],\r\n    datasets: [\r\n      {\r\n        data: [collectedOrders, pendingOrders],\r\n        backgroundColor: [\"#2563eb\", \"#fbbf24\"],\r\n        borderColor: [\"#1e40af\", \"#f59e42\"],\r\n        borderWidth: 2,\r\n      },\r\n    ],\r\n  };\r\n  const doughnutOptions = {\r\n    cutout: \"70%\",\r\n    plugins: {\r\n      legend: {\r\n        display: true,\r\n        position: \"bottom\" as const,\r\n        labels: {\r\n          color: \"#1a237e\",\r\n          font: { size: 14, weight: \"bold\" as const },\r\n        },\r\n      },\r\n      tooltip: {\r\n        callbacks: {\r\n          label: function (tooltipItem: TooltipItem<\"doughnut\">) {\r\n            const label = tooltipItem.label || \"\";\r\n            const value = tooltipItem.raw || 0;\r\n            return `${label}: ${value}`;\r\n          },\r\n        },\r\n      },\r\n    },\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n  };\r\n\r\n  // Add handlers for edit and delete\r\n  function handleEditProduct(product: Product) {\r\n    // Use Next.js router for navigation instead of window.location.href\r\n    // This ensures client-side navigation and works with the app directory\r\n    router.push(`/admin/products/${product.id}`);\r\n  }\r\n\r\n  async function handleDeleteProduct(productId: string | number) {\r\n    if (!confirm(\"Are you sure you want to delete this product?\")) return;\r\n    const token = localStorage.getItem(\"token\");\r\n    try {\r\n      const res = await fetch(\r\n        getApiUrl(`${API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS}/${productId}`),\r\n        {\r\n          method: \"DELETE\",\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }\r\n      );\r\n      if (!res.ok) throw new Error(\"Failed to delete product\");\r\n      setData((prev: typeof data) =>\r\n        prev && prev.products\r\n          ? {\r\n              ...prev,\r\n              products: prev.products.filter(\r\n                (p: Product) => p.id !== productId\r\n              ),\r\n            }\r\n          : prev\r\n      );\r\n    } catch {\r\n      alert(\"Error deleting product\");\r\n    }\r\n  }\r\n\r\n  async function handleMarkCollected(orderId: string | number | undefined) {\r\n    if (!orderId) return;\r\n    const token = localStorage.getItem(\"token\");\r\n    try {\r\n      const res = await fetch(\r\n        getApiUrl(\r\n          `${API_CONFIG.ENDPOINTS.ADMIN.ORDER_COLLECTED}/${orderId}/collected`\r\n        ),\r\n        {\r\n          method: \"PUT\",\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }\r\n      );\r\n      if (!res.ok) throw new Error(\"Failed to mark as collected\");\r\n      setData((prev) => {\r\n        if (!prev || !prev.orders) return prev;\r\n        return {\r\n          ...prev,\r\n          orders: prev.orders.map((o) =>\r\n            o.id === orderId ? { ...o, status: \"COLLECTED\" } : o\r\n          ),\r\n          stats: {\r\n            ...prev.stats,\r\n            collectedOrders: (prev.stats?.collectedOrders ?? 0) + 1,\r\n          },\r\n        };\r\n      });\r\n    } catch {\r\n      alert(\"Error updating order status\");\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n      <Header cartCount={cartCount} onCartClick={handleCartClick} />\r\n\r\n      {/* Hero Section */}\r\n      <div className=\"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-black/20\"></div>\r\n        <div className=\"relative z-10 max-w-7xl mx-auto px-4 py-16\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-4xl md:text-6xl font-black text-white mb-4\">\r\n              <span className=\"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent\">\r\n                Admin\r\n              </span>\r\n              <span className=\"text-white\"> Dashboard</span>\r\n            </h1>\r\n            <p className=\"text-xl text-gray-200 mb-8 max-w-2xl mx-auto\">\r\n              Manage your Six Light Media store with powerful tools and\r\n              real-time analytics\r\n            </p>\r\n\r\n            {/* Quick Stats */}\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto\">\r\n              <div className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\">\r\n                <div className=\"text-3xl font-black text-yellow-400 mb-2\">\r\n                  {data.stats?.users ?? \"-\"}\r\n                </div>\r\n                <div className=\"text-gray-200 text-sm\">Total Users</div>\r\n              </div>\r\n              <div className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\">\r\n                <div className=\"text-3xl font-black text-pink-400 mb-2\">\r\n                  {data.stats?.orders ?? \"-\"}\r\n                </div>\r\n                <div className=\"text-gray-200 text-sm\">Total Orders</div>\r\n              </div>\r\n              <div className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\">\r\n                <div className=\"text-3xl font-black text-purple-400 mb-2\">\r\n                  {data.stats?.collectedOrders ??\r\n                    (data.orders\r\n                      ? data.orders.filter((o) => o.status === \"COLLECTED\")\r\n                          .length\r\n                      : \"-\")}\r\n                </div>\r\n                <div className=\"text-gray-200 text-sm\">Collected</div>\r\n              </div>\r\n              <div className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\">\r\n                <div className=\"text-3xl font-black text-blue-400 mb-2\">\r\n                  {data.stats?.products ??\r\n                    (data.products ? data.products.length : \"-\")}\r\n                </div>\r\n                <div className=\"text-gray-200 text-sm\">Products</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\r\n        {/* Navigation Tabs */}\r\n        <div className=\"bg-white rounded-3xl shadow-xl p-2 mb-8 border border-gray-100\">\r\n          <div className=\"flex flex-wrap justify-center gap-2\">\r\n            <button\r\n              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ${\r\n                tab === \"products\"\r\n                  ? \"bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg\"\r\n                  : \"text-gray-600 hover:bg-gray-50 hover:text-indigo-600\"\r\n              }`}\r\n              onClick={() => setTab(\"products\")}\r\n              type=\"button\"\r\n            >\r\n              📦 Products\r\n            </button>\r\n            <button\r\n              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ${\r\n                tab === \"users\"\r\n                  ? \"bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg\"\r\n                  : \"text-gray-600 hover:bg-gray-50 hover:text-indigo-600\"\r\n              }`}\r\n              onClick={() => setTab(\"users\")}\r\n              type=\"button\"\r\n            >\r\n              👥 Users\r\n            </button>\r\n            <button\r\n              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ${\r\n                tab === \"orders\"\r\n                  ? \"bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg\"\r\n                  : \"text-gray-600 hover:bg-gray-50 hover:text-indigo-600\"\r\n              }`}\r\n              onClick={() => setTab(\"orders\")}\r\n              type=\"button\"\r\n            >\r\n              📋 Orders\r\n            </button>\r\n            <Link\r\n              href=\"/admin/categories\"\r\n              className=\"px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base text-gray-600 hover:bg-gray-50 hover:text-indigo-600 flex items-center justify-center\"\r\n            >\r\n              🏷️ Categories\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        {/* Analytics Dashboard */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12\">\r\n          {/* Performance Metrics */}\r\n          <div className=\"lg:col-span-2 space-y-6\">\r\n            <div className=\"bg-white rounded-3xl shadow-xl p-8 border border-gray-100\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-6 flex items-center\">\r\n                📊 Performance Metrics\r\n              </h3>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div className=\"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-6 border border-blue-200\">\r\n                  <div className=\"flex items-center justify-between mb-4\">\r\n                    <div className=\"w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center\">\r\n                      <svg\r\n                        className=\"w-6 h-6 text-white\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-sm font-semibold text-blue-700 bg-blue-200 px-3 py-1 rounded-full\">\r\n                      Completion Rate\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"text-3xl font-black text-blue-700 mb-2\">\r\n                    {data.stats?.orders && data.stats?.collectedOrders\r\n                      ? `${Math.round(\r\n                          (data.stats.collectedOrders / data.stats.orders) * 100\r\n                        )}%`\r\n                      : \"-\"}\r\n                  </div>\r\n                  <div className=\"text-sm text-blue-600\">\r\n                    Orders successfully completed\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-6 border border-green-200\">\r\n                  <div className=\"flex items-center justify-between mb-4\">\r\n                    <div className=\"w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center\">\r\n                      <svg\r\n                        className=\"w-6 h-6 text-white\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-sm font-semibold text-green-700 bg-green-200 px-3 py-1 rounded-full\">\r\n                      Pending\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"text-3xl font-black text-green-700 mb-2\">\r\n                    {data.stats?.orders &&\r\n                    data.stats?.collectedOrders !== undefined\r\n                      ? data.stats.orders - data.stats.collectedOrders\r\n                      : data.orders\r\n                      ? data.orders.filter((o) => o.status === \"PENDING\").length\r\n                      : \"-\"}\r\n                  </div>\r\n                  <div className=\"text-sm text-green-600\">\r\n                    Orders awaiting collection\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Order Status Chart */}\r\n          <div className=\"bg-white rounded-3xl shadow-xl p-8 border border-gray-100\">\r\n            <h3 className=\"text-xl font-bold text-gray-900 mb-6 text-center\">\r\n              📈 Order Status\r\n            </h3>\r\n            <div className=\"w-full h-64 flex items-center justify-center\">\r\n              <div className=\"w-48 h-48\">\r\n                <Doughnut data={doughnutData} options={doughnutOptions} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {/* Products Tab */}\r\n        {tab === \"products\" && items.length > 0 && (\r\n          <div className=\"bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100\">\r\n            <div className=\"flex items-center justify-between mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 flex items-center\">\r\n                📦 Product Management\r\n              </h3>\r\n              <Link\r\n                href=\"/admin/products\"\r\n                className=\"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                ➕ Add New Product\r\n              </Link>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {paginatedItems.map((product, idx) => (\r\n                <div\r\n                  key={product.id || idx}\r\n                  className=\"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\"\r\n                >\r\n                  <div className=\"aspect-square mb-4 bg-white rounded-xl overflow-hidden border border-gray-100\">\r\n                    <Image\r\n                      src={product.image || \"/bottle-dummy.jpg\"}\r\n                      alt={product.name}\r\n                      width={200}\r\n                      height={200}\r\n                      className=\"w-full h-full object-contain group-hover:scale-105 transition-transform duration-300\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <h4 className=\"font-bold text-gray-900 text-lg line-clamp-2\">\r\n                      {product.name}\r\n                    </h4>\r\n\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {product.category && (\r\n                        <span className=\"px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full\">\r\n                          {typeof product.category === \"string\"\r\n                            ? product.category\r\n                            : product.category?.name || \"Unknown Category\"}\r\n                        </span>\r\n                      )}\r\n                      <span className=\"px-3 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full\">\r\n                        K{product.price ?? 0}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <div className=\"flex gap-2 pt-2\">\r\n                      <button\r\n                        className=\"flex-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md\"\r\n                        onClick={() => handleEditProduct(product)}\r\n                      >\r\n                        ✏️ Edit\r\n                      </button>\r\n                      <button\r\n                        className=\"flex-1 px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white text-sm font-semibold rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-md\"\r\n                        onClick={() => handleDeleteProduct(product.id)}\r\n                      >\r\n                        🗑️ Delete\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Pagination */}\r\n            {totalPages > 1 && (\r\n              <div className=\"flex justify-center items-center gap-4 mt-8\">\r\n                <button\r\n                  className=\"px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg\"\r\n                  onClick={() =>\r\n                    setCurrentPage((p: number) => Math.max(1, p - 1))\r\n                  }\r\n                  disabled={currentPage === 1}\r\n                >\r\n                  ← Previous\r\n                </button>\r\n                <span className=\"px-4 py-2 bg-gray-100 rounded-xl font-semibold text-gray-700\">\r\n                  Page {currentPage} of {totalPages}\r\n                </span>\r\n                <button\r\n                  className=\"px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg\"\r\n                  onClick={() =>\r\n                    setCurrentPage((p: number) => Math.min(totalPages, p + 1))\r\n                  }\r\n                  disabled={currentPage === totalPages}\r\n                >\r\n                  Next →\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {/* Users Tab */}\r\n        {tab === \"users\" && data.users && data.users.length > 0 && (\r\n          <div className=\"bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100\">\r\n            <div className=\"flex items-center justify-between mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 flex items-center\">\r\n                👥 User Management\r\n              </h3>\r\n              <Link\r\n                href=\"/admin/users\"\r\n                className=\"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                👥 Manage All Users\r\n              </Link>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {data.users.slice(0, 9).map((user: User) => (\r\n                <div\r\n                  key={user.id}\r\n                  className=\"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\"\r\n                >\r\n                  <div className=\"flex items-center space-x-4 mb-4\">\r\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg\">\r\n                      {(user.name || user.email)?.charAt(0)?.toUpperCase() ||\r\n                        \"U\"}\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <h4 className=\"font-bold text-gray-900 truncate\">\r\n                        {user.name || user.email}\r\n                      </h4>\r\n                      <p className=\"text-sm text-gray-600 truncate\">\r\n                        {user.email}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-gray-600\">Role:</span>\r\n                      <span\r\n                        className={`px-3 py-1 text-xs font-semibold rounded-full ${\r\n                          user.role === \"ADMIN\"\r\n                            ? \"bg-red-100 text-red-700\"\r\n                            : \"bg-blue-100 text-blue-700\"\r\n                        }`}\r\n                      >\r\n                        {user.role}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-gray-600\">Status:</span>\r\n                      <span className=\"px-3 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full\">\r\n                        ✅ Active\r\n                      </span>\r\n                    </div>\r\n\r\n                    <div className=\"pt-2 border-t border-gray-100\">\r\n                      <div className=\"text-xs text-gray-500\">\r\n                        📅 Member since:{\" \"}\r\n                        {new Date(\r\n                          user.createdAt || Date.now()\r\n                        ).toLocaleDateString()}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Orders Tab */}\r\n        {tab === \"orders\" && data.orders && data.orders.length > 0 && (\r\n          <div className=\"bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100\">\r\n            <div className=\"flex items-center justify-between mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 flex items-center\">\r\n                📋 Order Management\r\n              </h3>\r\n              <Link\r\n                href=\"/admin/orders\"\r\n                className=\"px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                📋 View All Orders\r\n              </Link>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n              {data.orders\r\n                .slice(0, 8)\r\n                .map((order: UserOrOrder, idx: number) => (\r\n                  <div\r\n                    key={order.id || idx}\r\n                    className=\"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\"\r\n                  >\r\n                    <div className=\"flex items-start justify-between mb-4\">\r\n                      <div className=\"flex items-center space-x-3\">\r\n                        <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center text-white font-bold text-sm\">\r\n                          #{order.id}\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"font-bold text-gray-900\">\r\n                            Order #{order.id}\r\n                          </h4>\r\n                          <p className=\"text-sm text-gray-600\">\r\n                            {order.user?.name ||\r\n                              order.user?.email ||\r\n                              \"Unknown Customer\"}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <span\r\n                        className={`px-3 py-1 text-xs font-semibold rounded-full ${\r\n                          order.status === \"COLLECTED\"\r\n                            ? \"bg-green-100 text-green-700\"\r\n                            : order.status === \"PENDING\"\r\n                            ? \"bg-yellow-100 text-yellow-700\"\r\n                            : \"bg-gray-100 text-gray-700\"\r\n                        }`}\r\n                      >\r\n                        {order.status === \"COLLECTED\"\r\n                          ? \"✅ Collected\"\r\n                          : order.status === \"PENDING\"\r\n                          ? \"⏳ Pending\"\r\n                          : order.status}\r\n                      </span>\r\n                    </div>\r\n\r\n                    {order.product && (\r\n                      <div className=\"flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-xl\">\r\n                        <div className=\"w-12 h-12 bg-white rounded-lg overflow-hidden border border-gray-200\">\r\n                          <Image\r\n                            src={order.product.image || \"/bottle-dummy.jpg\"}\r\n                            alt={order.product.name}\r\n                            width={48}\r\n                            height={48}\r\n                            className=\"w-full h-full object-contain\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <h5 className=\"font-semibold text-gray-900 truncate\">\r\n                            {order.product.name}\r\n                          </h5>\r\n                          <p className=\"text-sm text-green-600 font-semibold\">\r\n                            K{order.product.price ?? 0}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div className=\"space-y-2 mb-4\">\r\n                      <div className=\"flex justify-between text-sm\">\r\n                        <span className=\"text-gray-600\">Order Date:</span>\r\n                        <span className=\"font-medium text-gray-900\">\r\n                          {new Date(\r\n                            order.createdAt || Date.now()\r\n                          ).toLocaleDateString()}\r\n                        </span>\r\n                      </div>\r\n\r\n                      {order.customization && (\r\n                        <div className=\"text-sm\">\r\n                          <span className=\"text-gray-600\">Customization:</span>\r\n                          <div className=\"mt-1 p-2 bg-blue-50 rounded-lg\">\r\n                            <div className=\"text-xs text-blue-700\">\r\n                              🎨 Color: {order.customization.color}\r\n                            </div>\r\n                            {order.customization.text && (\r\n                              <div className=\"text-xs text-blue-700\">\r\n                                📝 Text: &ldquo;{order.customization.text}\r\n                                &rdquo;\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {order.status === \"PENDING\" && (\r\n                      <button\r\n                        className=\"w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md\"\r\n                        onClick={() =>\r\n                          order.id && handleConfirmCollection(order.id)\r\n                        }\r\n                      >\r\n                        ✅ Mark as Collected\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Quick Actions Section */}\r\n        <div className=\"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 rounded-3xl p-8 text-center relative overflow-hidden\">\r\n          <div className=\"absolute inset-0 bg-black/20\"></div>\r\n          <div className=\"relative z-10\">\r\n            <h3 className=\"text-3xl font-bold text-white mb-4\">\r\n              🚀 Quick Actions\r\n            </h3>\r\n            <p className=\"text-gray-200 mb-8 max-w-2xl mx-auto\">\r\n              Manage your store efficiently with these powerful tools\r\n            </p>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\r\n              <Link\r\n                href=\"/admin/products\"\r\n                className=\"group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2\"\r\n              >\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <svg\r\n                    className=\"w-8 h-8 text-white\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h4 className=\"text-xl font-bold text-white mb-2\">\r\n                  Add Product\r\n                </h4>\r\n                <p className=\"text-gray-300 text-sm\">\r\n                  Upload new products to your store\r\n                </p>\r\n              </Link>\r\n\r\n              <Link\r\n                href=\"/admin/categories\"\r\n                className=\"group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2\"\r\n              >\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <svg\r\n                    className=\"w-8 h-8 text-white\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h4 className=\"text-xl font-bold text-white mb-2\">\r\n                  Categories\r\n                </h4>\r\n                <p className=\"text-gray-300 text-sm\">\r\n                  Organize your product categories\r\n                </p>\r\n              </Link>\r\n\r\n              <Link\r\n                href=\"/admin/orders\"\r\n                className=\"group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2\"\r\n              >\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <svg\r\n                    className=\"w-8 h-8 text-white\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h4 className=\"text-xl font-bold text-white mb-2\">\r\n                  All Orders\r\n                </h4>\r\n                <p className=\"text-gray-300 text-sm\">\r\n                  View and manage all orders\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Cart Modal */}\r\n      <Cart isOpen={isCartOpen} onClose={handleCartClose} />\r\n\r\n      {/* Confirmation Modal */}\r\n      <ConfirmationModal\r\n        isOpen={showConfirmModal}\r\n        title=\"Confirm Order Collection\"\r\n        message=\"Has the customer paid and collected this order? This action will mark the order as completed.\"\r\n        confirmText=\"Yes, Mark as Collected\"\r\n        cancelText=\"Cancel\"\r\n        onConfirm={handleConfirmModalConfirm}\r\n        onCancel={handleConfirmModalCancel}\r\n        type=\"info\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;AAkBA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CAAC,4JAAA,CAAA,aAAU,EAAE,4JAAA,CAAA,UAAO,EAAE,4JAAA,CAAA,SAAM;AAqC7B,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAUrB;IACV,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjD;IAEF,MAAM,iBAAiB;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB;QACtB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa,KAAK,MAAM;IAC1B;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;QACd,mBAAmB,gCAAgC;IACrD;IAEA,MAAM,0BAA0B,CAAC;QAC/B,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,4BAA4B;QAChC,IAAI,gBAAgB;YAClB,oBAAoB;QACtB;QACA,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,MAAM,2BAA2B;QAC/B,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;QAClB;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT;QACF;QACA,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG;YACrD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GACG,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,IACtB,IAAI,CAAC,CAAC;YACL,QAAQ;QACV,GACC,KAAK,CAAC,IAAM,SAAS;IAC1B,GAAG,EAAE;IAEL,IAAI,OACF,qBAAO,8OAAC;QAAI,WAAU;kBAAkC;;;;;;IAC1D,IAAI,CAAC,MAAM,qBAAO,8OAAC;QAAI,WAAU;kBAAoB;;;;;;IAErD,4DAA4D;IAC5D,MAAM,QAAmB,KAAK,QAAQ,IAAI,EAAE;IAC5C,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;IACxD,MAAM,iBAAiB,MAAM,KAAK,CAChC,CAAC,cAAc,CAAC,IAAI,gBACpB,cAAc;IAGhB,8BAA8B;IAC9B,MAAM,cAAc,KAAK,KAAK,EAAE,UAAU,KAAK,MAAM,EAAE,UAAU;IACjE,MAAM,kBACJ,KAAK,KAAK,EAAE,mBACZ,KAAK,MAAM,EAAE,OAAO,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,UACrD;IACF,MAAM,gBAAgB,cAAc;IACpC,MAAM,eAAe;QACnB,QAAQ;YAAC;YAAa;SAAU;QAChC,UAAU;YACR;gBACE,MAAM;oBAAC;oBAAiB;iBAAc;gBACtC,iBAAiB;oBAAC;oBAAW;iBAAU;gBACvC,aAAa;oBAAC;oBAAW;iBAAU;gBACnC,aAAa;YACf;SACD;IACH;IACA,MAAM,kBAAkB;QACtB,QAAQ;QACR,SAAS;YACP,QAAQ;gBACN,SAAS;gBACT,UAAU;gBACV,QAAQ;oBACN,OAAO;oBACP,MAAM;wBAAE,MAAM;wBAAI,QAAQ;oBAAgB;gBAC5C;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAU,WAAoC;wBACnD,MAAM,QAAQ,YAAY,KAAK,IAAI;wBACnC,MAAM,QAAQ,YAAY,GAAG,IAAI;wBACjC,OAAO,GAAG,MAAM,EAAE,EAAE,OAAO;oBAC7B;gBACF;YACF;QACF;QACA,YAAY;QACZ,qBAAqB;IACvB;IAEA,mCAAmC;IACnC,SAAS,kBAAkB,OAAgB;QACzC,oEAAoE;QACpE,uEAAuE;QACvE,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;IAC7C;IAEA,eAAe,oBAAoB,SAA0B;QAC3D,IAAI,CAAC,QAAQ,kDAAkD;QAC/D,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI;YACF,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,GAAG,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,GAC/D;gBACE,QAAQ;gBACR,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEF,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,QAAQ,CAAC,OACP,QAAQ,KAAK,QAAQ,GACjB;oBACE,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAC5B,CAAC,IAAe,EAAE,EAAE,KAAK;gBAE7B,IACA;QAER,EAAE,OAAM;YACN,MAAM;QACR;IACF;IAEA,eAAe,oBAAoB,OAAoC;QACrE,IAAI,CAAC,SAAS;QACd,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI;YACF,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EACN,GAAG,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,GAEtE;gBACE,QAAQ;gBACR,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEF,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,QAAQ,CAAC;gBACP,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE,OAAO;gBAClC,OAAO;oBACL,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,IACvB,EAAE,EAAE,KAAK,UAAU;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAY,IAAI;oBAErD,OAAO;wBACL,GAAG,KAAK,KAAK;wBACb,iBAAiB,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC,IAAI;oBACxD;gBACF;YACF;QACF,EAAE,OAAM;YACN,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;gBAAC,WAAW;gBAAW,aAAa;;;;;;0BAG3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAA4F;;;;;;sDAG5G,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAM5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,EAAE,SAAS;;;;;;8DAExB,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,EAAE,UAAU;;;;;;8DAEzB,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,EAAE,mBACX,CAAC,KAAK,MAAM,GACR,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aACpC,MAAM,GACT,GAAG;;;;;;8DAEX,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,EAAE,YACX,CAAC,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,mGAAmG,EAC7G,QAAQ,aACJ,wEACA,wDACJ;oCACF,SAAS,IAAM,OAAO;oCACtB,MAAK;8CACN;;;;;;8CAGD,8OAAC;oCACC,WAAW,CAAC,mGAAmG,EAC7G,QAAQ,UACJ,wEACA,wDACJ;oCACF,SAAS,IAAM,OAAO;oCACtB,MAAK;8CACN;;;;;;8CAGD,8OAAC;oCACC,WAAW,CAAC,mGAAmG,EAC7G,QAAQ,WACJ,wEACA,wDACJ;oCACF,SAAS,IAAM,OAAO;oCACtB,MAAK;8CACN;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0D;;;;;;sDAGxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAU;wEACV,MAAK;wEACL,QAAO;wEACP,SAAQ;kFAER,cAAA,8OAAC;4EACC,eAAc;4EACd,gBAAe;4EACf,aAAa;4EACb,GAAE;;;;;;;;;;;;;;;;8EAIR,8OAAC;oEAAK,WAAU;8EAAyE;;;;;;;;;;;;sEAI3F,8OAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK,EAAE,UAAU,KAAK,KAAK,EAAE,kBAC/B,GAAG,KAAK,KAAK,CACX,AAAC,KAAK,KAAK,CAAC,eAAe,GAAG,KAAK,KAAK,CAAC,MAAM,GAAI,KACnD,CAAC,CAAC,GACJ;;;;;;sEAEN,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAKzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAU;wEACV,MAAK;wEACL,QAAO;wEACP,SAAQ;kFAER,cAAA,8OAAC;4EACC,eAAc;4EACd,gBAAe;4EACf,aAAa;4EACb,GAAE;;;;;;;;;;;;;;;;8EAIR,8OAAC;oEAAK,WAAU;8EAA2E;;;;;;;;;;;;sEAI7F,8OAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK,EAAE,UACb,KAAK,KAAK,EAAE,oBAAoB,YAC5B,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,eAAe,GAC9C,KAAK,MAAM,GACX,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,WAAW,MAAM,GACxD;;;;;;sEAEN,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sJAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAM9C,QAAQ,cAAc,MAAM,MAAM,GAAG,mBACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,SAAS,oBAC5B,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,KAAK,IAAI;oDACtB,KAAK,QAAQ,IAAI;oDACjB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,IAAI;;;;;;kEAGf,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,QAAQ,kBACf,8OAAC;gEAAK,WAAU;0EACb,OAAO,QAAQ,QAAQ,KAAK,WACzB,QAAQ,QAAQ,GAChB,QAAQ,QAAQ,EAAE,QAAQ;;;;;;0EAGlC,8OAAC;gEAAK,WAAU;;oEAA2E;oEACvF,QAAQ,KAAK,IAAI;;;;;;;;;;;;;kEAIvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,SAAS,IAAM,kBAAkB;0EAClC;;;;;;0EAGD,8OAAC;gEACC,WAAU;gEACV,SAAS,IAAM,oBAAoB,QAAQ,EAAE;0EAC9C;;;;;;;;;;;;;;;;;;;uCAzCA,QAAQ,EAAE,IAAI;;;;;;;;;;4BAmDxB,aAAa,mBACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS,IACP,eAAe,CAAC,IAAc,KAAK,GAAG,CAAC,GAAG,IAAI;wCAEhD,UAAU,gBAAgB;kDAC3B;;;;;;kDAGD,8OAAC;wCAAK,WAAU;;4CAA+D;4CACvE;4CAAY;4CAAK;;;;;;;kDAEzB,8OAAC;wCACC,WAAU;wCACV,SAAS,IACP,eAAe,CAAC,IAAc,KAAK,GAAG,CAAC,YAAY,IAAI;wCAEzD,UAAU,gBAAgB;kDAC3B;;;;;;;;;;;;;;;;;;oBAQR,QAAQ,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,mBACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC3B,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,CAAC,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,OAAO,IAAI,iBACrC;;;;;;kEAEJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;0EAE1B,8OAAC;gEAAE,WAAU;0EACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;0DAKjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEACC,WAAW,CAAC,6CAA6C,EACvD,KAAK,IAAI,KAAK,UACV,4BACA,6BACJ;0EAED,KAAK,IAAI;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAU;0EAA2E;;;;;;;;;;;;kEAK7F,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;gEAAwB;gEACpB;gEAChB,IAAI,KACH,KAAK,SAAS,IAAI,KAAK,GAAG,IAC1B,kBAAkB;;;;;;;;;;;;;;;;;;;uCA5CrB,KAAK,EAAE;;;;;;;;;;;;;;;;oBAsDrB,QAAQ,YAAY,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;0CACZ,KAAK,MAAM,CACT,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAoB,oBACxB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEAAmI;oEAC9I,MAAM,EAAE;;;;;;;0EAEZ,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;;4EAA0B;4EAC9B,MAAM,EAAE;;;;;;;kFAElB,8OAAC;wEAAE,WAAU;kFACV,MAAM,IAAI,EAAE,QACX,MAAM,IAAI,EAAE,SACZ;;;;;;;;;;;;;;;;;;kEAKR,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EACvD,MAAM,MAAM,KAAK,cACb,gCACA,MAAM,MAAM,KAAK,YACjB,kCACA,6BACJ;kEAED,MAAM,MAAM,KAAK,cACd,gBACA,MAAM,MAAM,KAAK,YACjB,cACA,MAAM,MAAM;;;;;;;;;;;;4CAInB,MAAM,OAAO,kBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,MAAM,OAAO,CAAC,KAAK,IAAI;4DAC5B,KAAK,MAAM,OAAO,CAAC,IAAI;4DACvB,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,MAAM,OAAO,CAAC,IAAI;;;;;;0EAErB,8OAAC;gEAAE,WAAU;;oEAAuC;oEAChD,MAAM,OAAO,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;0DAMjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EACb,IAAI,KACH,MAAM,SAAS,IAAI,KAAK,GAAG,IAC3B,kBAAkB;;;;;;;;;;;;oDAIvB,MAAM,aAAa,kBAClB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EAAwB;4EAC1B,MAAM,aAAa,CAAC,KAAK;;;;;;;oEAErC,MAAM,aAAa,CAAC,IAAI,kBACvB,8OAAC;wEAAI,WAAU;;4EAAwB;4EACpB,MAAM,aAAa,CAAC,IAAI;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;4CASrD,MAAM,MAAM,KAAK,2BAChB,8OAAC;gDACC,WAAU;gDACV,SAAS,IACP,MAAM,EAAE,IAAI,wBAAwB,MAAM,EAAE;0DAE/C;;;;;;;uCA7FE,MAAM,EAAE,IAAI;;;;;;;;;;;;;;;;kCAuG7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,MAAK;4DACL,QAAO;4DACP,SAAQ;sEAER,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,aAAa;gEACb,GAAE;;;;;;;;;;;;;;;;kEAIR,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAGlD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAKvC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,MAAK;4DACL,QAAO;4DACP,SAAQ;sEAER,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,aAAa;gEACb,GAAE;;;;;;;;;;;;;;;;kEAIR,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAGlD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAKvC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,MAAK;4DACL,QAAO;4DACP,SAAQ;sEAER,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,aAAa;gEACb,GAAE;;;;;;;;;;;;;;;;kEAIR,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAGlD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU/C,8OAAC,0HAAA,CAAA,UAAI;gBAAC,QAAQ;gBAAY,SAAS;;;;;;0BAGnC,8OAAC,uIAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,OAAM;gBACN,SAAQ;gBACR,aAAY;gBACZ,YAAW;gBACX,WAAW;gBACX,UAAU;gBACV,MAAK;;;;;;;;;;;;AAIb", "debugId": null}}]}