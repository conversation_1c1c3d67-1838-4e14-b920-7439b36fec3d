import { PrismaService } from './prisma.service';
export declare class UserController {
    private prisma;
    constructor(prisma: PrismaService);
    getDashboard(req: any): Promise<{
        user: {
            id: number;
            email: string;
            name: string | null;
            role: import(".prisma/client").$Enums.Role;
            profileImage: string | null;
        } | null;
        orders: ({
            product: {
                id: number;
                name: string;
                image: string;
                price: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            userId: number;
            productId: number;
            status: import(".prisma/client").$Enums.OrderStatus;
            customerName: string;
            customerPhone: string | null;
            customerEmail: string | null;
            customerAddress: string | null;
            customColor: string | null;
            customText: string | null;
            isCustomized: boolean | null;
            customizationData: string | null;
            customizationPreview: string | null;
            quantity: number;
            unitPrice: number | null;
            totalPrice: number | null;
        })[];
    }>;
    changePassword(req: any, body: {
        oldPassword: string;
        newPassword: string;
    }): Promise<{
        success: boolean;
        message: string;
    } | {
        success: boolean;
        message?: undefined;
    }>;
    updateProfile(req: any, body: {
        name?: string;
        profileImage?: string;
    }): Promise<{
        id: number;
        email: string;
        name: string | null;
        role: import(".prisma/client").$Enums.Role;
        profileImage: string | null;
    }>;
    deleteAccount(req: any): Promise<{
        success: boolean;
    }>;
    createOrder(req: any, body: any): Promise<{
        user: {
            id: number;
            email: string;
            name: string | null;
        };
        product: {
            id: number;
            name: string;
            image: string;
            price: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        userId: number;
        productId: number;
        status: import(".prisma/client").$Enums.OrderStatus;
        customerName: string;
        customerPhone: string | null;
        customerEmail: string | null;
        customerAddress: string | null;
        customColor: string | null;
        customText: string | null;
        isCustomized: boolean | null;
        customizationData: string | null;
        customizationPreview: string | null;
        quantity: number;
        unitPrice: number | null;
        totalPrice: number | null;
    }>;
}
