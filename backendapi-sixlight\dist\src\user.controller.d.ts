import { PrismaService } from './prisma.service';
export declare class UserController {
    private prisma;
    constructor(prisma: PrismaService);
    getDashboard(req: any): Promise<{
        user: {
            id: number;
            email: string;
            name: string | null;
            profileImage: string | null;
            role: import(".prisma/client").$Enums.Role;
        } | null;
        orders: ({
            product: {
                id: number;
                name: string;
                image: string;
                price: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            userId: number;
            productId: number;
            status: import(".prisma/client").$Enums.OrderStatus;
            customerName: string;
            customerPhone: string;
            customerEmail: string;
            customerAddress: string;
            customColor: string | null;
            customText: string | null;
            isCustomized: boolean;
            quantity: number;
            unitPrice: number;
            totalPrice: number;
        })[];
    }>;
    changePassword(req: any, body: {
        oldPassword: string;
        newPassword: string;
    }): Promise<{
        success: boolean;
        message: string;
    } | {
        success: boolean;
        message?: undefined;
    }>;
    updateProfile(req: any, body: {
        name?: string;
        profileImage?: string;
    }): Promise<{
        id: number;
        email: string;
        name: string | null;
        profileImage: string | null;
        role: import(".prisma/client").$Enums.Role;
    }>;
    deleteAccount(req: any): Promise<{
        success: boolean;
    }>;
    createOrder(req: any, body: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        userId: number;
        productId: number;
        status: import(".prisma/client").$Enums.OrderStatus;
        customerName: string;
        customerPhone: string;
        customerEmail: string;
        customerAddress: string;
        customColor: string | null;
        customText: string | null;
        isCustomized: boolean;
        quantity: number;
        unitPrice: number;
        totalPrice: number;
    }>;
}
