(()=>{var e={};e.id=454,e.ids=[454],e.modules={329:(e,s,r)=>{Promise.resolve().then(r.bind(r,4530))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3931:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},4021:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4530:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx","default")},5043:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(687),a=r(3210),i=r(3282),o=r(5814),l=r.n(o),n=r(474),d=r(9275),c=r(2688);let m=(0,c.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var p=r(3613),u=r(3931),x=r(4021),h=r(2597),g=r(3861),f=r(5336);let b=(0,c.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),v=d.z.object({name:d.z.string().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"Name can only contain letters and spaces"),email:d.z.string().email("Please enter a valid email address").max(100,"Email must be less than 100 characters"),password:d.z.string().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),confirmPassword:d.z.string(),terms:d.z.boolean().refine(e=>!0===e,{message:"You must accept the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),y=e=>{let s=0,r={length:e.length>=8,uppercase:/[A-Z]/.test(e),lowercase:/[a-z]/.test(e),number:/[0-9]/.test(e),special:/[^A-Za-z0-9]/.test(e)};return Object.values(r).forEach(e=>e&&s++),{score:s,checks:r,strength:s<2?"weak":s<4?"medium":"strong"}};function w(){let[e,s]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:"",terms:!1}),[r,o]=(0,a.useState)({}),[d,c]=(0,a.useState)(""),[w,j]=(0,a.useState)(!1),[N,k]=(0,a.useState)(!1),[P,A]=(0,a.useState)(""),[z,C]=(0,a.useState)(!1),[_,S]=(0,a.useState)(!1),D=(e,t)=>{s(s=>({...s,[e]:t})),r[e]&&o(s=>({...s,[e]:void 0}))};async function q(s){s.preventDefault(),j(!0),c(""),k(!1),o({});let r=v.safeParse(e);if(!r.success){j(!1);let e={};r.error.errors.forEach(s=>{s.path[0]&&(e[s.path[0]]=s.message)}),o(e);return}try{let s=await (0,i.kz)(e.email,e.password,e.name);j(!1),s.message?(k(!0),A(s.message)):s.access_token?(k(!0),A("Registration successful! Please check your email to verify your account.")):c(s.error||"Registration failed")}catch{j(!1),c("Network error. Please try again.")}}return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-lg bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100",children:[(0,t.jsx)("div",{className:"flex flex-col items-center",children:(0,t.jsx)(n.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,t.jsx)("h2",{className:"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Create Account"}),(0,t.jsxs)("form",{onSubmit:q,className:"flex flex-col gap-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,t.jsx)("input",{id:"name",name:"name",value:e.name,onChange:e=>D("name",e.target.value),placeholder:"Full Name",type:"text",autoComplete:"name",className:`w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ${r.name?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,"aria-invalid":!!r.name,"aria-describedby":r.name?"name-error":void 0})]}),r.name&&(0,t.jsxs)("div",{id:"name-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(p.A,{size:16}),r.name]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,t.jsx)("input",{id:"email",name:"email",value:e.email,onChange:e=>D("email",e.target.value),placeholder:"Email Address",type:"email",autoComplete:"email",className:`w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ${r.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,"aria-invalid":!!r.email,"aria-describedby":r.email?"email-error":void 0})]}),r.email&&(0,t.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(p.A,{size:16}),r.email]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,t.jsx)("input",{id:"password",name:"password",value:e.password,onChange:e=>D("password",e.target.value),type:z?"text":"password",placeholder:"Password",autoComplete:"new-password",className:`w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ${r.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,"aria-invalid":!!r.password,"aria-describedby":r.password?"password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>C(!z),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":z?"Hide password":"Show password",children:z?(0,t.jsx)(h.A,{size:20}):(0,t.jsx)(g.A,{size:20})})]}),e.password&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(s=>{let r=y(e.password);return(0,t.jsx)("div",{className:`h-1 flex-1 rounded ${s<=r.score?"weak"===r.strength?"bg-red-500":"medium"===r.strength?"bg-yellow-500":"bg-green-500":"bg-gray-200"}`},s)})}),(0,t.jsx)("div",{className:"text-xs space-y-1",children:Object.entries(y(e.password).checks).map(([e,s])=>(0,t.jsxs)("div",{className:`flex items-center gap-1 ${s?"text-green-600":"text-gray-400"}`,children:[s?(0,t.jsx)(f.A,{size:12}):(0,t.jsx)(p.A,{size:12}),(0,t.jsxs)("span",{children:["length"===e&&"8+ characters","uppercase"===e&&"Uppercase letter","lowercase"===e&&"Lowercase letter","number"===e&&"Number","special"===e&&"Special character"]})]},e))})]}),r.password&&(0,t.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(p.A,{size:16}),r.password]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",value:e.confirmPassword,onChange:e=>D("confirmPassword",e.target.value),type:_?"text":"password",autoComplete:"new-password",placeholder:"Confirm Password",className:`w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ${r.confirmPassword?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,"aria-invalid":!!r.confirmPassword,"aria-describedby":r.confirmPassword?"confirm-password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>S(!_),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":_?"Hide password":"Show password",children:_?(0,t.jsx)(h.A,{size:20}):(0,t.jsx)(g.A,{size:20})})]}),r.confirmPassword&&(0,t.jsxs)("div",{id:"confirm-password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(p.A,{size:16}),r.confirmPassword]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("label",{className:"flex items-start gap-3 text-sm cursor-pointer",children:[(0,t.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:e.terms,onChange:e=>D("terms",e.target.checked),className:`mt-1 accent-blue-600 ${r.terms?"accent-red-600":""}`,"aria-invalid":!!r.terms,"aria-describedby":r.terms?"terms-error":void 0}),(0,t.jsxs)("span",{className:"text-gray-700",children:["I accept the"," ",(0,t.jsx)("a",{href:"#",className:"underline text-blue-600 hover:text-blue-800",children:"terms and conditions"})," ","and"," ",(0,t.jsx)("a",{href:"#",className:"underline text-blue-600 hover:text-blue-800",children:"privacy policy"})]})]}),r.terms&&(0,t.jsxs)("div",{id:"terms-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(p.A,{size:16}),r.terms]})]}),(0,t.jsx)("button",{type:"submit",className:`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${w?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"}`,disabled:w,children:w?(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,t.jsx)("span",{children:"Creating Account..."})]}):"Create Account"}),d&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,t.jsx)(p.A,{size:16}),d]}),N&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-green-600 text-sm bg-green-50 p-3 rounded-lg border border-green-200",children:[(0,t.jsx)(f.A,{size:16}),P||"Registration successful! Please check your email to verify your account."]})]}),(0,t.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(l(),{href:"/login",className:"text-red-700 font-semibold hover:underline",children:"Login"})]})]})})}},5336:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6557:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(5239),a=r(8088),i=r(8170),o=r.n(i),l=r(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(s,n);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4530)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6777:(e,s,r)=>{Promise.resolve().then(r.bind(r,5043))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[728,474,814,743,24],()=>r(6557));module.exports=t})();