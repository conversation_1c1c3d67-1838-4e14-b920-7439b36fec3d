(()=>{var e={};e.id=454,e.ids=[454],e.modules={329:(e,t,a)=>{Promise.resolve().then(a.bind(a,4530))},440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1755:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3282:(e,t,a)=>{"use strict";a.d(t,{i:()=>s,k:()=>i});let r="http://localhost:3001";async function s(e,t){return(await fetch(`${r}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})})).json()}async function i(e,t,a){return(await fetch(`${r}/auth/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:a})})).json()}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u,metadata:()=>o});var r=a(7413),s=a(2376),i=a.n(s),n=a(8726),d=a.n(n);a(1135);let o={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:`${i().variable} ${d().variable} antialiased`,children:e})})}},4530:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx","default")},4899:()=>{},6557:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>h,tree:()=>u});var r=a(5239),s=a(8088),i=a(8170),n=a.n(i),d=a(893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);a.d(t,o);let u={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4530)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\register\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6777:(e,t,a)=>{Promise.resolve().then(a.bind(a,8544))},7219:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6444,23)),Promise.resolve().then(a.t.bind(a,6042,23)),Promise.resolve().then(a.t.bind(a,8170,23)),Promise.resolve().then(a.t.bind(a,9477,23)),Promise.resolve().then(a.t.bind(a,9345,23)),Promise.resolve().then(a.t.bind(a,2089,23)),Promise.resolve().then(a.t.bind(a,6577,23)),Promise.resolve().then(a.t.bind(a,1307,23))},7467:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6346,23)),Promise.resolve().then(a.t.bind(a,7924,23)),Promise.resolve().then(a.t.bind(a,5656,23)),Promise.resolve().then(a.t.bind(a,99,23)),Promise.resolve().then(a.t.bind(a,8243,23)),Promise.resolve().then(a.t.bind(a,8827,23)),Promise.resolve().then(a.t.bind(a,2763,23)),Promise.resolve().then(a.t.bind(a,7173,23))},8544:(e,t,a)=>{"use strict";let r;a.r(t),a.d(t,{default:()=>eV});var s,i,n,d,o,u,l=a(687),c=a(3210),h=a(3282),p=a(5814),m=a.n(p),f=a(474);!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let a of e)t[a]=a;return t},e.getValidEnumValues=t=>{let a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(let e of a)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(let a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(i||(i={})).mergeShapes=(e,t)=>({...e,...t});let g=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),_=e=>{switch(typeof e){case"undefined":return g.undefined;case"string":return g.string;case"number":return Number.isNaN(e)?g.nan:g.number;case"boolean":return g.boolean;case"function":return g.function;case"bigint":return g.bigint;case"symbol":return g.symbol;case"object":if(Array.isArray(e))return g.array;if(null===e)return g.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return g.promise;if("undefined"!=typeof Map&&e instanceof Map)return g.map;if("undefined"!=typeof Set&&e instanceof Set)return g.set;if("undefined"!=typeof Date&&e instanceof Date)return g.date;return g.object;default:return g.unknown}},y=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class v extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},a={_errors:[]},r=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(r);else if("invalid_return_type"===s.code)r(s.returnTypeError);else if("invalid_arguments"===s.code)r(s.argumentsError);else if(0===s.path.length)a._errors.push(t(s));else{let e=a,r=0;for(;r<s.path.length;){let a=s.path[r];r===s.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(s))):e[a]=e[a]||{_errors:[]},e=e[a],r++}}};return r(this),a}static assert(e){if(!(e instanceof v))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},a=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):a.push(e(r));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}v.create=e=>new v(e);let x=(e,t)=>{let a;switch(e.code){case y.invalid_type:a=e.received===g.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case y.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case y.unrecognized_keys:a=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case y.invalid_union:a="Invalid input";break;case y.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case y.invalid_enum_value:a=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case y.invalid_arguments:a="Invalid function arguments";break;case y.invalid_return_type:a="Invalid function return type";break;case y.invalid_date:a="Invalid date";break;case y.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case y.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case y.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case y.custom:a="Invalid input";break;case y.invalid_intersection_types:a="Intersection results could not be merged";break;case y.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case y.not_finite:a="Number must be finite";break;default:a=t.defaultError,s.assertNever(e)}return{message:a}},b=e=>{let{data:t,path:a,errorMaps:r,issueData:s}=e,i=[...a,...s.path||[]],n={...s,path:i};if(void 0!==s.message)return{...s,path:i,message:s.message};let d="";for(let e of r.filter(e=>!!e).slice().reverse())d=e(n,{data:t,defaultError:d}).message;return{...s,path:i,message:d}};function k(e,t){let a=b({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,x,x==x?void 0:x].filter(e=>!!e)});e.common.issues.push(a)}class w{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let a=[];for(let r of t){if("aborted"===r.status)return T;"dirty"===r.status&&e.dirty(),a.push(r.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){let a=[];for(let e of t){let t=await e.key,r=await e.value;a.push({key:t,value:r})}return w.mergeObjectSync(e,a)}static mergeObjectSync(e,t){let a={};for(let r of t){let{key:t,value:s}=r;if("aborted"===t.status||"aborted"===s.status)return T;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||r.alwaysSet)&&(a[t.value]=s.value)}return{status:e.value,value:a}}}let T=Object.freeze({status:"aborted"}),C=e=>({status:"dirty",value:e}),Z=e=>({status:"valid",value:e}),j=e=>"aborted"===e.status,N=e=>"dirty"===e.status,O=e=>"valid"===e.status,S=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(n||(n={}));var A=function(e,t,a,r){if("a"===a&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===a?r:"a"===a?r.call(e):r?r.value:t.get(e)},P=function(e,t,a,r,s){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?s.call(e,a):s?s.value=a:t.set(e,a),a};class E{constructor(e,t,a,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let I=(e,t)=>{if(O(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new v(e.common.issues);return this._error=t,this._error}}};function R(e){if(!e)return{};let{errorMap:t,invalid_type_error:a,required_error:r,description:s}=e;if(t&&(a||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??s.defaultError}:void 0===s.data?{message:i??r??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:i??a??s.defaultError}},description:s}}class ${get description(){return this._def.description}_getType(e){return _(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:_(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new w,ctx:{common:e.parent.common,data:e.data,parsedType:_(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(S(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){let a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:_(e)},r=this._parseSync({data:e,path:a.path,parent:a});return I(a,r)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:_(e)};if(!this["~standard"].async)try{let a=this._parseSync({data:e,path:[],parent:t});return O(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>O(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){let a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:_(e)},r=this._parse({data:e,path:a.path,parent:a});return I(a,await (S(r)?r:Promise.resolve(r)))}refine(e,t){let a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let s=e(t),i=()=>r.addIssue({code:y.custom,...a(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(i(),!1)):!!s||(i(),!1)})}refinement(e,t){return this._refinement((a,r)=>!!e(a)||(r.addIssue("function"==typeof t?t(a,r):t),!1))}_refinement(e){return new eO({schema:this,typeName:u.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eS.create(this,this._def)}nullable(){return eA.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eh.create(this)}promise(){return eN.create(this,this._def)}or(e){return em.create([this,e],this._def)}and(e){return e_.create(this,e,this._def)}transform(e){return new eO({...R(this._def),schema:this,typeName:u.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eP({...R(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:u.ZodDefault})}brand(){return new eR({typeName:u.ZodBranded,type:this,...R(this._def)})}catch(e){return new eE({...R(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:u.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return e$.create(this,e)}readonly(){return eM.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let M=/^c[^\s-]{8,}$/i,F=/^[0-9a-z]+$/,L=/^[0-9A-HJKMNP-TV-Z]{26}$/i,D=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,z=/^[a-z0-9_-]{21}$/i,V=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,U=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,q=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,K=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,W=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,B=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,G=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,J=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,H=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Y="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",X=RegExp(`^${Y}$`);function Q(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let a=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${a}`}class ee extends ${_parse(e){var t,a,i,n;let d;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==g.string){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.string,received:t.parsedType}),T}let o=new w;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(k(d=this._getOrReturnCtx(e,d),{code:y.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),o.dirty());else if("max"===u.kind)e.data.length>u.value&&(k(d=this._getOrReturnCtx(e,d),{code:y.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),o.dirty());else if("length"===u.kind){let t=e.data.length>u.value,a=e.data.length<u.value;(t||a)&&(d=this._getOrReturnCtx(e,d),t?k(d,{code:y.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):a&&k(d,{code:y.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),o.dirty())}else if("email"===u.kind)q.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"email",code:y.invalid_string,message:u.message}),o.dirty());else if("emoji"===u.kind)r||(r=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),r.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"emoji",code:y.invalid_string,message:u.message}),o.dirty());else if("uuid"===u.kind)D.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"uuid",code:y.invalid_string,message:u.message}),o.dirty());else if("nanoid"===u.kind)z.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"nanoid",code:y.invalid_string,message:u.message}),o.dirty());else if("cuid"===u.kind)M.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"cuid",code:y.invalid_string,message:u.message}),o.dirty());else if("cuid2"===u.kind)F.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"cuid2",code:y.invalid_string,message:u.message}),o.dirty());else if("ulid"===u.kind)L.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"ulid",code:y.invalid_string,message:u.message}),o.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{k(d=this._getOrReturnCtx(e,d),{validation:"url",code:y.invalid_string,message:u.message}),o.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"regex",code:y.invalid_string,message:u.message}),o.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(k(d=this._getOrReturnCtx(e,d),{code:y.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),o.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(k(d=this._getOrReturnCtx(e,d),{code:y.invalid_string,validation:{startsWith:u.value},message:u.message}),o.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(k(d=this._getOrReturnCtx(e,d),{code:y.invalid_string,validation:{endsWith:u.value},message:u.message}),o.dirty()):"datetime"===u.kind?(function(e){let t=`${Y}T${Q(e)}`,a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,RegExp(`^${t}$`)})(u).test(e.data)||(k(d=this._getOrReturnCtx(e,d),{code:y.invalid_string,validation:"datetime",message:u.message}),o.dirty()):"date"===u.kind?X.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{code:y.invalid_string,validation:"date",message:u.message}),o.dirty()):"time"===u.kind?RegExp(`^${Q(u)}$`).test(e.data)||(k(d=this._getOrReturnCtx(e,d),{code:y.invalid_string,validation:"time",message:u.message}),o.dirty()):"duration"===u.kind?U.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"duration",code:y.invalid_string,message:u.message}),o.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(a=u.version)||!a)&&K.test(t)||("v6"===a||!a)&&B.test(t))&&1&&(k(d=this._getOrReturnCtx(e,d),{validation:"ip",code:y.invalid_string,message:u.message}),o.dirty())):"jwt"===u.kind?!function(e,t){if(!V.test(e))return!1;try{let[a]=e.split("."),r=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),s=JSON.parse(atob(r));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(k(d=this._getOrReturnCtx(e,d),{validation:"jwt",code:y.invalid_string,message:u.message}),o.dirty()):"cidr"===u.kind?(i=e.data,!(("v4"===(n=u.version)||!n)&&W.test(i)||("v6"===n||!n)&&G.test(i))&&1&&(k(d=this._getOrReturnCtx(e,d),{validation:"cidr",code:y.invalid_string,message:u.message}),o.dirty())):"base64"===u.kind?J.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"base64",code:y.invalid_string,message:u.message}),o.dirty()):"base64url"===u.kind?H.test(e.data)||(k(d=this._getOrReturnCtx(e,d),{validation:"base64url",code:y.invalid_string,message:u.message}),o.dirty()):s.assertNever(u);return{status:o.value,value:e.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:y.invalid_string,...n.errToObj(a)})}_addCheck(e){return new ee({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...n.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...n.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...n.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...n.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errToObj(t)})}nonempty(e){return this.min(1,n.errToObj(e))}trim(){return new ee({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ee({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ee({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ee.create=e=>new ee({checks:[],typeName:u.ZodString,coerce:e?.coerce??!1,...R(e)});class et extends ${constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==g.number){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.number,received:t.parsedType}),T}let a=new w;for(let r of this._def.checks)"int"===r.kind?s.isInteger(e.data)||(k(t=this._getOrReturnCtx(e,t),{code:y.invalid_type,expected:"integer",received:"float",message:r.message}),a.dirty()):"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(k(t=this._getOrReturnCtx(e,t),{code:y.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(k(t=this._getOrReturnCtx(e,t),{code:y.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"multipleOf"===r.kind?0!==function(e,t){let a=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=a>r?a:r;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,r.value)&&(k(t=this._getOrReturnCtx(e,t),{code:y.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(k(t=this._getOrReturnCtx(e,t),{code:y.not_finite,message:r.message}),a.dirty()):s.assertNever(r);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,a,r){return new et({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:n.toString(r)}]})}_addCheck(e){return new et({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let a of this._def.checks)if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;else"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return Number.isFinite(t)&&Number.isFinite(e)}}et.create=e=>new et({checks:[],typeName:u.ZodNumber,coerce:e?.coerce||!1,...R(e)});class ea extends ${constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==g.bigint)return this._getInvalidInput(e);let a=new w;for(let r of this._def.checks)"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(k(t=this._getOrReturnCtx(e,t),{code:y.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(k(t=this._getOrReturnCtx(e,t),{code:y.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(k(t=this._getOrReturnCtx(e,t),{code:y.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):s.assertNever(r);return{status:a.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.bigint,received:t.parsedType}),T}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,a,r){return new ea({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:n.toString(r)}]})}_addCheck(e){return new ea({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ea.create=e=>new ea({checks:[],typeName:u.ZodBigInt,coerce:e?.coerce??!1,...R(e)});class er extends ${_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==g.boolean){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.boolean,received:t.parsedType}),T}return Z(e.data)}}er.create=e=>new er({typeName:u.ZodBoolean,coerce:e?.coerce||!1,...R(e)});class es extends ${_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==g.date){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.date,received:t.parsedType}),T}if(Number.isNaN(e.data.getTime()))return k(this._getOrReturnCtx(e),{code:y.invalid_date}),T;let a=new w;for(let r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(k(t=this._getOrReturnCtx(e,t),{code:y.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),a.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(k(t=this._getOrReturnCtx(e,t),{code:y.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),a.dirty()):s.assertNever(r);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new es({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}es.create=e=>new es({checks:[],coerce:e?.coerce||!1,typeName:u.ZodDate,...R(e)});class ei extends ${_parse(e){if(this._getType(e)!==g.symbol){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.symbol,received:t.parsedType}),T}return Z(e.data)}}ei.create=e=>new ei({typeName:u.ZodSymbol,...R(e)});class en extends ${_parse(e){if(this._getType(e)!==g.undefined){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.undefined,received:t.parsedType}),T}return Z(e.data)}}en.create=e=>new en({typeName:u.ZodUndefined,...R(e)});class ed extends ${_parse(e){if(this._getType(e)!==g.null){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.null,received:t.parsedType}),T}return Z(e.data)}}ed.create=e=>new ed({typeName:u.ZodNull,...R(e)});class eo extends ${constructor(){super(...arguments),this._any=!0}_parse(e){return Z(e.data)}}eo.create=e=>new eo({typeName:u.ZodAny,...R(e)});class eu extends ${constructor(){super(...arguments),this._unknown=!0}_parse(e){return Z(e.data)}}eu.create=e=>new eu({typeName:u.ZodUnknown,...R(e)});class el extends ${_parse(e){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.never,received:t.parsedType}),T}}el.create=e=>new el({typeName:u.ZodNever,...R(e)});class ec extends ${_parse(e){if(this._getType(e)!==g.undefined){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.void,received:t.parsedType}),T}return Z(e.data)}}ec.create=e=>new ec({typeName:u.ZodVoid,...R(e)});class eh extends ${_parse(e){let{ctx:t,status:a}=this._processInputParams(e),r=this._def;if(t.parsedType!==g.array)return k(t,{code:y.invalid_type,expected:g.array,received:t.parsedType}),T;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,s=t.data.length<r.exactLength.value;(e||s)&&(k(t,{code:e?y.too_big:y.too_small,minimum:s?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),a.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(k(t,{code:y.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),a.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(k(t,{code:y.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new E(t,e,t.path,a)))).then(e=>w.mergeArray(a,e));let s=[...t.data].map((e,a)=>r.type._parseSync(new E(t,e,t.path,a)));return w.mergeArray(a,s)}get element(){return this._def.type}min(e,t){return new eh({...this._def,minLength:{value:e,message:n.toString(t)}})}max(e,t){return new eh({...this._def,maxLength:{value:e,message:n.toString(t)}})}length(e,t){return new eh({...this._def,exactLength:{value:e,message:n.toString(t)}})}nonempty(e){return this.min(1,e)}}eh.create=(e,t)=>new eh({type:e,minLength:null,maxLength:null,exactLength:null,typeName:u.ZodArray,...R(t)});class ep extends ${constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==g.object){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.object,received:t.parsedType}),T}let{status:t,ctx:a}=this._processInputParams(e),{shape:r,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof el&&"strip"===this._def.unknownKeys))for(let e in a.data)s.includes(e)||i.push(e);let n=[];for(let e of s){let t=r[e],s=a.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new E(a,s,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof el){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)i.length>0&&(k(a,{code:y.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let r=a.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new E(a,r,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let a=await t.key,r=await t.value;e.push({key:a,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>w.mergeObjectSync(t,e)):w.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return n.errToObj,new ep({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{let r=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:n.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new ep({...this._def,unknownKeys:"strip"})}passthrough(){return new ep({...this._def,unknownKeys:"passthrough"})}extend(e){return new ep({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ep({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:u.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ep({...this._def,catchall:e})}pick(e){let t={};for(let a of s.objectKeys(e))e[a]&&this.shape[a]&&(t[a]=this.shape[a]);return new ep({...this._def,shape:()=>t})}omit(e){let t={};for(let a of s.objectKeys(this.shape))e[a]||(t[a]=this.shape[a]);return new ep({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof ep){let a={};for(let r in t.shape){let s=t.shape[r];a[r]=eS.create(e(s))}return new ep({...t._def,shape:()=>a})}if(t instanceof eh)return new eh({...t._def,type:e(t.element)});if(t instanceof eS)return eS.create(e(t.unwrap()));if(t instanceof eA)return eA.create(e(t.unwrap()));if(t instanceof ey)return ey.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let a of s.objectKeys(this.shape)){let r=this.shape[a];e&&!e[a]?t[a]=r:t[a]=r.optional()}return new ep({...this._def,shape:()=>t})}required(e){let t={};for(let a of s.objectKeys(this.shape))if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof eS;)e=e._def.innerType;t[a]=e}return new ep({...this._def,shape:()=>t})}keyof(){return eC(s.objectKeys(this.shape))}}ep.create=(e,t)=>new ep({shape:()=>e,unknownKeys:"strip",catchall:el.create(),typeName:u.ZodObject,...R(t)}),ep.strictCreate=(e,t)=>new ep({shape:()=>e,unknownKeys:"strict",catchall:el.create(),typeName:u.ZodObject,...R(t)}),ep.lazycreate=(e,t)=>new ep({shape:e,unknownKeys:"strip",catchall:el.create(),typeName:u.ZodObject,...R(t)});class em extends ${_parse(e){let{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{let a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;let a=e.map(e=>new v(e.ctx.common.issues));return k(t,{code:y.invalid_union,unionErrors:a}),T});{let e,r=[];for(let s of a){let a={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:a});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:a}),a.common.issues.length&&r.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=r.map(e=>new v(e));return k(t,{code:y.invalid_union,unionErrors:s}),T}}get options(){return this._def.options}}em.create=(e,t)=>new em({options:e,typeName:u.ZodUnion,...R(t)});let ef=e=>{if(e instanceof ew)return ef(e.schema);if(e instanceof eO)return ef(e.innerType());if(e instanceof eT)return[e.value];if(e instanceof eZ)return e.options;if(e instanceof ej)return s.objectValues(e.enum);else if(e instanceof eP)return ef(e._def.innerType);else if(e instanceof en)return[void 0];else if(e instanceof ed)return[null];else if(e instanceof eS)return[void 0,...ef(e.unwrap())];else if(e instanceof eA)return[null,...ef(e.unwrap())];else if(e instanceof eR)return ef(e.unwrap());else if(e instanceof eM)return ef(e.unwrap());else if(e instanceof eE)return ef(e._def.innerType);else return[]};class eg extends ${_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==g.object)return k(t,{code:y.invalid_type,expected:g.object,received:t.parsedType}),T;let a=this.discriminator,r=t.data[a],s=this.optionsMap.get(r);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(k(t,{code:y.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),T)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){let r=new Map;for(let a of t){let t=ef(a.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(r.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);r.set(s,a)}}return new eg({typeName:u.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...R(a)})}}class e_ extends ${_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=(e,r)=>{if(j(e)||j(r))return T;let i=function e(t,a){let r=_(t),i=_(a);if(t===a)return{valid:!0,data:t};if(r===g.object&&i===g.object){let r=s.objectKeys(a),i=s.objectKeys(t).filter(e=>-1!==r.indexOf(e)),n={...t,...a};for(let r of i){let s=e(t[r],a[r]);if(!s.valid)return{valid:!1};n[r]=s.data}return{valid:!0,data:n}}if(r===g.array&&i===g.array){if(t.length!==a.length)return{valid:!1};let r=[];for(let s=0;s<t.length;s++){let i=e(t[s],a[s]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}if(r===g.date&&i===g.date&&+t==+a)return{valid:!0,data:t};return{valid:!1}}(e.value,r.value);return i.valid?((N(e)||N(r))&&t.dirty(),{status:t.value,value:i.data}):(k(a,{code:y.invalid_intersection_types}),T)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}e_.create=(e,t,a)=>new e_({left:e,right:t,typeName:u.ZodIntersection,...R(a)});class ey extends ${_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.array)return k(a,{code:y.invalid_type,expected:g.array,received:a.parsedType}),T;if(a.data.length<this._def.items.length)return k(a,{code:y.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),T;!this._def.rest&&a.data.length>this._def.items.length&&(k(a,{code:y.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...a.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new E(a,e,a.path,t)):null}).filter(e=>!!e);return a.common.async?Promise.all(r).then(e=>w.mergeArray(t,e)):w.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ey({...this._def,rest:e})}}ey.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ey({items:e,typeName:u.ZodTuple,rest:null,...R(t)})};class ev extends ${get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.object)return k(a,{code:y.invalid_type,expected:g.object,received:a.parsedType}),T;let r=[],s=this._def.keyType,i=this._def.valueType;for(let e in a.data)r.push({key:s._parse(new E(a,e,a.path,e)),value:i._parse(new E(a,a.data[e],a.path,e)),alwaysSet:e in a.data});return a.common.async?w.mergeObjectAsync(t,r):w.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,a){return new ev(t instanceof $?{keyType:e,valueType:t,typeName:u.ZodRecord,...R(a)}:{keyType:ee.create(),valueType:e,typeName:u.ZodRecord,...R(t)})}}class ex extends ${get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.map)return k(a,{code:y.invalid_type,expected:g.map,received:a.parsedType}),T;let r=this._def.keyType,s=this._def.valueType,i=[...a.data.entries()].map(([e,t],i)=>({key:r._parse(new E(a,e,a.path,[i,"key"])),value:s._parse(new E(a,t,a.path,[i,"value"]))}));if(a.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let a of i){let r=await a.key,s=await a.value;if("aborted"===r.status||"aborted"===s.status)return T;("dirty"===r.status||"dirty"===s.status)&&t.dirty(),e.set(r.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let a of i){let r=a.key,s=a.value;if("aborted"===r.status||"aborted"===s.status)return T;("dirty"===r.status||"dirty"===s.status)&&t.dirty(),e.set(r.value,s.value)}return{status:t.value,value:e}}}}ex.create=(e,t,a)=>new ex({valueType:t,keyType:e,typeName:u.ZodMap,...R(a)});class eb extends ${_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.set)return k(a,{code:y.invalid_type,expected:g.set,received:a.parsedType}),T;let r=this._def;null!==r.minSize&&a.data.size<r.minSize.value&&(k(a,{code:y.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&a.data.size>r.maxSize.value&&(k(a,{code:y.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let s=this._def.valueType;function i(e){let a=new Set;for(let r of e){if("aborted"===r.status)return T;"dirty"===r.status&&t.dirty(),a.add(r.value)}return{status:t.value,value:a}}let n=[...a.data.values()].map((e,t)=>s._parse(new E(a,e,a.path,t)));return a.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new eb({...this._def,minSize:{value:e,message:n.toString(t)}})}max(e,t){return new eb({...this._def,maxSize:{value:e,message:n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}eb.create=(e,t)=>new eb({valueType:e,minSize:null,maxSize:null,typeName:u.ZodSet,...R(t)});class ek extends ${constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==g.function)return k(t,{code:y.invalid_type,expected:g.function,received:t.parsedType}),T;function a(e,a){return b({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,x,x].filter(e=>!!e),issueData:{code:y.invalid_arguments,argumentsError:a}})}function r(e,a){return b({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,x,x].filter(e=>!!e),issueData:{code:y.invalid_return_type,returnTypeError:a}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof eN){let e=this;return Z(async function(...t){let n=new v([]),d=await e._def.args.parseAsync(t,s).catch(e=>{throw n.addIssue(a(t,e)),n}),o=await Reflect.apply(i,this,d);return await e._def.returns._def.type.parseAsync(o,s).catch(e=>{throw n.addIssue(r(o,e)),n})})}{let e=this;return Z(function(...t){let n=e._def.args.safeParse(t,s);if(!n.success)throw new v([a(t,n.error)]);let d=Reflect.apply(i,this,n.data),o=e._def.returns.safeParse(d,s);if(!o.success)throw new v([r(d,o.error)]);return o.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ek({...this._def,args:ey.create(e).rest(eu.create())})}returns(e){return new ek({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new ek({args:e||ey.create([]).rest(eu.create()),returns:t||eu.create(),typeName:u.ZodFunction,...R(a)})}}class ew extends ${get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ew.create=(e,t)=>new ew({getter:e,typeName:u.ZodLazy,...R(t)});class eT extends ${_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return k(t,{received:t.data,code:y.invalid_literal,expected:this._def.value}),T}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eC(e,t){return new eZ({values:e,typeName:u.ZodEnum,...R(t)})}eT.create=(e,t)=>new eT({value:e,typeName:u.ZodLiteral,...R(t)});class eZ extends ${constructor(){super(...arguments),d.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),a=this._def.values;return k(t,{expected:s.joinValues(a),received:t.parsedType,code:y.invalid_type}),T}if(A(this,d,"f")||P(this,d,new Set(this._def.values),"f"),!A(this,d,"f").has(e.data)){let t=this._getOrReturnCtx(e),a=this._def.values;return k(t,{received:t.data,code:y.invalid_enum_value,options:a}),T}return Z(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eZ.create(e,{...this._def,...t})}exclude(e,t=this._def){return eZ.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}d=new WeakMap,eZ.create=eC;class ej extends ${constructor(){super(...arguments),o.set(this,void 0)}_parse(e){let t=s.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==g.string&&a.parsedType!==g.number){let e=s.objectValues(t);return k(a,{expected:s.joinValues(e),received:a.parsedType,code:y.invalid_type}),T}if(A(this,o,"f")||P(this,o,new Set(s.getValidEnumValues(this._def.values)),"f"),!A(this,o,"f").has(e.data)){let e=s.objectValues(t);return k(a,{received:a.data,code:y.invalid_enum_value,options:e}),T}return Z(e.data)}get enum(){return this._def.values}}o=new WeakMap,ej.create=(e,t)=>new ej({values:e,typeName:u.ZodNativeEnum,...R(t)});class eN extends ${unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==g.promise&&!1===t.common.async?(k(t,{code:y.invalid_type,expected:g.promise,received:t.parsedType}),T):Z((t.parsedType===g.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eN.create=(e,t)=>new eN({type:e,typeName:u.ZodPromise,...R(t)});class eO extends ${innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===u.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=this._def.effect||null,i={addIssue:e=>{k(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===r.type){let e=r.transform(a.data,i);if(a.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return T;let r=await this._def.schema._parseAsync({data:e,path:a.path,parent:a});return"aborted"===r.status?T:"dirty"===r.status||"dirty"===t.value?C(r.value):r});{if("aborted"===t.value)return T;let r=this._def.schema._parseSync({data:e,path:a.path,parent:a});return"aborted"===r.status?T:"dirty"===r.status||"dirty"===t.value?C(r.value):r}}if("refinement"===r.type){let e=e=>{let t=r.refinement(e,i);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(a=>"aborted"===a.status?T:("dirty"===a.status&&t.dirty(),e(a.value).then(()=>({status:t.value,value:a.value}))));{let r=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===r.status?T:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===r.type)if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(e=>O(e)?Promise.resolve(r.transform(e.value,i)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!O(e))return e;let s=r.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}s.assertNever(r)}}eO.create=(e,t,a)=>new eO({schema:e,typeName:u.ZodEffects,effect:t,...R(a)}),eO.createWithPreprocess=(e,t,a)=>new eO({schema:t,effect:{type:"preprocess",transform:e},typeName:u.ZodEffects,...R(a)});class eS extends ${_parse(e){return this._getType(e)===g.undefined?Z(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eS.create=(e,t)=>new eS({innerType:e,typeName:u.ZodOptional,...R(t)});class eA extends ${_parse(e){return this._getType(e)===g.null?Z(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eA.create=(e,t)=>new eA({innerType:e,typeName:u.ZodNullable,...R(t)});class eP extends ${_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return t.parsedType===g.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eP.create=(e,t)=>new eP({innerType:e,typeName:u.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...R(t)});class eE extends ${_parse(e){let{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return S(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new v(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new v(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}eE.create=(e,t)=>new eE({innerType:e,typeName:u.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...R(t)});class eI extends ${_parse(e){if(this._getType(e)!==g.nan){let t=this._getOrReturnCtx(e);return k(t,{code:y.invalid_type,expected:g.nan,received:t.parsedType}),T}return{status:"valid",value:e.data}}}eI.create=e=>new eI({typeName:u.ZodNaN,...R(e)}),Symbol("zod_brand");class eR extends ${_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class e$ extends ${_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?T:"dirty"===e.status?(t.dirty(),C(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})();{let e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?T:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new e$({in:e,out:t,typeName:u.ZodPipeline})}}class eM extends ${_parse(e){let t=this._def.innerType._parse(e),a=e=>(O(e)&&(e.value=Object.freeze(e.value)),e);return S(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}eM.create=(e,t)=>new eM({innerType:e,typeName:u.ZodReadonly,...R(t)}),ep.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(u||(u={}));let eF=ee.create;et.create,eI.create,ea.create,er.create,es.create,ei.create,en.create,ed.create,eo.create,eu.create,el.create,ec.create,eh.create;let eL=ep.create;ep.strictCreate,em.create,eg.create,e_.create,ey.create,ev.create,ex.create,eb.create,ek.create,ew.create;let eD=eT.create;eZ.create,ej.create,eN.create,eO.create,eS.create,eA.create,eO.createWithPreprocess,e$.create;let ez=eL({name:eF().min(2,"Name is required"),email:eF().email("Invalid email address"),password:eF().min(6,"Password must be at least 6 characters"),terms:eD(!0,{errorMap:()=>({message:"You must accept the terms and conditions"})})});function eV(){let[e,t]=(0,c.useState)(""),[a,r]=(0,c.useState)(""),[s,i]=(0,c.useState)(""),[n,d]=(0,c.useState)(""),[o,u]=(0,c.useState)(!1),[p,g]=(0,c.useState)(!1),[_,y]=(0,c.useState)(!1);async function v(t){t.preventDefault(),u(!0),d(""),g(!1);let r=ez.safeParse({name:s,email:e,password:a,terms:_});if(!r.success){u(!1),d(r.error.errors[0].message);return}let i=await (0,h.k)(e,a,s);u(!1),i.access_token?(g(!0),localStorage.setItem("token",i.access_token),window.location.href="/login"):d(i.error||"Registration failed")}return(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-black",children:(0,l.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-xl p-8 flex flex-col gap-6",children:[(0,l.jsx)("div",{className:"flex flex-col items-center",children:(0,l.jsx)(f.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,l.jsx)("h2",{className:"text-3xl font-extrabold text-center text-red-700",children:"Create Account"}),(0,l.jsxs)("form",{onSubmit:v,className:"flex flex-col gap-4",children:[(0,l.jsx)("input",{value:s,onChange:e=>i(e.target.value),placeholder:"Name",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"name"}),(0,l.jsx)("input",{value:e,onChange:e=>t(e.target.value),placeholder:"Email",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"email"}),(0,l.jsx)("input",{value:a,onChange:e=>r(e.target.value),type:"password",placeholder:"Password",className:"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition",required:!0,autoComplete:"new-password"}),(0,l.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,l.jsx)("input",{type:"checkbox",checked:_,onChange:e=>y(e.target.checked),className:"accent-red-700"}),"I accept the"," ",(0,l.jsx)("a",{href:"#",className:"underline text-red-700",children:"terms and conditions"})]}),(0,l.jsx)("button",{type:"submit",className:"bg-black text-white font-semibold px-6 py-3 rounded-lg shadow hover:bg-green-700 transition disabled:opacity-60",disabled:o,children:o?"Registering...":"Register"}),n&&(0,l.jsx)("div",{className:"text-red-600 text-center text-sm mt-2",children:n}),p&&(0,l.jsx)("div",{className:"text-green-600 text-center text-sm mt-2",children:"Registration successful!"})]}),(0,l.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,l.jsx)(m(),{href:"/login",className:"text-red-700 font-semibold hover:underline",children:"Login"})]})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,145,658,474,814],()=>a(6557));module.exports=r})();