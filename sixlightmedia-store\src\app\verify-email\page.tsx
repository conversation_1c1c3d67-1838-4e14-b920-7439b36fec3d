"use client";
import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { verifyEmail, resendVerification } from "@/api/auth";
import Link from "next/link";
import Image from "next/image";
import { CheckCircle, XCircle, Mail, RefreshCw } from "lucide-react";

function VerifyEmailForm() {
  const [status, setStatus] = useState<
    "loading" | "success" | "error" | "expired"
  >("loading");
  const [message, setMessage] = useState("");
  const [email, setEmail] = useState("");
  const [resending, setResending] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const token = searchParams?.get("token");

    if (!token) {
      setStatus("error");
      setMessage(
        "Invalid verification link. Please check your email for the correct link."
      );
      return;
    }

    // Verify the email token
    verifyEmail(token)
      .then((result) => {
        if (result.message) {
          setStatus("success");
          setMessage(result.message);
          // Redirect to login after 3 seconds
          setTimeout(() => {
            router.push(
              "/login?message=Email verified successfully! You can now log in."
            );
          }, 3000);
        } else {
          setStatus("error");
          setMessage(result.error || "Verification failed");
        }
      })
      .catch((error) => {
        setStatus("error");
        if (error.message?.includes("expired")) {
          setStatus("expired");
          setMessage(
            "Your verification link has expired. Please request a new one."
          );
        } else if (error.message?.includes("already verified")) {
          setStatus("success");
          setMessage("Your email is already verified! You can log in now.");
          setTimeout(() => {
            router.push("/login");
          }, 2000);
        } else {
          setMessage(
            "Verification failed. Please try again or contact support."
          );
        }
      });
  }, [searchParams, router]);

  const handleResendVerification = async () => {
    if (!email) {
      alert("Please enter your email address");
      return;
    }

    setResending(true);
    try {
      const result = await resendVerification(email);
      alert(
        result.message || "Verification email sent! Please check your inbox."
      );
    } catch (error) {
      alert("Failed to resend verification email. Please try again.");
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100">
        <div className="flex flex-col items-center mb-6">
          <Image
            src="/6 Light Logo.png"
            alt="6 Light Logo"
            width={64}
            height={64}
            className="mb-4"
          />
          <h1 className="text-2xl font-bold text-gray-900">
            Email Verification
          </h1>
        </div>

        {status === "loading" && (
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600">Verifying your email...</p>
          </div>
        )}

        {status === "success" && (
          <div className="space-y-4">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
            <div className="space-y-2">
              <h2 className="text-xl font-semibold text-green-700">
                Email Verified!
              </h2>
              <p className="text-gray-600">{message}</p>
              <p className="text-sm text-gray-500">
                Redirecting to login page...
              </p>
            </div>
            <Link
              href="/login"
              className="inline-block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors"
            >
              Continue to Login
            </Link>
          </div>
        )}

        {status === "error" && (
          <div className="space-y-4">
            <XCircle className="h-16 w-16 text-red-500 mx-auto" />
            <div className="space-y-2">
              <h2 className="text-xl font-semibold text-red-700">
                Verification Failed
              </h2>
              <p className="text-gray-600">{message}</p>
            </div>
            <div className="space-y-3">
              <Link
                href="/login"
                className="inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Login
              </Link>
              <Link
                href="/register"
                className="inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Create New Account
              </Link>
            </div>
          </div>
        )}

        {status === "expired" && (
          <div className="space-y-4">
            <Mail className="h-16 w-16 text-orange-500 mx-auto" />
            <div className="space-y-2">
              <h2 className="text-xl font-semibold text-orange-700">
                Link Expired
              </h2>
              <p className="text-gray-600">{message}</p>
            </div>
            <div className="space-y-3">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                onClick={handleResendVerification}
                disabled={resending}
                className="w-full bg-orange-600 text-white py-3 px-6 rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors flex items-center justify-center gap-2"
              >
                {resending ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4" />
                    Resend Verification Email
                  </>
                )}
              </button>
              <Link
                href="/register"
                className="inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Create New Account
              </Link>
            </div>
          </div>
        )}

        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need help?{" "}
            <Link href="/contact" className="text-blue-600 hover:text-blue-800">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Verifying email...</p>
            </div>
          </div>
        </div>
      }
    >
      <VerifyEmailForm />
    </Suspense>
  );
}
