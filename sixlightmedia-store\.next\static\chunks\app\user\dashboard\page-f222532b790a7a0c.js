(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[503],{1810:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(5155),a=r(2115),l=r(6766),i=r(6874),n=r.n(i),d=r(4615),c=r(3389),o=r(3843);function x(e){let{user:t}=e,[r,i]=(0,a.useState)(null),[x,h]=(0,a.useState)([]),[m,u]=(0,a.useState)(""),[g,b]=(0,a.useState)(0),[p,N]=(0,a.useState)(!1),[j,f]=(0,a.useState)(!0),v=()=>{b(JSON.parse(localStorage.getItem("cart")||"[]").length)};(0,a.useEffect)(()=>{v()},[]),(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(!e){u("Not authenticated"),f(!1);return}fetch((0,o.e9)(o.i3.ENDPOINTS.USER.DASHBOARD),{headers:{Authorization:"Bearer ".concat(e)},credentials:"include"}).then(async e=>{if(e.ok){let t=await e.json();i(t.user),h(t.orders||[])}else{let t=await e.text();console.error("Dashboard API error:",e.status,t),u("Failed to load dashboard data: ".concat(e.status))}}).catch(e=>{console.error("Dashboard fetch error:",e),u("Network error. Please check your connection.")}).finally(()=>f(!1))},[]);let y=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PROCESSING":return"bg-blue-100 text-blue-800";case"COMPLETED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return j?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):m?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-red-600 text-center",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"⚠️"}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Error Loading Dashboard"}),(0,s.jsx)("p",{children:m})]})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{cartCount:g,onCartClick:()=>{N(!0)}}),(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-900 via-indigo-900 to-purple-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Welcome Back"})}),(0,s.jsxs)("p",{className:"text-xl text-gray-200 mb-4 max-w-2xl mx-auto",children:["Hello, ",(null==t?void 0:t.name)||(null==t?void 0:t.email),"! Manage your orders and profile."]}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDD12 Secure user access"})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"✅ Server-side authentication active"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,s.jsx)(n(),{href:"/user/profile",className:"bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDC64"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"My Profile"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Update your personal information"})]})}),(0,s.jsx)(n(),{href:"/",className:"bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDECD️"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Shop Products"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Browse our amazing collection"})]})}),(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-6 border border-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"Order Stats"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Total Orders: ",x.length]})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Recent Orders"}),0===x.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"Start shopping to see your orders here!"}),(0,s.jsx)(n(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Start Shopping"})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[x.slice(0,5).map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[e.product.image&&(0,s.jsx)(l.default,{src:e.product.image,alt:e.product.name,width:60,height:60,className:"rounded-lg object-cover"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:e.product.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Order #",e.id," • Qty: ",e.quantity]}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-semibold text-gray-900",children:["K",e.totalPrice.toFixed(2)]}),(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(y(e.status)),children:e.status})]})]}),e.isCustomized&&(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-blue-600",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"})}),"Customized Product"]})})]},e.id)),x.length>5&&(0,s.jsx)("div",{className:"text-center pt-4",children:(0,s.jsxs)("p",{className:"text-gray-500",children:["Showing 5 of ",x.length," orders"]})})]})]})]})]}),(0,s.jsx)(c.A,{isOpen:p,onClose:()=>{N(!1),v()}})]})}},2493:(e,t,r)=>{Promise.resolve().then(r.bind(r,1810))},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:x,iconNode:h,...m}=e;return(0,s.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:r,strokeWidth:i?24*Number(l)/Number(a):l,className:n("lucide",o),...!x&&!d(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{className:d,...c}=r;return(0,s.createElement)(o,{ref:l,iconNode:t,className:n("lucide-".concat(a(i(e))),"lucide-".concat(e),d),...c})});return r.displayName=i(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,211,441,684,358],()=>t(2493)),_N_E=e.O()}]);