{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n      VERIFY_EMAIL: \"/auth/verify-email\",\n      FORGOT_PASSWORD: \"/auth/forgot-password\",\n      RESET_PASSWORD: \"/auth/reset-password\",\n      RESEND_VERIFICATION: \"/auth/resend-verification\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      UPDATE_PROFILE: \"/user/profile\",\n      UPLOAD_PROFILE_IMAGE: \"/user/upload-profile-image\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACK;AAAtB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;QACvB;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,uCAAgC,aAAa,OAAO,CAAC;IACvD,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/api/auth.ts"], "sourcesContent": ["import { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport async function login(email: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function register(email: string, password: string, name?: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\", // Important: This allows cookies to be sent/received\r\n    body: JSON.stringify({ email, password, name }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function verifyEmail(token: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.VERIFY_EMAIL), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function forgotPassword(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.FORGOT_PASSWORD),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n\r\nexport async function resetPassword(token: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    credentials: \"include\",\r\n    body: JSON.stringify({ token, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function resendVerification(email: string) {\r\n  const res = await fetch(\r\n    getApiUrl(API_CONFIG.ENDPOINTS.AUTH.RESEND_VERIFICATION),\r\n    {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ email }),\r\n    }\r\n  );\r\n  return res.json();\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,eAAe,MAAM,KAAa,EAAE,QAAgB;IACzD,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG;QAClE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAa;IAC3E,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;YAAU;QAAK;IAC/C;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,YAAY,KAAa;IAC7C,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG;QACzE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,eAAe,KAAa;IAChD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GACnD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,cAAc,KAAa,EAAE,QAAgB;IACjE,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,GAAG;QAC3E,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,mBAAmB,KAAa;IACpD,MAAM,MAAM,MAAM,MAChB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,GACvD;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,aAAa;QACb,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEF,OAAO,IAAI,IAAI;AACjB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/login/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { login } from \"@/api/auth\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { z } from \"zod\";\r\nimport { Eye, EyeOff, AlertCircle } from \"lucide-react\";\r\n\r\n// Zod validation schema for login\r\nconst loginSchema = z.object({\r\n  email: z.string().email(\"Please enter a valid email address\"),\r\n  password: z.string().min(1, \"Password is required\"),\r\n});\r\n\r\ntype LoginFormData = z.infer<typeof loginSchema>;\r\n\r\nexport default function LoginPage() {\r\n  const [formData, setFormData] = useState<LoginFormData>({\r\n    email: \"\",\r\n    password: \"\",\r\n  });\r\n  const [errors, setErrors] = useState<Partial<LoginFormData>>({});\r\n  const [error, setError] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n\r\n  const handleInputChange = (field: keyof LoginFormData, value: string) => {\r\n    setFormData((prev) => ({ ...prev, [field]: value }));\r\n    // Clear field-specific error when user starts typing\r\n    if (errors[field]) {\r\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\r\n    }\r\n  };\r\n\r\n  async function handleSubmit(e: React.FormEvent) {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(\"\");\r\n    setErrors({});\r\n\r\n    // Validate form data with Zod\r\n    const validation = loginSchema.safeParse(formData);\r\n    if (!validation.success) {\r\n      setLoading(false);\r\n      const fieldErrors: Partial<LoginFormData> = {};\r\n      validation.error.errors.forEach((err) => {\r\n        if (err.path[0]) {\r\n          fieldErrors[err.path[0] as keyof LoginFormData] = err.message;\r\n        }\r\n      });\r\n      setErrors(fieldErrors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const result = await login(formData.email, formData.password);\r\n      setLoading(false);\r\n\r\n      if (result.access_token) {\r\n        localStorage.setItem(\"token\", result.access_token);\r\n\r\n        // Store user information including role\r\n        if (result.user) {\r\n          localStorage.setItem(\"user\", JSON.stringify(result.user));\r\n        }\r\n\r\n        // Check if there's a redirect parameter in the URL\r\n        const urlParams = new URLSearchParams(window.location.search);\r\n        const redirectPath = urlParams.get(\"redirect\");\r\n\r\n        // Check if user has admin role before redirecting to admin pages\r\n        const isAdmin = result.user?.role === \"ADMIN\";\r\n        if (redirectPath && redirectPath.startsWith(\"/admin/\") && !isAdmin) {\r\n          setError(\"You don't have permission to access the admin area\");\r\n          return;\r\n        }\r\n\r\n        // Redirect to the specified path or default to user dashboard\r\n        window.location.href = redirectPath || \"/user/dashboard\";\r\n      } else {\r\n        setError(result.error || \"Login failed\");\r\n      }\r\n    } catch {\r\n      setLoading(false);\r\n      setError(\"Network error. Please try again.\");\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4\">\r\n      <div className=\"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100\">\r\n        <div className=\"flex flex-col items-center\">\r\n          <Image\r\n            src=\"/6 Light Logo.png\"\r\n            alt=\"6 Light Logo\"\r\n            width={64}\r\n            height={64}\r\n            className=\"mb-2\"\r\n          />\r\n        </div>\r\n        <h2 className=\"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\r\n          Sign In\r\n        </h2>\r\n        <form onSubmit={handleSubmit} className=\"flex flex-col gap-4\">\r\n          {/* Email Input */}\r\n          <div className=\"space-y-1\">\r\n            <input\r\n              id=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={(e) => handleInputChange(\"email\", e.target.value)}\r\n              placeholder=\"Email\"\r\n              type=\"email\"\r\n              autoComplete=\"email\"\r\n              className={`w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition ${\r\n                errors.email\r\n                  ? \"border-red-500 focus:ring-red-300\"\r\n                  : \"border-gray-300 focus:ring-blue-300\"\r\n              }`}\r\n              aria-invalid={!!errors.email}\r\n              aria-describedby={errors.email ? \"email-error\" : undefined}\r\n            />\r\n            {errors.email && (\r\n              <div\r\n                id=\"email-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.email}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Password Input */}\r\n          <div className=\"space-y-1\">\r\n            <div className=\"relative\">\r\n              <input\r\n                id=\"password\"\r\n                name=\"password\"\r\n                value={formData.password}\r\n                onChange={(e) => handleInputChange(\"password\", e.target.value)}\r\n                type={showPassword ? \"text\" : \"password\"}\r\n                placeholder=\"Password\"\r\n                autoComplete=\"current-password\"\r\n                className={`w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition ${\r\n                  errors.password\r\n                    ? \"border-red-500 focus:ring-red-300\"\r\n                    : \"border-gray-300 focus:ring-blue-300\"\r\n                }`}\r\n                aria-invalid={!!errors.password}\r\n                aria-describedby={\r\n                  errors.password ? \"password-error\" : undefined\r\n                }\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowPassword(!showPassword)}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\"\r\n                aria-label={showPassword ? \"Hide password\" : \"Show password\"}\r\n              >\r\n                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\r\n              </button>\r\n            </div>\r\n            {errors.password && (\r\n              <div\r\n                id=\"password-error\"\r\n                className=\"flex items-center gap-1 text-red-600 text-sm\"\r\n              >\r\n                <AlertCircle size={16} />\r\n                {errors.password}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <button\r\n            type=\"submit\"\r\n            className={`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${\r\n              loading\r\n                ? \"bg-gray-400 cursor-not-allowed\"\r\n                : \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white\"\r\n            }`}\r\n            disabled={loading}\r\n          >\r\n            {loading ? (\r\n              <div className=\"flex items-center justify-center gap-2\">\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n                <span>Signing in...</span>\r\n              </div>\r\n            ) : (\r\n              \"Sign In\"\r\n            )}\r\n          </button>\r\n\r\n          {/* Global Error Display */}\r\n          {error && (\r\n            <div className=\"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200\">\r\n              <AlertCircle size={16} />\r\n              {error}\r\n            </div>\r\n          )}\r\n        </form>\r\n\r\n        <div className=\"text-center text-sm text-gray-600 space-y-2\">\r\n          <div>\r\n            <Link\r\n              href=\"/forgot-password\"\r\n              className=\"text-blue-600 hover:text-blue-800 font-medium\"\r\n            >\r\n              Forgot your password?\r\n            </Link>\r\n          </div>\r\n          <div>\r\n            Don&apos;t have an account?{\" \"}\r\n            <Link\r\n              href=\"/register\"\r\n              className=\"text-red-700 font-semibold hover:underline\"\r\n            >\r\n              Register\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;;;AANA;;;;;;;AAQA,kCAAkC;AAClC,MAAM,cAAc,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC,OAA4B;QACrD,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,qDAAqD;QACrD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,eAAe,aAAa,CAAkB;QAC5C,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,UAAU,CAAC;QAEX,8BAA8B;QAC9B,MAAM,aAAa,YAAY,SAAS,CAAC;QACzC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,WAAW;YACX,MAAM,cAAsC,CAAC;YAC7C,WAAW,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC/B,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;oBACf,WAAW,CAAC,IAAI,IAAI,CAAC,EAAE,CAAwB,GAAG,IAAI,OAAO;gBAC/D;YACF;YACA,UAAU;YACV;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC5D,WAAW;YAEX,IAAI,OAAO,YAAY,EAAE;gBACvB,aAAa,OAAO,CAAC,SAAS,OAAO,YAAY;gBAEjD,wCAAwC;gBACxC,IAAI,OAAO,IAAI,EAAE;oBACf,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,OAAO,IAAI;gBACzD;gBAEA,mDAAmD;gBACnD,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;gBAC5D,MAAM,eAAe,UAAU,GAAG,CAAC;gBAEnC,iEAAiE;gBACjE,MAAM,UAAU,OAAO,IAAI,EAAE,SAAS;gBACtC,IAAI,gBAAgB,aAAa,UAAU,CAAC,cAAc,CAAC,SAAS;oBAClE,SAAS;oBACT;gBACF;gBAEA,8DAA8D;gBAC9D,OAAO,QAAQ,CAAC,IAAI,GAAG,gBAAgB;YACzC,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;8BAGd,6LAAC;oBAAG,WAAU;8BAAiH;;;;;;8BAG/H,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,aAAY;oCACZ,MAAK;oCACL,cAAa;oCACb,WAAW,CAAC,8EAA8E,EACxF,OAAO,KAAK,GACR,sCACA,uCACJ;oCACF,gBAAc,CAAC,CAAC,OAAO,KAAK;oCAC5B,oBAAkB,OAAO,KAAK,GAAG,gBAAgB;;;;;;gCAElD,OAAO,KAAK,kBACX,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,KAAK;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,MAAM,eAAe,SAAS;4CAC9B,aAAY;4CACZ,cAAa;4CACb,WAAW,CAAC,oFAAoF,EAC9F,OAAO,QAAQ,GACX,sCACA,uCACJ;4CACF,gBAAc,CAAC,CAAC,OAAO,QAAQ;4CAC/B,oBACE,OAAO,QAAQ,GAAG,mBAAmB;;;;;;sDAGzC,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;4CACV,cAAY,eAAe,kBAAkB;sDAE5C,6BAAe,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;qEAAS,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;;gCAGrD,OAAO,QAAQ,kBACd,6LAAC;oCACC,IAAG;oCACH,WAAU;;sDAEV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;wCAClB,OAAO,QAAQ;;;;;;;;;;;;;sCAItB,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAC,6EAA6E,EACvF,UACI,mCACA,mGACJ;4BACF,UAAU;sCAET,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;kDAAK;;;;;;;;;;;uCAGR;;;;;;wBAKH,uBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;gCAClB;;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;sCAIH,6LAAC;;gCAAI;gCACyB;8CAC5B,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA/MwB;KAAA", "debugId": null}}]}