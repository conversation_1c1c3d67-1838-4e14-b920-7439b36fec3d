# SECURITY: Copy this file to .env and fill in the values
# NEVER commit .env file to version control

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/sixlight_db"

# JWT Security - CRITICAL: Generate a strong secret
# Use: openssl rand -base64 32
JWT_SECRET="CHANGE_THIS_TO_A_STRONG_RANDOM_SECRET_32_CHARS_OR_MORE"

# Environment
NODE_ENV="development"

# Server
PORT=3001

# Frontend URL for CORS (production only)
FRONTEND_URL="https://yourdomain.com"

# Optional: Rate limiting
RATE_LIMIT_TTL=60000
RATE_LIMIT_LIMIT=100

# Optional: File upload limits
MAX_FILE_SIZE=5242880

# Email configuration (REQUIRED for email verification and password reset)
# For Gmail: Use App Password (not regular password)
# For other providers: Check their SMTP settings
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Email security settings
EMAIL_VERIFICATION_EXPIRES_HOURS=24
PASSWORD_RESET_EXPIRES_HOURS=1

# Optional: Monitoring
LOG_LEVEL="info"
ENABLE_LOGGING="true"
