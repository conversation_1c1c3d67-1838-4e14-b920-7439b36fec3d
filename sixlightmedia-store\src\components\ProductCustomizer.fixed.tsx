"use client";

import React, { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import {
  Type,
  Image as ImageIcon,
  Palette,
  RotateCw,
  Move,
  Trash2,
  Download,
  Upload,
  Undo,
  Redo,
  Save,
} from "lucide-react";

// Dynamic import for fabric to avoid SSR issues
let fabric: any = null;
if (typeof window !== "undefined") {
  fabric = require("fabric").fabric;
}

interface CustomizationData {
  canvasData?: string;
  preview?: string;
}

interface ProductCustomizerProps {
  productImage: string;
  onCustomizationChange: (customization: CustomizationData) => void;
  initialCustomization?: CustomizationData | null;
  productId?: string | number; // Add productId for localStorage persistence
}

const ProductCustomizer: React.FC<ProductCustomizerProps> = ({
  productImage,
  onCustomizationChange,
  initialCustomization,
  productId,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvas, setCanvas] = useState<any>(null);
  const [selectedTool, setSelectedTool] = useState<string>("select");
  const [textColor, setTextColor] = useState("#000000");
  const [fontSize, setFontSize] = useState(20);
  const [fontFamily, setFontFamily] = useState("Arial");
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [fabricLoaded, setFabricLoaded] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Canva-like feature states
  const [showLayers, setShowLayers] = useState<boolean>(false);
  const [showTemplates, setShowTemplates] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>("elements");
  const [objects, setObjects] = useState<any[]>([]);

  // Show loading state while Fabric.js is loading
  if (!fabricLoaded) {
    return (
      <div
        className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 space-y-6"
        data-has-unsaved-changes={hasUnsavedChanges ? "true" : "false"}
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Advanced Customizer...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <React.Fragment>
      <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
        <div className="mb-4">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            {"🎨"} {"Advanced Product Customizer"}
          </h3>
          <p className="text-gray-600">
            Design your product with our Canva-like editor
          </p>
        </div>
      </div>
    </React.Fragment>
  );
};

export default ProductCustomizer;
