{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n      VERIFY_EMAIL: \"/auth/verify-email\",\n      FORGOT_PASSWORD: \"/auth/forgot-password\",\n      RESET_PASSWORD: \"/auth/reset-password\",\n      RESEND_VERIFICATION: \"/auth/resend-verification\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      UPDATE_PROFILE: \"/user/profile\",\n      UPLOAD_PROFILE_IMAGE: \"/user/upload-profile-image\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACjB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;QACvB;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,6EAAgE;IAClE,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport default function Header({\r\n  cartCount = 0,\r\n  onCartClick,\r\n}: {\r\n  cartCount?: number;\r\n  onCartClick?: () => void;\r\n}) {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n  const [userRole, setUserRole] = useState<string | null>(null);\r\n  const [profileImage, setProfileImage] = useState<string | null>(null);\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function handleProfileImageUpdated() {\r\n      const token = localStorage.getItem(\"token\");\r\n      if (token) {\r\n        try {\r\n          // Fetch latest user data from API instead of relying on JWT token\r\n          const response = await fetch(\r\n            getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD),\r\n            {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            }\r\n          );\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data && data.user) {\r\n              setProfileImage(data.user.profileImage || null);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Failed to fetch updated profile image:\", error);\r\n          // Fallback to JWT token method\r\n          try {\r\n            const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n            setProfileImage(payload.profileImage || null);\r\n          } catch {\r\n            setProfileImage(null);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n      const token = localStorage.getItem(\"token\");\r\n      setIsLoggedIn(!!token);\r\n      if (token) {\r\n        // Decode JWT to get role (simple base64 decode, not secure for prod, but fine for client display)\r\n        try {\r\n          const payload = JSON.parse(atob(token.split(\".\")[1]));\r\n          setUserRole(payload.role || null);\r\n          setProfileImage(payload.profileImage || null);\r\n        } catch {\r\n          setUserRole(null);\r\n          setProfileImage(null);\r\n        }\r\n      } else {\r\n        setUserRole(null);\r\n        setProfileImage(null);\r\n      }\r\n      // Listen for profile image update event\r\n      window.addEventListener(\"profileImageUpdated\", handleProfileImageUpdated);\r\n    }\r\n    // Cleanup event listener\r\n    return () => {\r\n      window.removeEventListener(\r\n        \"profileImageUpdated\",\r\n        handleProfileImageUpdated\r\n      );\r\n    };\r\n  }, []);\r\n\r\n  // Prevent background scroll and horizontal scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n      document.documentElement.style.overflowX = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = \"\";\r\n      document.documentElement.style.overflowX = \"\";\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close menus on Escape key\r\n  useEffect(() => {\r\n    function handleKeyDown(e: KeyboardEvent) {\r\n      if (e.key === \"Escape\") {\r\n        setShowDropdown(false);\r\n        setMobileMenuOpen(false);\r\n      }\r\n    }\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, []);\r\n\r\n  // Ensure dropdown closes when mobile menu opens\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) setShowDropdown(false);\r\n  }, [mobileMenuOpen]);\r\n\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"token\");\r\n    setIsLoggedIn(false);\r\n    window.location.href = \"/login\";\r\n  }\r\n\r\n  function handleDropdownToggle() {\r\n    setShowDropdown((prev) => !prev);\r\n  }\r\n\r\n  function handleDropdownClose() {\r\n    setShowDropdown(false);\r\n  }\r\n\r\n  return (\r\n    <header className=\"w-full bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-30\">\r\n      <nav className=\"max-w-7xl mx-auto flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 md:py-4\">\r\n        {/* Brand Logo */}\r\n        <Link href=\"/\" className=\"flex items-center gap-2 min-w-0 group\">\r\n          <div className=\"relative\">\r\n            <Image\r\n              src=\"/6 Light Logo.png\"\r\n              alt=\"Six Light Media Logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"h-8 w-auto sm:h-10 md:h-12 transition-transform duration-300 group-hover:scale-110\"\r\n              priority\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl\"></div>\r\n          </div>\r\n          <span className=\"truncate text-base xs:text-lg sm:text-2xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-indigo-500 group-hover:via-purple-500 group-hover:to-pink-500 transition-all duration-300\">\r\n            Store\r\n          </span>\r\n        </Link>\r\n        {/* Desktop Nav */}\r\n        <div className=\"hidden sm:flex gap-2 md:gap-4 lg:gap-6 items-center min-w-0\">\r\n          <Link\r\n            href=\"/\"\r\n            className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n          >\r\n            🏠 Home\r\n          </Link>\r\n          {isLoggedIn && (\r\n            <Link\r\n              href=\"/user/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base whitespace-nowrap transform hover:-translate-y-0.5\"\r\n            >\r\n              📊 Dashboard\r\n            </Link>\r\n          )}\r\n          {isLoggedIn && userRole === \"ADMIN\" && (\r\n            <Link\r\n              href=\"/admin/dashboard\"\r\n              className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n            >\r\n              ⚡ Admin\r\n            </Link>\r\n          )}\r\n          {!isLoggedIn ? (\r\n            <>\r\n              <Link\r\n                href=\"/login\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                🔐 Login\r\n              </Link>\r\n              <Link\r\n                href=\"/register\"\r\n                className=\"px-3 py-1.5 md:px-4 md:py-2 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base whitespace-nowrap shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n              >\r\n                ✨ Register\r\n              </Link>\r\n            </>\r\n          ) : (\r\n            <div className=\"relative min-w-0\">\r\n              <button\r\n                className=\"flex items-center gap-2 focus:outline-none min-w-0 px-2 py-1 rounded-2xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                onClick={handleDropdownToggle}\r\n                aria-label=\"Open user menu\"\r\n              >\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                    className=\"rounded-full border-2 border-gradient-to-r from-indigo-200 to-purple-200 object-cover bg-white h-8 w-8 md:h-9 md:w-9 shadow-md\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <span className=\"hidden md:inline font-semibold text-gray-700 truncate max-w-[80px]\">\r\n                  Account\r\n                </span>\r\n                <svg\r\n                  className={`w-4 h-4 ml-1 text-gray-600 transition-transform duration-300 ${\r\n                    showDropdown ? \"rotate-180\" : \"\"\r\n                  }`}\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M19 9l-7 7-7-7\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n              {showDropdown && (\r\n                <div\r\n                  className=\"absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-lg border border-gray-200 rounded-2xl shadow-2xl z-50 overflow-hidden\"\r\n                  onMouseLeave={handleDropdownClose}\r\n                >\r\n                  <div className=\"p-2\">\r\n                    <Link\r\n                      href=\"/user/dashboard\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">📊</span>\r\n                      Dashboard\r\n                    </Link>\r\n                    <Link\r\n                      href=\"/user/profile\"\r\n                      className=\"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                      onClick={handleDropdownClose}\r\n                    >\r\n                      <span className=\"text-lg\">⚙️</span>\r\n                      Profile Settings\r\n                    </Link>\r\n                    <div className=\"border-t border-gray-100 my-2\"></div>\r\n                    <button\r\n                      onClick={() => {\r\n                        handleLogout();\r\n                        handleDropdownClose();\r\n                      }}\r\n                      className=\"flex items-center gap-3 w-full text-left px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 text-sm md:text-base rounded-xl transition-all duration-300 transform hover:-translate-y-0.5\"\r\n                    >\r\n                      <span className=\"text-lg\">🚪</span>\r\n                      Logout\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {/* Responsive Mobile Menu Button */}\r\n        <button\r\n          type=\"button\"\r\n          className=\"flex items-center justify-center p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 sm:hidden hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300\"\r\n          aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\r\n          onClick={() => setMobileMenuOpen((open) => !open)}\r\n        >\r\n          {mobileMenuOpen ? (\r\n            <X\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={!mobileMenuOpen}\r\n            />\r\n          ) : (\r\n            <Menu\r\n              className=\"w-7 h-7 text-gray-700\"\r\n              aria-hidden={mobileMenuOpen}\r\n            />\r\n          )}\r\n        </button>\r\n        {/* Cart Button */}\r\n        <button\r\n          className=\"relative px-3 py-1 md:px-4 md:py-2 rounded-2xl font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg hover:from-red-600 hover:to-pink-700 hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 ml-1 md:ml-2 text-sm md:text-base transform hover:-translate-y-0.5\"\r\n          onClick={onCartClick}\r\n          aria-label=\"Open cart\"\r\n        >\r\n          <span className=\"hidden sm:inline\">🛒 Cart</span>\r\n          <span className=\"sm:hidden\">🛒</span>\r\n          <span className=\"sm:ml-1\">({cartCount})</span>\r\n          {cartCount > 0 && (\r\n            <span className=\"absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs rounded-full px-1.5 py-0.5 animate-bounce font-bold shadow-lg\">\r\n              {cartCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </nav>\r\n      {/* Mobile Nav Drawer Overlay & Drawer: Only render when open */}\r\n      {mobileMenuOpen && (\r\n        <>\r\n          {/* Overlay */}\r\n          <div\r\n            className=\"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300 block\"\r\n            style={{ left: 0, right: 0 }}\r\n            onClick={() => setMobileMenuOpen(false)}\r\n            aria-hidden={!mobileMenuOpen}\r\n          />\r\n          {/* Drawer */}\r\n          <div\r\n            className=\"fixed top-0 right-0 z-50 w-full max-w-xs h-screen bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 sm:max-w-xs md:max-w-xs lg:max-w-xs xl:max-w-xs 2xl:max-w-xs min-w-0 sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden translate-x-0 border-l border-gray-200\"\r\n            role=\"dialog\"\r\n            aria-modal=\"true\"\r\n            tabIndex={-1}\r\n            style={{ right: 0, left: \"auto\" }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Tab\") {\r\n                // Basic focus trap: keep focus inside drawer\r\n                const focusable = Array.from(\r\n                  (e.currentTarget as HTMLElement).querySelectorAll(\r\n                    'a, button, input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n                  )\r\n                ) as HTMLElement[];\r\n                if (focusable.length === 0) return;\r\n                const first = focusable[0];\r\n                const last = focusable[focusable.length - 1];\r\n                if (!e.shiftKey && document.activeElement === last) {\r\n                  e.preventDefault();\r\n                  first.focus();\r\n                } else if (e.shiftKey && document.activeElement === first) {\r\n                  e.preventDefault();\r\n                  last.focus();\r\n                }\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center justify-between px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n              <span className=\"font-black text-lg bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent\">\r\n                📱 Menu\r\n              </span>\r\n              <button\r\n                className=\"p-2 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-400 hover:bg-white/50 transition-all duration-300\"\r\n                aria-label=\"Close menu\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n                tabIndex={mobileMenuOpen ? 0 : -1}\r\n              >\r\n                <X className=\"w-6 h-6 text-gray-700\" />\r\n              </button>\r\n            </div>\r\n            <nav className=\"flex flex-col gap-2 px-4 py-6 flex-1 overflow-y-auto max-h-[calc(100vh-80px)]\">\r\n              <Link\r\n                href=\"/\"\r\n                className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n              >\r\n                <span className=\"text-lg\">🏠</span>\r\n                Home\r\n              </Link>\r\n              {isLoggedIn && (\r\n                <Link\r\n                  href=\"/user/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">📊</span>\r\n                  Dashboard\r\n                </Link>\r\n              )}\r\n              {isLoggedIn && userRole === \"ADMIN\" && (\r\n                <Link\r\n                  href=\"/admin/dashboard\"\r\n                  className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"text-lg\">⚡</span>\r\n                  Admin Panel\r\n                </Link>\r\n              )}\r\n              {!isLoggedIn ? (\r\n                <>\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">🔐</span>\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 text-base shadow-lg transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">✨</span>\r\n                    Register\r\n                  </Link>\r\n                </>\r\n              ) : (\r\n                <div className=\"flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200\">\r\n                  <Link\r\n                    href=\"/user/profile\"\r\n                    className=\"flex items-center gap-3 px-4 py-3 rounded-2xl font-semibold text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                    onClick={() => setMobileMenuOpen(false)}\r\n                  >\r\n                    <span className=\"text-lg\">⚙️</span>\r\n                    Profile Settings\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleLogout();\r\n                      setMobileMenuOpen(false);\r\n                    }}\r\n                    className=\"flex items-center gap-3 w-full text-left px-4 py-3 rounded-2xl font-semibold text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-300 text-base transform hover:-translate-y-0.5\"\r\n                  >\r\n                    <span className=\"text-lg\">🚪</span>\r\n                    Logout\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </nav>\r\n            {isLoggedIn && (\r\n              <div className=\"flex items-center gap-3 px-4 py-4 border-t border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50\">\r\n                <div className=\"relative\">\r\n                  <Image\r\n                    src={profileImage || \"/usericon.png\"}\r\n                    alt=\"Profile\"\r\n                    width={40}\r\n                    height={40}\r\n                    className=\"rounded-full border-2 border-indigo-200 object-cover bg-white shadow-md w-10 h-10\"\r\n                  />\r\n                  <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"font-bold text-gray-900 truncate text-sm\">\r\n                    Account\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-600 truncate\">Logged in</p>\r\n                  <span className=\"inline-block mt-1 px-2 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full\">\r\n                    {userRole}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS,OAAO,EAC7B,YAAY,CAAC,EACb,WAAW,EAIZ;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,IAAI;oBACF,kEAAkE;oBAClE,MAAM,WAAW,MAAM,MACrB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAC7C;wBACE,SAAS;4BAAE,eAAe,CAAC,OAAO,EAAE,OAAO;wBAAC;oBAC9C;oBAGF,IAAI,SAAS,EAAE,EAAE;wBACf,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,IAAI,QAAQ,KAAK,IAAI,EAAE;4BACrB,gBAAgB,KAAK,IAAI,CAAC,YAAY,IAAI;wBAC5C;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,+BAA+B;oBAC/B,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;wBACnD,gBAAgB,QAAQ,YAAY,IAAI;oBAC1C,EAAE,OAAM;wBACN,gBAAgB;oBAClB;gBACF;YACF;QACF;QACA,uCAAmC;;QAmBnC;QACA,yBAAyB;QACzB,OAAO;YACL,OAAO,mBAAmB,CACxB,uBACA;QAEJ;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;QAC7C,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;QAC7C;QACA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG;QAC7C;IACF,GAAG;QAAC;KAAe;IAEnB,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,cAAc,CAAgB;YACrC,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,gBAAgB;gBAChB,kBAAkB;YACpB;QACF;QACA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,gBAAgB;IACtC,GAAG;QAAC;KAAe;IAEnB,SAAS;QACP,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,SAAS;QACP,gBAAgB,CAAC,OAAS,CAAC;IAC7B;IAEA,SAAS;QACP,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,QAAQ;;;;;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAK,WAAU;0CAA2Q;;;;;;;;;;;;kCAK7R,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,4BACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,cAAc,aAAa,yBAC1B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIF,CAAC,2BACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;6DAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;;0DAEX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,gBAAgB;wDACrB,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAK,WAAU;0DAAqE;;;;;;0DAGrF,8OAAC;gDACC,WAAW,CAAC,6DAA6D,EACvE,eAAe,eAAe,IAC9B;gDACF,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,SAAQ;0DAER,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;oCAIP,8BACC,8OAAC;wCACC,WAAU;wCACV,cAAc;kDAEd,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;8DAGrC,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDACC,SAAS;wDACP;wDACA;oDACF;oDACA,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUjD,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,cAAY,iBAAiB,eAAe;wBAC5C,SAAS,IAAM,kBAAkB,CAAC,OAAS,CAAC;kCAE3C,+BACC,8OAAC,4LAAA,CAAA,IAAC;4BACA,WAAU;4BACV,eAAa,CAAC;;;;;iDAGhB,8OAAC,kMAAA,CAAA,OAAI;4BACH,WAAU;4BACV,eAAa;;;;;;;;;;;kCAKnB,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAW;;0CAEX,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,8OAAC;gCAAK,WAAU;0CAAY;;;;;;0CAC5B,8OAAC;gCAAK,WAAU;;oCAAU;oCAAE;oCAAU;;;;;;;4BACrC,YAAY,mBACX,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAMR,gCACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,MAAM;4BAAG,OAAO;wBAAE;wBAC3B,SAAS,IAAM,kBAAkB;wBACjC,eAAa,CAAC;;;;;;kCAGhB,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,cAAW;wBACX,UAAU,CAAC;wBACX,OAAO;4BAAE,OAAO;4BAAG,MAAM;wBAAO;wBAChC,WAAW,CAAC;4BACV,IAAI,EAAE,GAAG,KAAK,OAAO;gCACnB,6CAA6C;gCAC7C,MAAM,YAAY,MAAM,IAAI,CAC1B,AAAC,EAAE,aAAa,CAAiB,gBAAgB,CAC/C;gCAGJ,IAAI,UAAU,MAAM,KAAK,GAAG;gCAC5B,MAAM,QAAQ,SAAS,CAAC,EAAE;gCAC1B,MAAM,OAAO,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;gCAC5C,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,MAAM;oCAClD,EAAE,cAAc;oCAChB,MAAM,KAAK;gCACb,OAAO,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,OAAO;oCACzD,EAAE,cAAc;oCAChB,KAAK,KAAK;gCACZ;4BACF;wBACF;;0CAEA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAkG;;;;;;kDAGlH,8OAAC;wCACC,WAAU;wCACV,cAAW;wCACX,SAAS,IAAM,kBAAkB;wCACjC,UAAU,iBAAiB,IAAI,CAAC;kDAEhC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAGpC,4BACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAS;;;;;;;oCAItC,cAAc,aAAa,yBAC1B,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAQ;;;;;;;oCAIrC,CAAC,2BACA;;0DACE,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;qEAKtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;0DAGrC,8OAAC;gDACC,SAAS;oDACP;oDACA,kBAAkB;gDACpB;gDACA,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;4BAM1C,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,gBAAgB;gDACrB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,8OAAC;gDAAE,WAAU;0DAAiC;;;;;;0DAC9C,8OAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/OrderForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  customizationData?: {\n    canvasData?: string;\n    preview?: string;\n  };\n  price: number;\n  quantity?: number;\n};\n\ntype OrderFormProps = {\n  cartItems: CartItem[];\n  onOrderSuccess: () => void;\n  onCancel: () => void;\n};\n\ntype OrderFormData = {\n  customerName: string;\n  customerPhone: string;\n  customerAddress: string;\n};\n\nexport default function OrderForm({\n  cartItems,\n  onOrderSuccess,\n  onCancel,\n}: OrderFormProps) {\n  const [formData, setFormData] = useState<OrderFormData>({\n    customerName: \"\",\n    customerPhone: \"\",\n    customerAddress: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  useEffect(() => {\n    // Get user info to pre-fill name\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD), {\n        headers: { Authorization: `Bearer ${token}` },\n      })\n        .then((res) => res.json())\n        .then((data) => {\n          if (data && data.user) {\n            setFormData((prev) => ({\n              ...prev,\n              customerName: data.user.name || \"\",\n            }));\n          }\n        })\n        .catch(() => {\n          // Ignore error, user can still fill form manually\n        });\n    }\n  }, []);\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setError(\"Please log in to place an order\");\n      setLoading(false);\n      return;\n    }\n\n    try {\n      // Create orders for each cart item\n      const orderPromises = cartItems.map((item) =>\n        fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.ORDERS), {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({\n            productId: item.productId,\n            customerName: formData.customerName,\n            customerPhone: formData.customerPhone,\n            customerAddress: formData.customerAddress,\n            customColor: item.color,\n            customText: item.text,\n            isCustomized: item.customized,\n            customizationData: item.customizationData?.canvasData,\n            customizationPreview: item.customizationData?.preview,\n            quantity: item.quantity || 1,\n          }),\n        })\n      );\n\n      const responses = await Promise.all(orderPromises);\n\n      // Check if all orders were successful\n      const allSuccessful = responses.every((res) => res.ok);\n\n      if (allSuccessful) {\n        // Clear cart\n        localStorage.removeItem(\"cart\");\n        onOrderSuccess();\n      } else {\n        setError(\"Some orders failed to process. Please try again.\");\n      }\n    } catch {\n      setError(\"Failed to place order. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">\n              Complete Your Order\n            </h2>\n            <button\n              onClick={onCancel}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close\"\n            >\n              ×\n            </button>\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h3 className=\"font-semibold mb-3\">Order Summary</h3>\n            {cartItems.map((item, index) => (\n              <div\n                key={index}\n                className=\"flex gap-4 py-3 border-b border-gray-200 last:border-b-0\"\n              >\n                {/* Customization Preview */}\n                {item.customized && item.customizationData?.preview && (\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-16 h-16 border border-gray-300 rounded-lg overflow-hidden bg-gray-50\">\n                      <img\n                        src={item.customizationData.preview}\n                        alt=\"Customization preview\"\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex-1\">\n                  <div className=\"font-medium\">{item.name}</div>\n                  {item.customized && (\n                    <div className=\"text-sm text-gray-600 mt-1\">\n                      {item.color && (\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <span>Color:</span>\n                          <div\n                            className=\"w-3 h-3 rounded border border-gray-300\"\n                            style={{ backgroundColor: item.color }}\n                          ></div>\n                          <span>{item.color}</span>\n                        </div>\n                      )}\n                      {item.text && (\n                        <div className=\"mb-1\">\n                          <span>Text: &ldquo;{item.text}&rdquo;</span>\n                        </div>\n                      )}\n                      {item.customizationData?.canvasData && (\n                        <div className=\"inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                          <svg\n                            className=\"w-3 h-3\"\n                            fill=\"currentColor\"\n                            viewBox=\"0 0 20 20\"\n                          >\n                            <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z\" />\n                          </svg>\n                          Advanced Design\n                        </div>\n                      )}\n                    </div>\n                  )}\n                  <div className=\"text-sm text-gray-500 mt-1\">\n                    Qty: {item.quantity || 1}\n                  </div>\n                </div>\n\n                <div className=\"text-right\">\n                  <div className=\"font-semibold\">\n                    K{(item.price * (item.quantity || 1)).toFixed(2)}\n                  </div>\n                </div>\n              </div>\n            ))}\n            <div className=\"flex justify-between items-center pt-3 mt-3 border-t border-gray-300\">\n              <div className=\"font-bold text-lg\">Total:</div>\n              <div className=\"font-bold text-lg text-[#1a237e]\">\n                K{calculateTotal().toFixed(2)}\n              </div>\n            </div>\n          </div>\n\n          {/* Order Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label\n                htmlFor=\"customerName\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Full Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"customerName\"\n                name=\"customerName\"\n                value={formData.customerName}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerPhone\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Phone Number *\n              </label>\n              <input\n                type=\"tel\"\n                id=\"customerPhone\"\n                name=\"customerPhone\"\n                value={formData.customerPhone}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"e.g. +260 971 781 907\"\n              />\n            </div>\n\n            <div>\n              <label\n                htmlFor=\"customerAddress\"\n                className=\"block text-sm font-medium text-gray-700 mb-1\"\n              >\n                Delivery/Pickup Address *\n              </label>\n              <textarea\n                id=\"customerAddress\"\n                name=\"customerAddress\"\n                value={formData.customerAddress}\n                onChange={handleInputChange}\n                required\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#1a237e] focus:border-transparent\"\n                placeholder=\"Enter your full address or preferred pickup location\"\n              />\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm bg-red-50 p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"flex gap-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={onCancel}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold transition ${\n                  loading\n                    ? \"opacity-50 cursor-not-allowed\"\n                    : \"hover:bg-[#2a3490]\"\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center gap-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                    <span>Placing Order...</span>\n                  </div>\n                ) : (\n                  `Place Order - K${calculateTotal().toFixed(2)}`\n                )}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-4 text-xs text-gray-500 text-center\">\n            By placing this order, you agree to our terms and conditions. You\n            will be contacted for payment and delivery arrangements.\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgCe,SAAS,UAAU,EAChC,SAAS,EACT,cAAc,EACd,QAAQ,EACO;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,cAAc;QACd,eAAe;QACf,iBAAiB;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG;gBACpD,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C,GACG,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,IACtB,IAAI,CAAC,CAAC;gBACL,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,YAAY,CAAC,OAAS,CAAC;4BACrB,GAAG,IAAI;4BACP,cAAc,KAAK,IAAI,CAAC,IAAI,IAAI;wBAClC,CAAC;gBACH;YACF,GACC,KAAK,CAAC;YACL,kDAAkD;YACpD;QACJ;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,mCAAmC;YACnC,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAC,OACnC,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;oBAClC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,WAAW,KAAK,SAAS;wBACzB,cAAc,SAAS,YAAY;wBACnC,eAAe,SAAS,aAAa;wBACrC,iBAAiB,SAAS,eAAe;wBACzC,aAAa,KAAK,KAAK;wBACvB,YAAY,KAAK,IAAI;wBACrB,cAAc,KAAK,UAAU;wBAC7B,mBAAmB,KAAK,iBAAiB,EAAE;wBAC3C,sBAAsB,KAAK,iBAAiB,EAAE;wBAC9C,UAAU,KAAK,QAAQ,IAAI;oBAC7B;gBACF;YAGF,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC;YAEpC,sCAAsC;YACtC,MAAM,gBAAgB,UAAU,KAAK,CAAC,CAAC,MAAQ,IAAI,EAAE;YAErD,IAAI,eAAe;gBACjB,aAAa;gBACb,aAAa,UAAU,CAAC;gBACxB;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;4BAClC,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oCAEC,WAAU;;wCAGT,KAAK,UAAU,IAAI,KAAK,iBAAiB,EAAE,yBAC1C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK,KAAK,iBAAiB,CAAC,OAAO;oDACnC,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAe,KAAK,IAAI;;;;;;gDACtC,KAAK,UAAU,kBACd,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,kBACT,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,KAAK,KAAK;oEAAC;;;;;;8EAEvC,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;;wDAGpB,KAAK,IAAI,kBACR,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;oEAAK;oEAAc,KAAK,IAAI;oEAAC;;;;;;;;;;;;wDAGjC,KAAK,iBAAiB,EAAE,4BACvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;8EAER,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;gEACJ;;;;;;;;;;;;;8DAMd,8OAAC;oDAAI,WAAU;;wDAA6B;wDACpC,KAAK,QAAQ,IAAI;;;;;;;;;;;;;sDAI3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAgB;oDAC3B,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;mCAxD7C;;;;;0CA6DT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;;4CAAmC;4CAC9C,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,QAAQ;wCACR,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAIf,uBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,6EAA6E,EACvF,UACI,kCACA,sBACJ;kDAED,wBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;mDAGR,CAAC,eAAe,EAAE,iBAAiB,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;kCAMvD,8OAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;;;;;;AAQlE", "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/components/Cart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport OrderForm from \"./OrderForm\";\n\ntype CartItem = {\n  id: number;\n  productId: number;\n  name: string;\n  color?: string;\n  text?: string;\n  customized: boolean;\n  customizationData?: {\n    canvasData?: string;\n    preview?: string;\n  };\n  price: number;\n  quantity?: number;\n};\n\ntype CartProps = {\n  isOpen: boolean;\n  onClose: () => void;\n};\n\nexport default function Cart({ isOpen, onClose }: CartProps) {\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n  const [showOrderForm, setShowOrderForm] = useState(false);\n  const [orderSuccess, setOrderSuccess] = useState(false);\n\n  useEffect(() => {\n    if (isOpen) {\n      loadCartItems();\n    }\n  }, [isOpen]);\n\n  const loadCartItems = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\n    setCartItems(cart);\n  };\n\n  const removeItem = (itemId: number) => {\n    const updatedCart = cartItems.filter((item) => item.id !== itemId);\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const updateQuantity = (itemId: number, newQuantity: number) => {\n    if (newQuantity < 1) {\n      removeItem(itemId);\n      return;\n    }\n\n    const updatedCart = cartItems.map((item) =>\n      item.id === itemId ? { ...item, quantity: newQuantity } : item\n    );\n    setCartItems(updatedCart);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n  };\n\n  const calculateTotal = () => {\n    return cartItems.reduce(\n      (total, item) => total + item.price * (item.quantity || 1),\n      0\n    );\n  };\n\n  const handleCheckout = () => {\n    setShowOrderForm(true);\n  };\n\n  const handleOrderSuccess = () => {\n    setShowOrderForm(false);\n    setOrderSuccess(true);\n    setCartItems([]);\n\n    // Close success message after 3 seconds\n    setTimeout(() => {\n      setOrderSuccess(false);\n      onClose();\n    }, 3000);\n  };\n\n  if (!isOpen) return null;\n\n  if (showOrderForm) {\n    return (\n      <OrderForm\n        cartItems={cartItems}\n        onOrderSuccess={handleOrderSuccess}\n        onCancel={() => setShowOrderForm(false)}\n      />\n    );\n  }\n\n  if (orderSuccess) {\n    return (\n      <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-xl shadow-lg p-8 text-center max-w-md\">\n          <div className=\"text-green-600 text-6xl mb-4\">✓</div>\n          <h2 className=\"text-2xl font-bold text-[#1a237e] mb-2\">\n            Order Placed Successfully!\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Thank you for your order. We will contact you soon for payment and\n            delivery arrangements.\n          </p>\n          <div className=\"animate-pulse text-sm text-gray-500\">\n            Closing automatically...\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-[#1a237e]\">Shopping Cart</h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n              aria-label=\"Close cart\"\n            >\n              ×\n            </button>\n          </div>\n\n          {cartItems.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">🛒</div>\n              <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n                Your cart is empty\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                Add some products to get started!\n              </p>\n              <button\n                onClick={onClose}\n                className=\"px-6 py-2 bg-[#1a237e] text-white rounded-md hover:bg-[#2a3490] transition\"\n              >\n                Continue Shopping\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Cart Items */}\n              <div className=\"space-y-4 mb-6\">\n                {cartItems.map((item) => (\n                  <div\n                    key={item.id}\n                    className=\"flex items-start gap-4 p-4 border border-gray-200 rounded-lg\"\n                  >\n                    {/* Customization Preview */}\n                    {item.customized && item.customizationData?.preview && (\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-20 h-20 border border-gray-300 rounded-lg overflow-hidden bg-gray-50\">\n                          <img\n                            src={item.customizationData.preview}\n                            alt=\"Customization preview\"\n                            className=\"w-full h-full object-cover\"\n                          />\n                        </div>\n                        <div className=\"text-xs text-center text-gray-500 mt-1\">\n                          Custom Design\n                        </div>\n                      </div>\n                    )}\n\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-lg\">{item.name}</h3>\n                      {item.customized && (\n                        <div className=\"text-sm text-gray-600 mt-1\">\n                          {item.color && (\n                            <div className=\"flex items-center gap-2 mb-1\">\n                              <span>Color:</span>\n                              <div\n                                className=\"w-4 h-4 rounded border border-gray-300\"\n                                style={{ backgroundColor: item.color }}\n                              ></div>\n                              <span className=\"font-mono text-xs\">\n                                {item.color}\n                              </span>\n                            </div>\n                          )}\n                          {item.text && (\n                            <div className=\"mb-1\">\n                              <span>Text: &ldquo;</span>\n                              <span className=\"font-medium\">{item.text}</span>\n                              <span>&rdquo;</span>\n                            </div>\n                          )}\n                          {item.customizationData?.canvasData && (\n                            <div className=\"inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                              <svg\n                                className=\"w-3 h-3\"\n                                fill=\"currentColor\"\n                                viewBox=\"0 0 20 20\"\n                              >\n                                <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z\" />\n                              </svg>\n                              Advanced Design\n                            </div>\n                          )}\n                        </div>\n                      )}\n                      <div className=\"text-lg font-semibold text-[#1a237e] mt-2\">\n                        K{item.price.toFixed(2)} each\n                      </div>\n                    </div>\n\n                    {/* Quantity Controls */}\n                    <div className=\"flex items-center gap-2\">\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) - 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        -\n                      </button>\n                      <span className=\"w-8 text-center font-semibold\">\n                        {item.quantity || 1}\n                      </span>\n                      <button\n                        onClick={() =>\n                          updateQuantity(item.id, (item.quantity || 1) + 1)\n                        }\n                        className=\"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition\"\n                      >\n                        +\n                      </button>\n                    </div>\n\n                    {/* Remove Button */}\n                    <button\n                      onClick={() => removeItem(item.id)}\n                      className=\"text-red-500 hover:text-red-700 p-2\"\n                      aria-label=\"Remove item\"\n                    >\n                      🗑️\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              {/* Cart Total */}\n              <div className=\"border-t border-gray-200 pt-4 mb-6\">\n                <div className=\"flex justify-between items-center text-xl font-bold\">\n                  <span>Total:</span>\n                  <span className=\"text-[#1a237e]\">\n                    K{calculateTotal().toFixed(2)}\n                  </span>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={onClose}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition\"\n                >\n                  Continue Shopping\n                </button>\n                <button\n                  onClick={handleCheckout}\n                  className=\"flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold hover:bg-[#2a3490] transition\"\n                >\n                  Proceed to Checkout\n                </button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAyBe,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO,EAAa;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QAC3D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,cAAc,GAAG;YACnB,WAAW;YACX;QACF;QAEA,MAAM,cAAc,UAAU,GAAG,CAAC,CAAC,OACjC,KAAK,EAAE,KAAK,SAAS;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY,IAAI;QAE5D,aAAa;QACb,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,iBAAiB;QACrB,OAAO,UAAU,MAAM,CACrB,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,GACzD;IAEJ;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,gBAAgB;QAChB,aAAa,EAAE;QAEf,wCAAwC;QACxC,WAAW;YACT,gBAAgB;YAChB;QACF,GAAG;IACL;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,IAAI,eAAe;QACjB,qBACE,8OAAC,+HAAA,CAAA,UAAS;YACR,WAAW;YACX,gBAAgB;YAChB,UAAU,IAAM,iBAAiB;;;;;;IAGvC;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;kCAC9C,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAIlC,8OAAC;wBAAI,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAM7D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CACZ;;;;;;;;;;;;oBAKF,UAAU,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;6CAKH;;0CAEE,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wCAEC,WAAU;;4CAGT,KAAK,UAAU,IAAI,KAAK,iBAAiB,EAAE,yBAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAK,KAAK,iBAAiB,CAAC,OAAO;4DACnC,KAAI;4DACJ,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;kEAAyC;;;;;;;;;;;;0DAM5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyB,KAAK,IAAI;;;;;;oDAC/C,KAAK,UAAU,kBACd,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,kBACT,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,KAAK,KAAK;wEAAC;;;;;;kFAEvC,8OAAC;wEAAK,WAAU;kFACb,KAAK,KAAK;;;;;;;;;;;;4DAIhB,KAAK,IAAI,kBACR,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,8OAAC;kFAAK;;;;;;;;;;;;4DAGT,KAAK,iBAAiB,EAAE,4BACvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,WAAU;wEACV,MAAK;wEACL,SAAQ;kFAER,cAAA,8OAAC;4EAAK,GAAE;;;;;;;;;;;oEACJ;;;;;;;;;;;;;kEAMd,8OAAC;wDAAI,WAAU;;4DAA4C;4DACvD,KAAK,KAAK,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAK5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ,IAAI;;;;;;kEAEpB,8OAAC;wDACC,SAAS,IACP,eAAe,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;wDAEjD,WAAU;kEACX;;;;;;;;;;;;0DAMH,8OAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;gDACV,cAAW;0DACZ;;;;;;;uCAzFI,KAAK,EAAE;;;;;;;;;;0CAiGlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAiB;gDAC7B,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;0CAMjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 1950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/user/profile/UserProfileClient.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState, useRef } from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport Header from \"@/components/Header\";\nimport Cart from \"@/components/Cart\";\nimport { getApiUrl, API_CONFIG } from \"@/lib/config\";\nimport { User } from \"@/lib/auth\";\n\ntype UserData = {\n  id: number;\n  name?: string;\n  email: string;\n  role: string;\n  profileImage?: string;\n};\n\ninterface UserProfileClientProps {\n  user?: User;\n}\n\nexport default function UserProfileClient({ user }: UserProfileClientProps) {\n  const [userData, setUserData] = useState<UserData | null>(null);\n  const [editName, setEditName] = useState(\"\");\n  const [profileImage, setProfileImage] = useState<string>(\"\");\n  const [imageUploading, setImageUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [profileSaving, setProfileSaving] = useState(false);\n  const [passwordChanging, setPasswordChanging] = useState(false);\n  const [passwords, setPasswords] = useState({\n    oldPassword: \"\",\n    newPassword: \"\",\n    confirmPassword: \"\",\n  });\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [cartCount, setCartCount] = useState(0);\n  const [isCartOpen, setIsCartOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const updateCartCount = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\") || \"[]\");\n    setCartCount(cart.length);\n  };\n\n  const handleCartClick = () => {\n    setIsCartOpen(true);\n  };\n\n  const handleCartClose = () => {\n    setIsCartOpen(false);\n    updateCartCount();\n  };\n\n  useEffect(() => {\n    updateCartCount();\n  }, []);\n\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setError(\"Not authenticated\");\n      setLoading(false);\n      return;\n    }\n\n    fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD), {\n      headers: { Authorization: `Bearer ${token}` },\n      credentials: \"include\", // Include cookies for authentication\n    })\n      .then(async (response) => {\n        if (response.ok) {\n          const dashboardData = await response.json();\n          const userData = dashboardData.user;\n          setUserData(userData);\n          setEditName(userData.name || \"\");\n          setProfileImage(userData.profileImage || \"\");\n        } else {\n          const errorText = await response.text();\n          console.error(\"Profile API error:\", response.status, errorText);\n          setError(`Failed to load profile: ${response.status}`);\n        }\n      })\n      .catch((error) => {\n        console.error(\"Profile fetch error:\", error);\n        setError(\"Network error. Please check your connection.\");\n      })\n      .finally(() => setLoading(false));\n  }, []);\n\n  const handleImageUpload = async (\n    event: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      setError(\"Image size must be less than 5MB\");\n      return;\n    }\n\n    setImageUploading(true);\n    setUploadProgress(0);\n    setError(\"\");\n\n    const formData = new FormData();\n    formData.append(\"profileImage\", file);\n\n    try {\n      const token = localStorage.getItem(\"token\");\n      const xhr = new XMLHttpRequest();\n\n      xhr.upload.addEventListener(\"progress\", (e) => {\n        if (e.lengthComputable) {\n          const progress = (e.loaded / e.total) * 100;\n          setUploadProgress(progress);\n        }\n      });\n\n      xhr.onload = () => {\n        if (xhr.status === 200) {\n          const response = JSON.parse(xhr.responseText);\n          setProfileImage(response.profileImage);\n          setUserData((prev) =>\n            prev ? { ...prev, profileImage: response.profileImage } : null\n          );\n          setSuccess(\"Profile image updated successfully!\");\n        } else {\n          setError(\"Failed to upload image\");\n        }\n        setImageUploading(false);\n        setUploadProgress(0);\n      };\n\n      xhr.onerror = () => {\n        setError(\"Failed to upload image\");\n        setImageUploading(false);\n        setUploadProgress(0);\n      };\n\n      // For now, disable the upload since the endpoint doesn't exist\n      // Instead, we'll handle image upload differently\n      setError(\n        \"Image upload feature is temporarily disabled. Please update your name instead.\"\n      );\n      setImageUploading(false);\n      setUploadProgress(0);\n      return;\n    } catch {\n      setError(\"Failed to upload image\");\n      setImageUploading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  const handleProfileUpdate = async () => {\n    setProfileSaving(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await fetch(\n        getApiUrl(API_CONFIG.ENDPOINTS.USER.UPDATE_PROFILE),\n        {\n          method: \"PATCH\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n          credentials: \"include\", // Include cookies for authentication\n          body: JSON.stringify({ name: editName }),\n        }\n      );\n\n      if (response.ok) {\n        const updatedUser = await response.json();\n        setUserData(updatedUser);\n        setSuccess(\"Profile updated successfully!\");\n      } else {\n        setError(\"Failed to update profile\");\n      }\n    } catch {\n      setError(\"Failed to update profile\");\n    } finally {\n      setProfileSaving(false);\n    }\n  };\n\n  const handlePasswordChange = async () => {\n    if (passwords.newPassword !== passwords.confirmPassword) {\n      setError(\"New passwords don't match\");\n      return;\n    }\n\n    if (passwords.newPassword.length < 6) {\n      setError(\"New password must be at least 6 characters\");\n      return;\n    }\n\n    setPasswordChanging(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await fetch(\n        getApiUrl(API_CONFIG.ENDPOINTS.USER.CHANGE_PASSWORD),\n        {\n          method: \"PATCH\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n          credentials: \"include\", // Include cookies for authentication\n          body: JSON.stringify({\n            oldPassword: passwords.oldPassword,\n            newPassword: passwords.newPassword,\n          }),\n        }\n      );\n\n      if (response.ok) {\n        setPasswords({ oldPassword: \"\", newPassword: \"\", confirmPassword: \"\" });\n        setSuccess(\"Password changed successfully!\");\n      } else {\n        const errorData = await response.json();\n        setError(errorData.message || \"Failed to change password\");\n      }\n    } catch {\n      setError(\"Failed to change password\");\n    } finally {\n      setPasswordChanging(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <Header cartCount={cartCount} onCartClick={handleCartClick} />\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\n        {/* Hero Section */}\n        <div className=\"bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-800 relative overflow-hidden\">\n          <div className=\"absolute inset-0 bg-black/20\"></div>\n          <div className=\"relative z-10 max-w-7xl mx-auto px-4 py-16\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl md:text-6xl font-black text-white mb-4\">\n                <span className=\"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent\">\n                  Profile\n                </span>\n                <span className=\"text-white\"> Management</span>\n              </h1>\n              <p className=\"text-xl text-gray-200 mb-4 max-w-2xl mx-auto\">\n                Manage your account settings and personal information\n              </p>\n              <div className=\"inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                <span className=\"text-white text-sm\">\n                  🔒 Secure profile access for {user?.name || user?.email}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto px-4 py-12\">\n          <div className=\"text-center mb-8\">\n            <div className=\"inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n              <span className=\"text-sm font-medium\">\n                ✅ Server-side authentication active\n              </span>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <div className=\"mb-8\">\n            <Link\n              href=\"/user/dashboard\"\n              className=\"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors\"\n            >\n              ← Back to Dashboard\n            </Link>\n          </div>\n\n          {/* Error/Success Messages */}\n          {error && (\n            <div className=\"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n          {success && (\n            <div className=\"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\">\n              {success}\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {/* Profile Information */}\n            <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                Profile Information\n              </h2>\n\n              {/* Profile Image */}\n              <div className=\"text-center mb-6\">\n                <div className=\"relative inline-block\">\n                  <div className=\"w-32 h-32 rounded-full overflow-hidden bg-gray-200 mx-auto mb-4\">\n                    {profileImage ? (\n                      <Image\n                        src={profileImage}\n                        alt=\"Profile\"\n                        width={128}\n                        height={128}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-full flex items-center justify-center text-gray-400 text-4xl\">\n                        👤\n                      </div>\n                    )}\n                  </div>\n                  {imageUploading && (\n                    <div className=\"absolute inset-0 flex items-center justify-center bg-black/50 rounded-full\">\n                      <div className=\"text-white text-sm\">\n                        {Math.round(uploadProgress)}%\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageUpload}\n                  className=\"hidden\"\n                />\n                <button\n                  onClick={() => fileInputRef.current?.click()}\n                  disabled={imageUploading}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {imageUploading ? \"Uploading...\" : \"Change Photo\"}\n                </button>\n              </div>\n\n              {/* Name */}\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={editName}\n                    onChange={(e) => setEditName(e.target.value)}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Enter your name\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={userData?.email || \"\"}\n                    disabled\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Email cannot be changed\n                  </p>\n                </div>\n\n                <button\n                  onClick={handleProfileUpdate}\n                  disabled={profileSaving}\n                  className=\"w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\"\n                >\n                  {profileSaving ? \"Saving...\" : \"Save Profile\"}\n                </button>\n              </div>\n            </div>\n\n            {/* Password Change */}\n            <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                Change Password\n              </h2>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Current Password\n                  </label>\n                  <input\n                    type=\"password\"\n                    value={passwords.oldPassword}\n                    onChange={(e) =>\n                      setPasswords((prev) => ({\n                        ...prev,\n                        oldPassword: e.target.value,\n                      }))\n                    }\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    New Password\n                  </label>\n                  <input\n                    type=\"password\"\n                    value={passwords.newPassword}\n                    onChange={(e) =>\n                      setPasswords((prev) => ({\n                        ...prev,\n                        newPassword: e.target.value,\n                      }))\n                    }\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Confirm New Password\n                  </label>\n                  <input\n                    type=\"password\"\n                    value={passwords.confirmPassword}\n                    onChange={(e) =>\n                      setPasswords((prev) => ({\n                        ...prev,\n                        confirmPassword: e.target.value,\n                      }))\n                    }\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n\n                <button\n                  onClick={handlePasswordChange}\n                  disabled={\n                    passwordChanging ||\n                    !passwords.oldPassword ||\n                    !passwords.newPassword ||\n                    !passwords.confirmPassword\n                  }\n                  className=\"w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors\"\n                >\n                  {passwordChanging ? \"Changing...\" : \"Change Password\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <Cart isOpen={isCartOpen} onClose={handleCartClose} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAqBe,SAAS,kBAAkB,EAAE,IAAI,EAA0B;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,aAAa;QACb,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,kBAAkB;QACtB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,WAAW;QACxD,aAAa,KAAK,MAAM;IAC1B;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,SAAS;YACT,WAAW;YACX;QACF;QAEA,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG;YACpD,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;YAC5C,aAAa;QACf,GACG,IAAI,CAAC,OAAO;YACX,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,gBAAgB,MAAM,SAAS,IAAI;gBACzC,MAAM,WAAW,cAAc,IAAI;gBACnC,YAAY;gBACZ,YAAY,SAAS,IAAI,IAAI;gBAC7B,gBAAgB,SAAS,YAAY,IAAI;YAC3C,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,sBAAsB,SAAS,MAAM,EAAE;gBACrD,SAAS,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE;YACvD;QACF,GACC,KAAK,CAAC,CAAC;YACN,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,GACC,OAAO,CAAC,IAAM,WAAW;IAC9B,GAAG,EAAE;IAEL,MAAM,oBAAoB,OACxB;QAEA,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,SAAS;YACT;QACF;QAEA,kBAAkB;QAClB,kBAAkB;QAClB,SAAS;QAET,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,gBAAgB;QAEhC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,MAAM,IAAI;YAEhB,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;gBACvC,IAAI,EAAE,gBAAgB,EAAE;oBACtB,MAAM,WAAW,AAAC,EAAE,MAAM,GAAG,EAAE,KAAK,GAAI;oBACxC,kBAAkB;gBACpB;YACF;YAEA,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,KAAK,KAAK;oBACtB,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;oBAC5C,gBAAgB,SAAS,YAAY;oBACrC,YAAY,CAAC,OACX,OAAO;4BAAE,GAAG,IAAI;4BAAE,cAAc,SAAS,YAAY;wBAAC,IAAI;oBAE5D,WAAW;gBACb,OAAO;oBACL,SAAS;gBACX;gBACA,kBAAkB;gBAClB,kBAAkB;YACpB;YAEA,IAAI,OAAO,GAAG;gBACZ,SAAS;gBACT,kBAAkB;gBAClB,kBAAkB;YACpB;YAEA,+DAA+D;YAC/D,iDAAiD;YACjD,SACE;YAEF,kBAAkB;YAClB,kBAAkB;YAClB;QACF,EAAE,OAAM;YACN,SAAS;YACT,kBAAkB;YAClB,kBAAkB;QACpB;IACF;IAEA,MAAM,sBAAsB;QAC1B,iBAAiB;QACjB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MACrB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,GAClD;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAS;YACxC;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,cAAc,MAAM,SAAS,IAAI;gBACvC,YAAY;gBACZ,WAAW;YACb,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,UAAU,WAAW,KAAK,UAAU,eAAe,EAAE;YACvD,SAAS;YACT;QACF;QAEA,IAAI,UAAU,WAAW,CAAC,MAAM,GAAG,GAAG;YACpC,SAAS;YACT;QACF;QAEA,oBAAoB;QACpB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MACrB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,GACnD;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa,UAAU,WAAW;oBAClC,aAAa,UAAU,WAAW;gBACpC;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa;oBAAE,aAAa;oBAAI,aAAa;oBAAI,iBAAiB;gBAAG;gBACrE,WAAW;YACb,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;YAChC;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE;;0BACE,8OAAC,4HAAA,CAAA,UAAM;gBAAC,WAAW;gBAAW,aAAa;;;;;;0BAC3C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAA4F;;;;;;8DAG5G,8OAAC;oDAAK,WAAU;8DAAa;;;;;;;;;;;;sDAE/B,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAG5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;;wDAAqB;wDACL,MAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;0CAO1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;4BAMF,uBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;4BAGJ,yBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAKtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,6BACC,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK;oEACL,KAAI;oEACJ,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;yFAGZ,8OAAC;oEAAI,WAAU;8EAAwE;;;;;;;;;;;4DAK1F,gCACC,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;wEACZ,KAAK,KAAK,CAAC;wEAAgB;;;;;;;;;;;;;;;;;;kEAMpC,8OAAC;wDACC,KAAK;wDACL,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDACC,SAAS,IAAM,aAAa,OAAO,EAAE;wDACrC,UAAU;wDACV,WAAU;kEAET,iBAAiB,iBAAiB;;;;;;;;;;;;0DAKvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC3C,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,UAAU,SAAS;gEAC1B,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAK5C,8OAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAU;kEAET,gBAAgB,cAAc;;;;;;;;;;;;;;;;;;kDAMrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAItD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,UAAU,WAAW;gEAC5B,UAAU,CAAC,IACT,aAAa,CAAC,OAAS,CAAC;4EACtB,GAAG,IAAI;4EACP,aAAa,EAAE,MAAM,CAAC,KAAK;wEAC7B,CAAC;gEAEH,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,UAAU,WAAW;gEAC5B,UAAU,CAAC,IACT,aAAa,CAAC,OAAS,CAAC;4EACtB,GAAG,IAAI;4EACP,aAAa,EAAE,MAAM,CAAC,KAAK;wEAC7B,CAAC;gEAEH,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,UAAU,eAAe;gEAChC,UAAU,CAAC,IACT,aAAa,CAAC,OAAS,CAAC;4EACtB,GAAG,IAAI;4EACP,iBAAiB,EAAE,MAAM,CAAC,KAAK;wEACjC,CAAC;gEAEH,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDACC,SAAS;wDACT,UACE,oBACA,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,eAAe;wDAE5B,WAAU;kEAET,mBAAmB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC,0HAAA,CAAA,UAAI;gBAAC,QAAQ;gBAAY,SAAS;;;;;;;;AAGzC", "debugId": null}}]}