"use client";
import { useState } from "react";
import { register } from "@/api/auth";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";
import {
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  User,
  Mail,
  Lock,
  Shield,
} from "lucide-react";

// Enhanced password validation with strength requirements
const registerSchema = z
  .object({
    name: z
      .string()
      .min(2, "Name must be at least 2 characters")
      .max(50, "Name must be less than 50 characters")
      .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces"),
    email: z
      .string()
      .email("Please enter a valid email address")
      .max(100, "Email must be less than 100 characters"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[0-9]/, "Password must contain at least one number")
      .regex(
        /[^A-Za-z0-9]/,
        "Password must contain at least one special character"
      ),
    confirmPassword: z.string(),
    terms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type RegisterFormData = z.infer<typeof registerSchema>;

// Password strength checker
const getPasswordStrength = (password: string) => {
  let score = 0;
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[^A-Za-z0-9]/.test(password),
  };

  Object.values(checks).forEach((check) => check && score++);

  return {
    score,
    checks,
    strength: score < 2 ? "weak" : score < 4 ? "medium" : "strong",
  };
};

export default function RegisterPage() {
  const [formData, setFormData] = useState<RegisterFormData>({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    terms: false,
  });
  const [errors, setErrors] = useState<
    Partial<Record<keyof RegisterFormData, string>>
  >({});
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (
    field: keyof RegisterFormData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess(false);
    setErrors({});

    // Validate form data with Zod
    const validation = registerSchema.safeParse(formData);
    if (!validation.success) {
      setLoading(false);
      const fieldErrors: Partial<Record<keyof RegisterFormData, string>> = {};
      validation.error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as keyof RegisterFormData] = err.message;
        }
      });
      setErrors(fieldErrors);
      return;
    }

    try {
      const result = await register(
        formData.email,
        formData.password,
        formData.name
      );
      setLoading(false);

      if (result.message) {
        setSuccess(true);
        setSuccessMessage(result.message);
        // Don't auto-login anymore - user needs to verify email first
      } else if (result.access_token) {
        // Fallback for old registration flow
        setSuccess(true);
        setSuccessMessage(
          "Registration successful! Please check your email to verify your account."
        );
      } else {
        setError(result.error || "Registration failed");
      }
    } catch {
      setLoading(false);
      setError("Network error. Please try again.");
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4">
      <div className="w-full max-w-lg bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100">
        <div className="flex flex-col items-center">
          <Image
            src="/6 Light Logo.png"
            alt="6 Light Logo"
            width={64}
            height={64}
            className="mb-2"
          />
        </div>
        <h2 className="text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Create Account
        </h2>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          {/* Name Input */}
          <div className="space-y-1">
            <div className="relative">
              <User
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={20}
              />
              <input
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Full Name"
                type="text"
                className={`w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ${
                  errors.name
                    ? "border-red-500 focus:ring-red-300"
                    : "border-gray-300 focus:ring-blue-300"
                }`}
                autoComplete="name"
                aria-invalid={!!errors.name}
                aria-describedby={errors.name ? "name-error" : undefined}
              />
            </div>
            {errors.name && (
              <div
                id="name-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.name}
              </div>
            )}
          </div>

          {/* Email Input */}
          <div className="space-y-1">
            <div className="relative">
              <Mail
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={20}
              />
              <input
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="Email Address"
                type="email"
                className={`w-full border rounded-lg pl-10 pr-4 py-3 focus:outline-none focus:ring-2 transition ${
                  errors.email
                    ? "border-red-500 focus:ring-red-300"
                    : "border-gray-300 focus:ring-blue-300"
                }`}
                autoComplete="email"
                aria-invalid={!!errors.email}
                aria-describedby={errors.email ? "email-error" : undefined}
              />
            </div>
            {errors.email && (
              <div
                id="email-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.email}
              </div>
            )}
          </div>

          {/* Password Input with Strength Indicator */}
          <div className="space-y-1">
            <div className="relative">
              <Lock
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={20}
              />
              <input
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                className={`w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ${
                  errors.password
                    ? "border-red-500 focus:ring-red-300"
                    : "border-gray-300 focus:ring-blue-300"
                }`}
                autoComplete="new-password"
                aria-invalid={!!errors.password}
                aria-describedby={
                  errors.password ? "password-error" : undefined
                }
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            {/* Password Strength Indicator */}
            {formData.password && (
              <div className="space-y-2">
                <div className="flex gap-1">
                  {[1, 2, 3, 4, 5].map((level) => {
                    const strength = getPasswordStrength(formData.password);
                    return (
                      <div
                        key={level}
                        className={`h-1 flex-1 rounded ${
                          level <= strength.score
                            ? strength.strength === "weak"
                              ? "bg-red-500"
                              : strength.strength === "medium"
                              ? "bg-yellow-500"
                              : "bg-green-500"
                            : "bg-gray-200"
                        }`}
                      />
                    );
                  })}
                </div>
                <div className="text-xs space-y-1">
                  {Object.entries(
                    getPasswordStrength(formData.password).checks
                  ).map(([key, passed]) => (
                    <div
                      key={key}
                      className={`flex items-center gap-1 ${
                        passed ? "text-green-600" : "text-gray-400"
                      }`}
                    >
                      {passed ? (
                        <CheckCircle size={12} />
                      ) : (
                        <AlertCircle size={12} />
                      )}
                      <span>
                        {key === "length" && "8+ characters"}
                        {key === "uppercase" && "Uppercase letter"}
                        {key === "lowercase" && "Lowercase letter"}
                        {key === "number" && "Number"}
                        {key === "special" && "Special character"}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {errors.password && (
              <div
                id="password-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.password}
              </div>
            )}
          </div>

          {/* Confirm Password Input */}
          <div className="space-y-1">
            <div className="relative">
              <Shield
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={20}
              />
              <input
                value={formData.confirmPassword}
                onChange={(e) =>
                  handleInputChange("confirmPassword", e.target.value)
                }
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                className={`w-full border rounded-lg pl-10 pr-12 py-3 focus:outline-none focus:ring-2 transition ${
                  errors.confirmPassword
                    ? "border-red-500 focus:ring-red-300"
                    : "border-gray-300 focus:ring-blue-300"
                }`}
                autoComplete="new-password"
                aria-invalid={!!errors.confirmPassword}
                aria-describedby={
                  errors.confirmPassword ? "confirm-password-error" : undefined
                }
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                aria-label={
                  showConfirmPassword ? "Hide password" : "Show password"
                }
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            {errors.confirmPassword && (
              <div
                id="confirm-password-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.confirmPassword}
              </div>
            )}
          </div>
          {/* Terms and Conditions */}
          <div className="space-y-1">
            <label className="flex items-start gap-3 text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={formData.terms}
                onChange={(e) => handleInputChange("terms", e.target.checked)}
                className={`mt-1 accent-blue-600 ${
                  errors.terms ? "accent-red-600" : ""
                }`}
                aria-invalid={!!errors.terms}
                aria-describedby={errors.terms ? "terms-error" : undefined}
              />
              <span className="text-gray-700">
                I accept the{" "}
                <a
                  href="#"
                  className="underline text-blue-600 hover:text-blue-800"
                >
                  terms and conditions
                </a>{" "}
                and{" "}
                <a
                  href="#"
                  className="underline text-blue-600 hover:text-blue-800"
                >
                  privacy policy
                </a>
              </span>
            </label>
            {errors.terms && (
              <div
                id="terms-error"
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <AlertCircle size={16} />
                {errors.terms}
              </div>
            )}
          </div>

          <button
            type="submit"
            className={`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${
              loading
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
            }`}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating Account...</span>
              </div>
            ) : (
              "Create Account"
            )}
          </button>

          {/* Global Error Display */}
          {error && (
            <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200">
              <AlertCircle size={16} />
              {error}
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="flex items-center gap-2 text-green-600 text-sm bg-green-50 p-3 rounded-lg border border-green-200">
              <CheckCircle size={16} />
              {successMessage ||
                "Registration successful! Please check your email to verify your account."}
            </div>
          )}
        </form>
        <div className="text-center text-sm text-gray-600">
          Already have an account?{" "}
          <Link
            href="/login"
            className="text-red-700 font-semibold hover:underline"
          >
            Login
          </Link>
        </div>
      </div>
    </div>
  );
}
