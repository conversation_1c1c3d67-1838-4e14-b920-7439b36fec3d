"use client";

import React from "react";

interface CustomizationWarningModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CustomizationWarningModal: React.FC<CustomizationWarningModalProps> = ({
  isOpen,
  onClose,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all">
        {/* Modal header with gradient */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-1">
          <div className="bg-white dark:bg-gray-800 rounded-t-xl p-4">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <svg
                  className="w-6 h-6 text-amber-500 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                Save Your Design
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Modal body */}
        <div className="p-6">
          <div className="flex items-start mb-4">
            <div className="flex-shrink-0 bg-amber-100 rounded-full p-2">
              <svg
                className="h-6 w-6 text-amber-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-gray-700 dark:text-gray-300 mb-2">
                Please create and save your design before adding to cart.
              </p>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Click the{" "}
                <span className="font-bold text-indigo-600 dark:text-indigo-400">
                  'Save Design'
                </span>{" "}
                button in the customizer to save your changes.
              </p>
            </div>
          </div>

          {/* Steps */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              Follow these steps:
            </h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700 dark:text-gray-300">
              <li>Complete your design using the customizer tools</li>
              <li>
                Click the{" "}
                <span className="font-bold text-indigo-600 dark:text-indigo-400">
                  Save Design
                </span>{" "}
                button at the bottom of the customizer
              </li>
              <li>Then click "Add to Cart" to proceed</li>
            </ol>
          </div>
        </div>

        {/* Modal footer */}
        <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg text-sm hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 flex items-center"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
            Got it
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomizationWarningModal;
