# 🚀 Complete Production Environment Setup

## ❌ Current Issue
Frontend can't connect to backend in production because environment variables are not properly configured in hosting platforms.

## 🔧 Root Cause
- **Frontend**: Using localhost API URL in production
- **Backend**: CORS blocking requests due to missing environment variables
- **Environment Variables**: Not set in hosting platforms (Netlify & Render)

## ✅ Complete Fix

### 1. **Render Backend Environment Variables**

Go to Render Dashboard → Your Backend Service → Environment Tab

Add these **EXACT** variables:

```
NODE_ENV=production
FRONTEND_URL=https://sixlightmediastorebeta.netlify.app
CORS_ORIGIN=https://sixlightmediastorebeta.netlify.app
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=dxsl tqan xfzc vynn
EMAIL_FROM_NAME=Six Light Media Store
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_VERIFICATION_EXPIRES_HOURS=24
PASSWORD_RESET_EXPIRES_HOURS=1
COOKIE_SECURE=true
TRUST_PROXY=true
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
DIRECT_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
JWT_SECRET=tVfn+VlANCzmunn+0AvG3WKJ+frOoPBUfLD8XsLS+bA=
```

### 2. **Netlify Frontend Environment Variables**

Go to Netlify Dashboard → Your Site → Site Settings → Environment Variables

Add these **EXACT** variables:

```
NEXT_PUBLIC_API_URL=https://backendapi-sixlight.onrender.com
NEXT_PUBLIC_SITE_URL=https://sixlightmediastorebeta.netlify.app
NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/fwbvmq9re
NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY=public_kFR0vTVL7DIDI8YW9eF6/luGjB4=
IMAGEKIT_PRIVATE_KEY=private_jtsBIKXyuTkkHzHJOBSHLqbyK74=
```

### 3. **Deploy Both Services**

1. **Deploy Backend** (Render will auto-deploy after env vars are added)
2. **Deploy Frontend** (Netlify will auto-deploy after env vars are added)

## 🔍 Verification Steps

### Check Backend Logs (Render)
Look for these messages:

```
🔧 Initializing Email Service...
📧 SMTP Configuration:
   Host: smtp.gmail.com
   User: <EMAIL>
   Pass: ***configured***
   Environment: production
✅ SMTP connection verified successfully

🔧 CORS allowed origins: ["https://sixlightmediastorebeta.netlify.app"]
```

### Check Frontend Console (Browser)
After login attempt, look for:

```
Verifying auth with: https://backendapi-sixlight.onrender.com/auth/verify
Auth verification response: 200
Auth verification successful for user: <EMAIL>
Redirecting to: /user/dashboard
```

### Test Email Verification
1. Register new user in production
2. Check email for verification link
3. Verify email links point to Netlify domain

## 🆘 Troubleshooting

### If Login Still Fails:
1. **Clear browser data** (cookies, localStorage)
2. **Check Network tab** for failed API requests
3. **Look for CORS errors** in console
4. **Verify environment variables** are set correctly

### If Email Not Received:
1. **Check spam folder**
2. **Verify SMTP credentials** in Render logs
3. **Check backend logs** for email sending errors
4. **Test with different email address**

### Common Issues:
- **Typos in environment variables** (case-sensitive)
- **Missing protocol** (https:// vs http://)
- **Trailing slashes** in URLs
- **Cached builds** (clear and redeploy)

## ✅ Success Indicators

- ✅ **Login works** without redirect loops
- ✅ **Email verification** emails received
- ✅ **Dashboard loads** after login
- ✅ **No CORS errors** in browser console
- ✅ **Backend logs** show successful SMTP connection

## 📞 Quick Test

**Test API Connection:**
```bash
curl https://backendapi-sixlight.onrender.com/auth/verify
# Should return 401 (Unauthorized) - means API is working
```

**Test CORS:**
```bash
curl -H "Origin: https://sixlightmediastorebeta.netlify.app" \
     -X OPTIONS \
     https://backendapi-sixlight.onrender.com/auth/verify
# Should return 200 with CORS headers
```

After setting these environment variables, everything should work! 🎯
