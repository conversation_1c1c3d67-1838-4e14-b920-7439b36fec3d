"use client";

import React, { useState, useEffect } from "react";
import OrderForm from "./OrderForm";

type CartItem = {
  id: number;
  productId: number;
  name: string;
  color?: string;
  text?: string;
  customized: boolean;
  customizationData?: {
    canvasData?: string;
    preview?: string;
  };
  price: number;
  quantity?: number;
};

type CartProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function Cart({ isOpen, onClose }: CartProps) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadCartItems();
    }
  }, [isOpen]);

  const loadCartItems = () => {
    const cart = JSON.parse(localStorage.getItem("cart") || "[]");
    setCartItems(cart);
  };

  const removeItem = (itemId: number) => {
    const updatedCart = cartItems.filter((item) => item.id !== itemId);
    setCartItems(updatedCart);
    localStorage.setItem("cart", JSON.stringify(updatedCart));
  };

  const updateQuantity = (itemId: number, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(itemId);
      return;
    }

    const updatedCart = cartItems.map((item) =>
      item.id === itemId ? { ...item, quantity: newQuantity } : item
    );
    setCartItems(updatedCart);
    localStorage.setItem("cart", JSON.stringify(updatedCart));
  };

  const calculateTotal = () => {
    return cartItems.reduce(
      (total, item) => total + item.price * (item.quantity || 1),
      0
    );
  };

  const handleCheckout = () => {
    setShowOrderForm(true);
  };

  const handleOrderSuccess = () => {
    setShowOrderForm(false);
    setOrderSuccess(true);
    setCartItems([]);

    // Close success message after 3 seconds
    setTimeout(() => {
      setOrderSuccess(false);
      onClose();
    }, 3000);
  };

  if (!isOpen) return null;

  if (showOrderForm) {
    return (
      <OrderForm
        cartItems={cartItems}
        onOrderSuccess={handleOrderSuccess}
        onCancel={() => setShowOrderForm(false)}
      />
    );
  }

  if (orderSuccess) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center max-w-md">
          <div className="text-green-600 text-6xl mb-4">✓</div>
          <h2 className="text-2xl font-bold text-[#1a237e] mb-2">
            Order Placed Successfully!
          </h2>
          <p className="text-gray-600 mb-4">
            Thank you for your order. We will contact you soon for payment and
            delivery arrangements.
          </p>
          <div className="animate-pulse text-sm text-gray-500">
            Closing automatically...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#1a237e]">Shopping Cart</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
              aria-label="Close cart"
            >
              ×
            </button>
          </div>

          {cartItems.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🛒</div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                Your cart is empty
              </h3>
              <p className="text-gray-500 mb-6">
                Add some products to get started!
              </p>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-[#1a237e] text-white rounded-md hover:bg-[#2a3490] transition"
              >
                Continue Shopping
              </button>
            </div>
          ) : (
            <>
              {/* Cart Items */}
              <div className="space-y-4 mb-6">
                {cartItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg"
                  >
                    {/* Customization Preview */}
                    {item.customized && item.customizationData?.preview && (
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 border border-gray-300 rounded-lg overflow-hidden bg-gray-50">
                          <img
                            src={item.customizationData.preview}
                            alt="Customization preview"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="text-xs text-center text-gray-500 mt-1">
                          Custom Design
                        </div>
                      </div>
                    )}

                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{item.name}</h3>
                      {item.customized && (
                        <div className="text-sm text-gray-600 mt-1">
                          {item.color && (
                            <div className="flex items-center gap-2 mb-1">
                              <span>Color:</span>
                              <div
                                className="w-4 h-4 rounded border border-gray-300"
                                style={{ backgroundColor: item.color }}
                              ></div>
                              <span className="font-mono text-xs">
                                {item.color}
                              </span>
                            </div>
                          )}
                          {item.text && (
                            <div className="mb-1">
                              <span>Text: &ldquo;</span>
                              <span className="font-medium">{item.text}</span>
                              <span>&rdquo;</span>
                            </div>
                          )}
                          {item.customizationData?.canvasData && (
                            <div className="inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                              <svg
                                className="w-3 h-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z" />
                              </svg>
                              Advanced Design
                            </div>
                          )}
                        </div>
                      )}
                      <div className="text-lg font-semibold text-[#1a237e] mt-2">
                        K{item.price.toFixed(2)} each
                      </div>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() =>
                          updateQuantity(item.id, (item.quantity || 1) - 1)
                        }
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition"
                      >
                        -
                      </button>
                      <span className="w-8 text-center font-semibold">
                        {item.quantity || 1}
                      </span>
                      <button
                        onClick={() =>
                          updateQuantity(item.id, (item.quantity || 1) + 1)
                        }
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition"
                      >
                        +
                      </button>
                    </div>

                    {/* Remove Button */}
                    <button
                      onClick={() => removeItem(item.id)}
                      className="text-red-500 hover:text-red-700 p-2"
                      aria-label="Remove item"
                    >
                      🗑️
                    </button>
                  </div>
                ))}
              </div>

              {/* Cart Total */}
              <div className="border-t border-gray-200 pt-4 mb-6">
                <div className="flex justify-between items-center text-xl font-bold">
                  <span>Total:</span>
                  <span className="text-[#1a237e]">
                    K{calculateTotal().toFixed(2)}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition"
                >
                  Continue Shopping
                </button>
                <button
                  onClick={handleCheckout}
                  className="flex-1 px-4 py-2 bg-[#1a237e] text-white rounded-md font-semibold hover:bg-[#2a3490] transition"
                >
                  Proceed to Checkout
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
