{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/config.ts"], "sourcesContent": ["/**\n * Configuration utilities for the Six Light Media Store\n * Handles environment-specific settings and API endpoints\n */\n\n// Environment detection\nexport const isDevelopment = process.env.NODE_ENV === \"development\";\nexport const isProduction = process.env.NODE_ENV === \"production\";\n\n// API Configuration\nexport const API_CONFIG = {\n  // Backend API URL with environment-specific defaults\n  BASE_URL:\n    process.env.NEXT_PUBLIC_API_URL ||\n    (isProduction\n      ? \"https://backendapi-sixlight.onrender.com\"\n      : \"http://localhost:3001\"),\n\n  // API endpoints\n  ENDPOINTS: {\n    AUTH: {\n      LOGIN: \"/auth/login\",\n      REGISTER: \"/auth/register\",\n      REFRESH: \"/auth/refresh\",\n    },\n    PRODUCTS: \"/product\",\n    CATEGORIES: \"/categories\",\n    ORDERS: \"/orders\",\n    ADMIN: {\n      DASHBOARD: \"/admin/dashboard\",\n      ORDERS: \"/admin/orders\",\n      USERS: \"/admin/users\",\n      PRODUCTS: \"/admin/products\",\n      CATEGORIES: \"/admin/categories\",\n      ORDER_COLLECTED: \"/admin/orders\", // Base path, will append /{id}/collected\n    },\n    USER: {\n      DASHBOARD: \"/user/dashboard\",\n      PROFILE: \"/user/profile\",\n      CHANGE_PASSWORD: \"/user/change-password\",\n      DELETE: \"/user/delete\",\n      ORDERS: \"/user/orders\",\n    },\n  },\n\n  // Request configuration\n  DEFAULT_HEADERS: {\n    \"Content-Type\": \"application/json\",\n  },\n\n  // Timeout settings\n  TIMEOUT: isProduction ? 10000 : 5000, // 10s prod, 5s dev\n};\n\n// Site Configuration\nexport const SITE_CONFIG = {\n  URL:\n    process.env.NEXT_PUBLIC_SITE_URL ||\n    (isProduction ? \"https://yourdomain.com\" : \"http://localhost:3000\"),\n  NAME: \"Six Light Media Store\",\n  DESCRIPTION: \"Premium custom products and personalized gifts\",\n  LOGO: \"/6 Light Logo.png\",\n};\n\n// ImageKit Configuration\nexport const IMAGEKIT_CONFIG = {\n  URL_ENDPOINT: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT || \"\",\n  PUBLIC_KEY: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY || \"\",\n  PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || \"\",\n};\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  GA_ID: process.env.NEXT_PUBLIC_GA_ID || \"\",\n  ENABLED: isProduction && !!process.env.NEXT_PUBLIC_GA_ID,\n};\n\n// Utility functions\nexport const getApiUrl = (endpoint: string): string => {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, \"\"); // Remove trailing slash\n  const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n};\n\nexport const getAuthHeaders = (): Record<string, string> => {\n  const token =\n    typeof window !== \"undefined\" ? localStorage.getItem(\"token\") : null;\n  return {\n    ...API_CONFIG.DEFAULT_HEADERS,\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n};\n\n// Environment validation\nexport const validateEnvironment = (): {\n  isValid: boolean;\n  errors: string[];\n} => {\n  const errors: string[] = [];\n\n  if (!API_CONFIG.BASE_URL) {\n    errors.push(\"NEXT_PUBLIC_API_URL is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.URL_ENDPOINT) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT is not configured\");\n  }\n\n  if (!IMAGEKIT_CONFIG.PUBLIC_KEY) {\n    errors.push(\"NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY is not configured\");\n  }\n\n  if (isProduction && !SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n    // Only warn in production if still using placeholder\n    if (SITE_CONFIG.URL.includes(\"yourdomain.com\")) {\n      errors.push(\n        \"NEXT_PUBLIC_SITE_URL should be updated with your actual domain in production\"\n      );\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Debug information (development only)\nexport const getDebugInfo = () => {\n  if (!isDevelopment) return null;\n\n  return {\n    environment: process.env.NODE_ENV,\n    apiUrl: API_CONFIG.BASE_URL,\n    siteUrl: SITE_CONFIG.URL,\n    imagekitConfigured: !!IMAGEKIT_CONFIG.URL_ENDPOINT,\n    analyticsEnabled: ANALYTICS_CONFIG.ENABLED,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;AACjB,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,eAAe,oDAAyB;AAG9C,MAAM,aAAa;IACxB,qDAAqD;IACrD,UACE,6DACA,CAAC,6EAEG,uBAAuB;IAE7B,gBAAgB;IAChB,WAAW;QACT,MAAM;YACJ,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;QACnB;QACA,MAAM;YACJ,WAAW;YACX,SAAS;YACT,iBAAiB;YACjB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,iBAAiB;QACf,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,SAAS,6EAAuB;AAClC;AAGO,MAAM,cAAc;IACzB,KACE,6DACA,CAAC,6EAA0C,uBAAuB;IACpE,MAAM;IACN,aAAa;IACb,MAAM;AACR;AAGO,MAAM,kBAAkB;IAC7B,cAAc,wEAAiD;IAC/D,YAAY,2EAA+C;IAC3D,aAAa,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AACnD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IACxC,SAAS,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB;AAC1D;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QACJ,6EAAgE;IAClE,OAAO;QACL,GAAG,WAAW,eAAe;QAC7B,GAAI,SAAS;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,CAAC;IACnD;AACF;AAGO,MAAM,sBAAsB;IAIjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,WAAW,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,YAAY,EAAE;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,gBAAgB,UAAU,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,gBAAgB,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,mBAAmB;;IAOjE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,uCAAoB;;IAAW;IAE/B,OAAO;QACL,WAAW;QACX,QAAQ,WAAW,QAAQ;QAC3B,SAAS,YAAY,GAAG;QACxB,oBAAoB,CAAC,CAAC,gBAAgB,YAAY;QAClD,kBAAkB,iBAAiB,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/api/auth.ts"], "sourcesContent": ["import { getApiUrl, API_CONFIG } from \"@/lib/config\";\r\n\r\nexport async function login(email: string, password: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.LOGIN), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    body: JSON.stringify({ email, password }),\r\n  });\r\n  return res.json();\r\n}\r\n\r\nexport async function register(email: string, password: string, name?: string) {\r\n  const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER), {\r\n    method: \"POST\",\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    body: JSON.stringify({ email, password, name }),\r\n  });\r\n  return res.json();\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,MAAM,KAAa,EAAE,QAAgB;IACzD,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG;QAClE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IACA,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,SAAS,KAAa,EAAE,QAAgB,EAAE,IAAa;IAC3E,MAAM,MAAM,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;YAAU;QAAK;IAC/C;IACA,OAAO,IAAI,IAAI;AACjB", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/register/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { register } from \"@/api/auth\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { z } from \"zod\";\r\nimport {\r\n  Eye,\r\n  EyeOff,\r\n  AlertCircle,\r\n  CheckCircle,\r\n  User,\r\n  Mail,\r\n  Lock,\r\n  Shield,\r\n} from \"lucide-react\";\r\n\r\n// Enhanced password validation with strength requirements\r\nconst registerSchema = z\r\n  .object({\r\n    name: z\r\n      .string()\r\n      .min(2, \"Name must be at least 2 characters\")\r\n      .max(50, \"Name must be less than 50 characters\")\r\n      .regex(/^[a-zA-Z\\s]+$/, \"Name can only contain letters and spaces\"),\r\n    email: z\r\n      .string()\r\n      .email(\"Please enter a valid email address\")\r\n      .max(100, \"Email must be less than 100 characters\"),\r\n    password: z\r\n      .string()\r\n      .min(8, \"Password must be at least 8 characters\")\r\n      .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\r\n      .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\r\n      .regex(/[0-9]/, \"Password must contain at least one number\")\r\n      .regex(\r\n        /[^A-Za-z0-9]/,\r\n        \"Password must contain at least one special character\"\r\n      ),\r\n    confirmPassword: z.string(),\r\n    terms: z.boolean().refine((val) => val === true, {\r\n      message: \"You must accept the terms and conditions\",\r\n    }),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\ntype RegisterFormData = z.infer<typeof registerSchema>;\r\n\r\n// Password strength checker\r\nconst getPasswordStrength = (password: string) => {\r\n  let score = 0;\r\n  const checks = {\r\n    length: password.length >= 8,\r\n    uppercase: /[A-Z]/.test(password),\r\n    lowercase: /[a-z]/.test(password),\r\n    number: /[0-9]/.test(password),\r\n    special: /[^A-Za-z0-9]/.test(password),\r\n  };\r\n\r\n  Object.values(checks).forEach((check) => check && score++);\r\n\r\n  return {\r\n    score,\r\n    checks,\r\n    strength: score < 2 ? \"weak\" : score < 4 ? \"medium\" : \"strong\",\r\n  };\r\n};\r\n\r\nexport default function RegisterPage() {\r\n  const [formData, setFormData] = useState<RegisterFormData>({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    confirmPassword: \"\",\r\n    terms: false,\r\n  });\r\n  const [errors, setErrors] = useState<\r\n    Partial<Record<keyof RegisterFormData, string>>\r\n  >({});\r\n  const [error, setError] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [success, setSuccess] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n\r\n  const handleInputChange = (\r\n    field: keyof RegisterFormData,\r\n    value: string | boolean\r\n  ) => {\r\n    setFormData((prev) => ({ ...prev, [field]: value }));\r\n    // Clear field-specific error when user starts typing\r\n    if (errors[field]) {\r\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\r\n    }\r\n  };\r\n\r\n  async function handleSubmit(e: React.FormEvent) {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(\"\");\r\n    setSuccess(false);\r\n    setErrors({});\r\n\r\n    // Validate form data with Zod\r\n    const validation = registerSchema.safeParse(formData);\r\n    if (!validation.success) {\r\n      setLoading(false);\r\n      const fieldErrors: Partial<Record<keyof RegisterFormData, string>> = {};\r\n      validation.error.errors.forEach((err) => {\r\n        if (err.path[0]) {\r\n          fieldErrors[err.path[0] as keyof RegisterFormData] = err.message;\r\n        }\r\n      });\r\n      setErrors(fieldErrors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const result = await register(\r\n        formData.email,\r\n        formData.password,\r\n        formData.name\r\n      );\r\n      setLoading(false);\r\n\r\n      if (result.access_token) {\r\n        setSuccess(true);\r\n        localStorage.setItem(\"token\", result.access_token);\r\n        // Redirect to login after successful registration\r\n        setTimeout(() => {\r\n          window.location.href = \"/login\";\r\n        }, 1500);\r\n      } else {\r\n        setError(result.error || \"Registration failed\");\r\n      }\r\n    } catch (err) {\r\n      setLoading(false);\r\n      setError(\"Network error. Please try again.\");\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-black\">\r\n      <div className=\"w-full max-w-md bg-white rounded-2xl shadow-xl p-8 flex flex-col gap-6\">\r\n        <div className=\"flex flex-col items-center\">\r\n          <Image\r\n            src=\"/6 Light Logo.png\"\r\n            alt=\"6 Light Logo\"\r\n            width={64}\r\n            height={64}\r\n            className=\"mb-2\"\r\n          />\r\n        </div>\r\n        <h2 className=\"text-3xl font-extrabold text-center text-red-700\">\r\n          Create Account\r\n        </h2>\r\n        <form onSubmit={handleSubmit} className=\"flex flex-col gap-4\">\r\n          <input\r\n            value={name}\r\n            onChange={(e) => setName(e.target.value)}\r\n            placeholder=\"Name\"\r\n            className=\"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition\"\r\n            required\r\n            autoComplete=\"name\"\r\n          />\r\n          <input\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            placeholder=\"Email\"\r\n            className=\"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition\"\r\n            required\r\n            autoComplete=\"email\"\r\n          />\r\n          <input\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            type=\"password\"\r\n            placeholder=\"Password\"\r\n            className=\"border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-300 transition\"\r\n            required\r\n            autoComplete=\"new-password\"\r\n          />\r\n          <label className=\"flex items-center gap-2 text-sm\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={terms}\r\n              onChange={(e) => setTerms(e.target.checked)}\r\n              className=\"accent-red-700\"\r\n            />\r\n            I accept the{\" \"}\r\n            <a href=\"#\" className=\"underline text-red-700\">\r\n              terms and conditions\r\n            </a>\r\n          </label>\r\n          <button\r\n            type=\"submit\"\r\n            className=\"bg-black text-white font-semibold px-6 py-3 rounded-lg shadow hover:bg-green-700 transition disabled:opacity-60\"\r\n            disabled={loading}\r\n          >\r\n            {loading ? \"Registering...\" : \"Register\"}\r\n          </button>\r\n          {error && (\r\n            <div className=\"text-red-600 text-center text-sm mt-2\">{error}</div>\r\n          )}\r\n          {success && (\r\n            <div className=\"text-green-600 text-center text-sm mt-2\">\r\n              Registration successful!\r\n            </div>\r\n          )}\r\n        </form>\r\n        <div className=\"text-center text-sm text-gray-600\">\r\n          Already have an account?{\" \"}\r\n          <Link\r\n            href=\"/login\"\r\n            className=\"text-red-700 font-semibold hover:underline\"\r\n          >\r\n            Login\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AALA;;;;;;;AAiBA,0DAA0D;AAC1D,MAAM,iBAAiB,iLAAA,CAAA,IAAC,CACrB,MAAM,CAAC;IACN,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,sCACP,GAAG,CAAC,IAAI,wCACR,KAAK,CAAC,iBAAiB;IAC1B,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,sCACN,GAAG,CAAC,KAAK;IACZ,UAAU,iLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,6CACf,KAAK,CACJ,gBACA;IAEJ,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM;IACzB,OAAO,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;QAC/C,SAAS;IACX;AACF,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIF,4BAA4B;AAC5B,MAAM,sBAAsB,CAAC;IAC3B,IAAI,QAAQ;IACZ,MAAM,SAAS;QACb,QAAQ,UAAS,MAAM,IAAI;QAC3B,WAAW,QAAQ,IAAI,CAAC;QACxB,WAAW,QAAQ,IAAI,CAAC;QACxB,QAAQ,QAAQ,IAAI,CAAC;QACrB,SAAS,eAAe,IAAI,CAAC;IAC/B;IAEA,OAAO,MAAM,CAAC,QAAQ,OAAO,CAAC,CAAC,QAAU,SAAS;IAElD,OAAO;QACL;QACA;QACA,UAAU,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;IACxD;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEjC,CAAC;IACH,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,oBAAoB,CACxB,OACA;QAEA,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,qDAAqD;QACrD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,eAAe,aAAa,CAAkB;QAC5C,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QACX,UAAU,CAAC;QAEX,8BAA8B;QAC9B,MAAM,aAAa,eAAe,SAAS,CAAC;QAC5C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,WAAW;YACX,MAAM,cAA+D,CAAC;YACtE,WAAW,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC/B,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;oBACf,WAAW,CAAC,IAAI,IAAI,CAAC,EAAE,CAA2B,GAAG,IAAI,OAAO;gBAClE;YACF;YACA,UAAU;YACV;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kHAAA,CAAA,WAAQ,AAAD,EAC1B,SAAS,KAAK,EACd,SAAS,QAAQ,EACjB,SAAS,IAAI;YAEf,WAAW;YAEX,IAAI,OAAO,YAAY,EAAE;gBACvB,WAAW;gBACX,aAAa,OAAO,CAAC,SAAS,OAAO,YAAY;gBACjD,kDAAkD;gBAClD,WAAW;oBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB,GAAG;YACL,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,WAAW;YACX,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;8BAGd,8OAAC;oBAAG,WAAU;8BAAmD;;;;;;8BAGjE,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4BACvC,aAAY;4BACZ,WAAU;4BACV,QAAQ;4BACR,cAAa;;;;;;sCAEf,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAY;4BACZ,WAAU;4BACV,QAAQ;4BACR,cAAa;;;;;;sCAEf,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,QAAQ;4BACR,cAAa;;;;;;sCAEf,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,OAAO;oCAC1C,WAAU;;;;;;gCACV;gCACW;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;sCAIjD,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,UAAU,mBAAmB;;;;;;wBAE/B,uBACC,8OAAC;4BAAI,WAAU;sCAAyC;;;;;;wBAEzD,yBACC,8OAAC;4BAAI,WAAU;sCAA0C;;;;;;;;;;;;8BAK7D,8OAAC;oBAAI,WAAU;;wBAAoC;wBACxB;sCACzB,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}