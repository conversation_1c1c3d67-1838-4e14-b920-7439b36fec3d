import { CreateProductDto, UpdateProductDto } from './product.dto';
export declare class ProductService {
    private prisma;
    findAll(): Promise<({
        category: {
            id: number;
            name: string;
        };
    } & {
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    })[]>;
    findOne(slug: string): Promise<({
        category: {
            id: number;
            name: string;
        };
    } & {
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }) | null>;
    create(data: CreateProductDto): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    update(slug: string, data: UpdateProductDto): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    delete(slug: string): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
}
