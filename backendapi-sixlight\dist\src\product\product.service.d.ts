import { CreateProductDto, UpdateProductDto } from './product.dto';
export declare class ProductService {
    private prisma;
    findAll(): Promise<{
        category: string;
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        modelUrl: string | null;
        price: number;
    }[]>;
    findOne(slug: string): Promise<{
        category: string;
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        modelUrl: string | null;
        price: number;
    } | null>;
    create(data: CreateProductDto): Promise<{
        category: string;
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        modelUrl: string | null;
        price: number;
    }>;
    update(slug: string, data: UpdateProductDto): Promise<{
        category: string;
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        modelUrl: string | null;
        price: number;
    }>;
    delete(slug: string): Promise<{
        category: string;
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        modelUrl: string | null;
        price: number;
    }>;
}
