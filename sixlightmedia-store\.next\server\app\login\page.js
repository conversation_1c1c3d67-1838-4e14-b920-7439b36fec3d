(()=>{var e={};e.id=520,e.ids=[520],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4934:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx","default")},5791:(e,r,s)=>{Promise.resolve().then(s.bind(s,4934))},6567:(e,r,s)=>{Promise.resolve().then(s.bind(s,9488))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9488:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(687),a=s(3210),i=s(3282),o=s(5814),l=s.n(o),n=s(474),d=s(9275),c=s(3613),p=s(2597),u=s(3861);let m=d.z.object({email:d.z.string().email("Please enter a valid email address"),password:d.z.string().min(1,"Password is required")});function x(){let[e,r]=(0,a.useState)({email:"",password:""}),[s,o]=(0,a.useState)({}),[d,x]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[f,b]=(0,a.useState)(!1),v=(e,t)=>{r(r=>({...r,[e]:t})),s[e]&&o(r=>({...r,[e]:void 0}))};async function w(r){r.preventDefault(),g(!0),x(""),o({});let s=m.safeParse(e);if(!s.success){g(!1);let e={};s.error.errors.forEach(r=>{r.path[0]&&(e[r.path[0]]=r.message)}),o(e);return}try{let r=await (0,i.iD)(e.email,e.password);if(g(!1),r.access_token){localStorage.setItem("token",r.access_token),r.user&&localStorage.setItem("user",JSON.stringify(r.user));let e=new URLSearchParams(window.location.search).get("redirect"),s=r.user?.role==="ADMIN";if(e&&e.startsWith("/admin/")&&!s)return void x("You don't have permission to access the admin area");window.location.href=e||"/user/dashboard"}else x(r.error||"Login failed")}catch{g(!1),x("Network error. Please try again.")}}return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100",children:[(0,t.jsx)("div",{className:"flex flex-col items-center",children:(0,t.jsx)(n.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,t.jsx)("h2",{className:"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Sign In"}),(0,t.jsxs)("form",{onSubmit:w,className:"flex flex-col gap-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("input",{id:"email",name:"email",value:e.email,onChange:e=>v("email",e.target.value),placeholder:"Email",type:"email",autoComplete:"email",className:`w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition ${s.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,"aria-invalid":!!s.email,"aria-describedby":s.email?"email-error":void 0}),s.email&&(0,t.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(c.A,{size:16}),s.email]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",name:"password",value:e.password,onChange:e=>v("password",e.target.value),type:f?"text":"password",placeholder:"Password",autoComplete:"current-password",className:`w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition ${s.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,"aria-invalid":!!s.password,"aria-describedby":s.password?"password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":f?"Hide password":"Show password",children:f?(0,t.jsx)(p.A,{size:20}):(0,t.jsx)(u.A,{size:20})})]}),s.password&&(0,t.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(c.A,{size:16}),s.password]})]}),(0,t.jsx)("button",{type:"submit",className:`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${h?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"}`,disabled:h,children:h?(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,t.jsx)("span",{children:"Signing in..."})]}):"Sign In"}),d&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,t.jsx)(c.A,{size:16}),d]})]}),(0,t.jsxs)("div",{className:"text-center text-sm text-gray-600 space-y-2",children:[(0,t.jsx)("div",{children:(0,t.jsx)(l(),{href:"/forgot-password",className:"text-blue-600 hover:text-blue-800 font-medium",children:"Forgot your password?"})}),(0,t.jsxs)("div",{children:["Don't have an account?"," ",(0,t.jsx)(l(),{href:"/register",className:"text-red-700 font-semibold hover:underline",children:"Register"})]})]})]})})}},9924:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(5239),a=s(8088),i=s(8170),o=s.n(i),l=s(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(r,n);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4934)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[728,474,814,743,24],()=>s(9924));module.exports=t})();