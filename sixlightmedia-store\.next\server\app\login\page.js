(()=>{var e={};e.id=520,e.ids=[520],e.modules={191:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9167,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3282:(e,t,r)=>{"use strict";r.d(t,{i:()=>i,k:()=>a});var s=r(5475);async function i(e,t){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})})).json()}async function a(e,t,r){return(await fetch((0,s.e9)(s.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t,name:r})})).json()}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(7413),i=r(2376),a=r.n(i),o=r(8726),n=r.n(o),l=r(6162);r(1135);let d={title:{default:"Six Light Media Store - Premium Custom Products & Personalized Gifts",template:"%s | Six Light Media Store"},description:"Discover premium custom products and personalized gifts at Six Light Media Store. From engraved bottles to custom t-shirts, we create unique items tailored just for you. Fast delivery, premium quality, 100% satisfaction guaranteed.",keywords:["custom products","personalized gifts","engraved bottles","custom t-shirts","Six Light Media","premium quality","custom printing","personalized items","unique gifts","custom design","Zambia","e-commerce"],authors:[{name:"Six Light Media"}],creator:"Six Light Media",publisher:"Six Light Media",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://yourdomain.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_US",url:"/",title:"Six Light Media Store - Premium Custom Products & Personalized Gifts",description:"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.",siteName:"Six Light Media Store",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Six Light Media Store - Premium Custom Products"}]},twitter:{card:"summary_large_image",title:"Six Light Media Store - Premium Custom Products & Personalized Gifts",description:"Discover premium custom products and personalized gifts. From engraved bottles to custom t-shirts, we create unique items tailored just for you.",images:["/og-image.jpg"],creator:"@sixlightmedia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"},category:"e-commerce"};function c({children:e}){return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5"}),(0,s.jsx)("meta",{name:"theme-color",content:"#1a237e"}),(0,s.jsx)("meta",{name:"msapplication-TileColor",content:"#1a237e"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Six Light Media Store"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"}),(0,s.jsx)("link",{rel:"icon",href:"/icon.svg",type:"image/svg+xml"}),(0,s.jsx)("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),(0,s.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"//fonts.gstatic.com"}),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"Six Light Media Store",description:"Premium custom products and personalized gifts",url:"https://yourdomain.com",logo:"https://yourdomain.com/6 Light Logo.png",contactPoint:{"@type":"ContactPoint",contactType:"customer service",availableLanguage:"English"},sameAs:["https://facebook.com/sixlightmedia","https://twitter.com/sixlightmedia","https://instagram.com/sixlightmedia"]})}})]}),(0,s.jsxs)("body",{className:`${a().variable} ${n().variable} antialiased`,children:[e,process.env.NEXT_PUBLIC_GA_ID&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.default,{src:`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`,strategy:"afterInteractive"}),(0,s.jsx)(l.default,{id:"google-analytics",strategy:"afterInteractive",children:`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
              `})]})]})]})}},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx","default")},5475:(e,t,r)=>{"use strict";r.d(t,{e9:()=>i,i3:()=>s});let s={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};process.env.IMAGEKIT_PRIVATE_KEY,process.env.NEXT_PUBLIC_GA_ID,process.env.NEXT_PUBLIC_GA_ID;let i=e=>{let t=s.BASE_URL.replace(/\/$/,""),r=e.startsWith("/")?e:`/${e}`;return`${t}${r}`}},5791:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))},6567:(e,t,r)=>{Promise.resolve().then(r.bind(r,9488))},7219:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7467:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(687),i=r(3210),a=r(3282),o=r(5814),n=r.n(o),l=r(474),d=r(9275),c=r(3613),m=r(2597),p=r(3861);let u=d.z.object({email:d.z.string().email("Please enter a valid email address"),password:d.z.string().min(1,"Password is required")});function g(){let[e,t]=(0,i.useState)({email:"",password:""}),[r,o]=(0,i.useState)({}),[d,g]=(0,i.useState)(""),[h,x]=(0,i.useState)(!1),[f,b]=(0,i.useState)(!1),v=(e,s)=>{t(t=>({...t,[e]:s})),r[e]&&o(t=>({...t,[e]:void 0}))};async function y(t){t.preventDefault(),x(!0),g(""),o({});let r=u.safeParse(e);if(!r.success){x(!1);let e={};r.error.errors.forEach(t=>{t.path[0]&&(e[t.path[0]]=t.message)}),o(e);return}try{let t=await (0,a.i)(e.email,e.password);x(!1),t.access_token?(localStorage.setItem("token",t.access_token),window.location.href="/user/dashboard"):g(t.error||"Login failed")}catch(e){x(!1),g("Network error. Please try again.")}}return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100",children:[(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-2"})}),(0,s.jsx)("h2",{className:"text-3xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Sign In"}),(0,s.jsxs)("form",{onSubmit:y,className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("input",{value:e.email,onChange:e=>v("email",e.target.value),placeholder:"Email",type:"email",className:`w-full border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition ${r.email?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,autoComplete:"email","aria-invalid":!!r.email,"aria-describedby":r.email?"email-error":void 0}),r.email&&(0,s.jsxs)("div",{id:"email-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,s.jsx)(c.A,{size:16}),r.email]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{value:e.password,onChange:e=>v("password",e.target.value),type:f?"text":"password",placeholder:"Password",className:`w-full border rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 transition ${r.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"}`,autoComplete:"current-password","aria-invalid":!!r.password,"aria-describedby":r.password?"password-error":void 0}),(0,s.jsx)("button",{type:"button",onClick:()=>b(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700","aria-label":f?"Hide password":"Show password",children:f?(0,s.jsx)(m.A,{size:20}):(0,s.jsx)(p.A,{size:20})})]}),r.password&&(0,s.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,s.jsx)(c.A,{size:16}),r.password]})]}),(0,s.jsx)("button",{type:"submit",className:`w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ${h?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"}`,disabled:h,children:h?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Signing in..."})]}):"Sign In"}),d&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,s.jsx)(c.A,{size:16}),d]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/register",className:"text-red-700 font-semibold hover:underline",children:"Register"})]})]})})}},9924:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),o=r.n(a),n=r(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4934)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\login\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9927:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,7429,23))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[728,474,814,479],()=>r(9924));module.exports=s})();