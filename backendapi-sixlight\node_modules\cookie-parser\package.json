{"name": "cookie-parser", "description": "Parse HTTP request cookies", "version": "1.4.7", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "expressjs/cookie-parser", "keywords": ["cookie", "middleware"], "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.6"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0", "supertest": "6.1.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}