{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/lib/auth.ts"], "sourcesContent": ["import { cookies } from \"next/headers\";\nimport { redirect } from \"next/navigation\";\n\nexport interface User {\n  id: number;\n  email: string;\n  role: \"USER\" | \"ADMIN\";\n  name?: string;\n  profileImage?: string;\n}\n\nexport interface AuthResult {\n  isAuthenticated: boolean;\n  user?: User;\n}\n\n/**\n * Verify authentication status by checking JWT token with backend\n */\nexport async function verifyAuth(): Promise<AuthResult> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get(\"jwt\")?.value;\n\n    if (!token) {\n      console.log(\"No JWT token found in cookies\");\n      return { isAuthenticated: false };\n    }\n\n    // Get API URL with fallback\n    const apiUrl =\n      process.env.NEXT_PUBLIC_API_URL ||\n      (process.env.NODE_ENV === \"production\"\n        ? \"https://backendapi-sixlight.onrender.com\"\n        : \"http://localhost:3001\");\n\n    console.log(`Verifying auth with: ${apiUrl}/auth/verify`);\n\n    // Verify token with backend\n    const response = await fetch(`${apiUrl}/auth/verify`, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      },\n      cache: \"no-store\", // Always verify fresh\n    });\n\n    console.log(`Auth verification response: ${response.status}`);\n\n    if (!response.ok) {\n      console.log(\n        `Auth verification failed: ${response.status} ${response.statusText}`\n      );\n      return { isAuthenticated: false };\n    }\n\n    const user = await response.json();\n    console.log(`Auth verification successful for user: ${user.email}`);\n\n    return {\n      isAuthenticated: true,\n      user: {\n        id: user.id,\n        email: user.email,\n        role: user.role,\n        name: user.name,\n        profileImage: user.profileImage,\n      },\n    };\n  } catch (error) {\n    console.error(\"Auth verification failed:\", error);\n    return { isAuthenticated: false };\n  }\n}\n\n/**\n * Require authentication - redirect to login if not authenticated\n */\nexport async function requireAuth(redirectTo = \"/login\"): Promise<AuthResult> {\n  const auth = await verifyAuth();\n  if (!auth.isAuthenticated) {\n    redirect(redirectTo);\n  }\n  return auth;\n}\n\n/**\n * Require admin role - redirect if not admin\n */\nexport async function requireAdmin(redirectTo = \"/\"): Promise<AuthResult> {\n  const auth = await requireAuth(\"/login\");\n  if (auth.user?.role !== \"ADMIN\") {\n    redirect(redirectTo);\n  }\n  return auth;\n}\n\n/**\n * Get current page URL for redirect after login\n */\nexport function getCurrentPageUrl(request?: Request): string {\n  if (typeof window !== \"undefined\") {\n    return window.location.pathname + window.location.search;\n  }\n\n  if (request) {\n    const url = new URL(request.url);\n    return url.pathname + url.search;\n  }\n\n  return \"/\";\n}\n\n/**\n * Create login redirect URL with return path\n */\nexport function createLoginRedirect(returnPath?: string): string {\n  const encodedPath = returnPath ? encodeURIComponent(returnPath) : \"\";\n  return `/login${encodedPath ? `?redirect=${encodedPath}` : \"\"}`;\n}\n\n/**\n * Client-side auth check (for components)\n */\nexport function getClientAuth(): {\n  isAuthenticated: boolean;\n  token: string | null;\n} {\n  if (typeof window === \"undefined\") {\n    return { isAuthenticated: false, token: null };\n  }\n\n  const token = localStorage.getItem(\"token\");\n  return {\n    isAuthenticated: !!token,\n    token,\n  };\n}\n\n/**\n * Client-side logout\n */\nexport async function logout(): Promise<void> {\n  try {\n    // Call backend logout endpoint\n    await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/logout`, {\n      method: \"POST\",\n      credentials: \"include\",\n    });\n  } catch (error) {\n    console.error(\"Logout request failed:\", error);\n  } finally {\n    // Clear client-side storage regardless of backend response\n    if (typeof window !== \"undefined\") {\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"user\");\n      window.location.href = \"/login\";\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AAAA;;;AAkBO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,QAAQ;QAEtC,IAAI,CAAC,OAAO;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,iBAAiB;YAAM;QAClC;QAEA,4BAA4B;QAC5B,MAAM,SACJ,6DACA,CAAC,6EAEG,uBAAuB;QAE7B,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,YAAY,CAAC;QAExD,4BAA4B;QAC5B,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,YAAY,CAAC,EAAE;YACpD,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;YACA,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS,MAAM,EAAE;QAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAEvE,OAAO;gBAAE,iBAAiB;YAAM;QAClC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,KAAK,KAAK,EAAE;QAElE,OAAO;YACL,iBAAiB;YACjB,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY;YACjC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAKO,eAAe,YAAY,aAAa,QAAQ;IACrD,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IACA,OAAO;AACT;AAKO,eAAe,aAAa,aAAa,GAAG;IACjD,MAAM,OAAO,MAAM,YAAY;IAC/B,IAAI,KAAK,IAAI,EAAE,SAAS,SAAS;QAC/B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IACA,OAAO;AACT;AAKO,SAAS,kBAAkB,OAAiB;IACjD,uCAAmC;;IAEnC;IAEA,IAAI,SAAS;QACX,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,OAAO,IAAI,QAAQ,GAAG,IAAI,MAAM;IAClC;IAEA,OAAO;AACT;AAKO,SAAS,oBAAoB,UAAmB;IACrD,MAAM,cAAc,aAAa,mBAAmB,cAAc;IAClE,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,UAAU,EAAE,aAAa,GAAG,IAAI;AACjE;AAKO,SAAS;IAId,wCAAmC;QACjC,OAAO;YAAE,iBAAiB;YAAO,OAAO;QAAK;IAC/C;;IAEA,MAAM;AAKR;AAKO,eAAe;IACpB,IAAI;QACF,+BAA+B;QAC/B,MAAM,MAAM,6DAAmC,YAAY,CAAC,EAAE;YAC5D,QAAQ;YACR,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;IAC1C,SAAU;QACR,2DAA2D;QAC3D,uCAAmC;;QAInC;IACF;AACF", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/user/dashboard/UserDashboardClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/user/dashboard/UserDashboardClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/user/dashboard/UserDashboardClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/user/dashboard/UserDashboardClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/user/dashboard/UserDashboardClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/user/dashboard/UserDashboardClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dev/fullstack-sixlight/sixlightmedia-store/src/app/user/dashboard/page.tsx"], "sourcesContent": ["import { requireAuth } from \"@/lib/auth\";\nimport UserDashboardClient from \"./UserDashboardClient\";\n\n// Force dynamic rendering\nexport const dynamic = \"force-dynamic\";\n\ntype User = {\n  name?: string;\n  email: string;\n  profileImage?: string;\n};\n\ntype Order = {\n  id: number;\n  customerName: string;\n  customerPhone: string;\n  customerEmail: string;\n  customerAddress: string;\n  customColor?: string;\n  customText?: string;\n  isCustomized: boolean;\n  customizationData?: string;\n  customizationPreview?: string;\n  quantity: number;\n  unitPrice: number;\n  totalPrice: number;\n  status: string;\n  createdAt: string;\n  product: {\n    id: number;\n    name: string;\n    price: number;\n    image?: string;\n  };\n  customization?: {\n    color?: string;\n    text?: string;\n  };\n};\n\nexport default async function UserDashboard() {\n  // Server-side authentication check - redirects if not authenticated\n  const auth = await requireAuth();\n\n  return <UserDashboardClient user={auth.user} />;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAGO,MAAM,UAAU;AAoCR,eAAe;IAC5B,oEAAoE;IACpE,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAE7B,qBAAO,8OAAC,uJAAA,CAAA,UAAmB;QAAC,MAAM,KAAK,IAAI;;;;;;AAC7C", "debugId": null}}]}