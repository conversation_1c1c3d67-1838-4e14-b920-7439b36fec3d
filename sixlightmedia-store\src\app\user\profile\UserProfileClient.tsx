"use client";
import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import Header from "@/components/Header";
import Cart from "@/components/Cart";
import { getApiUrl, API_CONFIG } from "@/lib/config";
import { User } from "@/lib/auth";

type UserData = {
  id: number;
  name?: string;
  email: string;
  role: string;
  profileImage?: string;
};

interface UserProfileClientProps {
  user?: User;
}

export default function UserProfileClient({ user }: UserProfileClientProps) {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [editName, setEditName] = useState("");
  const [profileImage, setProfileImage] = useState<string>("");
  const [imageUploading, setImageUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [profileSaving, setProfileSaving] = useState(false);
  const [passwordChanging, setPasswordChanging] = useState(false);
  const [passwords, setPasswords] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [cartCount, setCartCount] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const updateCartCount = () => {
    const cart = JSON.parse(localStorage.getItem("cart") || "[]");
    setCartCount(cart.length);
  };

  const handleCartClick = () => {
    setIsCartOpen(true);
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
    updateCartCount();
  };

  useEffect(() => {
    updateCartCount();
  }, []);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      setError("Not authenticated");
      setLoading(false);
      return;
    }

    fetch(getApiUrl(API_CONFIG.ENDPOINTS.USER.DASHBOARD), {
      headers: { Authorization: `Bearer ${token}` },
      credentials: "include", // Include cookies for authentication
    })
      .then(async (response) => {
        if (response.ok) {
          const dashboardData = await response.json();
          const userData = dashboardData.user;
          setUserData(userData);
          setEditName(userData.name || "");
          setProfileImage(userData.profileImage || "");
        } else {
          const errorText = await response.text();
          console.error("Profile API error:", response.status, errorText);
          setError(`Failed to load profile: ${response.status}`);
        }
      })
      .catch((error) => {
        console.error("Profile fetch error:", error);
        setError("Network error. Please check your connection.");
      })
      .finally(() => setLoading(false));
  }, []);

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      setError("Image size must be less than 5MB");
      return;
    }

    setImageUploading(true);
    setUploadProgress(0);
    setError("");

    const formData = new FormData();
    formData.append("profileImage", file);

    try {
      const token = localStorage.getItem("token");
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener("progress", (e) => {
        if (e.lengthComputable) {
          const progress = (e.loaded / e.total) * 100;
          setUploadProgress(progress);
        }
      });

      xhr.onload = () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          setProfileImage(response.profileImage);
          setUserData((prev) =>
            prev ? { ...prev, profileImage: response.profileImage } : null
          );
          setSuccess("Profile image updated successfully!");
        } else {
          setError("Failed to upload image");
        }
        setImageUploading(false);
        setUploadProgress(0);
      };

      xhr.onerror = () => {
        setError("Failed to upload image");
        setImageUploading(false);
        setUploadProgress(0);
      };

      // For now, disable the upload since the endpoint doesn't exist
      // Instead, we'll handle image upload differently
      setError(
        "Image upload feature is temporarily disabled. Please update your name instead."
      );
      setImageUploading(false);
      setUploadProgress(0);
      return;
    } catch {
      setError("Failed to upload image");
      setImageUploading(false);
      setUploadProgress(0);
    }
  };

  const handleProfileUpdate = async () => {
    setProfileSaving(true);
    setError("");
    setSuccess("");

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        getApiUrl(API_CONFIG.ENDPOINTS.USER.UPDATE_PROFILE),
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include", // Include cookies for authentication
          body: JSON.stringify({ name: editName }),
        }
      );

      if (response.ok) {
        const updatedUser = await response.json();
        setUserData(updatedUser);
        setSuccess("Profile updated successfully!");
      } else {
        setError("Failed to update profile");
      }
    } catch {
      setError("Failed to update profile");
    } finally {
      setProfileSaving(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwords.newPassword !== passwords.confirmPassword) {
      setError("New passwords don't match");
      return;
    }

    if (passwords.newPassword.length < 6) {
      setError("New password must be at least 6 characters");
      return;
    }

    setPasswordChanging(true);
    setError("");
    setSuccess("");

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        getApiUrl(API_CONFIG.ENDPOINTS.USER.CHANGE_PASSWORD),
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          credentials: "include", // Include cookies for authentication
          body: JSON.stringify({
            oldPassword: passwords.oldPassword,
            newPassword: passwords.newPassword,
          }),
        }
      );

      if (response.ok) {
        setPasswords({ oldPassword: "", newPassword: "", confirmPassword: "" });
        setSuccess("Password changed successfully!");
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Failed to change password");
      }
    } catch {
      setError("Failed to change password");
    } finally {
      setPasswordChanging(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"></div>
      </div>
    );
  }

  return (
    <>
      <Header cartCount={cartCount} onCartClick={handleCartClick} />
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-800 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10 max-w-7xl mx-auto px-4 py-16">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
                <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                  Profile
                </span>
                <span className="text-white"> Management</span>
              </h1>
              <p className="text-xl text-gray-200 mb-4 max-w-2xl mx-auto">
                Manage your account settings and personal information
              </p>
              <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 border border-white/20">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-white text-sm">
                  🔒 Secure profile access for {user?.name || user?.email}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full border border-green-200">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">
                ✅ Server-side authentication active
              </span>
            </div>
          </div>

          {/* Navigation */}
          <div className="mb-8">
            <Link
              href="/user/dashboard"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              ← Back to Dashboard
            </Link>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}
          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Profile Information */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Profile Information
              </h2>

              {/* Profile Image */}
              <div className="text-center mb-6">
                <div className="relative inline-block">
                  <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-200 mx-auto mb-4">
                    {profileImage ? (
                      <Image
                        src={profileImage}
                        alt="Profile"
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-4xl">
                        👤
                      </div>
                    )}
                  </div>
                  {imageUploading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
                      <div className="text-white text-sm">
                        {Math.round(uploadProgress)}%
                      </div>
                    </div>
                  )}
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={imageUploading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {imageUploading ? "Uploading..." : "Change Photo"}
                </button>
              </div>

              {/* Name */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Name
                  </label>
                  <input
                    type="text"
                    value={editName}
                    onChange={(e) => setEditName(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={userData?.email || ""}
                    disabled
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Email cannot be changed
                  </p>
                </div>

                <button
                  onClick={handleProfileUpdate}
                  disabled={profileSaving}
                  className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
                >
                  {profileSaving ? "Saving..." : "Save Profile"}
                </button>
              </div>
            </div>

            {/* Password Change */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Change Password
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Current Password
                  </label>
                  <input
                    type="password"
                    value={passwords.oldPassword}
                    onChange={(e) =>
                      setPasswords((prev) => ({
                        ...prev,
                        oldPassword: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                  </label>
                  <input
                    type="password"
                    value={passwords.newPassword}
                    onChange={(e) =>
                      setPasswords((prev) => ({
                        ...prev,
                        newPassword: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    value={passwords.confirmPassword}
                    onChange={(e) =>
                      setPasswords((prev) => ({
                        ...prev,
                        confirmPassword: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <button
                  onClick={handlePasswordChange}
                  disabled={
                    passwordChanging ||
                    !passwords.oldPassword ||
                    !passwords.newPassword ||
                    !passwords.confirmPassword
                  }
                  className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
                >
                  {passwordChanging ? "Changing..." : "Change Password"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Cart isOpen={isCartOpen} onClose={handleCartClose} />
    </>
  );
}
