"use client";

import { Canvas } from "@react-three/fiber";
import { OrbitControls, Text } from "@react-three/drei";
import { useState } from "react";
import { HexColorPicker } from "react-colorful";

type Tshirt3DProps = {
  color?: string;
  text?: string;
  onColorChange?: (color: string) => void;
};

export default function Tshirt3D({
  color = "white",
  text = "",
  onColorChange,
}: Tshirt3DProps) {
  const [showPicker, setShowPicker] = useState(false);
  return (
    <div style={{ width: "100%" }}>
      <Canvas
        style={{ height: 320, width: "100%", background: "#f9f9f9" }}
        camera={{ position: [0, 0, 5], fov: 50 }}
      >
        <ambientLight intensity={0.7} />
        <directionalLight position={[5, 5, 5]} intensity={0.7} />
        {/* T-shirt body (box for demo) */}
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[2, 2.2, 0.5]} />
          <meshStandardMaterial color={color} />
        </mesh>
        {/* T-shirt sleeves (cylinders for demo) */}
        <mesh position={[-1.2, 0.5, 0]} rotation={[0, 0, Math.PI / 2]}>
          <cylinderGeometry args={[0.25, 0.25, 1, 32]} />
          <meshStandardMaterial color={color} />
        </mesh>
        <mesh position={[1.2, 0.5, 0]} rotation={[0, 0, Math.PI / 2]}>
          <cylinderGeometry args={[0.25, 0.25, 1, 32]} />
          <meshStandardMaterial color={color} />
        </mesh>
        {/* Custom text */}
        {text && text.trim() !== "" && (
          <Text
            position={[0, 0.3, 0.28]}
            fontSize={0.3}
            color="#222"
            anchorX="center"
            anchorY="middle"
            maxWidth={1.5}
          >
            {text}
          </Text>
        )}
        <OrbitControls enablePan enableZoom enableRotate />
      </Canvas>
      <div className="flex flex-col items-center mt-2">
        <button
          className="bg-[#ffd600] text-[#171717] font-semibold px-4 py-1 rounded-full shadow hover:bg-[#ffe066] transition mb-2"
          onClick={() => setShowPicker((v) => !v)}
        >
          {showPicker ? "Close Color Picker" : "Pick Custom Color"}
        </button>
        {showPicker && (
          <HexColorPicker
            color={color}
            onChange={onColorChange}
            style={{ width: 180, height: 120 }}
          />
        )}
      </div>
    </div>
  );
}
