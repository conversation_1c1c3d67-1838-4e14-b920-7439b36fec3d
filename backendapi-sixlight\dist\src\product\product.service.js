"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let ProductService = class ProductService {
    prisma = new client_1.PrismaClient();
    async findAll() {
        return this.prisma.product.findMany();
    }
    async findOne(slug) {
        return this.prisma.product.findUnique({
            where: { slug },
        });
    }
    async create(data) {
        const { categoryId, ...rest } = data;
        let categoryName = 'Uncategorized';
        if (categoryId) {
            try {
                const categoryRecord = await this.prisma.category.findUnique({
                    where: { id: categoryId },
                });
                if (categoryRecord) {
                    categoryName = categoryRecord.name;
                }
            }
            catch (error) {
                console.error('Error fetching category:', error);
            }
        }
        return this.prisma.product.create({
            data: {
                ...rest,
                category: categoryName,
            },
        });
    }
    async update(slug, data) {
        const { categoryId, ...rest } = data;
        let updateData = { ...rest };
        if (categoryId !== undefined) {
            try {
                const categoryRecord = await this.prisma.category.findUnique({
                    where: { id: categoryId },
                });
                if (categoryRecord) {
                    updateData.category = categoryRecord.name;
                }
            }
            catch (error) {
                console.error('Error fetching category:', error);
            }
        }
        return this.prisma.product.update({
            where: { slug },
            data: updateData,
        });
    }
    async delete(slug) {
        return this.prisma.product.delete({ where: { slug } });
    }
};
exports.ProductService = ProductService;
exports.ProductService = ProductService = __decorate([
    (0, common_1.Injectable)()
], ProductService);
//# sourceMappingURL=product.service.js.map