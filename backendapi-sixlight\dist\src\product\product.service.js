"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let ProductService = class ProductService {
    prisma = new client_1.PrismaClient();
    async findAll() {
        return this.prisma.product.findMany({ include: { category: true } });
    }
    async findOne(slug) {
        return this.prisma.product.findUnique({
            where: { slug },
            include: { category: true },
        });
    }
    async create(data) {
        const { category, ...rest } = data;
        return this.prisma.product.create({ data: rest });
    }
    async update(slug, data) {
        const { category, ...rest } = data;
        return this.prisma.product.update({ where: { slug }, data: rest });
    }
    async delete(slug) {
        return this.prisma.product.delete({ where: { slug } });
    }
};
exports.ProductService = ProductService;
exports.ProductService = ProductService = __decorate([
    (0, common_1.Injectable)()
], ProductService);
//# sourceMappingURL=product.service.js.map