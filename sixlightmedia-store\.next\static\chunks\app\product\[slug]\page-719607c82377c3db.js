(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[490],{255:(e,t,r)=>{"use strict";function s(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return s}}),r(5155),r(7650),r(5744),r(589)},2146:(e,t,r)=>{"use strict";function s(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}}),r(5262)},4054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return n},createAsyncLocalStorage:function(){return l},createSnapshot:function(){return o}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class s{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function l(){return a?new a:new s}function n(e){return a?a.bind(e):s.bind(e)}function o(){return a?a.snapshot():function(e,...t){return e(...t)}}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},5744:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return s.workAsyncStorageInstance}});let s=r(7828)},6645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let s=r(8229)._(r(7357));function a(e,t){var r;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,s.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6731:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(5155),a=r(6766),l=r(2115),n=r(5695),o=r(6874),i=r.n(o),c=r(6645),d=r.n(c),u=r(802),x=r(4615),m=r(3389);let h=d()(()=>Promise.all([r.e(831),r.e(367),r.e(413),r.e(683),r.e(825),r.e(321),r.e(156)]).then(r.bind(r,9156)),{loadableGenerated:{webpack:()=>[9156]},ssr:!1}),g=d()(()=>Promise.all([r.e(831),r.e(367),r.e(413),r.e(683),r.e(825),r.e(632)]).then(r.bind(r,6632)),{loadableGenerated:{webpack:()=>[6632]},ssr:!1});function f(e){var t,r,o;let{params:c}=e,d=l.use(c).slug,f=(0,n.useRouter)(),[p,b]=(0,l.useState)(null),[j,v]=(0,l.useState)(!0),[y,N]=(0,l.useState)(!1),[w,C]=(0,l.useState)("#ffffff"),[k,D]=(0,l.useState)(""),[P,S]=(0,l.useState)(!1),[L,_]=(0,l.useState)(!1),[O,A]=(0,l.useState)(!1),[M,z]=(0,l.useState)(0),[E,B]=(0,l.useState)(!1),F=(0,l.useRef)(null),W=(0,l.useRef)(null);(0,l.useEffect)(()=>{v(!0),N(!1),fetch("".concat("http://localhost:3001","/product/").concat(d)).then(async e=>{if(!e.ok)throw Error("Product not found");let t=await e.json();b(t),A(t.customizable||!1),v(!1)}).catch(()=>{N(!0),v(!1)})},[d]),(0,l.useEffect)(()=>{F.current&&u.Ay.fromTo(F.current,{opacity:0,y:40},{opacity:1,y:0,duration:.7,ease:"power3.out"})},[p]),(0,l.useEffect)(()=>{W.current&&u.Ay.fromTo(W.current,{scale:.92,opacity:0},{scale:1,opacity:1,duration:.5,ease:"power2.out"})},[O,null==p?void 0:p.slug]);let R=()=>{z(JSON.parse(localStorage.getItem("cart")||"[]").length)};(0,l.useEffect)(()=>{R()},[]);let T=()=>{B(!0)};function I(){return!!localStorage.getItem("token")}async function G(e){if(e.preventDefault(),p){if(!I())return void f.push("/login?redirect=/product/".concat(d));_(!0);try{await new Promise(e=>setTimeout(e,500));let e=JSON.parse(localStorage.getItem("cart")||"[]");e.push({id:Date.now(),productId:p.id,name:p.name,color:O?w:void 0,text:O?k:void 0,customized:O,price:p.price,quantity:1}),localStorage.setItem("cart",JSON.stringify(e)),R(),S(!0),setTimeout(()=>{S(!1),f.push("/")},1200)}catch(e){console.error("Error adding to cart:",e)}finally{_(!1)}}}return j?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(x.A,{cartCount:M,onCartClick:T}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading product..."})]})})]}):y||!p?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(x.A,{cartCount:M,onCartClick:T}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Product Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the product you're looking for."}),(0,s.jsxs)(i(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Shop"]})]})})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(x.A,{cartCount:M,onCartClick:T}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 pt-8",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-8",children:[(0,s.jsx)(i(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Home"}),(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,s.jsx)(i(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Products"}),(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,s.jsx)("span",{className:"text-gray-900 font-medium",children:p.name})]})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 pb-16",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{ref:W,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:O&&"engraved-bottle"===p.slug?(0,s.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,s.jsx)(h,{color:w,text:k,onColorChange:C,modelUrl:p.modelUrl}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mt-4 text-center",children:"\uD83C\uDFAE 3D Preview: Drag to rotate, scroll to zoom"})]}):O&&"custom-tshirt"===p.slug?(0,s.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,s.jsx)(g,{color:w,text:k,onColorChange:C}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mt-4 text-center",children:"\uD83C\uDFAE 3D Preview: Drag to rotate, scroll to zoom"})]}):(0,s.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,s.jsx)("div",{className:"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden",children:(0,s.jsx)(a.default,{src:p.image&&p.image.startsWith("http")?p.image:p.image?"/"+p.image.replace(/^\/+/,""):"/bottle-dummy.jpg",alt:p.name,width:400,height:400,className:"w-full h-full object-contain hover:scale-105 transition-transform duration-500",unoptimized:!!(p.image&&p.image.startsWith("http")),onError:e=>{e.target.src="/bottle-dummy.jpg"}})}),!p.image&&(0,s.jsx)("div",{className:"text-sm text-amber-600 mt-4 bg-amber-50 px-4 py-2 rounded-lg",children:"⚠️ No product image found. Showing fallback."})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Product Features"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Premium Quality"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Fast Delivery"})]}),p.customizable&&(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Customizable"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Guaranteed"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{ref:F,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)("span",{className:"px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full",children:(null==(t=p.category)?void 0:t.name)||"Product"}),p.customizable&&(0,s.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-700 text-sm font-semibold rounded-full",children:"✨ Customizable"})]}),(0,s.jsx)("h1",{className:"text-4xl font-black text-gray-900 mb-4 leading-tight",children:p.name}),(0,s.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-6",children:p.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"text-3xl font-black text-green-600",children:["K",null!=(o=p.price)?o:0]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"\uD83D\uDCB0 Best price guaranteed"})]})]}),p.customizable&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border border-purple-200",children:[(0,s.jsx)("input",{type:"checkbox",id:"customize-toggle",checked:O,onChange:()=>A(e=>!e),className:"w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"}),(0,s.jsx)("label",{htmlFor:"customize-toggle",className:"text-lg font-semibold text-gray-900 cursor-pointer select-none",children:"\uD83C\uDFA8 Customize this product"})]})}),(0,s.jsxs)("form",{onSubmit:G,className:"space-y-6",children:[O&&(0,s.jsxs)("div",{className:"space-y-6 p-6 bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl border border-gray-200",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"\uD83C\uDFA8 Customize Your Product"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block text-sm font-semibold text-gray-700",children:"Choose Color"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("input",{type:"color",name:"color",value:w,onChange:e=>C(e.target.value),className:"w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer hover:border-indigo-400 transition-colors duration-200",title:"Choose color"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("div",{className:"text-sm font-mono text-gray-600 bg-white px-3 py-2 rounded-lg border",children:w.toUpperCase()})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:["Text to Engrave/Print",(0,s.jsxs)("span",{className:"text-xs text-gray-500 font-normal ml-2",children:["(",k.length,"/30 characters)"]})]}),(0,s.jsx)("input",{type:"text",name:"text",value:k,onChange:e=>D(e.target.value),maxLength:30,placeholder:"e.g. Your Name or Message",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",required:!0}),k.length>25&&(0,s.jsx)("div",{className:"text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg",children:"⚠️ Character limit almost reached"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{type:"submit",className:"w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ".concat(L||P?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white hover:shadow-xl transform hover:-translate-y-1"),disabled:L||P,children:L?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,s.jsx)("span",{children:"Adding to Cart..."})]}):P?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{children:"Added to Cart!"})]}):(0,s.jsxs)("span",{children:["\uD83D\uDED2"," ",O?"Add Custom Product to Cart":"Add to Cart"]})}),(0,s.jsx)("button",{type:"button",onClick:function(){if(!I())return void f.push("/login?redirect=/product/".concat(d));alert("Order Now functionality coming soon!")},className:"w-full py-4 px-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"⚡ Order Now - Fast Delivery"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDCCB Product Information"}),(0,s.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Category:"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900",children:(null==(r=p.category)?void 0:r.name)||"General"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Customizable:"}),(0,s.jsx)("span",{className:"font-semibold ".concat(p.customizable?"text-green-600":"text-gray-500"),children:p.customizable?"✅ Yes":"❌ No"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Delivery:"}),(0,s.jsx)("span",{className:"font-semibold text-blue-600",children:"\uD83D\uDE9A 24-48 hours"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Warranty:"}),(0,s.jsx)("span",{className:"font-semibold text-green-600",children:"✅ 30 days"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-6 border border-green-200",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDEE1️ Why Choose Us?"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,s.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Premium Quality"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl mb-2",children:"⚡"}),(0,s.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Fast Delivery"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCAF"}),(0,s.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"100% Guaranteed"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFA8"}),(0,s.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Custom Design"})]})]})]})]})]})}),(0,s.jsx)(m.A,{isOpen:E,onClose:()=>{B(!1),R()}})]})}},7266:(e,t,r)=>{Promise.resolve().then(r.bind(r,6731))},7357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let s=r(5155),a=r(2115),l=r(2146);function n(e){return{default:e&&"default"in e?e.default:e}}r(255);let o={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},i=function(e){let t={...o,...e},r=(0,a.lazy)(()=>t.loader().then(n)),i=t.loading;function c(e){let n=i?(0,s.jsx)(i,{isLoading:!0,pastDelay:!0,error:null}):null,o=!t.ssr||!!t.loading,c=o?a.Suspense:a.Fragment,d=t.ssr?(0,s.jsxs)(s.Fragment,{children:[null,(0,s.jsx)(r,{...e})]}):(0,s.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(r,{...e})});return(0,s.jsx)(c,{...o?{fallback:n}:{},children:d})}return c.displayName="LoadableComponent",c}},7828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,r(4054).createAsyncLocalStorage)()}},e=>{var t=t=>e(e.s=t);e.O(0,[592,766,874,795,211,441,684,358],()=>t(7266)),_N_E=e.O()}]);