(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[727],{582:(e,t,a)=>{Promise.resolve().then(a.bind(a,6189))},3843:(e,t,a)=>{"use strict";a.d(t,{e9:()=>s,i3:()=>l});var r=a(9509);let l={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};r.env.IMAGEKIT_PRIVATE_KEY,r.env.NEXT_PUBLIC_GA_ID,r.env.NEXT_PUBLIC_GA_ID;let s=e=>{let t=l.BASE_URL.replace(/\/$/,""),a=e.startsWith("/")?e:"/".concat(e);return"".concat(t).concat(a)}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},6189:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(5155),l=a(2115),s=a(5695),n=a(6766),o=a(5089),i=a(3843);function d(){var e;let t=(0,s.useRouter)(),a=(0,s.useParams)(),d=null==a?void 0:a.id,[c,u]=(0,l.useState)(null),[m,h]=(0,l.useState)(!0),[p,g]=(0,l.useState)(""),[x,b]=(0,l.useState)(!1),[f,E]=(0,l.useState)([]),[N,S]=(0,l.useState)(!0),[v,R]=(0,l.useState)("");async function j(e){e.preventDefault(),b(!0);let a=localStorage.getItem("token"),r={...c};delete r.id;let l=await fetch((0,i.e9)("".concat(i.i3.ENDPOINTS.ADMIN.PRODUCTS,"/").concat(d)),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(r)});b(!1),l.ok?t.push("/admin/dashboard"):g("Failed to update product")}return((0,l.useEffect)(()=>{S(!0),fetch((0,i.e9)(i.i3.ENDPOINTS.CATEGORIES)).then(e=>{if(!e.ok)throw Error("Failed to fetch categories");return e.json()}).then(e=>{E(e),R("")}).catch(()=>{E([]),R("Failed to load categories")}).finally(()=>S(!1))},[]),(0,l.useEffect)(()=>{if(!d)return;let e=localStorage.getItem("token");fetch((0,i.e9)("".concat(i.i3.ENDPOINTS.ADMIN.PRODUCTS,"/").concat(d)),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{var t,a,r,l,s,n,o,i,d,c,m;u({id:e.id,name:null!=(a=e.name)?a:"",price:null!=(r=e.price)?r:0,image:null!=(l=e.image)?l:"",slug:null!=(s=e.slug)?s:"",description:null!=(n=e.description)?n:"",customizable:null!=(o=e.customizable)&&o,categoryId:null!=(d=null!=(i=e.categoryId)?i:null==(t=e.category)?void 0:t.id)?d:void 0,modelUrl:null!=(c=e.modelUrl)?c:"",category:null!=(m=e.category)?m:void 0})}).catch(()=>g("Failed to load product")).finally(()=>h(!1))},[d]),m)?(0,r.jsx)("div",{className:"text-center mt-16",children:"Loading..."}):p?(0,r.jsx)("div",{className:"text-red-600 text-center mt-16",children:p}):c?(0,r.jsxs)("div",{className:"max-w-lg mx-auto mt-10 bg-white rounded-2xl shadow p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center text-[#1a237e]",children:"Edit Product"}),(0,r.jsxs)("form",{onSubmit:j,className:"flex flex-col gap-4",children:[(0,r.jsxs)("label",{className:"font-semibold",children:["Name",(0,r.jsx)("input",{className:"w-full border rounded px-3 py-2 mt-1",value:c.name||"",onChange:e=>u({...c,name:e.target.value}),required:!0})]}),(0,r.jsxs)("label",{className:"font-semibold",children:["Price",(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)("span",{className:"px-2 py-2 bg-gray-100 border border-r-0 rounded-l",children:"K"}),(0,r.jsx)("input",{type:"number",min:"0",step:"0.01",className:"w-full border rounded-r px-3 py-2 focus:outline-none",value:void 0!==c.price?c.price:0,onChange:e=>u({...c,price:Number(e.target.value)}),required:!0})]})]}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-green-700 mt-2",children:["Price: ",(0,r.jsxs)("span",{className:"font-bold",children:["K",null!=(e=c.price)?e:0]})]}),(0,r.jsxs)("label",{className:"font-semibold",children:["Product Image",(0,r.jsxs)(o.IKContext,{publicKey:"public_kFR0vTVL7DIDI8YW9eF6/luGjB4=".replace(/"/g,""),urlEndpoint:"https://ik.imagekit.io/fwbvmq9re".replace(/"/g,""),authenticator:async()=>(await fetch("/api/imagekit-auth")).json(),children:[(0,r.jsx)(o.IKUpload,{fileName:c.slug?"".concat(c.slug,".jpg"):"product.jpg",onSuccess:e=>u({...c,image:e.url}),onError:()=>g("Image upload failed"),className:"border rounded px-3 py-2 w-full"}),c.image&&(0,r.jsx)(n.default,{src:c.image,alt:"Preview",width:96,height:96,className:"mt-2 h-24 w-auto rounded shadow"})]})]}),(0,r.jsxs)("label",{className:"font-semibold",children:["Category",N?(0,r.jsx)("div",{className:"text-gray-500 text-sm mt-1",children:"Loading categories..."}):v?(0,r.jsx)("div",{className:"text-red-600 text-sm mt-1",children:v}):null,(0,r.jsxs)("select",{className:"w-full border rounded px-3 py-2 mt-1",value:c.categoryId||"",onChange:e=>u({...c,categoryId:Number(e.target.value)}),required:!0,disabled:N||!!v,children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"Select category"}),f.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,r.jsx)("a",{href:"/admin/categories",className:"text-blue-600 text-xs underline mt-1 inline-block hover:text-blue-800",target:"_blank",rel:"noopener noreferrer",children:"Manage categories"})]}),(0,r.jsx)("button",{type:"submit",className:"bg-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow hover:bg-blue-700 transition mt-4",disabled:x,children:x?"Saving...":"Save Changes"})]})]}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,89,441,684,358],()=>t(582)),_N_E=e.O()}]);