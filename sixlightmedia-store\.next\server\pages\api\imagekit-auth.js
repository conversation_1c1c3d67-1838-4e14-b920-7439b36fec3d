"use strict";(()=>{var e={};e.id=57,e.ids=[57],e.modules={3480:(e,t,r)=>{e.exports=r(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5744:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>d,routeModule:()=>l});var i={};r.r(i),r.d(i,{default:()=>P});var n=r(3480),a=r(8667),u=r(6435);let o=require("imagekit"),s=new(r.n(o)())({publicKey:"public_kFR0vTVL7DIDI8YW9eF6/luGjB4=",privateKey:process.env.IMAGEKIT_PRIVATE_KEY,urlEndpoint:"https://ik.imagekit.io/fwbvmq9re"});function P(e,t){try{let e=s.getAuthenticationParameters();t.status(200).json(e)}catch(e){t.status(500).json({error:"ImageKit server error",details:e.message})}}let d=(0,u.M)(i,"default"),p=(0,u.M)(i,"config"),l=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/imagekit-auth",pathname:"/api/imagekit-auth",bundlePath:"",filename:""},userland:i})},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../webpack-api-runtime.js");t.C(e);var r=t(t.s=5744);module.exports=r})();