import { PrismaService } from './prisma.service';
export declare class AdminController {
    private prisma;
    constructor(prisma: PrismaService);
    getDashboard(): Promise<{
        users: {
            id: number;
            email: string;
            name: string | null;
            role: import(".prisma/client").$Enums.Role;
        }[];
        products: {
            category: {
                id: number;
                name: string;
            };
            id: number;
            name: string;
            image: string;
            slug: string;
            price: number;
        }[];
        orders: ({
            user: {
                id: number;
                email: string;
                name: string | null;
            };
            product: {
                id: number;
                name: string;
                image: string;
                price: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            userId: number;
            productId: number;
            status: import(".prisma/client").$Enums.OrderStatus;
            customerName: string;
            customerPhone: string;
            customerEmail: string;
            customerAddress: string;
            customColor: string | null;
            customText: string | null;
            isCustomized: boolean;
            customizationData: string | null;
            customizationPreview: string | null;
            quantity: number;
            unitPrice: number;
            totalPrice: number;
        })[];
        stats: {
            users: number;
            orders: number;
            products: number;
            collectedOrders: number;
        };
    }>;
    getUsers(): Promise<{
        id: number;
        email: string;
        name: string | null;
        role: import(".prisma/client").$Enums.Role;
    }[]>;
    getProductById(id: string): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    updateProductById(id: string, data: any): Promise<{
        id: number;
        name: string;
        image: string;
        description: string;
        customizable: boolean;
        slug: string;
        categoryId: number;
        modelUrl: string | null;
        price: number;
    }>;
    getOrders(): Promise<({
        user: {
            id: number;
            email: string;
            name: string | null;
        };
        product: {
            id: number;
            name: string;
            image: string;
            price: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        userId: number;
        productId: number;
        status: import(".prisma/client").$Enums.OrderStatus;
        customerName: string;
        customerPhone: string;
        customerEmail: string;
        customerAddress: string;
        customColor: string | null;
        customText: string | null;
        isCustomized: boolean;
        customizationData: string | null;
        customizationPreview: string | null;
        quantity: number;
        unitPrice: number;
        totalPrice: number;
    })[]>;
    markOrderCollected(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        userId: number;
        productId: number;
        status: import(".prisma/client").$Enums.OrderStatus;
        customerName: string;
        customerPhone: string;
        customerEmail: string;
        customerAddress: string;
        customColor: string | null;
        customText: string | null;
        isCustomized: boolean;
        customizationData: string | null;
        customizationPreview: string | null;
        quantity: number;
        unitPrice: number;
        totalPrice: number;
    }>;
    updateOrderStatus(id: string, body: {
        status: string;
    }): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        userId: number;
        productId: number;
        status: import(".prisma/client").$Enums.OrderStatus;
        customerName: string;
        customerPhone: string;
        customerEmail: string;
        customerAddress: string;
        customColor: string | null;
        customText: string | null;
        isCustomized: boolean;
        customizationData: string | null;
        customizationPreview: string | null;
        quantity: number;
        unitPrice: number;
        totalPrice: number;
    }>;
    updateUserRole(id: string, body: {
        role: string;
    }): Promise<{
        id: number;
        email: string;
        name: string | null;
        role: import(".prisma/client").$Enums.Role;
    }>;
}
