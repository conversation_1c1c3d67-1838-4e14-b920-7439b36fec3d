(()=>{var e={};e.id=798,e.ids=[798],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1447:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\admin\\\\orders\\\\AdminOrdersClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\AdminOrdersClient.tsx","default")},1860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2688:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...x},u)=>(0,r.createElement)("svg",{ref:u,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",i),...!n&&!d(x)&&{"aria-hidden":"true"},...x},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),x=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},d)=>(0,r.createElement)(c,{ref:d,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},2909:(e,t,s)=>{"use strict";s.d(t,{ZT:()=>l,oC:()=>n});var r=s(4999),a=s(9916);async function i(){try{let e=await (0,r.UL)(),t=e.get("jwt")?.value;if(!t)return{isAuthenticated:!1};let s=await fetch("https://backendapi-sixlight.onrender.com/auth/verify",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},cache:"no-store"});if(!s.ok)return{isAuthenticated:!1};let a=await s.json();return{isAuthenticated:!0,user:{id:a.id,email:a.email,role:a.role,name:a.name,profileImage:a.profileImage}}}catch(e){return console.error("Auth verification failed:",e),{isAuthenticated:!1}}}async function n(e="/login"){let t=await i();return t.isAuthenticated||(0,a.redirect)(e),t}async function l(e="/"){let t=await n("/login");return t.user?.role!=="ADMIN"&&(0,a.redirect)(e),t}},2941:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4034:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(687),a=s(3210),i=s(6189),n=s(1891),l=s(5356),d=s(5475);function o({user:e}){let[t,s]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[x,u]=(0,a.useState)(""),[m,h]=(0,a.useState)(0),[p,g]=(0,a.useState)(!1),f=(0,i.useRouter)(),y=()=>{h(JSON.parse(localStorage.getItem("cart")||"[]").length)},v=async(e,r)=>{let a=localStorage.getItem("token");try{(await fetch((0,d.e9)(`${d.i3.ENDPOINTS.ADMIN.ORDERS}/${e}/status`),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({status:r})})).ok?s(t.map(t=>t.id===e?{...t,status:r}:t)):u("Failed to update order status")}catch{u("Failed to update order status")}},j=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PROCESSING":return"bg-blue-100 text-blue-800";case"COMPLETED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return o?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a237e]"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A,{cartCount:m,onCartClick:()=>{g(!0)}}),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-[#1a237e]",children:"Order Management"}),(0,r.jsxs)("div",{className:"mt-2 inline-flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full border border-green-200",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-sm font-medium",children:["\uD83D\uDD12 Secure admin access for ",e?.name||e?.email]})]})]}),(0,r.jsx)("button",{onClick:()=>f.push("/admin/dashboard"),className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition",children:"Back to Dashboard"})]}),x&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:x}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("h2",{className:"text-xl font-semibold",children:["All Orders (",t.length,")"]})}),0===t.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCE6"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No orders yet"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Orders will appear here when customers place them."})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order Details"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer Info"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customization"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:["Order #",e.id]}),(0,r.jsx)("div",{className:"text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsxs)("div",{className:"text-gray-500",children:["Qty: ",e.quantity]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.customerName}),(0,r.jsx)("div",{className:"text-gray-500",children:e.customerPhone}),(0,r.jsx)("div",{className:"text-gray-500",children:e.customerEmail}),(0,r.jsx)("div",{className:"text-gray-500 text-xs mt-1 max-w-xs",children:e.customerAddress})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.product.name}),(0,r.jsxs)("div",{className:"text-gray-500",children:["K",e.unitPrice.toFixed(2)," each"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm",children:e.isCustomized?(0,r.jsxs)("div",{className:"space-y-2",children:[e.customizationPreview&&(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-16 h-16 border border-gray-300 rounded-lg overflow-hidden bg-gray-50 flex-shrink-0",children:(0,r.jsx)("img",{src:e.customizationPreview,alt:"Customization preview",className:"w-full h-full object-cover"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"inline-flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded mb-1",children:[(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"})}),"Advanced Design"]}),(0,r.jsx)("button",{onClick:()=>{if(e.customizationPreview){let t=document.createElement("a");t.href=e.customizationPreview,t.download=`order-${e.id}-design.png`,t.click()}},className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"Download Design"})]})]}),e.customColor&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:e.customColor}}),(0,r.jsx)("span",{className:"text-xs font-mono",children:e.customColor})]}),e.customText&&(0,r.jsxs)("div",{className:"text-xs text-gray-600",children:["Text: “",e.customText,"”"]})]}):(0,r.jsx)("span",{className:"text-gray-400 text-xs",children:"No customization"})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["K",e.totalPrice.toFixed(2)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${j(e.status)}`,children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:(0,r.jsxs)("select",{value:e.status,onChange:t=>v(e.id,t.target.value),className:"border border-gray-300 rounded px-2 py-1 text-xs",children:[(0,r.jsx)("option",{value:"PENDING",children:"Pending"}),(0,r.jsx)("option",{value:"PROCESSING",children:"Processing"}),(0,r.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,r.jsx)("option",{value:"CANCELLED",children:"Cancelled"})]})})]},e.id))})]})})]})]})}),(0,r.jsx)(l.A,{isOpen:p,onClose:()=>{g(!1),y()}})]})}},4146:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8322)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\admin\\orders\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/orders/page",pathname:"/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5849:(e,t,s)=>{Promise.resolve().then(s.bind(s,4034))},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},8322:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(7413),a=s(2909),i=s(1447);async function n(){let e=await (0,a.ZT)();return(0,r.jsx)(i.default,{user:e.user})}},8873:(e,t,s)=>{Promise.resolve().then(s.bind(s,1447))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[728,474,814,994,834],()=>s(4146));module.exports=r})();