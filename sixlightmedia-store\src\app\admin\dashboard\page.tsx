"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  TooltipItem,
} from "chart.js";
import Header from "@/components/Header";
import Cart from "@/components/Cart";
import ConfirmationModal from "@/components/ConfirmationModal";

ChartJS.register(ArcElement, Tooltip, Legend);

type UserOrOrder = {
  id?: string | number;
  name?: string;
  email?: string;
  status?: string;
  user?: { id: string | number; name?: string; email: string };
  product?: {
    id: string | number;
    name: string;
    price?: number;
    image?: string;
  };
  customization?: {
    color?: string;
    text?: string;
  };
  createdAt?: string;
};

type Product = {
  id: string | number;
  name: string;
  price?: number;
  image?: string;
  category?: { id: number; name: string } | null;
};

type User = {
  id: string | number;
  name?: string;
  email: string;
  role?: string;
  createdAt?: string;
};

export default function AdminDashboard() {
  const [data, setData] = useState<{
    users?: User[];
    orders?: UserOrOrder[];
    products?: Product[];
    stats?: {
      users?: number;
      orders?: number;
      products?: number;
      collectedOrders?: number;
    };
  } | null>(null);
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [tab, setTab] = useState<"products" | "users" | "orders">("products");
  const [cartCount, setCartCount] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingOrderId, setPendingOrderId] = useState<string | number | null>(
    null
  );
  const ITEMS_PER_PAGE = 5;
  const router = useRouter();

  const updateCartCount = () => {
    const cart = JSON.parse(localStorage.getItem("cart") || "[]");
    setCartCount(cart.length);
  };

  const handleCartClick = () => {
    setIsCartOpen(true);
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
    updateCartCount(); // Update count when cart closes
  };

  const handleConfirmCollection = (orderId: string | number) => {
    setPendingOrderId(orderId);
    setShowConfirmModal(true);
  };

  const handleConfirmModalConfirm = () => {
    if (pendingOrderId) {
      handleMarkCollected(pendingOrderId);
    }
    setShowConfirmModal(false);
    setPendingOrderId(null);
  };

  const handleConfirmModalCancel = () => {
    setShowConfirmModal(false);
    setPendingOrderId(null);
  };

  useEffect(() => {
    // Load cart count
    updateCartCount();
  }, []);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      setError("Not authenticated");
      return;
    }
    fetch(process.env.NEXT_PUBLIC_API_URL + "/admin/dashboard", {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((dashboardData) => {
        setData(dashboardData);
      })
      .catch(() => setError("Failed to load dashboard"));
  }, []);

  if (error)
    return <div className="text-red-600 text-center mt-16">{error}</div>;
  if (!data) return <div className="text-center mt-16">Loading...</div>;

  // Always show products in the dashboard list for management
  const items: Product[] = data.products || [];
  const totalPages = Math.max(1, Math.ceil(items.length / ITEMS_PER_PAGE));
  const paginatedItems = items.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  // Chart data for order status
  const totalOrders = data.stats?.orders ?? data.orders?.length ?? 0;
  const collectedOrders =
    data.stats?.collectedOrders ??
    data.orders?.filter((o) => o.status === "COLLECTED").length ??
    0;
  const pendingOrders = totalOrders - collectedOrders;
  const doughnutData = {
    labels: ["Collected", "Pending"],
    datasets: [
      {
        data: [collectedOrders, pendingOrders],
        backgroundColor: ["#2563eb", "#fbbf24"],
        borderColor: ["#1e40af", "#f59e42"],
        borderWidth: 2,
      },
    ],
  };
  const doughnutOptions = {
    cutout: "70%",
    plugins: {
      legend: {
        display: true,
        position: "bottom" as const,
        labels: {
          color: "#1a237e",
          font: { size: 14, weight: "bold" as const },
        },
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem: TooltipItem<"doughnut">) {
            const label = tooltipItem.label || "";
            const value = tooltipItem.raw || 0;
            return `${label}: ${value}`;
          },
        },
      },
    },
    responsive: true,
    maintainAspectRatio: false,
  };

  // Add handlers for edit and delete
  function handleEditProduct(product: Product) {
    // Use Next.js router for navigation instead of window.location.href
    // This ensures client-side navigation and works with the app directory
    router.push(`/admin/products/${product.id}`);
  }

  async function handleDeleteProduct(productId: string | number) {
    if (!confirm("Are you sure you want to delete this product?")) return;
    const token = localStorage.getItem("token");
    try {
      const res = await fetch(
        process.env.NEXT_PUBLIC_API_URL + "/admin/products/" + productId,
        {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      if (!res.ok) throw new Error("Failed to delete product");
      setData((prev: typeof data) =>
        prev && prev.products
          ? {
              ...prev,
              products: prev.products.filter(
                (p: Product) => p.id !== productId
              ),
            }
          : prev
      );
    } catch {
      alert("Error deleting product");
    }
  }

  async function handleMarkCollected(orderId: string | number | undefined) {
    if (!orderId) return;
    const token = localStorage.getItem("token");
    try {
      const res = await fetch(
        process.env.NEXT_PUBLIC_API_URL +
          "/admin/orders/" +
          orderId +
          "/collected",
        {
          method: "PUT",
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      if (!res.ok) throw new Error("Failed to mark as collected");
      setData((prev) => {
        if (!prev || !prev.orders) return prev;
        return {
          ...prev,
          orders: prev.orders.map((o) =>
            o.id === orderId ? { ...o, status: "COLLECTED" } : o
          ),
          stats: {
            ...prev.stats,
            collectedOrders: (prev.stats?.collectedOrders ?? 0) + 1,
          },
        };
      });
    } catch {
      alert("Error updating order status");
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header cartCount={cartCount} onCartClick={handleCartClick} />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
              <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                Admin
              </span>
              <span className="text-white"> Dashboard</span>
            </h1>
            <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
              Manage your Six Light Media store with powerful tools and
              real-time analytics
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-black text-yellow-400 mb-2">
                  {data.stats?.users ?? "-"}
                </div>
                <div className="text-gray-200 text-sm">Total Users</div>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-black text-pink-400 mb-2">
                  {data.stats?.orders ?? "-"}
                </div>
                <div className="text-gray-200 text-sm">Total Orders</div>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-black text-purple-400 mb-2">
                  {data.stats?.collectedOrders ??
                    (data.orders
                      ? data.orders.filter((o) => o.status === "COLLECTED")
                          .length
                      : "-")}
                </div>
                <div className="text-gray-200 text-sm">Collected</div>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-black text-blue-400 mb-2">
                  {data.stats?.products ??
                    (data.products ? data.products.length : "-")}
                </div>
                <div className="text-gray-200 text-sm">Products</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Navigation Tabs */}
        <div className="bg-white rounded-3xl shadow-xl p-2 mb-8 border border-gray-100">
          <div className="flex flex-wrap justify-center gap-2">
            <button
              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ${
                tab === "products"
                  ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg"
                  : "text-gray-600 hover:bg-gray-50 hover:text-indigo-600"
              }`}
              onClick={() => setTab("products")}
              type="button"
            >
              📦 Products
            </button>
            <button
              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ${
                tab === "users"
                  ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg"
                  : "text-gray-600 hover:bg-gray-50 hover:text-indigo-600"
              }`}
              onClick={() => setTab("users")}
              type="button"
            >
              👥 Users
            </button>
            <button
              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ${
                tab === "orders"
                  ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg"
                  : "text-gray-600 hover:bg-gray-50 hover:text-indigo-600"
              }`}
              onClick={() => setTab("orders")}
              type="button"
            >
              📋 Orders
            </button>
            <Link
              href="/admin/categories"
              className="px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base text-gray-600 hover:bg-gray-50 hover:text-indigo-600 flex items-center justify-center"
            >
              🏷️ Categories
            </Link>
          </div>
        </div>
        {/* Analytics Dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Performance Metrics */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-3xl shadow-xl p-8 border border-gray-100">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                📊 Performance Metrics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-6 border border-blue-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-blue-700 bg-blue-200 px-3 py-1 rounded-full">
                      Completion Rate
                    </span>
                  </div>
                  <div className="text-3xl font-black text-blue-700 mb-2">
                    {data.stats?.orders && data.stats?.collectedOrders
                      ? `${Math.round(
                          (data.stats.collectedOrders / data.stats.orders) * 100
                        )}%`
                      : "-"}
                  </div>
                  <div className="text-sm text-blue-600">
                    Orders successfully completed
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-6 border border-green-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-green-700 bg-green-200 px-3 py-1 rounded-full">
                      Pending
                    </span>
                  </div>
                  <div className="text-3xl font-black text-green-700 mb-2">
                    {data.stats?.orders &&
                    data.stats?.collectedOrders !== undefined
                      ? data.stats.orders - data.stats.collectedOrders
                      : data.orders
                      ? data.orders.filter((o) => o.status === "PENDING").length
                      : "-"}
                  </div>
                  <div className="text-sm text-green-600">
                    Orders awaiting collection
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Order Status Chart */}
          <div className="bg-white rounded-3xl shadow-xl p-8 border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
              📈 Order Status
            </h3>
            <div className="w-full h-64 flex items-center justify-center">
              <div className="w-48 h-48">
                <Doughnut data={doughnutData} options={doughnutOptions} />
              </div>
            </div>
          </div>
        </div>
        {/* Products Tab */}
        {tab === "products" && items.length > 0 && (
          <div className="bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100">
            <div className="flex items-center justify-between mb-8">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                📦 Product Management
              </h3>
              <Link
                href="/admin/products"
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                ➕ Add New Product
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedItems.map((product, idx) => (
                <div
                  key={product.id || idx}
                  className="group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <div className="aspect-square mb-4 bg-white rounded-xl overflow-hidden border border-gray-100">
                    <Image
                      src={product.image || "/bottle-dummy.jpg"}
                      alt={product.name}
                      width={200}
                      height={200}
                      className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-bold text-gray-900 text-lg line-clamp-2">
                      {product.name}
                    </h4>

                    <div className="flex items-center gap-2">
                      {product.category && (
                        <span className="px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
                          {typeof product.category === "string"
                            ? product.category
                            : product.category?.name || "Unknown Category"}
                        </span>
                      )}
                      <span className="px-3 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full">
                        K{product.price ?? 0}
                      </span>
                    </div>

                    <div className="flex gap-2 pt-2">
                      <button
                        className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md"
                        onClick={() => handleEditProduct(product)}
                      >
                        ✏️ Edit
                      </button>
                      <button
                        className="flex-1 px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white text-sm font-semibold rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-md"
                        onClick={() => handleDeleteProduct(product.id)}
                      >
                        🗑️ Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center gap-4 mt-8">
                <button
                  className="px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
                  onClick={() =>
                    setCurrentPage((p: number) => Math.max(1, p - 1))
                  }
                  disabled={currentPage === 1}
                >
                  ← Previous
                </button>
                <span className="px-4 py-2 bg-gray-100 rounded-xl font-semibold text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  className="px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
                  onClick={() =>
                    setCurrentPage((p: number) => Math.min(totalPages, p + 1))
                  }
                  disabled={currentPage === totalPages}
                >
                  Next →
                </button>
              </div>
            )}
          </div>
        )}
        {/* Users Tab */}
        {tab === "users" && data.users && data.users.length > 0 && (
          <div className="bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100">
            <div className="flex items-center justify-between mb-8">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                👥 User Management
              </h3>
              <Link
                href="/admin/users"
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                👥 Manage All Users
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {data.users.slice(0, 9).map((user: User) => (
                <div
                  key={user.id}
                  className="group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {(user.name || user.email)?.charAt(0)?.toUpperCase() ||
                        "U"}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-bold text-gray-900 truncate">
                        {user.name || user.email}
                      </h4>
                      <p className="text-sm text-gray-600 truncate">
                        {user.email}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Role:</span>
                      <span
                        className={`px-3 py-1 text-xs font-semibold rounded-full ${
                          user.role === "ADMIN"
                            ? "bg-red-100 text-red-700"
                            : "bg-blue-100 text-blue-700"
                        }`}
                      >
                        {user.role}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Status:</span>
                      <span className="px-3 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full">
                        ✅ Active
                      </span>
                    </div>

                    <div className="pt-2 border-t border-gray-100">
                      <div className="text-xs text-gray-500">
                        📅 Member since:{" "}
                        {new Date(
                          user.createdAt || Date.now()
                        ).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Orders Tab */}
        {tab === "orders" && data.orders && data.orders.length > 0 && (
          <div className="bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100">
            <div className="flex items-center justify-between mb-8">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                📋 Order Management
              </h3>
              <Link
                href="/admin/orders"
                className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                📋 View All Orders
              </Link>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {data.orders
                .slice(0, 8)
                .map((order: UserOrOrder, idx: number) => (
                  <div
                    key={order.id || idx}
                    className="group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center text-white font-bold text-sm">
                          #{order.id}
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900">
                            Order #{order.id}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {order.user?.name ||
                              order.user?.email ||
                              "Unknown Customer"}
                          </p>
                        </div>
                      </div>

                      <span
                        className={`px-3 py-1 text-xs font-semibold rounded-full ${
                          order.status === "COLLECTED"
                            ? "bg-green-100 text-green-700"
                            : order.status === "PENDING"
                            ? "bg-yellow-100 text-yellow-700"
                            : "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {order.status === "COLLECTED"
                          ? "✅ Collected"
                          : order.status === "PENDING"
                          ? "⏳ Pending"
                          : order.status}
                      </span>
                    </div>

                    {order.product && (
                      <div className="flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-xl">
                        <div className="w-12 h-12 bg-white rounded-lg overflow-hidden border border-gray-200">
                          <Image
                            src={order.product.image || "/bottle-dummy.jpg"}
                            alt={order.product.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h5 className="font-semibold text-gray-900 truncate">
                            {order.product.name}
                          </h5>
                          <p className="text-sm text-green-600 font-semibold">
                            K{order.product.price ?? 0}
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Order Date:</span>
                        <span className="font-medium text-gray-900">
                          {new Date(
                            order.createdAt || Date.now()
                          ).toLocaleDateString()}
                        </span>
                      </div>

                      {order.customization && (
                        <div className="text-sm">
                          <span className="text-gray-600">Customization:</span>
                          <div className="mt-1 p-2 bg-blue-50 rounded-lg">
                            <div className="text-xs text-blue-700">
                              🎨 Color: {order.customization.color}
                            </div>
                            {order.customization.text && (
                              <div className="text-xs text-blue-700">
                                📝 Text: &ldquo;{order.customization.text}
                                &rdquo;
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {order.status === "PENDING" && (
                      <button
                        className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md"
                        onClick={() =>
                          order.id && handleConfirmCollection(order.id)
                        }
                      >
                        ✅ Mark as Collected
                      </button>
                    )}
                  </div>
                ))}
            </div>
          </div>
        )}
        {/* Quick Actions Section */}
        <div className="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 rounded-3xl p-8 text-center relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10">
            <h3 className="text-3xl font-bold text-white mb-4">
              🚀 Quick Actions
            </h3>
            <p className="text-gray-200 mb-8 max-w-2xl mx-auto">
              Manage your store efficiently with these powerful tools
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <Link
                href="/admin/products"
                className="group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-white mb-2">
                  Add Product
                </h4>
                <p className="text-gray-300 text-sm">
                  Upload new products to your store
                </p>
              </Link>

              <Link
                href="/admin/categories"
                className="group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                    />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-white mb-2">
                  Categories
                </h4>
                <p className="text-gray-300 text-sm">
                  Organize your product categories
                </p>
              </Link>

              <Link
                href="/admin/orders"
                className="group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-white mb-2">
                  All Orders
                </h4>
                <p className="text-gray-300 text-sm">
                  View and manage all orders
                </p>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Cart Modal */}
      <Cart isOpen={isCartOpen} onClose={handleCartClose} />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        title="Confirm Order Collection"
        message="Has the customer paid and collected this order? This action will mark the order as completed."
        confirmText="Yes, Mark as Collected"
        cancelText="Cancel"
        onConfirm={handleConfirmModalConfirm}
        onCancel={handleConfirmModalCancel}
        type="info"
      />
    </div>
  );
}
