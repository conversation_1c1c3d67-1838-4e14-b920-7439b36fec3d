import { CategoryService } from './category.service';
export declare class CategoryController {
    private readonly categoryService;
    constructor(categoryService: CategoryService);
    findAll(): Promise<{
        id: number;
        name: string;
    }[]>;
    create(body: {
        name: string;
    }): Promise<{
        id: number;
        name: string;
    }>;
    delete(name: string): Promise<{
        id: number;
        name: string;
    }>;
}
