(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[700],{646:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2657:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3843:(e,s,r)=>{"use strict";r.d(s,{e9:()=>n,i3:()=>a});var t=r(9509);let a={BASE_URL:"https://backendapi-sixlight.onrender.com",ENDPOINTS:{AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",REFRESH:"/auth/refresh",VERIFY_EMAIL:"/auth/verify-email",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",RESEND_VERIFICATION:"/auth/resend-verification"},PRODUCTS:"/product",CATEGORIES:"/categories",ORDERS:"/orders",ADMIN:{DASHBOARD:"/admin/dashboard",ORDERS:"/admin/orders",USERS:"/admin/users",PRODUCTS:"/admin/products",CATEGORIES:"/admin/categories",ORDER_COLLECTED:"/admin/orders"},USER:{DASHBOARD:"/user/dashboard",PROFILE:"/user/profile",UPDATE_PROFILE:"/user/profile",UPLOAD_PROFILE_IMAGE:"/user/upload-profile-image",CHANGE_PASSWORD:"/user/change-password",DELETE:"/user/delete",ORDERS:"/user/orders"}},DEFAULT_HEADERS:{"Content-Type":"application/json"},TIMEOUT:1e4};t.env.IMAGEKIT_PRIVATE_KEY,t.env.NEXT_PUBLIC_GA_ID,t.env.NEXT_PUBLIC_GA_ID;let n=e=>{let s=a.BASE_URL.replace(/\/$/,""),r=e.startsWith("/")?e:"/".concat(e);return"".concat(s).concat(r)}},4622:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(5155),a=r(2115),n=r(5695),o=r(9958),i=r(6874),d=r.n(i),l=r(6766),c=r(1153),u=r(5339),m=r(646),h=r(2919),p=r(8749),x=r(2657);let f=c.z.object({password:c.z.string().min(6,"Password must be at least 6 characters"),confirmPassword:c.z.string().min(6,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function g(){let[e,s]=(0,a.useState)({password:"",confirmPassword:""}),[r,i]=(0,a.useState)({}),[c,g]=(0,a.useState)(!1),[w,b]=(0,a.useState)(!1),[y,N]=(0,a.useState)(""),[j,v]=(0,a.useState)(!1),[P,S]=(0,a.useState)(!1),[E,R]=(0,a.useState)(null),A=(0,n.useSearchParams)(),O=(0,n.useRouter)();(0,a.useEffect)(()=>{let e=null==A?void 0:A.get("token");if(!e)return void N("Invalid reset link. Please request a new password reset.");R(e)},[A]);let T=(e,t)=>{s(s=>({...s,[e]:t})),r[e]&&i(s=>({...s,[e]:void 0}))},I=async s=>{if(s.preventDefault(),!E)return void N("Invalid reset token. Please request a new password reset.");g(!0),i({}),N("");let r=f.safeParse(e);if(!r.success){g(!1);let e={};r.error.errors.forEach(s=>{s.path[0]&&(e[s.path[0]]=s.message)}),i(e);return}try{let s=await (0,o.xw)(E,e.password);g(!1),b(!0),N(s.message||"Password reset successfully!"),setTimeout(()=>{O.push("/login?message=Password reset successfully! You can now log in with your new password.")},3e3)}catch(e){var t,a;g(!1),(null==(t=e.message)?void 0:t.includes("expired"))?N("Reset link has expired. Please request a new password reset."):(null==(a=e.message)?void 0:a.includes("Invalid"))?N("Invalid reset link. Please request a new password reset."):N("Failed to reset password. Please try again.")}};return E||w?w?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-indigo-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,t.jsx)(m.A,{className:"h-16 w-16 text-green-500 mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-green-700",children:"Password Reset!"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-600",children:y}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to login page..."})]}),(0,t.jsx)(d(),{href:"/login",className:"inline-block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors mt-6",children:"Continue to Login"})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reset Password"}),(0,t.jsx)("p",{className:"text-gray-600 text-center mt-2",children:"Enter your new password below."})]}),(0,t.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("input",{id:"password",name:"password",type:j?"text":"password",value:e.password,onChange:e=>T("password",e.target.value),placeholder:"Enter new password",autoComplete:"new-password",className:"w-full pl-10 pr-12 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ".concat(r.password?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),"aria-invalid":!!r.password,"aria-describedby":r.password?"password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>v(!j),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:j?(0,t.jsx)(p.A,{size:20}):(0,t.jsx)(x.A,{size:20})})]}),r.password&&(0,t.jsxs)("div",{id:"password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(u.A,{size:16}),r.password]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:P?"text":"password",value:e.confirmPassword,onChange:e=>T("confirmPassword",e.target.value),autoComplete:"new-password",placeholder:"Confirm new password",className:"w-full pl-10 pr-12 py-3 border rounded-lg focus:outline-none focus:ring-2 transition ".concat(r.confirmPassword?"border-red-500 focus:ring-red-300":"border-gray-300 focus:ring-blue-300"),"aria-invalid":!!r.confirmPassword,"aria-describedby":r.confirmPassword?"confirm-password-error":void 0}),(0,t.jsx)("button",{type:"button",onClick:()=>S(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:P?(0,t.jsx)(p.A,{size:20}):(0,t.jsx)(x.A,{size:20})})]}),r.confirmPassword&&(0,t.jsxs)("div",{id:"confirm-password-error",className:"flex items-center gap-1 text-red-600 text-sm",children:[(0,t.jsx)(u.A,{size:16}),r.confirmPassword]})]}),(0,t.jsx)("button",{type:"submit",className:"w-full font-semibold px-6 py-3 rounded-lg shadow transition-all duration-200 ".concat(c?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"),disabled:c,children:c?(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,t.jsx)("span",{children:"Resetting Password..."})]}):"Reset Password"}),y&&!w&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200",children:[(0,t.jsx)(u.A,{size:16}),y]})]}),(0,t.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Remember your password?"," ",(0,t.jsx)(d(),{href:"/login",className:"text-blue-600 hover:text-blue-800 font-medium",children:"Sign in"})]})})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-orange-50 to-yellow-100 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)(l.default,{src:"/6 Light Logo.png",alt:"6 Light Logo",width:64,height:64,className:"mb-4"}),(0,t.jsx)(u.A,{className:"h-16 w-16 text-red-500 mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-700",children:"Invalid Link"})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:y}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(d(),{href:"/forgot-password",className:"inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors",children:"Request New Reset Link"}),(0,t.jsx)(d(),{href:"/login",className:"inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors",children:"Back to Login"})]})]})})}function w(){return(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})})}),children:(0,t.jsx)(g,{})})}},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},8749:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9233:(e,s,r)=>{Promise.resolve().then(r.bind(r,4622))},9958:(e,s,r)=>{"use strict";r.d(s,{A$:()=>o,BD:()=>i,RS:()=>l,iD:()=>a,kz:()=>n,xw:()=>d});var t=r(3843);async function a(e,s){return(await fetch((0,t.e9)(t.i3.ENDPOINTS.AUTH.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})})).json()}async function n(e,s,r){return(await fetch((0,t.e9)(t.i3.ENDPOINTS.AUTH.REGISTER),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s,name:r})})).json()}async function o(e){return(await fetch((0,t.e9)(t.i3.ENDPOINTS.AUTH.VERIFY_EMAIL),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e})})).json()}async function i(e){return(await fetch((0,t.e9)(t.i3.ENDPOINTS.AUTH.FORGOT_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}async function d(e,s){return(await fetch((0,t.e9)(t.i3.ENDPOINTS.AUTH.RESET_PASSWORD),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({token:e,password:s})})).json()}async function l(e){return(await fetch((0,t.e9)(t.i3.ENDPOINTS.AUTH.RESEND_VERIFICATION),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e})})).json()}}},e=>{var s=s=>e(e.s=s);e.O(0,[766,874,619,441,684,358],()=>s(9233)),_N_E=e.O()}]);