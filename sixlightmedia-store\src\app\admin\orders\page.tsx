import { requireAdmin } from "@/lib/auth";
import AdminOrdersClient from "./AdminOrdersClient";

type Order = {
  id: number;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  customerAddress: string;
  customColor?: string;
  customText?: string;
  isCustomized: boolean;
  customizationData?: string; // JSON string containing Fabric.js canvas data
  customizationPreview?: string; // Base64 image preview
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: string;
  createdAt: string;
  product: {
    id: number;
    name: string;
    price: number;
    image?: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
};

export default async function AdminOrders() {
  // Server-side authentication check - redirects if not admin
  const auth = await requireAdmin();

  return <AdminOrdersClient user={auth.user} />;
}
