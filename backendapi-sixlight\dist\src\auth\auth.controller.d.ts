import { AuthService } from './auth.service';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(body: {
        email: string;
        password: string;
    }): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            role: any;
            name: any;
        };
    } | {
        error: string;
    }>;
    register(body: {
        email: string;
        password: string;
        name?: string;
        role?: string;
    }): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            role: any;
            name: any;
        };
    }>;
    me(req: any): Promise<any>;
}
