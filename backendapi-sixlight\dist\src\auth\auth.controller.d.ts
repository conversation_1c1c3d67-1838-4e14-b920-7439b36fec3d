import { AuthService } from './auth.service';
import { Response as ExpressResponse } from 'express';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(body: {
        email: string;
        password: string;
    }, res: ExpressResponse): Promise<ExpressResponse<any, Record<string, any>>>;
    register(body: {
        email: string;
        password: string;
        name?: string;
        role?: string;
    }): Promise<{
        message: string;
        user: {
            id: number;
            email: string;
            name: string | null;
            emailVerified: boolean;
        };
    }>;
    me(req: any): Promise<any>;
    verify(req: any): Promise<{
        id: any;
        email: any;
        role: any;
        name: any;
        profileImage: any;
    }>;
    logout(res: ExpressResponse): Promise<ExpressResponse<any, Record<string, any>>>;
    verifyEmail(body: {
        token: string;
    }): Promise<{
        message: string;
    }>;
    forgotPassword(body: {
        email: string;
    }): Promise<{
        message: string;
    }>;
    resetPassword(body: {
        token: string;
        password: string;
    }): Promise<{
        message: string;
    }>;
    resendVerification(body: {
        email: string;
    }): Promise<{
        message: string;
    }>;
}
