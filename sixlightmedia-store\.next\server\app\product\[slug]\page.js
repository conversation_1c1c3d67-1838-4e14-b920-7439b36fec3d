(()=>{var e={};e.id=490,e.ids=[490],e.modules={36:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(1968),a=s.n(r)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1414:(e,t,s)=>{Promise.resolve().then(s.bind(s,9990))},1968:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=s(4985)._(s(4963));function a(e,t){var s;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,r.default)({...l,modules:null==(s=l.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2958:(e,t,s)=>{Promise.resolve().then(s.bind(s,7618))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4777:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return i}});let r=s(687),a=s(1215),l=s(9294),n=s(4825);function i(e){let{moduleIds:t}=e,s=l.workAsyncStorage.getStore();if(void 0===s)return null;let i=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files;i.push(...t)}}return 0===i.length?null:(0,r.jsx)(r.Fragment,{children:i.map(e=>{let t=s.assetPrefix+"/_next/"+(0,n.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,a.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},4825:(e,t)=>{"use strict";function s(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return s}})},4963:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let r=s(687),a=s(3210),l=s(6780),n=s(4777);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},s=(0,a.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=!t.ssr||!!t.loading,c=o?a.Suspense:a.Fragment,u=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.PreloadChunks,{moduleIds:t.modules}),(0,r.jsx)(s,{...e})]}):(0,r.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(c,{...o?{fallback:i}:{},children:u})}return c.displayName="LoadableComponent",c}},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},6780:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let r=s(1208);function a(e){let{reason:t,children:s}=e;throw Object.defineProperty(new r.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},7618:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\product\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx","default")},7894:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(5239),a=s(8088),l=s(8170),n=s.n(l),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7618)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9990:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(687),a=s(474),l=s(3210),n=s.n(l),i=s(6189),o=s(5814),d=s.n(o),c=s(36);s(6208);var u=s(1891),x=s(5356);let m=({isOpen:e,onClose:t})=>e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:t}),(0,r.jsxs)("div",{className:"relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-1",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-t-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-amber-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),"Save Your Design"]}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-500 focus:outline-none",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start mb-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-amber-100 rounded-full p-2",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-amber-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-gray-700 dark:text-gray-300 mb-2",children:"Please create and save your design before adding to cart."}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:["Click the"," ",(0,r.jsx)("span",{className:"font-bold text-indigo-600 dark:text-indigo-400",children:"'Save Design'"})," ","button in the customizer to save your changes."]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Follow these steps:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm text-gray-700 dark:text-gray-300",children:[(0,r.jsx)("li",{children:"Complete your design using the customizer tools"}),(0,r.jsxs)("li",{children:["Click the"," ",(0,r.jsx)("span",{className:"font-bold text-indigo-600 dark:text-indigo-400",children:"Save Design"})," ","button at the bottom of the customizer"]}),(0,r.jsx)("li",{children:'Then click "Add to Cart" to proceed'})]})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end",children:(0,r.jsxs)("button",{onClick:t,className:"px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg text-sm hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 flex items-center",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),"Got it"]})})]})]}):null;s(5475),(0,c.default)(async()=>{},{loadableGenerated:{modules:["app\\product\\[slug]\\page.tsx -> ./Bottle3D"]},ssr:!1}),(0,c.default)(async()=>{},{loadableGenerated:{modules:["app\\product\\[slug]\\page.tsx -> ./Tshirt3D"]},ssr:!1});let h=(0,c.default)(async()=>{},{loadableGenerated:{modules:["app\\product\\[slug]\\page.tsx -> @/components/ProductCustomizerFallback"]},ssr:!1,loading:()=>(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading Customizer..."})]})})})});function p({params:e}){let t=n().use(e).slug,s=(0,i.useRouter)(),[o,c]=(0,l.useState)(null),[p,g]=(0,l.useState)(!0),[f,j]=(0,l.useState)(!1),[b,v]=(0,l.useState)(!1),[y,N]=(0,l.useState)(!1),[w,k]=(0,l.useState)(!1),[C,P]=(0,l.useState)(null),[D,L]=(0,l.useState)(0),[_,z]=(0,l.useState)(!1),[M,S]=(0,l.useState)(!1),B=(0,l.useRef)(null),A=(0,l.useRef)(null),O=()=>{},W=()=>{z(!0)};async function R(e){if(e.preventDefault(),o)return void s.push(`/login?redirect=/product/${t}`)}return p?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(u.A,{cartCount:D,onCartClick:W}),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading product..."})]})})]}):f||!o?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(u.A,{cartCount:D,onCartClick:W}),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Product Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the product you're looking for."}),(0,r.jsxs)(d(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Shop"]})]})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(u.A,{cartCount:D,onCartClick:W}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 pt-8",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-8",children:[(0,r.jsx)(d(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Home"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,r.jsx)(d(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Products"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:o.name})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 pb-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{ref:A,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:w&&C?.preview?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)("div",{className:"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden",children:(0,r.jsx)("img",{src:C.preview,alt:"Customization preview",className:"w-full h-full object-contain hover:scale-105 transition-transform duration-500"})}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-4 text-center",children:"� Your Custom Design Preview"})]}):(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)("div",{className:"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden",children:(0,r.jsx)(a.default,{src:o.image&&o.image.startsWith("http")?o.image:o.image?"/"+o.image.replace(/^\/+/,""):"/bottle-dummy.jpg",alt:o.name,width:400,height:400,className:"w-full h-full object-contain hover:scale-105 transition-transform duration-500",unoptimized:!!(o.image&&o.image.startsWith("http")),onError:e=>{e.target.src="/bottle-dummy.jpg"}})}),!o.image&&(0,r.jsx)("div",{className:"text-sm text-amber-600 mt-4 bg-amber-50 px-4 py-2 rounded-lg",children:"⚠️ No product image found. Showing fallback."})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Product Features"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Premium Quality"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Fast Delivery"})]}),o.customizable&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Customizable"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Guaranteed"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{ref:B,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("span",{className:"px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full",children:o.category?.name||"Product"}),o.customizable&&(0,r.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-700 text-sm font-semibold rounded-full",children:"✨ Customizable"})]}),(0,r.jsx)("h1",{className:"text-4xl font-black text-gray-900 mb-4 leading-tight",children:o.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-6",children:o.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-3xl font-black text-green-600",children:["K",o.price??0]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"\uD83D\uDCB0 Best price guaranteed"})]})]}),o.customizable&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border border-purple-200",children:[(0,r.jsx)("input",{type:"checkbox",id:"customize-toggle",checked:w,onChange:()=>k(e=>!e),className:"w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"}),(0,r.jsx)("label",{htmlFor:"customize-toggle",className:"text-lg font-semibold text-gray-900 cursor-pointer select-none",children:"\uD83C\uDFA8 Customize this product"})]}),w&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-blue-700",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"})}),(0,r.jsx)("span",{className:"font-medium",children:"Advanced Designer Enabled"}),(0,r.jsx)("span",{className:"text-blue-600",children:"- Create professional designs with our smart customization tools"})]})})]}),(0,r.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[w&&(0,r.jsx)(h,{productImage:o.image&&o.image.startsWith("http")?o.image:o.image?"/"+o.image.replace(/^\/+/,""):"/bottle-dummy.jpg",onCustomizationChange:P,initialCustomization:C}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{type:"submit",className:`w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ${y||b?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white hover:shadow-xl transform hover:-translate-y-1"}`,disabled:y||b,children:y?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Adding to Cart..."})]}):b?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,r.jsx)("span",{children:"Added to Cart!"})]}):(0,r.jsxs)("span",{children:["\uD83D\uDED2"," ",w?"Add Custom Product to Cart":"Add to Cart"]})}),(0,r.jsx)("button",{type:"button",onClick:function(){s.push(`/login?redirect=/product/${t}`)},className:"w-full py-4 px-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"⚡ Order Now - Fast Delivery"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDCCB Product Information"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Category:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:o.category?.name||"General"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Customizable:"}),(0,r.jsx)("span",{className:`font-semibold ${o.customizable?"text-green-600":"text-gray-500"}`,children:o.customizable?"✅ Yes":"❌ No"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Delivery:"}),(0,r.jsx)("span",{className:"font-semibold text-blue-600",children:"\uD83D\uDE9A 24-48 hours"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Warranty:"}),(0,r.jsx)("span",{className:"font-semibold text-green-600",children:"✅ 30 days"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-6 border border-green-200",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDEE1️ Why Choose Us?"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Premium Quality"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"⚡"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Fast Delivery"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCAF"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"100% Guaranteed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFA8"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Custom Design"})]})]})]})]})]})}),(0,r.jsx)(x.A,{isOpen:_,onClose:()=>{z(!1),O()}}),(0,r.jsx)(m,{isOpen:M,onClose:()=>S(!1)})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[728,474,814,263,834],()=>s(7894));module.exports=r})();