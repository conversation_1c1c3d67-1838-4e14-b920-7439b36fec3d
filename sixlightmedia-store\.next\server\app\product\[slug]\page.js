(()=>{var e={};e.id=490,e.ids=[490],e.modules={577:(e,t,s)=>{Promise.resolve().then(s.bind(s,7618))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1414:(e,t,s)=>{Promise.resolve().then(s.bind(s,3241))},1968:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=s(4985)._(s(7344));function a(e,t){var s;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,r.default)({...l,modules:null==(s=l.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3241:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(687),a=s(474),l=s(3210),n=s.n(l),o=s(6189),i=s(5814),d=s.n(i),c=s(1968),u=s.n(c);s(6208);var x=s(1891),m=s(5356);let h=u()(async()=>{},{loadableGenerated:{modules:["app\\product\\[slug]\\page.tsx -> ./Bottle3D"]},ssr:!1}),p=u()(async()=>{},{loadableGenerated:{modules:["app\\product\\[slug]\\page.tsx -> ./Tshirt3D"]},ssr:!1});function g({params:e}){let t=n().use(e).slug,s=(0,o.useRouter)(),[i,c]=(0,l.useState)(null),[u,g]=(0,l.useState)(!0),[f,b]=(0,l.useState)(!1),[j,v]=(0,l.useState)("#ffffff"),[y,N]=(0,l.useState)(""),[w,C]=(0,l.useState)(!1),[k,D]=(0,l.useState)(!1),[P,_]=(0,l.useState)(!1),[L,M]=(0,l.useState)(0),[z,A]=(0,l.useState)(!1),S=(0,l.useRef)(null),E=(0,l.useRef)(null),O=()=>{},R=()=>{A(!0)};async function B(e){if(e.preventDefault(),i)return void s.push(`/login?redirect=/product/${t}`)}return u?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(x.A,{cartCount:L,onCartClick:R}),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 font-medium",children:"Loading product..."})]})})]}):f||!i?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(x.A,{cartCount:L,onCartClick:R}),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Product Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the product you're looking for."}),(0,r.jsxs)(d(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Shop"]})]})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,r.jsx)(x.A,{cartCount:L,onCartClick:R}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 pt-8",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-8",children:[(0,r.jsx)(d(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Home"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,r.jsx)(d(),{href:"/",className:"hover:text-indigo-600 transition-colors duration-200",children:"Products"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:i.name})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 pb-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{ref:E,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:P&&"engraved-bottle"===i.slug?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)(h,{color:j,text:y,onColorChange:v,modelUrl:i.modelUrl}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-4 text-center",children:"\uD83C\uDFAE 3D Preview: Drag to rotate, scroll to zoom"})]}):P&&"custom-tshirt"===i.slug?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)(p,{color:j,text:y,onColorChange:v}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-4 text-center",children:"\uD83C\uDFAE 3D Preview: Drag to rotate, scroll to zoom"})]}):(0,r.jsxs)("div",{className:"w-full flex flex-col items-center",children:[(0,r.jsx)("div",{className:"aspect-square w-full max-w-md bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden",children:(0,r.jsx)(a.default,{src:i.image&&i.image.startsWith("http")?i.image:i.image?"/"+i.image.replace(/^\/+/,""):"/bottle-dummy.jpg",alt:i.name,width:400,height:400,className:"w-full h-full object-contain hover:scale-105 transition-transform duration-500",unoptimized:!!(i.image&&i.image.startsWith("http")),onError:e=>{e.target.src="/bottle-dummy.jpg"}})}),!i.image&&(0,r.jsx)("div",{className:"text-sm text-amber-600 mt-4 bg-amber-50 px-4 py-2 rounded-lg",children:"⚠️ No product image found. Showing fallback."})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Product Features"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Premium Quality"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Fast Delivery"})]}),i.customizable&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Customizable"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Guaranteed"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{ref:S,className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("span",{className:"px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-semibold rounded-full",children:i.category?.name||"Product"}),i.customizable&&(0,r.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-700 text-sm font-semibold rounded-full",children:"✨ Customizable"})]}),(0,r.jsx)("h1",{className:"text-4xl font-black text-gray-900 mb-4 leading-tight",children:i.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-6",children:i.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-3xl font-black text-green-600",children:["K",i.price??0]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"\uD83D\uDCB0 Best price guaranteed"})]})]}),i.customizable&&(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border border-purple-200",children:[(0,r.jsx)("input",{type:"checkbox",id:"customize-toggle",checked:P,onChange:()=>_(e=>!e),className:"w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"}),(0,r.jsx)("label",{htmlFor:"customize-toggle",className:"text-lg font-semibold text-gray-900 cursor-pointer select-none",children:"\uD83C\uDFA8 Customize this product"})]})}),(0,r.jsxs)("form",{onSubmit:B,className:"space-y-6",children:[P&&(0,r.jsxs)("div",{className:"space-y-6 p-6 bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl border border-gray-200",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"\uD83C\uDFA8 Customize Your Product"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-gray-700",children:"Choose Color"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("input",{type:"color",name:"color",value:j,onChange:e=>v(e.target.value),className:"w-16 h-12 border-2 border-gray-300 rounded-xl cursor-pointer hover:border-indigo-400 transition-colors duration-200",title:"Choose color"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("div",{className:"text-sm font-mono text-gray-600 bg-white px-3 py-2 rounded-lg border",children:j.toUpperCase()})})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"block text-sm font-semibold text-gray-700",children:["Text to Engrave/Print",(0,r.jsxs)("span",{className:"text-xs text-gray-500 font-normal ml-2",children:["(",y.length,"/30 characters)"]})]}),(0,r.jsx)("input",{type:"text",name:"text",value:y,onChange:e=>N(e.target.value),maxLength:30,placeholder:"e.g. Your Name or Message",className:"w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 text-lg",required:!0}),y.length>25&&(0,r.jsx)("div",{className:"text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg",children:"⚠️ Character limit almost reached"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{type:"submit",className:`w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ${k||w?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white hover:shadow-xl transform hover:-translate-y-1"}`,disabled:k||w,children:k?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Adding to Cart..."})]}):w?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,r.jsx)("span",{children:"Added to Cart!"})]}):(0,r.jsxs)("span",{children:["\uD83D\uDED2"," ",P?"Add Custom Product to Cart":"Add to Cart"]})}),(0,r.jsx)("button",{type:"button",onClick:function(){s.push(`/login?redirect=/product/${t}`)},className:"w-full py-4 px-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"⚡ Order Now - Fast Delivery"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-lg p-6 border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDCCB Product Information"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Category:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:i.category?.name||"General"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Customizable:"}),(0,r.jsx)("span",{className:`font-semibold ${i.customizable?"text-green-600":"text-gray-500"}`,children:i.customizable?"✅ Yes":"❌ No"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Delivery:"}),(0,r.jsx)("span",{className:"font-semibold text-blue-600",children:"\uD83D\uDE9A 24-48 hours"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Warranty:"}),(0,r.jsx)("span",{className:"font-semibold text-green-600",children:"✅ 30 days"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-6 border border-green-200",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"\uD83D\uDEE1️ Why Choose Us?"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Premium Quality"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"⚡"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Fast Delivery"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCAF"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"100% Guaranteed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFA8"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-gray-700",children:"Custom Design"})]})]})]})]})]})}),(0,r.jsx)(m.A,{isOpen:z,onClose:()=>{A(!1),O()}})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4777:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return o}});let r=s(687),a=s(1215),l=s(9294),n=s(4825);function o(e){let{moduleIds:t}=e,s=l.workAsyncStorage.getStore();if(void 0===s)return null;let o=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files;o.push(...t)}}return 0===o.length?null:(0,r.jsx)(r.Fragment,{children:o.map(e=>{let t=s.assetPrefix+"/_next/"+(0,n.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,a.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},4825:(e,t)=>{"use strict";function s(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return s}})},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},6780:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let r=s(1208);function a(e){let{reason:t,children:s}=e;throw Object.defineProperty(new r.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},7344:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let r=s(687),a=s(3210),l=s(6780),n=s(4777);function o(e){return{default:e&&"default"in e?e.default:e}}let i={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},d=function(e){let t={...i,...e},s=(0,a.lazy)(()=>t.loader().then(o)),d=t.loading;function c(e){let o=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,i=!t.ssr||!!t.loading,c=i?a.Suspense:a.Fragment,u=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.PreloadChunks,{moduleIds:t.modules}),(0,r.jsx)(s,{...e})]}):(0,r.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(c,{...i?{fallback:o}:{},children:u})}return c.displayName="LoadableComponent",c}},7618:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\fullstack-sixlight\\\\sixlightmedia-store\\\\src\\\\app\\\\product\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx","default")},7894:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(5239),a=s(8088),l=s(8170),n=s.n(l),o=s(893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let d={children:["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7618)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Dev\\fullstack-sixlight\\sixlightmedia-store\\src\\app\\product\\[slug]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,145,658,474,814,263,249],()=>s(7894));module.exports=r})();