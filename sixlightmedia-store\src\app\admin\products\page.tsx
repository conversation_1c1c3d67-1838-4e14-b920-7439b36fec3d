"use client";
import { useState, useEffect } from "react";
import { IKContext, IKUpload } from "imagekitio-react";
import Image from "next/image";
import Link from "next/link";
import { getApiUrl, API_CONFIG } from "@/lib/config";

export default function AdminProducts() {
  const [form, setForm] = useState({
    name: "",
    image: "",
    description: "",
    customizable: false,
    slug: "",
    categoryId: "",
  });
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [price, setPrice] = useState(0);
  const [categories, setCategories] = useState<{ id: number; name: string }[]>(
    []
  );
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState("");

  // Fetch categories on mount
  useEffect(() => {
    setCategoriesLoading(true);
    fetch(getApiUrl(API_CONFIG.ENDPOINTS.CATEGORIES))
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fetch categories");
        return res.json();
      })
      .then((data) => {
        setCategories(data);
        setCategoriesError("");
      })
      .catch(() => {
        setCategories([]);
        setCategoriesError("Failed to load categories");
      })
      .finally(() => setCategoriesLoading(false));
  }, []);

  function handleChange(
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) {
    const { name, value, type } = e.target;
    setForm((f) => ({
      ...f,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setMessage("");
    setError("");
    const token = localStorage.getItem("token");
    if (!token) {
      setError("Not authenticated");
      return;
    }
    const res = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.PRODUCTS), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        ...form,
        price,
        categoryId: Number(form.categoryId),
      }),
    });
    const data = await res.json();
    if (res.ok) {
      setMessage("Product created!");
      setForm({
        name: "",
        image: "",
        description: "",
        customizable: false,
        slug: "",
        categoryId: "",
      });
      setPrice(0);
    } else {
      setError(data.error || "Failed to create product");
    }
  }

  return (
    <div className="max-w-xl mx-auto mt-16">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Add New Product</h2>
        <Link
          href="/admin/categories"
          className="bg-gray-200 text-gray-800 px-4 py-2 rounded-full font-semibold shadow hover:bg-gray-300 transition text-sm"
        >
          Manage Categories
        </Link>
      </div>
      <IKContext
        publicKey={process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY?.replace(
          /"/g,
          ""
        )}
        urlEndpoint={process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT?.replace(
          /"/g,
          ""
        )}
        authenticator={async () => {
          const res = await fetch("/api/imagekit-auth");
          return res.json();
        }}
      >
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <input
            name="name"
            value={form.name}
            onChange={handleChange}
            placeholder="Name"
            className="border rounded px-3 py-2"
            required
          />
          <input
            name="slug"
            value={form.slug}
            onChange={handleChange}
            placeholder="Slug (unique)"
            className="border rounded px-3 py-2"
            required
          />
          {/* ImageKit Upload */}
          <div>
            <label className="block mb-1 font-semibold">Product Image</label>
            <IKUpload
              fileName={form.slug ? `${form.slug}.jpg` : "product.jpg"}
              onSuccess={(res: { url: string }) =>
                setForm((f) => ({ ...f, image: res.url }))
              }
              onError={() => setError("Image upload failed")}
              className="border rounded px-3 py-2 w-full"
            />
            {form.image && (
              <Image
                src={form.image}
                alt="Preview"
                width={96}
                height={96}
                className="mt-2 h-24 w-auto rounded shadow"
              />
            )}
          </div>
          <textarea
            name="description"
            value={form.description}
            onChange={handleChange}
            placeholder="Description"
            className="border rounded px-3 py-2"
            required
          />
          <select
            name="categoryId"
            value={form.categoryId || ""}
            onChange={handleChange}
            className="border rounded px-3 py-2"
            required
            disabled={categoriesLoading || !!categoriesError}
          >
            <option value="" disabled>
              {categoriesLoading ? "Loading categories..." : "Select category"}
            </option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.name}
              </option>
            ))}
          </select>
          {categoriesError && (
            <div className="text-red-600 text-sm">{categoriesError}</div>
          )}
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              name="customizable"
              checked={form.customizable}
              onChange={handleChange}
            />
            Customizable
          </label>
          <label className="font-semibold">
            Price
            <div className="flex items-center mt-1">
              <span className="px-2 py-2 bg-gray-100 border border-r-0 rounded-l">
                K
              </span>
              <input
                type="number"
                min="0"
                step="0.01"
                className="w-full border rounded-r px-3 py-2 focus:outline-none"
                value={price}
                onChange={(e) => setPrice(Number(e.target.value))}
                required
              />
            </div>
          </label>
          <button
            type="submit"
            className="bg-[#1a237e] text-white font-semibold px-6 py-2 rounded-full shadow hover:bg-[#283593] transition"
          >
            Add Product
          </button>
          {message && <div className="text-green-600">{message}</div>}
          {error && <div className="text-red-600">{error}</div>}
        </form>
      </IKContext>
    </div>
  );
}
