generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Product {
  id           Int      @id @default(autoincrement())
  name         String
  image        String
  description  String
  customizable Boolean
  slug         String   @unique
  category     String
  modelUrl     String?
  price        Float    @default(0)
  orders       Order[]
}

model Category {
  id   Int    @id @default(autoincrement())
  name String @unique
}

model User {
  id                    Int      @id @default(autoincrement())
  email                 String   @unique
  password              String
  name                  String?
  role                  Role     @default(USER)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  profileImage          String?

  // Email verification fields
  emailVerified         Boolean  @default(false)
  emailVerificationToken String?  @unique
  emailVerificationExpires DateTime?

  // Password reset fields
  passwordResetToken    String?  @unique
  passwordResetExpires  DateTime?

  orders                Order[]
}

model Order {
  id                   Int         @id @default(autoincrement())
  userId               Int
  productId            Int
  status               OrderStatus @default(PENDING)
  createdAt            DateTime    @default(now())
  updatedAt            DateTime    @updatedAt
  customerName         String      @default("")
  customerPhone        String?
  customerEmail        String?
  customerAddress      String?
  customColor          String?
  customText           String?
  isCustomized         Boolean?    @default(false)
  customizationData    String?
  customizationPreview String?
  quantity             Int         @default(1)
  unitPrice            Float?
  totalPrice           Float?
  product              Product     @relation(fields: [productId], references: [id])
  user                 User        @relation(fields: [userId], references: [id])

  @@index([createdAt])
  @@index([productId])
  @@index([status])
}

enum Role {
  USER
  ADMIN
}

enum OrderStatus {
  PENDING
  COLLECTED
}
