// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Category {
  id      Int      @id @default(autoincrement())
  name    String   @unique
  products Product[]
}

model Product {
  id           Int      @id @default(autoincrement())
  name         String
  image        String
  description  String
  customizable Boolean
  slug         String   @unique
  categoryId   Int
  category     Category @relation(fields: [categoryId], references: [id])
  modelUrl     String?  // Optional, for 3D models
  price        Float    @default(0) // Price in Zambian Kwacha
  orders       Order[]
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  name      String?
  profileImage String? // URL to profile image (ImageKit)
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orders    Order[]
}

model Order {
  id         Int      @id @default(autoincrement())
  user       User     @relation(fields: [userId], references: [id])
  userId     Int
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  status     OrderStatus @default(PENDING)

  // Customer information
  customerName    String
  customerPhone   String
  customerEmail   String
  customerAddress String

  // Product customization details
  customColor     String?
  customText      String?
  isCustomized    Boolean @default(false)

  // Order details
  quantity        Int     @default(1)
  unitPrice       Float
  totalPrice      Float

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

enum Role {
  USER
  ADMIN
}

enum OrderStatus {
  PENDING
  COLLECTED
}
