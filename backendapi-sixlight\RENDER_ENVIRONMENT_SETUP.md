# 🚀 Render Environment Variables Setup

## ❌ Current Error: Missing SMTP Credentials

The error "Missing credentials for PLAIN" means your SMTP environment variables are not set in Render.

## 📋 Required Environment Variables for Render

### **CRITICAL - Email Configuration**
```
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=dxsl tqan xfzc vynn
```

### **CRITICAL - Production URLs**
```
NODE_ENV=production
FRONTEND_URL=https://sixlightmediastorebeta.netlify.app
CORS_ORIGIN=https://sixlightmediastorebeta.netlify.app
```

### **Database (Already Set)**
```
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
DIRECT_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
JWT_SECRET=tVfn+VlANCzmunn+0AvG3WKJ+frOoPBUfLD8XsLS+bA=
```

### **Email Service Configuration**
```
EMAIL_FROM_NAME=Six Light Media Store
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_VERIFICATION_EXPIRES_HOURS=24
PASSWORD_RESET_EXPIRES_HOURS=1
```

### **Security Settings**
```
COOKIE_SECURE=true
TRUST_PROXY=true
```

## 🔧 Step-by-Step Setup in Render

### 1. Go to Render Dashboard
- Navigate to your backend service
- Click "Environment" tab

### 2. Add Each Variable
Click "Add Environment Variable" and add each one:

| Key | Value |
|-----|-------|
| `SMTP_HOST` | `smtp.gmail.com` |
| `SMTP_PORT` | `587` |
| `SMTP_USER` | `<EMAIL>` |
| `SMTP_PASS` | `dxsl tqan xfzc vynn` |
| `NODE_ENV` | `production` |
| `FRONTEND_URL` | `https://sixlightmediastorebeta.netlify.app` |
| `CORS_ORIGIN` | `https://sixlightmediastorebeta.netlify.app` |
| `EMAIL_FROM_NAME` | `Six Light Media Store` |
| `EMAIL_FROM_ADDRESS` | `<EMAIL>` |
| `EMAIL_VERIFICATION_EXPIRES_HOURS` | `24` |
| `PASSWORD_RESET_EXPIRES_HOURS` | `1` |
| `COOKIE_SECURE` | `true` |
| `TRUST_PROXY` | `true` |

### 3. Deploy Service
After adding all variables, click "Deploy Latest Commit" or trigger a new deployment.

## 🔍 Debugging Steps

### Check Logs After Deployment
Look for these messages in Render logs:

✅ **Success:**
```
🔧 Initializing Email Service...
📧 SMTP Configuration:
   Host: smtp.gmail.com
   Port: 587
   User: <EMAIL>
   Pass: ***configured***
   Environment: production
✅ SMTP connection verified successfully
```

❌ **Still Failing:**
```
❌ CRITICAL: Missing SMTP credentials!
   SMTP_USER: MISSING
   SMTP_PASS: MISSING
```

## 🆘 If Still Not Working

1. **Double-check variable names** - They must be EXACT
2. **No quotes** - Don't add quotes around values in Render
3. **Redeploy** - Environment changes require redeployment
4. **Check logs** - Look for the debug messages we added

## ✅ Verification

After setup, test email verification:
1. Register new user in production
2. Check if verification email arrives
3. Verify email links work correctly

## 📞 Quick Fix Commands

If you have access to Render CLI:
```bash
render env set SMTP_USER=<EMAIL>
render env set SMTP_PASS="dxsl tqan xfzc vynn"
render env set NODE_ENV=production
render env set FRONTEND_URL=https://sixlightmediastorebeta.netlify.app
```
