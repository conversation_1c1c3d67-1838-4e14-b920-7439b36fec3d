(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{4049:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(5155),l=r(2115),a=r(6874),o=r.n(a),n=r(5695),d=r(6766),i=r(4065),c=r(2502),x=r(4615),m=r(3389);function u(e){let{isOpen:t,title:r,message:a,confirmText:o="Confirm",cancelText:n="Cancel",onConfirm:d,onCancel:i,type:c="info"}=e;if((0,l.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t&&i()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,i]),!t)return null;let x=(()=>{switch(c){case"warning":return{icon:"⚠️",iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmBg:"bg-yellow-600 hover:bg-yellow-700",titleColor:"text-yellow-800"};case"danger":return{icon:"\uD83D\uDEA8",iconBg:"bg-red-100",iconColor:"text-red-600",confirmBg:"bg-red-600 hover:bg-red-700",titleColor:"text-red-800"};case"success":return{icon:"✅",iconBg:"bg-green-100",iconColor:"text-green-600",confirmBg:"bg-green-600 hover:bg-green-700",titleColor:"text-green-800"};default:return{icon:"\uD83D\uDCB0",iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmBg:"bg-blue-600 hover:bg-blue-700",titleColor:"text-blue-800"}}})();return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out scale-100",children:[(0,s.jsx)("div",{className:"p-6 pb-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-full ".concat(x.iconBg," flex items-center justify-center flex-shrink-0"),children:(0,s.jsx)("span",{className:"text-2xl",children:x.icon})}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("h3",{className:"text-xl font-bold ".concat(x.titleColor," leading-tight"),children:r})})]})}),(0,s.jsx)("div",{className:"px-6 pb-6",children:(0,s.jsx)("p",{className:"text-gray-700 text-base leading-relaxed",children:a})}),(0,s.jsxs)("div",{className:"px-6 pb-6 flex gap-3 justify-end",children:[(0,s.jsx)("button",{onClick:i,className:"px-6 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300",children:n}),(0,s.jsx)("button",{onClick:d,className:"px-6 py-2.5 text-white ".concat(x.confirmBg," rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg"),children:o})]})]})})}var h=r(3843);function g(){var e,t,r,a,c,g,b,p,f,v,j,N,w,y,C,k,D,E,O,L;let[M,A]=(0,l.useState)(null),[B,S]=(0,l.useState)(""),[P,T]=(0,l.useState)(1),[z,I]=(0,l.useState)("products"),[U,R]=(0,l.useState)(0),[_,F]=(0,l.useState)(!1),[V,W]=(0,l.useState)(!1),[H,G]=(0,l.useState)(null),K=(0,n.useRouter)(),q=()=>{R(JSON.parse(localStorage.getItem("cart")||"[]").length)},J=e=>{G(e),W(!0)};if((0,l.useEffect)(()=>{q()},[]),(0,l.useEffect)(()=>{let e=localStorage.getItem("token");if(!e)return void S("Not authenticated");fetch((0,h.e9)(h.i3.ENDPOINTS.ADMIN.DASHBOARD),{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.json()).then(e=>{A(e)}).catch(()=>S("Failed to load dashboard"))},[]),B)return(0,s.jsx)("div",{className:"text-red-600 text-center mt-16",children:B});if(!M)return(0,s.jsx)("div",{className:"text-center mt-16",children:"Loading..."});let Q=M.products||[],Y=Math.max(1,Math.ceil(Q.length/5)),$=Q.slice((P-1)*5,5*P),X=null!=(y=null!=(w=null==(e=M.stats)?void 0:e.orders)?w:null==(t=M.orders)?void 0:t.length)?y:0,Z=null!=(k=null!=(C=null==(r=M.stats)?void 0:r.collectedOrders)?C:null==(a=M.orders)?void 0:a.filter(e=>"COLLECTED"===e.status).length)?k:0;async function ee(e){if(!confirm("Are you sure you want to delete this product?"))return;let t=localStorage.getItem("token");try{if(!(await fetch((0,h.e9)("".concat(h.i3.ENDPOINTS.ADMIN.PRODUCTS,"/").concat(e)),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to delete product");A(t=>t&&t.products?{...t,products:t.products.filter(t=>t.id!==e)}:t)}catch(e){alert("Error deleting product")}}async function et(e){if(!e)return;let t=localStorage.getItem("token");try{if(!(await fetch((0,h.e9)("".concat(h.i3.ENDPOINTS.ADMIN.ORDER_COLLECTED,"/").concat(e,"/collected")),{method:"PUT",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to mark as collected");A(t=>{var r,s;return t&&t.orders?{...t,orders:t.orders.map(t=>t.id===e?{...t,status:"COLLECTED"}:t),stats:{...t.stats,collectedOrders:(null!=(s=null==(r=t.stats)?void 0:r.collectedOrders)?s:0)+1}}:t})}catch(e){alert("Error updating order status")}}return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,s.jsx)(x.A,{cartCount:U,onCartClick:()=>{F(!0)}}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-black text-white mb-4",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"Admin"}),(0,s.jsx)("span",{className:"text-white",children:" Dashboard"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto",children:"Manage your Six Light Media store with powerful tools and real-time analytics"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("div",{className:"text-3xl font-black text-yellow-400 mb-2",children:null!=(D=null==(c=M.stats)?void 0:c.users)?D:"-"}),(0,s.jsx)("div",{className:"text-gray-200 text-sm",children:"Total Users"})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("div",{className:"text-3xl font-black text-pink-400 mb-2",children:null!=(E=null==(g=M.stats)?void 0:g.orders)?E:"-"}),(0,s.jsx)("div",{className:"text-gray-200 text-sm",children:"Total Orders"})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("div",{className:"text-3xl font-black text-purple-400 mb-2",children:null!=(O=null==(b=M.stats)?void 0:b.collectedOrders)?O:M.orders?M.orders.filter(e=>"COLLECTED"===e.status).length:"-"}),(0,s.jsx)("div",{className:"text-gray-200 text-sm",children:"Collected"})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("div",{className:"text-3xl font-black text-blue-400 mb-2",children:null!=(L=null==(p=M.stats)?void 0:p.products)?L:M.products?M.products.length:"-"}),(0,s.jsx)("div",{className:"text-gray-200 text-sm",children:"Products"})]})]})]})})]}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,s.jsx)("div",{className:"bg-white rounded-3xl shadow-xl p-2 mb-8 border border-gray-100",children:(0,s.jsxs)("div",{className:"flex flex-wrap justify-center gap-2",children:[(0,s.jsx)("button",{className:"px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ".concat("products"===z?"bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:bg-gray-50 hover:text-indigo-600"),onClick:()=>I("products"),type:"button",children:"\uD83D\uDCE6 Products"}),(0,s.jsx)("button",{className:"px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ".concat("users"===z?"bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:bg-gray-50 hover:text-indigo-600"),onClick:()=>I("users"),type:"button",children:"\uD83D\uDC65 Users"}),(0,s.jsx)("button",{className:"px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base ".concat("orders"===z?"bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:bg-gray-50 hover:text-indigo-600"),onClick:()=>I("orders"),type:"button",children:"\uD83D\uDCCB Orders"}),(0,s.jsx)(o(),{href:"/admin/categories",className:"px-6 py-3 rounded-2xl font-semibold transition-all duration-300 min-w-[140px] text-sm md:text-base text-gray-600 hover:bg-gray-50 hover:text-indigo-600 flex items-center justify-center",children:"\uD83C\uDFF7️ Categories"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[(0,s.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6 flex items-center",children:"\uD83D\uDCCA Performance Metrics"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-6 border border-blue-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-blue-700 bg-blue-200 px-3 py-1 rounded-full",children:"Completion Rate"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-blue-700 mb-2",children:(null==(f=M.stats)?void 0:f.orders)&&(null==(v=M.stats)?void 0:v.collectedOrders)?"".concat(Math.round(M.stats.collectedOrders/M.stats.orders*100),"%"):"-"}),(0,s.jsx)("div",{className:"text-sm text-blue-600",children:"Orders successfully completed"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-6 border border-green-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-green-700 bg-green-200 px-3 py-1 rounded-full",children:"Pending"})]}),(0,s.jsx)("div",{className:"text-3xl font-black text-green-700 mb-2",children:(null==(j=M.stats)?void 0:j.orders)&&(null==(N=M.stats)?void 0:N.collectedOrders)!==void 0?M.stats.orders-M.stats.collectedOrders:M.orders?M.orders.filter(e=>"PENDING"===e.status).length:"-"}),(0,s.jsx)("div",{className:"text-sm text-green-600",children:"Orders awaiting collection"})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 border border-gray-100",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6 text-center",children:"\uD83D\uDCC8 Order Status"}),(0,s.jsx)("div",{className:"w-full h-64 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-48 h-48",children:(0,s.jsx)(i.nu,{data:{labels:["Collected","Pending"],datasets:[{data:[Z,X-Z],backgroundColor:["#2563eb","#fbbf24"],borderColor:["#1e40af","#f59e42"],borderWidth:2}]},options:{cutout:"70%",plugins:{legend:{display:!0,position:"bottom",labels:{color:"#1a237e",font:{size:14,weight:"bold"}}},tooltip:{callbacks:{label:function(e){let t=e.label||"",r=e.raw||0;return"".concat(t,": ").concat(r)}}}},responsive:!0,maintainAspectRatio:!1}})})})]})]}),"products"===z&&Q.length>0&&(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 flex items-center",children:"\uD83D\uDCE6 Product Management"}),(0,s.jsx)(o(),{href:"/admin/products",className:"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"➕ Add New Product"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:$.map((e,t)=>{var r,l;return(0,s.jsxs)("div",{className:"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsx)("div",{className:"aspect-square mb-4 bg-white rounded-xl overflow-hidden border border-gray-100",children:(0,s.jsx)(d.default,{src:e.image||"/bottle-dummy.jpg",alt:e.name,width:200,height:200,className:"w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"font-bold text-gray-900 text-lg line-clamp-2",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.category&&(0,s.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full",children:"string"==typeof e.category?e.category:(null==(r=e.category)?void 0:r.name)||"Unknown Category"}),(0,s.jsxs)("span",{className:"px-3 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full",children:["K",null!=(l=e.price)?l:0]})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,s.jsx)("button",{className:"flex-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md",onClick:()=>{K.push("/admin/products/".concat(e.id))},children:"✏️ Edit"}),(0,s.jsx)("button",{className:"flex-1 px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white text-sm font-semibold rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-md",onClick:()=>ee(e.id),children:"\uD83D\uDDD1️ Delete"})]})]})]},e.id||t)})}),Y>1&&(0,s.jsxs)("div",{className:"flex justify-center items-center gap-4 mt-8",children:[(0,s.jsx)("button",{className:"px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",onClick:()=>T(e=>Math.max(1,e-1)),disabled:1===P,children:"← Previous"}),(0,s.jsxs)("span",{className:"px-4 py-2 bg-gray-100 rounded-xl font-semibold text-gray-700",children:["Page ",P," of ",Y]}),(0,s.jsx)("button",{className:"px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg",onClick:()=>T(e=>Math.min(Y,e+1)),disabled:P===Y,children:"Next →"})]})]}),"users"===z&&M.users&&M.users.length>0&&(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 flex items-center",children:"\uD83D\uDC65 User Management"}),(0,s.jsx)(o(),{href:"/admin/users",className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"\uD83D\uDC65 Manage All Users"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:M.users.slice(0,9).map(e=>{var t,r;return(0,s.jsxs)("div",{className:"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg",children:(null==(r=e.name||e.email)||null==(t=r.charAt(0))?void 0:t.toUpperCase())||"U"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h4",{className:"font-bold text-gray-900 truncate",children:e.name||e.email}),(0,s.jsx)("p",{className:"text-sm text-gray-600 truncate",children:e.email})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Role:"}),(0,s.jsx)("span",{className:"px-3 py-1 text-xs font-semibold rounded-full ".concat("ADMIN"===e.role?"bg-red-100 text-red-700":"bg-blue-100 text-blue-700"),children:e.role})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Status:"}),(0,s.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full",children:"✅ Active"})]}),(0,s.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["\uD83D\uDCC5 Member since:"," ",new Date(e.createdAt||Date.now()).toLocaleDateString()]})})]})]},e.id)})})]}),"orders"===z&&M.orders&&M.orders.length>0&&(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 mb-12 border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 flex items-center",children:"\uD83D\uDCCB Order Management"}),(0,s.jsx)(o(),{href:"/admin/orders",className:"px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"\uD83D\uDCCB View All Orders"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:M.orders.slice(0,8).map((e,t)=>{var r,l,a;return(0,s.jsxs)("div",{className:"group bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center text-white font-bold text-sm",children:["#",e.id]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-bold text-gray-900",children:["Order #",e.id]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:(null==(r=e.user)?void 0:r.name)||(null==(l=e.user)?void 0:l.email)||"Unknown Customer"})]})]}),(0,s.jsx)("span",{className:"px-3 py-1 text-xs font-semibold rounded-full ".concat("COLLECTED"===e.status?"bg-green-100 text-green-700":"PENDING"===e.status?"bg-yellow-100 text-yellow-700":"bg-gray-100 text-gray-700"),children:"COLLECTED"===e.status?"✅ Collected":"PENDING"===e.status?"⏳ Pending":e.status})]}),e.product&&(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-xl",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-white rounded-lg overflow-hidden border border-gray-200",children:(0,s.jsx)(d.default,{src:e.product.image||"/bottle-dummy.jpg",alt:e.product.name,width:48,height:48,className:"w-full h-full object-contain"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h5",{className:"font-semibold text-gray-900 truncate",children:e.product.name}),(0,s.jsxs)("p",{className:"text-sm text-green-600 font-semibold",children:["K",null!=(a=e.product.price)?a:0]})]})]}),(0,s.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Order Date:"}),(0,s.jsx)("span",{className:"font-medium text-gray-900",children:new Date(e.createdAt||Date.now()).toLocaleDateString()})]}),e.customization&&(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Customization:"}),(0,s.jsxs)("div",{className:"mt-1 p-2 bg-blue-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-xs text-blue-700",children:["\uD83C\uDFA8 Color: ",e.customization.color]}),e.customization.text&&(0,s.jsxs)("div",{className:"text-xs text-blue-700",children:["\uD83D\uDCDD Text: “",e.customization.text,"”"]})]})]})]}),"PENDING"===e.status&&(0,s.jsx)("button",{className:"w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md",onClick:()=>e.id&&J(e.id),children:"✅ Mark as Collected"})]},e.id||t)})})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-800 rounded-3xl p-8 text-center relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold text-white mb-4",children:"\uD83D\uDE80 Quick Actions"}),(0,s.jsx)("p",{className:"text-gray-200 mb-8 max-w-2xl mx-auto",children:"Manage your store efficiently with these powerful tools"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto",children:[(0,s.jsxs)(o(),{href:"/admin/products",className:"group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,s.jsx)("h4",{className:"text-xl font-bold text-white mb-2",children:"Add Product"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm",children:"Upload new products to your store"})]}),(0,s.jsxs)(o(),{href:"/admin/categories",className:"group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})}),(0,s.jsx)("h4",{className:"text-xl font-bold text-white mb-2",children:"Categories"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm",children:"Organize your product categories"})]}),(0,s.jsxs)(o(),{href:"/admin/orders",className:"group bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})}),(0,s.jsx)("h4",{className:"text-xl font-bold text-white mb-2",children:"All Orders"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm",children:"View and manage all orders"})]})]})]})]})]}),(0,s.jsx)(m.A,{isOpen:_,onClose:()=>{F(!1),q()}}),(0,s.jsx)(u,{isOpen:V,title:"Confirm Order Collection",message:"Has the customer paid and collected this order? This action will mark the order as completed.",confirmText:"Yes, Mark as Collected",cancelText:"Cancel",onConfirm:()=>{H&&et(H),W(!1),G(null)},onCancel:()=>{W(!1),G(null)},type:"info"})]})}c.t1.register(c.Bs,c.m_,c.s$)},5821:(e,t,r)=>{Promise.resolve().then(r.bind(r,4049))}},e=>{var t=t=>e(e.s=t);e.O(0,[647,766,874,276,211,441,684,358],()=>t(5821)),_N_E=e.O()}]);